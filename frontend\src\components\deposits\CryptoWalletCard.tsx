import React, { useState } from 'react';
import {
  Box,
  Card,
  CardBody,
  VStack,
  HStack,
  Text,
  Button,
  Icon,
  Image,
  Skeleton,
  useToast,
  Tooltip,
  Badge,
  Flex,
  Divider
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { FaCopy, FaQrcode, FaWallet, FaArrowUp } from 'react-icons/fa';
import { Link as RouterLink } from 'react-router-dom';

const MotionCard = motion(Card);

interface CryptoWalletCardProps {
  currency: string;
  address: string;
  balance: number;
  usdtValue: number;
  lastUpdated: Date;
  isLoading?: boolean;
  onCopy?: (address: string) => void;
}

const CryptoWalletCard: React.FC<CryptoWalletCardProps> = ({
  currency,
  address,
  balance,
  usdtValue,
  lastUpdated,
  isLoading = false,
  onCopy
}) => {
  const toast = useToast();
  const [isCopying, setIsCopying] = useState(false);

  // Binance theme colors
  const bgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Currency icons mapping
  const currencyIcons: { [key: string]: string } = {
    BTC: '/icons/btc.svg',
    ETH: '/icons/eth.svg',
    USDT: '/icons/usdt.svg',
    BNB: '/icons/bnb.svg',
    ADA: '/icons/ada.svg',
    DOT: '/icons/dot.svg'
  };

  // Format address for display (first 6 + last 4 characters)
  const formatAddress = (addr: string): string => {
    if (!addr || addr.length <= 10) return addr;
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  // Handle copy address
  const handleCopyAddress = async () => {
    if (isCopying) return;
    
    setIsCopying(true);
    try {
      await navigator.clipboard.writeText(address);
      
      toast({
        title: "Adres Kopyalandı",
        description: `${currency} cüzdan adresi panoya kopyalandı`,
        status: "success",
        duration: 2000,
        isClosable: true,
        position: "top"
      });

      if (onCopy) {
        onCopy(address);
      }
    } catch (error) {
      toast({
        title: "Kopyalama Hatası",
        description: "Adres kopyalanırken bir hata oluştu",
        status: "error",
        duration: 3000,
        isClosable: true,
        position: "top"
      });
    } finally {
      setTimeout(() => setIsCopying(false), 1000);
    }
  };

  // Format balance display
  const formatBalance = (bal: number): string => {
    if (bal === 0) return '0.00';
    if (bal < 0.000001) return '< 0.000001';
    if (bal < 1) return bal.toFixed(6);
    return bal.toFixed(4);
  };

  // Format USDT value
  const formatUSDTValue = (value: number): string => {
    if (value === 0) return '$0.00';
    if (value < 0.01) return '< $0.01';
    return `$${value.toFixed(2)}`;
  };

  // Get currency color
  const getCurrencyColor = (curr: string): string => {
    const colors: { [key: string]: string } = {
      BTC: '#F7931A',
      ETH: '#627EEA',
      USDT: '#26A17B',
      BNB: '#F3BA2F',
      ADA: '#0033AD',
      DOT: '#E6007A'
    };
    return colors[curr] || primaryColor;
  };

  if (isLoading) {
    return (
      <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
        <CardBody p={6}>
          <VStack spacing={4} align="stretch">
            <Skeleton height="40px" />
            <Skeleton height="20px" />
            <Skeleton height="20px" />
            <Skeleton height="36px" />
          </VStack>
        </CardBody>
      </Card>
    );
  }

  return (
    <MotionCard
      bg={bgColor}
      borderColor={borderColor}
      borderWidth="1px"
      _hover={{
        borderColor: primaryColor,
        transform: 'translateY(-2px)',
        boxShadow: `0 8px 25px rgba(240, 185, 11, 0.15)`
      }}
      transition="all 0.3s ease"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2 }}
    >
      <CardBody p={6}>
        <VStack spacing={4} align="stretch">
          {/* Header with Currency Info */}
          <Flex justify="space-between" align="center">
            <HStack spacing={3}>
              <Box
                w="40px"
                h="40px"
                borderRadius="full"
                bg={`${getCurrencyColor(currency)}20`}
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                {currencyIcons[currency] ? (
                  <Image
                    src={currencyIcons[currency]}
                    alt={currency}
                    w="24px"
                    h="24px"
                    fallback={
                      <Icon as={FaWallet} color={getCurrencyColor(currency)} boxSize={5} />
                    }
                  />
                ) : (
                  <Icon as={FaWallet} color={getCurrencyColor(currency)} boxSize={5} />
                )}
              </Box>
              <Box>
                <Text color={textColor} fontWeight="bold" fontSize="lg">
                  {currency}
                </Text>
                <Text color={secondaryTextColor} fontSize="sm">
                  Cüzdan
                </Text>
              </Box>
            </HStack>
            
            {balance > 0 && (
              <Badge
                colorScheme="green"
                variant="subtle"
                borderRadius="full"
                px={3}
                py={1}
              >
                Aktif
              </Badge>
            )}
          </Flex>

          <Divider borderColor={borderColor} />

          {/* Balance Information */}
          <VStack spacing={2} align="stretch">
            <Flex justify="space-between" align="center">
              <Text color={secondaryTextColor} fontSize="sm">
                Bakiye:
              </Text>
              <Text color={textColor} fontWeight="semibold">
                {formatBalance(balance)} {currency}
              </Text>
            </Flex>
            
            <Flex justify="space-between" align="center">
              <Text color={secondaryTextColor} fontSize="sm">
                USDT Değeri:
              </Text>
              <Text color={primaryColor} fontWeight="bold">
                {formatUSDTValue(usdtValue)}
              </Text>
            </Flex>
          </VStack>

          <Divider borderColor={borderColor} />

          {/* Wallet Address */}
          <VStack spacing={2} align="stretch">
            <Text color={secondaryTextColor} fontSize="sm">
              Cüzdan Adresi:
            </Text>
            <Tooltip label={address} placement="top">
              <HStack
                bg={`${borderColor}50`}
                p={3}
                borderRadius="md"
                cursor="pointer"
                onClick={handleCopyAddress}
                _hover={{ bg: `${primaryColor}20` }}
                transition="all 0.2s"
              >
                <Text
                  color={textColor}
                  fontSize="sm"
                  fontFamily="mono"
                  flex={1}
                  isTruncated
                >
                  {formatAddress(address)}
                </Text>
                <Icon
                  as={FaCopy}
                  color={isCopying ? primaryColor : secondaryTextColor}
                  boxSize={4}
                  transition="color 0.2s"
                />
              </HStack>
            </Tooltip>
          </VStack>

          {/* Action Buttons */}
          <VStack spacing={2}>
            <Button
              as={RouterLink}
              to={`/deposit/${currency.toLowerCase()}`}
              leftIcon={<FaArrowUp />}
              bg={primaryColor}
              color="#0B0E11"
              _hover={{ bg: "#F8D12F" }}
              size="md"
              width="full"
              fontWeight="bold"
            >
              Para Yatır
            </Button>
            
            <Button
              leftIcon={<FaQrcode />}
              variant="outline"
              borderColor={borderColor}
              color={textColor}
              _hover={{
                borderColor: primaryColor,
                color: primaryColor
              }}
              size="sm"
              width="full"
              onClick={() => {
                // QR code modal açılacak
                toast({
                  title: "QR Kod",
                  description: "QR kod özelliği yakında eklenecek",
                  status: "info",
                  duration: 2000,
                  isClosable: true
                });
              }}
            >
              QR Kod
            </Button>
          </VStack>

          {/* Last Updated */}
          <Text color={secondaryTextColor} fontSize="xs" textAlign="center">
            Son güncelleme: {new Date(lastUpdated).toLocaleString('tr-TR')}
          </Text>
        </VStack>
      </CardBody>
    </MotionCard>
  );
};

export default CryptoWalletCard;
