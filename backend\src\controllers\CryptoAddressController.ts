import { Request, Response } from 'express';
import { CryptoAddressService } from '../services/CryptoAddressService';
import { body, param, validationResult } from 'express-validator';
import rateLimit from 'express-rate-limit';

// Rate limiting for address generation (max 5 addresses per hour per user)
export const addressGenerationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5,
  message: {
    error: 'Too many address generation requests',
    retryAfter: '1 hour'
  },
  keyGenerator: (req) => `address_gen_${req.user?.id || req.ip}`,
  standardHeaders: true,
  legacyHeaders: false
});

export class CryptoAddressController {
  private cryptoAddressService: CryptoAddressService;

  constructor() {
    this.cryptoAddressService = CryptoAddressService.getInstance();
  }

  /**
   * Generate new crypto address for user
   * POST /api/crypto/addresses/generate
   */
  public generateAddress = async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { currency } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User authentication required'
        });
        return;
      }

      // Generate address
      const address = await this.cryptoAddressService.generateAddress(currency, userId);

      res.status(201).json({
        success: true,
        message: 'Address generated successfully',
        data: {
          address: address.address,
          currency: address.currency,
          createdAt: address.createdAt
        }
      });

    } catch (error) {
      console.error('Address generation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate address',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };

  /**
   * Get user's crypto addresses
   * GET /api/crypto/addresses
   */
  public getUserAddresses = async (req: Request, res: Response): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { currency } = req.query;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User authentication required'
        });
        return;
      }

      const addresses = await this.cryptoAddressService.getUserAddresses(
        userId,
        currency as string
      );

      res.status(200).json({
        success: true,
        message: 'Addresses retrieved successfully',
        data: addresses.map(addr => ({
          address: addr.address,
          currency: addr.currency,
          createdAt: addr.createdAt
        }))
      });

    } catch (error) {
      console.error('Get addresses error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve addresses'
      });
    }
  };

  /**
   * Validate crypto address
   * POST /api/crypto/addresses/validate
   */
  public validateAddress = async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { address, currency } = req.body;

      const validation = this.cryptoAddressService.validateAddress(address, currency);

      res.status(200).json({
        success: true,
        message: 'Address validation completed',
        data: validation
      });

    } catch (error) {
      console.error('Address validation error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to validate address'
      });
    }
  };

  /**
   * Get deposit address for specific currency
   * GET /api/crypto/addresses/deposit/:currency
   */
  public getDepositAddress = async (req: Request, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { currency } = req.params;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          message: 'User authentication required'
        });
        return;
      }

      // Get existing active address or generate new one
      const existingAddresses = await this.cryptoAddressService.getUserAddresses(userId, currency);

      let depositAddress;
      if (existingAddresses.length > 0) {
        // Use the most recent address
        depositAddress = existingAddresses[0];
      } else {
        // Generate new address
        depositAddress = await this.cryptoAddressService.generateAddress(currency, userId);
      }

      res.status(200).json({
        success: true,
        message: 'Deposit address retrieved successfully',
        data: {
          address: depositAddress.address,
          currency: depositAddress.currency,
          qrCode: `${currency.toLowerCase()}:${depositAddress.address}`,
          instructions: {
            minConfirmations: this.getMinConfirmations(currency),
            networkFee: this.getNetworkFee(currency),
            processingTime: this.getProcessingTime(currency)
          }
        }
      });

    } catch (error) {
      console.error('Get deposit address error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get deposit address'
      });
    }
  };

  /**
   * Get minimum confirmations for currency
   */
  private getMinConfirmations(currency: string): number {
    const confirmations = {
      BTC: 3,
      ETH: 12,
      USDT: 12,
      BNB: 12,
      TRX: 19,
      SOL: 1
    };
    return confirmations[currency.toUpperCase() as keyof typeof confirmations] || 3;
  }

  /**
   * Get network fee information
   */
  private getNetworkFee(currency: string): string {
    const fees = {
      BTC: 'Variable (network dependent)',
      ETH: 'Variable (gas dependent)',
      USDT: 'Variable (gas dependent)',
      BNB: 'Variable (gas dependent)',
      TRX: 'Variable (bandwidth/energy)',
      SOL: '~0.000005 SOL'
    };
    return fees[currency.toUpperCase() as keyof typeof fees] || 'Variable';
  }

  /**
   * Get processing time information
   */
  private getProcessingTime(currency: string): string {
    const times = {
      BTC: '30-60 minutes',
      ETH: '5-15 minutes',
      USDT: '5-15 minutes',
      BNB: '3-5 minutes',
      TRX: '1-3 minutes',
      SOL: '1-2 minutes'
    };
    return times[currency.toUpperCase() as keyof typeof times] || '10-30 minutes';
  }
}

// Validation middleware
export const generateAddressValidation = [
  body('currency')
    .isIn(['BTC', 'ETH', 'USDT', 'BNB', 'TRX', 'SOL'])
    .withMessage('Invalid currency. Supported: BTC, ETH, USDT, BNB, TRX, SOL'),
];

export const validateAddressValidation = [
  body('address')
    .isLength({ min: 20, max: 100 })
    .withMessage('Invalid address length'),
  body('currency')
    .isIn(['BTC', 'ETH', 'USDT', 'BNB', 'TRX', 'SOL'])
    .withMessage('Invalid currency'),
];

export const getDepositAddressValidation = [
  param('currency')
    .isIn(['BTC', 'ETH', 'USDT', 'BNB', 'TRX', 'SOL'])
    .withMessage('Invalid currency. Supported: BTC, ETH, USDT, BNB, TRX, SOL'),
];