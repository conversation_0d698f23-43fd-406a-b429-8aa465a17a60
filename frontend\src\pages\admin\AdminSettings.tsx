import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  VStack,
  FormControl,
  FormLabel,
  Input,
  Button,
  Divider,
  Text,
  useToast,
  Switch,
  HStack,
  Select,
  Textarea,
  Grid,
  GridItem,
  Card,
  CardHeader,
  CardBody,
  Flex,
  Spinner,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { adminApiService } from '../../services/adminApi';

interface SystemConfig {
  siteName: string;
  siteDescription: string;
  maintenanceMode: boolean;
  maintenanceMessage?: string;
  commissionRate: number;
  referralRate: number;
  minimumDeposit: number;
  minimumWithdrawal: number;
  withdrawalsEnabled: boolean;
  depositsEnabled: boolean;
  emailNotifications?: string;
  supportedCurrencies: string[];
}

const AdminSettings = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form states
  const [systemConfig, setSystemConfig] = useState<SystemConfig>({
    siteName: 'Shipping Finance',
    siteDescription: 'Secure Crypto Investment Platform',
    maintenanceMode: false,
    commissionRate: 1,
    referralRate: 3,
    minimumDeposit: 100,
    minimumWithdrawal: 50,
    withdrawalsEnabled: true,
    depositsEnabled: true,
    supportedCurrencies: ['BTC', 'ETH', 'USDT', 'BNB', 'DOGE', 'XRP'],
  });

  // Notification settings
  const [maintenanceMessage, setMaintenanceMessage] = useState('');
  const [emailNotifications, setEmailNotifications] = useState('all');

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  // Fetch system configuration on component mount
  useEffect(() => {
    fetchSystemConfig();
  }, []);

  const fetchSystemConfig = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await adminApiService.getSystemConfig();

      if (response.data.success) {
        const config = response.data.data;
        setSystemConfig(config);
        // Set notification settings from config
        if (config.maintenanceMessage) {
          setMaintenanceMessage(config.maintenanceMessage);
        }
        if (config.emailNotifications) {
          setEmailNotifications(config.emailNotifications);
        }
      }
    } catch (err: any) {
      console.error('Error fetching system config:', err);
      setError(t('admin.system.errorFetchingConfig'));
      toast({
        title: t('admin.system.error'),
        description: t('admin.system.errorFetchingConfig'),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    try {
      setIsSubmitting(true);
      setError(null);

      const updateData = {
        ...systemConfig,
        maintenanceMessage,
        emailNotifications,
      };

      const response = await adminApiService.updateSystemConfig(updateData);

      if (response.data.success) {
        toast({
          title: t('admin.system.success'),
          description: t('admin.system.configSaved'),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err: any) {
      console.error('Error saving settings:', err);
      const errorMessage = err.response?.data?.message || t('admin.system.errorSavingConfig');
      setError(errorMessage);
      toast({
        title: t('admin.system.error'),
        description: errorMessage,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateSystemConfig = (field: keyof SystemConfig, value: any) => {
    setSystemConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minH="400px">
        <VStack spacing={4}>
          <Spinner size="xl" color="#F0B90B" />
          <Text color={textColor}>{t('admin.system.loadingConfig')}</Text>
        </VStack>
      </Box>
    );
  }

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>{t('admin.system.title')}</Heading>

      {error && (
        <Alert status="error" mb={6} bg="red.900" borderColor="red.600">
          <AlertIcon />
          {error}
        </Alert>
      )}

      <Grid templateColumns={{ base: "1fr", lg: "repeat(2, 1fr)" }} gap={6}>
        {/* Commission Settings */}
        <GridItem>
          <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
            <CardHeader>
              <Heading size="md" color={textColor}>{t('admin.system.financialSettings')}</Heading>
            </CardHeader>
            <Divider borderColor={borderColor} />
            <CardBody>
              <VStack spacing={6} align="stretch">
                <FormControl>
                  <FormLabel color={secondaryTextColor}>{t('admin.system.commissionRate')}</FormLabel>
                  <Flex align="center">
                    <Input
                      type="number"
                      value={systemConfig.commissionRate}
                      onChange={(e) => updateSystemConfig('commissionRate', parseFloat(e.target.value) || 0)}
                      bg={cardBgColor}
                      borderColor={borderColor}
                      color={textColor}
                      maxW="100px"
                      step="0.1"
                      min="0"
                      max="100"
                    />
                    <Text ml={2} color={secondaryTextColor}>%</Text>
                  </Flex>
                  <Text fontSize="sm" color={secondaryTextColor} mt={1}>
                    {t('admin.system.commissionRateDescription')}
                  </Text>
                </FormControl>

                <FormControl>
                  <FormLabel color={secondaryTextColor}>{t('admin.system.referralRate')}</FormLabel>
                  <Flex align="center">
                    <Input
                      type="number"
                      value={systemConfig.referralRate}
                      onChange={(e) => updateSystemConfig('referralRate', parseFloat(e.target.value) || 0)}
                      bg={cardBgColor}
                      borderColor={borderColor}
                      color={textColor}
                      maxW="100px"
                      step="0.1"
                      min="0"
                      max="100"
                    />
                    <Text ml={2} color={secondaryTextColor}>%</Text>
                  </Flex>
                  <Text fontSize="sm" color={secondaryTextColor} mt={1}>
                    {t('admin.system.referralRateDescription')}
                  </Text>
                </FormControl>
              </VStack>
            </CardBody>
          </Card>
        </GridItem>

        {/* Transaction Settings */}
        <GridItem>
          <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
            <CardHeader>
              <Heading size="md" color={textColor}>{t('admin.system.transactionSettings')}</Heading>
            </CardHeader>
            <Divider borderColor={borderColor} />
            <CardBody>
              <VStack spacing={6} align="stretch">
                <FormControl>
                  <FormLabel color={secondaryTextColor}>{t('admin.system.minimumDeposit')}</FormLabel>
                  <Flex align="center">
                    <Input
                      type="number"
                      value={systemConfig.minimumDeposit}
                      onChange={(e) => updateSystemConfig('minimumDeposit', parseFloat(e.target.value) || 0)}
                      bg={cardBgColor}
                      borderColor={borderColor}
                      color={textColor}
                      maxW="150px"
                      min="0"
                      step="1"
                    />
                    <Text ml={2} color={secondaryTextColor}>USD</Text>
                  </Flex>
                </FormControl>

                <FormControl>
                  <FormLabel color={secondaryTextColor}>{t('admin.system.minimumWithdrawal')}</FormLabel>
                  <Flex align="center">
                    <Input
                      type="number"
                      value={systemConfig.minimumWithdrawal}
                      onChange={(e) => updateSystemConfig('minimumWithdrawal', parseFloat(e.target.value) || 0)}
                      bg={cardBgColor}
                      borderColor={borderColor}
                      color={textColor}
                      maxW="150px"
                      min="0"
                      step="1"
                    />
                    <Text ml={2} color={secondaryTextColor}>USD</Text>
                  </Flex>
                </FormControl>
              </VStack>
            </CardBody>
          </Card>
        </GridItem>

        {/* System Settings */}
        <GridItem>
          <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
            <CardHeader>
              <Heading size="md" color={textColor}>{t('admin.system.systemSettings')}</Heading>
            </CardHeader>
            <Divider borderColor={borderColor} />
            <CardBody>
              <VStack spacing={6} align="stretch">
                <FormControl display="flex" alignItems="center">
                  <FormLabel htmlFor="maintenance-mode" mb="0" color={secondaryTextColor}>
                    {t('admin.system.maintenanceMode')}
                  </FormLabel>
                  <Switch
                    id="maintenance-mode"
                    isChecked={systemConfig.maintenanceMode}
                    onChange={(e) => updateSystemConfig('maintenanceMode', e.target.checked)}
                    colorScheme="yellow"
                  />
                </FormControl>

                <FormControl display="flex" alignItems="center">
                  <FormLabel htmlFor="enable-deposits" mb="0" color={secondaryTextColor}>
                    {t('admin.system.depositsEnabled')}
                  </FormLabel>
                  <Switch
                    id="enable-deposits"
                    isChecked={systemConfig.depositsEnabled}
                    onChange={(e) => updateSystemConfig('depositsEnabled', e.target.checked)}
                    colorScheme="yellow"
                  />
                </FormControl>

                <FormControl display="flex" alignItems="center">
                  <FormLabel htmlFor="enable-withdrawals" mb="0" color={secondaryTextColor}>
                    {t('admin.system.withdrawalsEnabled')}
                  </FormLabel>
                  <Switch
                    id="enable-withdrawals"
                    isChecked={systemConfig.withdrawalsEnabled}
                    onChange={(e) => updateSystemConfig('withdrawalsEnabled', e.target.checked)}
                    colorScheme="yellow"
                  />
                </FormControl>
              </VStack>
            </CardBody>
          </Card>
        </GridItem>

        {/* Notification Settings */}
        <GridItem>
          <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
            <CardHeader>
              <Heading size="md" color={textColor}>{t('admin.system.notificationSettings')}</Heading>
            </CardHeader>
            <Divider borderColor={borderColor} />
            <CardBody>
              <VStack spacing={6} align="stretch">
                <FormControl>
                  <FormLabel color={secondaryTextColor}>{t('admin.system.maintenanceMessage')}</FormLabel>
                  <Textarea
                    placeholder={t('admin.system.maintenanceMessagePlaceholder')}
                    value={maintenanceMessage}
                    onChange={(e) => setMaintenanceMessage(e.target.value)}
                    bg={cardBgColor}
                    borderColor={borderColor}
                    color={textColor}
                    rows={4}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel color={secondaryTextColor}>{t('admin.system.emailNotifications')}</FormLabel>
                  <Select
                    value={emailNotifications}
                    onChange={(e) => setEmailNotifications(e.target.value)}
                    bg={cardBgColor}
                    borderColor={borderColor}
                    color={textColor}
                  >
                    <option value="all">{t('admin.system.allTransactions')}</option>
                    <option value="deposits">{t('admin.system.depositsOnly')}</option>
                    <option value="withdrawals">{t('admin.system.withdrawalsOnly')}</option>
                    <option value="none">{t('admin.system.none')}</option>
                  </Select>
                </FormControl>
              </VStack>
            </CardBody>
          </Card>
        </GridItem>
      </Grid>

      <Flex justify="flex-end" mt={8}>
        <Button
          colorScheme="yellow"
          size="lg"
          onClick={handleSaveSettings}
          isLoading={isSubmitting}
          loadingText={t('admin.system.saving')}
        >
          {t('admin.system.saveSettings')}
        </Button>
      </Flex>
    </Box>
  );
};

export default AdminSettings;
