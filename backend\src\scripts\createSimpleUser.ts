import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../models/userModel';
import { db } from '../config/database';

// Load environment variables
dotenv.config();

// Connect to database
const createSimpleUser = async () => {
  try {
    await db.connect();
    console.log('MongoDB connected');

    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });

    if (existingUser) {
      console.log('Admin user already exists');
      console.log('Email: <EMAIL>');
      console.log('Password: Admin123!');

      // Update password to a simpler one that meets requirements and ensure admin status
      existingUser.password = 'Admin123!';
      existingUser.isAdmin = true;
      await existingUser.save();
      console.log('Password updated and admin status set');

      process.exit(0);
    }

    // Create test user with a password that meets the requirements
    const user = await User.create({
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'Admin123!', // Meets requirements: uppercase, lowercase, number, special char
      walletAddress: '******************************************',
      isAdmin: true
    });

    console.log('Admin user created successfully');
    console.log('Email: <EMAIL>');
    console.log('Password: Admin123!');

    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
};

createSimpleUser();
