import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Flex,
  Grid,
  GridItem,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useToast,
  Divider,
  VStack,
} from '@chakra-ui/react';
import {
  FaCoins,
  FaChartLine,
  FaMoneyBillWave,
  FaExchangeAlt,
  FaHandHoldingUsd,
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import useAuth from '../hooks/useAuth';
import UserInvestments from '../components/UserInvestments';
import InvestmentDashboard from '../components/InvestmentDashboard';

import { investmentService } from '../services/investmentService';

const InvestmentsPage: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const toast = useToast();
  const [totalInvested, setTotalInvested] = useState(0);
  const [totalEarned, setTotalEarned] = useState(0);
  const [activeInvestments, setActiveInvestments] = useState(0);
  const [loading, setLoading] = useState(true);



  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Fetch investment data
  useEffect(() => {
    fetchInvestmentData();
  }, [toast]); // Add toast as dependency since it's used in fetchInvestmentData

  const fetchInvestmentData = async () => {
    try {
      setLoading(true);
      const response = await investmentService.getInvestments();

      if (response.data && response.data.investments) {
        const investments = response.data.investments;

        // Calculate total invested amount
        const totalAmount = investments
          .filter(inv => inv.status === 'approved')
          .reduce((sum, inv) => sum + inv.amount, 0);

        // Count active investments
        const activeCount = investments
          .filter(inv => inv.status === 'approved')
          .length;

        // Calculate total earned (1% daily of total invested) using reduce instead of forEach
        const totalEarnedAmount = investments
          .filter(inv => inv.status === 'approved')
          .reduce((total, investment) => {
            const depositDate = new Date(investment.createdAt);
            const currentDate = new Date();
            const diffTime = Math.abs(currentDate.getTime() - depositDate.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            // 1% daily interest
            const earned = investment.amount * 0.01 * diffDays;
            return total + earned;
          }, 0);

        setTotalInvested(totalAmount);
        setActiveInvestments(activeCount);
        setTotalEarned(totalEarnedAmount);
      }
    } catch (error) {
      console.error('Error fetching investment data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch investment data',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };



  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <Heading size="lg" color={primaryColor} mb={8}>{t('investments.title', 'Investments')}</Heading>

        {/* Investment Stats - Removed */}

        <Divider borderColor={borderColor} mb={8} />

        {/* Investment Dashboard */}
        <Box mb={8}>
          <InvestmentDashboard />
        </Box>

        {/* User Investments List */}
        <Box>
          <UserInvestments />
        </Box>
      </Container>
    </Box>
  );
};

export default InvestmentsPage;
