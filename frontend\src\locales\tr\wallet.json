{"title": "Cüzdan", "subtitle": "Kripto varlıklarınızı yönetin", "overview": {"title": "Cüzdan Özeti", "totalBalance": "Toplam Bakiye", "availableBalance": "Kullanılabilir Bakiye", "lockedBalance": "<PERSON><PERSON>li Bakiye", "estimatedValue": "<PERSON><PERSON><PERSON>", "portfolioValue": "Portföy <PERSON>", "totalAssets": "Toplam Varlık", "activeAssets": "Aktif <PERSON>", "profitLoss": "Kar/Zarar", "dailyChange": "Günlük Değişim", "weeklyChange": "Haftalık Değişim", "monthlyChange": "<PERSON><PERSON><PERSON><PERSON>"}, "assets": {"title": "Varlıklarım", "noAssets": "Henüz varlık yok", "loading": "Varlıklar yükleniyor...", "error": "Varlıklar yüklenirken hata oluştu", "refresh": "<PERSON><PERSON><PERSON>", "addAsset": "Varlık Ekle", "hideSmallBalances": "Küçük bakiyeleri gizle", "showAll": "Tümünü <PERSON>ö<PERSON>", "sortBy": "S<PERSON>rala", "filterBy": "Filtrele", "searchAssets": "Varlık ara...", "assetName": "Varlık Adı", "balance": "Bakiye", "value": "<PERSON><PERSON><PERSON>", "change24h": "24s <PERSON><PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "actions": "İşlemler"}, "transactions": {"title": "İşlem Geçmişi", "noTransactions": "Henüz işlem yok", "loading": "İşlemler yükleniyor...", "error": "İşlemler yüklenirken hata oluştu", "viewAll": "<PERSON>ümü<PERSON><PERSON>", "filter": "Filtrele", "export": "Dışa Aktar", "transactionId": "İşlem ID", "type": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "currency": "Para Birimi", "status": "Durum", "date": "<PERSON><PERSON><PERSON>", "fee": "Ücret", "confirmations": "<PERSON><PERSON><PERSON>", "txHash": "İşlem Hash", "blockHeight": "Blok Yüksekliği", "types": {"deposit": "Para Yatırma", "withdrawal": "Para Çekme", "transfer": "Transfer", "exchange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "earning": "Kazanç", "fee": "Ücret", "bonus": "Bonus", "referral": "Referans", "staking": "Staking", "unstaking": "Unstaking"}}, "deposit": {"title": "Para Yatırma", "subtitle": "Cüzdanınıza kripto para yatırın", "selectCurrency": "Para Birimi <PERSON>", "depositAddress": "<PERSON><PERSON><PERSON><PERSON>", "qrCode": "QR Kod", "copyAddress": "<PERSON><PERSON><PERSON>", "generateNewAddress": "<PERSON><PERSON>", "minimumDeposit": "Minimum Yatırma", "networkFee": "<PERSON><PERSON>", "confirmations": "<PERSON><PERSON><PERSON><PERSON>", "processingTime": "İşlem Süresi", "instructions": "<PERSON><PERSON><PERSON><PERSON>", "warning": "Uyarı", "warningText": "Sadece {currency} gönderin. Diğer coinleri gönderirseniz kaybedebilirsiniz.", "steps": {"step1": "Para birimi seçin", "step2": "<PERSON><PERSON><PERSON>", "step3": "Transfer yapın", "step4": "<PERSON><PERSON> be<PERSON>in"}, "recentDeposits": "<PERSON>", "depositHistory": "<PERSON><PERSON><PERSON><PERSON>"}, "withdrawal": {"title": "Para Çekme", "subtitle": "Cüzdanınızdan kripto para çekin", "selectCurrency": "Para Birimi <PERSON>", "withdrawalAddress": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "availableBalance": "Kullanılabilir Bakiye", "minimumWithdrawal": "Minimum Çekim", "maximumWithdrawal": "<PERSON><PERSON><PERSON><PERSON>", "networkFee": "<PERSON><PERSON>", "processingFee": "İşlem Ücreti", "totalFee": "Toplam Ücret", "youWillReceive": "Alacağınız Tutar", "processingTime": "İşlem Süresi", "addressBook": "<PERSON><PERSON>", "addToAddressBook": "<PERSON><PERSON>", "savedAddresses": "Kayıtlı Adresler", "verifyAddress": "<PERSON><PERSON><PERSON>", "confirmWithdrawal": "<PERSON><PERSON><PERSON><PERSON>", "withdrawButton": "Para Çek", "cancelButton": "İptal", "steps": {"step1": "Para birimi seçin", "step2": "<PERSON><PERSON> ve tutar girin", "step3": "<PERSON><PERSON><PERSON>", "step4": "<PERSON><PERSON><PERSON><PERSON>"}, "recentWithdrawals": "<PERSON>", "withdrawalHistory": "<PERSON><PERSON><PERSON>", "validation": {"addressRequired": "<PERSON><PERSON><PERSON>", "invalidAddress": "Geçersiz adres", "amountRequired": "<PERSON><PERSON>", "insufficientBalance": "<PERSON><PERSON><PERSON>", "belowMinimum": "Minimum çekim tutarının altında", "aboveMaximum": "<PERSON><PERSON><PERSON>um çekim tutarının üstünde", "invalidAmount": "Geçersiz tutar"}}, "transfer": {"title": "Transfer", "subtitle": "Başka bir kullanıcıya transfer yapın", "recipientEmail": "Alıcı E-posta", "recipientUsername": "Alıcı Kullanıcı Adı", "selectCurrency": "Para Birimi", "amount": "<PERSON><PERSON>", "transferFee": "Transfer Ücreti", "description": "A<PERSON>ıklama (Opsiyonel)", "confirmTransfer": "<PERSON>i <PERSON>ayla", "transferButton": "Transfer Yap", "cancelButton": "İptal", "recentTransfers": "<PERSON>", "transferHistory": "Transfer Geçmişi", "validation": {"recipientRequired": "Alıcı bilgisi gerekli", "invalidRecipient": "Geçersiz alıcı", "cannotTransferToSelf": "Kendinize transfer yapamazsınız", "amountRequired": "<PERSON><PERSON>", "insufficientBalance": "<PERSON><PERSON><PERSON>", "invalidAmount": "Geçersiz tutar"}}, "exchange": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Kripto paralarınızı değiştirin", "fromCurrency": "Gönderen Para Birimi", "toCurrency": "<PERSON>", "fromAmount": "<PERSON><PERSON><PERSON><PERSON>", "toAmount": "<PERSON>", "exchangeRate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exchangeFee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "estimatedReceive": "<PERSON><PERSON><PERSON>", "minimumExchange": "Minimum Değişim", "maximumExchange": "<PERSON><PERSON><PERSON><PERSON>", "exchangeButton": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cancelButton": "İptal", "recentExchanges": "<PERSON>", "exchangeHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON>ç<PERSON>şi", "validation": {"fromCurrencyRequired": "Gönderen para birimi gerekli", "toCurrencyRequired": "Alan para birimi gerekli", "sameCurrency": "Aynı para birimini seçemezsiniz", "amountRequired": "<PERSON><PERSON>", "insufficientBalance": "<PERSON><PERSON><PERSON>", "belowMinimum": "Minimum değişim tutarının altında", "aboveMaximum": "<PERSON><PERSON><PERSON><PERSON> değişim tutarının üstünde"}}, "security": {"title": "Güvenlik", "twoFactorAuth": "İki Faktörlü Doğrulama", "withdrawalPassword": "<PERSON><PERSON><PERSON>", "addressWhitelist": "<PERSON><PERSON>", "sessionManagement": "<PERSON><PERSON><PERSON>", "loginHistory": "<PERSON><PERSON><PERSON>ş<PERSON>", "deviceManagement": "<PERSON><PERSON><PERSON>", "securityAlerts": "Güvenlik Uyarıları", "enable": "Etkinleştir", "disable": "Devre Dışı Bırak", "configure": "Yapılandır", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "settings": {"title": "Cüzdan Ayarları", "displaySettings": "Gör<PERSON><PERSON><PERSON>", "defaultCurrency": "Varsayılan Para Birimi", "hideSmallBalances": "Küçük bakiyeleri gizle", "showTestnet": "Testnet'i göster", "notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "depositNotifications": "<PERSON><PERSON><PERSON><PERSON>", "withdrawalNotifications": "<PERSON><PERSON><PERSON>", "transferNotifications": "Transfer bildirimleri", "priceAlerts": "Fiyat uyarıları", "backup": "<PERSON><PERSON><PERSON><PERSON>", "exportWallet": "Cüzdanı dışa aktar", "importWallet": "Cüzdan içe aktar", "backupPhrase": "<PERSON><PERSON>", "privateKeys": "<PERSON><PERSON>"}, "errors": {"networkError": "<PERSON><PERSON>ı", "insufficientFunds": "<PERSON><PERSON><PERSON>", "invalidAddress": "Geçersiz adres", "transactionFailed": "İşlem başarısız", "addressGenerationFailed": "Adres oluşturma başarısız", "balanceUpdateFailed": "Bakiye güncelleme başarısız", "withdrawalLimitExceeded": "Çekim limiti aşıldı", "dailyLimitExceeded": "Günlük limit aşıldı", "maintenanceMode": "Bakım modu", "serviceUnavailable": "<PERSON><PERSON>lamıyor"}, "success": {"depositInitiated": "Para yatırma başlatıldı", "withdrawalInitiated": "Para çekme başlatıldı", "transferCompleted": "Transfer tamamlandı", "exchangeCompleted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addressCopied": "<PERSON><PERSON> k<PERSON>alandı", "settingsSaved": "<PERSON><PERSON><PERSON>", "addressGenerated": "<PERSON><PERSON> ad<PERSON>", "backupCompleted": "<PERSON><PERSON><PERSON><PERSON>"}}