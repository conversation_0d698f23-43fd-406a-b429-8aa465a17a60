import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON><PERSON>,
  <PERSON>ge,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  Switch,
  VStack,
  Flex,
  Text,
  useDisclosure,
  useToast,
  useColorModeValue,
  Divider,
  Select,
  IconButton
} from '@chakra-ui/react';
import { EditIcon, DeleteIcon } from '@chakra-ui/icons';
import { useTranslation } from 'react-i18next';
import { API_URL } from "../../config";
import axios from 'axios';
import useAuth from '../../hooks/useAuth';
import { CRYPTO_NETWORKS, getDefaultNetwork } from '../../utils/cryptoNetworks';

// Define address with network interface
interface AddressWithNetwork {
  address: string;
  network: string;
}

// Define crypto address interface
interface CryptoAddress {
  currency: string;
  addresses: (string | AddressWithNetwork)[];
  currentIndex: number;
  enabled: boolean;
  network?: string;
}

// Define system config interface
interface SystemConfig {
  siteName: string;
  siteDescription: string;
  maintenanceMode: boolean;
  commissionRate: number;
  referralRate: number;
  minimumDeposit: number;
  minimumWithdrawal: number;
  withdrawalsEnabled: boolean;
  depositsEnabled: boolean;
  cryptoAddresses: CryptoAddress[];
  supportedCurrencies: string[];
}

const CryptoAddressManagement: React.FC = () => {
  const { t } = useTranslation('translation');
  const toast = useToast();
  const { user } = useAuth();

  // Color mode values
  const bgColor = useColorModeValue('#FFFFFF', '#0B0E11');
  const cardBgColor = useColorModeValue('#F8F9FA', '#1E2026');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#4A5568', '#848E9C');
  const borderColor = useColorModeValue('#E2E8F0', '#2B3139');

  // State management
  const [systemConfig, setSystemConfig] = useState<SystemConfig>({
    siteName: '',
    siteDescription: '',
    maintenanceMode: false,
    commissionRate: 0,
    referralRate: 0,
    minimumDeposit: 0,
    minimumWithdrawal: 0,
    withdrawalsEnabled: true,
    depositsEnabled: true,
    cryptoAddresses: [],
    supportedCurrencies: ['BTC', 'ETH', 'USDT', 'DOGE', 'TRX']
  });

  const [isSaving, setIsSaving] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [addressList, setAddressList] = useState<AddressWithNetwork[]>([]);
  const [addressInput, setAddressInput] = useState('');
  const [isAddressEnabled, setIsAddressEnabled] = useState(true);
  const [selectedNetwork, setSelectedNetwork] = useState('');

  // Modal controls
  const { isOpen: isAddressModalOpen, onOpen: onAddressModalOpen, onClose: onAddressModalClose } = useDisclosure();

  // Load system configuration on component mount
  useEffect(() => {
    loadSystemConfig();
  }, []);

  // Load system configuration from API
  const loadSystemConfig = async () => {
    try {
      const response = await axios.get(`${API_URL}/admin/system/config`, {
        withCredentials: true
      });

      if (response.data.success) {
        setSystemConfig(response.data.data);
      }
    } catch (error) {
      console.error('Error loading system config:', error);
      toast({
        title: t('admin.system.error'),
        description: t('admin.system.errorLoadingConfig'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Open modal to edit crypto addresses
  const handleEditAddresses = (currency: string) => {
    const cryptoAddress = systemConfig.cryptoAddresses.find(ca => ca.currency === currency);

    if (cryptoAddress) {
      setSelectedCurrency(currency);

      // Convert addresses to AddressWithNetwork format
      const convertedAddresses: AddressWithNetwork[] = cryptoAddress.addresses.map((addr) => {
        if (typeof addr === 'string') {
          return {
            address: addr,
            network: getDefaultNetwork(currency)?.id || 'mainnet'
          };
        } else {
          return addr;
        }
      });

      setAddressList(convertedAddresses);
      setIsAddressEnabled(cryptoAddress.enabled);
    } else {
      setSelectedCurrency(currency);
      setAddressList([]);
      setIsAddressEnabled(true);
    }

    // Set default network for new addresses
    const defaultNetwork = getDefaultNetwork(currency);
    setSelectedNetwork(defaultNetwork?.id || 'mainnet');

    onAddressModalOpen();
  };

  // Add address to the list
  const addAddress = () => {
    if (!addressInput.trim()) {
      toast({
        title: t('admin.system.error'),
        description: t('admin.system.errorEmptyAddress'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Add new address with selected network
    const newAddress: AddressWithNetwork = {
      address: addressInput.trim(),
      network: selectedNetwork
    };

    setAddressList([...addressList, newAddress]);
    setAddressInput('');
  };

  // Remove address from the list
  const removeAddress = (index: number) => {
    const newList = [...addressList];
    newList.splice(index, 1);
    setAddressList(newList);
  };

  // Update network for a specific address
  const updateAddressNetwork = (index: number, network: string) => {
    const newList = [...addressList];
    newList[index] = {
      ...newList[index],
      network
    };
    setAddressList(newList);
  };

  // Save addresses for the selected currency
  const saveAddresses = async () => {
    try {
      setIsSaving(true);

      // Validate addresses before saving
      if (addressList.length === 0) {
        toast({
          title: t('admin.system.error'),
          description: t('admin.system.errorNoAddresses', 'At least one address is required'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Validate each address format
      for (const addressItem of addressList) {
        if (!addressItem.address || !addressItem.address.trim()) {
          toast({
            title: t('admin.system.error'),
            description: t('admin.system.errorEmptyAddress', 'Address cannot be empty'),
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
          return;
        }

        if (!addressItem.network) {
          toast({
            title: t('admin.system.error'),
            description: t('admin.system.errorNoNetwork', 'Network must be specified for each address'),
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
          return;
        }
      }

      console.log('Saving addresses for', selectedCurrency, ':', addressList);

      const response = await axios.put(
        `${API_URL}/admin/system/crypto-addresses/${selectedCurrency}`,
        {
          addresses: addressList,
          enabled: isAddressEnabled,
        },
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Save response:', response.data);

      if (response.data.success) {
        // Clear cache after successful update to force refresh across the app
        try {
          const { cryptoAddressCache } = await import('../../services/cryptoAddressCache');
          cryptoAddressCache.clearCache();
          console.log('🗑️ Cleared crypto address cache after admin update');
        } catch (error) {
          console.warn('Failed to clear crypto address cache:', error);
        }

        // Update local state
        const updatedAddresses = [...systemConfig.cryptoAddresses];
        const existingIndex = updatedAddresses.findIndex(ca => ca.currency === selectedCurrency);

        if (existingIndex !== -1) {
          updatedAddresses[existingIndex] = {
            ...updatedAddresses[existingIndex],
            addresses: addressList,
            enabled: isAddressEnabled,
          };
        } else {
          updatedAddresses.push({
            currency: selectedCurrency,
            addresses: addressList,
            currentIndex: 0,
            enabled: isAddressEnabled,
          });
        }

        setSystemConfig({
          ...systemConfig,
          cryptoAddresses: updatedAddresses,
        });

        toast({
          title: t('admin.system.success'),
          description: t('admin.system.addressesSaved', { currency: selectedCurrency }),
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        onAddressModalClose();
      } else {
        throw new Error(response.data.message || 'Failed to save addresses');
      }
    } catch (error: any) {
      console.error('Error saving addresses:', error);

      // Enhanced error handling
      let errorMessage = t('admin.system.errorSavingAddresses');

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: t('admin.system.error'),
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Box p={4}>
      <Heading size="lg" mb={6} color={textColor}>
        {t('admin.nav.cryptoAddresses', 'Crypto Addresses')}
      </Heading>

      <Box bg={bgColor} p={6} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
        <Heading size="md" mb={4} color={textColor}>
          {t('admin.system.cryptoAddresses')}
        </Heading>

        <Table variant="simple" size="md">
          <Thead>
            <Tr>
              <Th color={secondaryTextColor} borderColor={borderColor}>
                {t('admin.system.currency')}
              </Th>
              <Th color={secondaryTextColor} borderColor={borderColor}>
                {t('admin.system.addresses')}
              </Th>
              <Th color={secondaryTextColor} borderColor={borderColor}>
                {t('admin.system.network', 'Network')}
              </Th>
              <Th color={secondaryTextColor} borderColor={borderColor}>
                {t('admin.system.status')}
              </Th>
              <Th color={secondaryTextColor} borderColor={borderColor}>
                {t('admin.system.actions')}
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {systemConfig.supportedCurrencies.map((currency) => {
              const cryptoAddress = systemConfig.cryptoAddresses.find(ca => ca.currency === currency);
              return (
                <Tr key={currency}>
                  <Td color={textColor} borderColor={borderColor}>{currency}</Td>
                  <Td color={textColor} borderColor={borderColor}>
                    {cryptoAddress ? cryptoAddress.addresses.length : 0} {t('admin.system.addresses').toLowerCase()}
                  </Td>
                  <Td color={textColor} borderColor={borderColor}>
                    {cryptoAddress?.network ? (
                      <Badge colorScheme="blue" borderRadius="full" px={2}>
                        {CRYPTO_NETWORKS[currency]?.find(n => n.id === cryptoAddress.network)?.name || cryptoAddress.network}
                      </Badge>
                    ) : (
                      <Badge colorScheme="gray" borderRadius="full" px={2}>
                        {t('admin.system.defaultNetwork', 'Default')}
                      </Badge>
                    )}
                  </Td>
                  <Td borderColor={borderColor}>
                    <Badge
                      colorScheme={cryptoAddress?.enabled ? 'green' : 'red'}
                      borderRadius="full"
                      px={2}
                    >
                      {cryptoAddress?.enabled ? t('admin.system.enabled') : t('admin.system.disabled')}
                    </Badge>
                  </Td>
                  <Td borderColor={borderColor}>
                    <Button
                      size="sm"
                      leftIcon={<EditIcon />}
                      colorScheme="blue"
                      onClick={() => handleEditAddresses(currency)}
                    >
                      {t('admin.system.edit')}
                    </Button>
                  </Td>
                </Tr>
              );
            })}
          </Tbody>
        </Table>
      </Box>

      {/* Crypto Address Modal */}
      <Modal isOpen={isAddressModalOpen} onClose={onAddressModalClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={bgColor} color={textColor}>
          <ModalHeader>{t('admin.system.editAddresses')} {selectedCurrency}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl display="flex" alignItems="center">
                <FormLabel htmlFor="address-enabled" mb="0">
                  {t('admin.system.enable')} {selectedCurrency}
                </FormLabel>
                <Switch
                  id="address-enabled"
                  isChecked={isAddressEnabled}
                  onChange={(e) => setIsAddressEnabled(e.target.checked)}
                  colorScheme="yellow"
                />
              </FormControl>

              {/* Network Selector for new addresses */}
              <FormControl>
                <FormLabel>{t('admin.system.networkForNewAddresses', 'Network for new addresses')}</FormLabel>
                <Select
                  value={selectedNetwork}
                  onChange={(e) => setSelectedNetwork(e.target.value)}
                  bg={cardBgColor}
                  borderColor={borderColor}
                  color={textColor}
                >
                  {CRYPTO_NETWORKS[selectedCurrency]?.map((network) => (
                    <option key={network.id} value={network.id}>
                      {network.name} {network.isDefault ? '(Recommended)' : ''}
                    </option>
                  ))}
                </Select>
                <FormHelperText>
                  {t('admin.system.networkHelperText', 'This network will be used for new addresses you add')}
                </FormHelperText>
              </FormControl>

              <Divider borderColor={borderColor} />

              <FormControl>
                <FormLabel>{t('admin.system.addNewAddress')}</FormLabel>
                <Flex>
                  <Input
                    value={addressInput}
                    onChange={(e) => setAddressInput(e.target.value)}
                    placeholder={`${t('admin.system.add')} ${selectedCurrency} ${t('admin.system.addresses').toLowerCase()}`}
                    bg={cardBgColor}
                    borderColor={borderColor}
                    mr={2}
                  />
                  <Button colorScheme="green" onClick={addAddress}>
                    {t('admin.system.add')}
                  </Button>
                </Flex>
              </FormControl>

              <Box>
                <Text fontWeight="bold" mb={2}>{t('admin.system.currentAddresses')}</Text>
                {addressList.length === 0 ? (
                  <Text color={secondaryTextColor}>{t('admin.system.noAddresses')}</Text>
                ) : (
                  <VStack spacing={2} align="stretch">
                    {addressList.map((addressItem, index) => (
                      <Flex key={index} justify="space-between" align="center" p={2} bg={cardBgColor} borderRadius="md" flexWrap="wrap">
                        <Flex flex="1" direction={{ base: "column", md: "row" }} alignItems={{ base: "flex-start", md: "center" }} mb={{ base: 2, md: 0 }}>
                          <Text fontSize="sm" isTruncated maxW={{ base: "100%", md: "60%" }} mr={2}>{addressItem.address}</Text>
                          <Select
                            size="sm"
                            value={addressItem.network}
                            onChange={(e) => updateAddressNetwork(index, e.target.value)}
                            bg={cardBgColor}
                            borderColor={borderColor}
                            color={textColor}
                            maxW={{ base: "100%", md: "150px" }}
                            mt={{ base: 2, md: 0 }}
                          >
                            {CRYPTO_NETWORKS[selectedCurrency]?.map((network) => (
                              <option key={network.id} value={network.id}>
                                {network.name}
                              </option>
                            ))}
                          </Select>
                        </Flex>
                        <IconButton
                          aria-label={t('admin.system.removeAddress')}
                          icon={<DeleteIcon />}
                          size="sm"
                          colorScheme="red"
                          variant="ghost"
                          onClick={() => removeAddress(index)}
                          ml={2}
                        />
                      </Flex>
                    ))}
                  </VStack>
                )}
              </Box>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onAddressModalClose}>
              {t('common.cancel')}
            </Button>
            <Button
              colorScheme="yellow"
              onClick={saveAddresses}
              isLoading={isSaving}
              loadingText={t('common.saving')}
            >
              {t('admin.system.saveAddresses')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default CryptoAddressManagement;
