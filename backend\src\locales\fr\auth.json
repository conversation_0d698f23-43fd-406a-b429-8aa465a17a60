{"login": {"success": "Connexion réussie", "failed": "Échec de la connexion", "invalidCredentials": "E-mail ou mot de passe invalide", "userNotFound": "Utilisateur non trouvé", "passwordIncorrect": "Mot de passe incorrect"}, "register": {"success": "Inscription réussie. Veuillez vérifier votre e-mail pour confirmer votre compte.", "failed": "Échec de l'inscription. Veuillez réessayer.", "emailExists": "Cette adresse e-mail est déjà enregistrée", "invalidReferralCode": "Code de parrainage invalide"}, "validation": {"fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs", "emailRequired": "Le champ e-mail est requis", "passwordRequired": "Le champ mot de passe est requis", "firstNameRequired": "Le prénom est requis", "lastNameRequired": "Le nom de famille est requis", "invalidEmailFormat": "Format d'e-mail invalide", "validEmailRequired": "Veuillez entrer une adresse e-mail valide"}, "errors": {"validationError": "Erreur de validation", "databaseError": "Erreur de base de données", "serviceUnavailable": "Service indisponible", "internalError": "<PERSON>rreur interne du serveur", "tryAgainLater": "Veuillez réessayer plus tard", "unexpectedError": "Une erreur inattendue s'est produite. Veuillez réessayer plus tard.", "notAuthorizedAsAdmin": "Non autorisé en tant qu'administrateur", "adminPrivilegesRequired": "Ce point de terminaison nécessite des privilèges d'administrateur", "userNotFound": "Utilisateur non trouvé"}, "logout": {"success": "Déconnexion réussie"}, "token": {"refreshSuccess": "Token actualisé avec succès", "refreshFailed": "Échec de l'actualisation du token", "expired": "Le token a expiré", "invalid": "<PERSON><PERSON> invalide", "notProvided": "Aucun token fourni pour l'actualisation", "userNotFound": "Utilisateur non trouvé pour l'actualisation du token"}, "twoFactor": {"enabled": "Authentification à deux facteurs activée", "disabled": "Authentification à deux facteurs désactivée"}, "kyc": {"verified": "Vérification KYC réussie", "cancelled": "Vérification KYC annulée"}}