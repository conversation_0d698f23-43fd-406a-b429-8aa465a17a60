import { apiClient } from '../utils/apiClient';

/**
 * API service for wallet-related endpoints
 */
export const walletService = {
  // ===== LEGACY WALLET ENDPOINTS =====

  /**
   * Connect wallet
   * @param address Wallet address
   */
  connectWallet: (address: string) => apiClient.post('/wallets/connect', { address }),

  /**
   * Get wallet info (legacy)
   */
  getWalletBalance: () => apiClient.get('/wallets/info'),

  /**
   * Toggle between commission and interest mode
   * @param asset Asset symbol
   * @param mode Mode ('commission' or 'interest')
   */
  toggleMode: (asset: string, mode: 'commission' | 'interest') =>
    apiClient.post('/wallets/toggle-mode', { asset, mode }),

  /**
   * Deposit asset (legacy)
   * @param data Deposit data
   */
  depositAsset: (data: {
    asset: string;
    amount: number;
    txHash?: string;
    network?: string;
  }) => apiClient.post('/wallets/deposit', data),

  /**
   * Get transaction history (legacy)
   * @param params Optional query parameters
   */
  getTransactionHistory: (params?: {
    page?: number;
    limit?: number;
    type?: string;
    asset?: string;
  }) => apiClient.get('/wallets/transactions', { params }),


};

export default walletService;
