import express from 'express';
import { protect, admin } from '../middleware/authMiddleware';
import {
  generateReferralCode,
  validateReferralCode,
  getReferralInfo,
  getReferralStats,
  getMyReferralCode,
  applyReferralCode,
  adminSearchReferrals,
  adminAdjustCommission,
  adminGetReferralStats,
  getFirstDepositCommissionStats,
  checkFirstDepositEligibility,
  getFirstDepositCommissions,
  getFirstDepositCommissionSummary
} from '../controllers/referralController';

const router = express.Router();

// Public routes
router.post('/validate', validateReferralCode);

// Protected user routes
router.use(protect); // All routes below require authentication

router.post('/generate', generateReferralCode);
router.get('/info', getReferralInfo);
router.get('/stats', getReferralStats);
router.get('/my-code', getMyReferralCode);
router.post('/apply', applyReferralCode);

// First deposit commission routes
router.get('/first-deposit-stats/:userId', getFirstDepositCommissionStats);

// Admin routes
router.post('/admin/search', protect, admin, adminSearchReferrals);
router.put('/admin/commission/:id', protect, admin, adminAdjustCommission);
router.get('/admin/stats', protect, admin, adminGetReferralStats);

// First deposit commission admin routes
router.get('/admin/first-deposit-eligibility/:userId', protect, admin, checkFirstDepositEligibility);
router.get('/admin/first-deposit-commissions', protect, admin, getFirstDepositCommissions);
router.get('/admin/first-deposit-summary', protect, admin, getFirstDepositCommissionSummary);

export default router;
