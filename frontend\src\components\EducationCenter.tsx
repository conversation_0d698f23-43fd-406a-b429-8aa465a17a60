import React, { useState } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  Flex,
  Grid,
  GridItem,
  Button,
  Input,
  InputGroup,
  InputLeftElement,
  Badge,
  Image,
  Link,
  Divider,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  useColorModeValue,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  AspectRatio
} from '@chakra-ui/react';
import { SearchIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import { 
  FaGraduationCap, 
  FaBook, 
  FaVideo, 
  FaChartLine, 
  FaLightbulb,
  FaRegFileAlt,
  FaRegQuestionCircle,
  FaRegNewspaper,
  FaRegPlayCircle,
  FaRegCommentDots,
  FaRegBookmark,
  FaRegClock,
  FaRegStar,
  FaRegCheckCircle
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

// Mock data for educational content
const educationalContent = {
  beginnerGuides: [
    {
      id: 'guide1',
      title: 'Getting Started with Shipping Finance',
      description: 'Learn the basics of our platform and how to make your first investment.',
      category: 'beginner',
      type: 'article',
      duration: '5 min read',
      thumbnail: '/images/education/getting-started.jpg',
      featured: true
    },
    {
      id: 'guide2',
      title: 'Understanding Global Trade Investments',
      description: 'Discover how investments in global trade work and why they are profitable.',
      category: 'beginner',
      type: 'article',
      duration: '8 min read',
      thumbnail: '/images/education/global-trade.jpg'
    },
    {
      id: 'guide3',
      title: 'How to Earn Daily Returns',
      description: 'A step-by-step guide to earning daily returns on your investments.',
      category: 'beginner',
      type: 'video',
      duration: '12 min',
      thumbnail: '/images/education/daily-returns.jpg'
    },
    {
      id: 'guide4',
      title: 'Security Features Explained',
      description: 'Learn about the security measures we have in place to protect your investments.',
      category: 'beginner',
      type: 'article',
      duration: '6 min read',
      thumbnail: '/images/education/security.jpg'
    }
  ],
  
  advancedTopics: [
    {
      id: 'advanced1',
      title: 'Maximizing Your Referral Earnings',
      description: 'Advanced strategies to grow your network and increase your referral commissions.',
      category: 'advanced',
      type: 'article',
      duration: '10 min read',
      thumbnail: '/images/education/referrals.jpg',
      featured: true
    },
    {
      id: 'advanced2',
      title: 'Portfolio Diversification Strategies',
      description: 'Learn how to diversify your investments across different assets and markets.',
      category: 'advanced',
      type: 'article',
      duration: '15 min read',
      thumbnail: '/images/education/diversification.jpg'
    },
    {
      id: 'advanced3',
      title: 'Understanding Risk Management',
      description: 'Advanced techniques for managing risk in your investment portfolio.',
      category: 'advanced',
      type: 'video',
      duration: '18 min',
      thumbnail: '/images/education/risk-management.jpg'
    },
    {
      id: 'advanced4',
      title: 'Tax Optimization for Investors',
      description: 'Learn how to optimize your tax strategy for investment returns.',
      category: 'advanced',
      type: 'article',
      duration: '12 min read',
      thumbnail: '/images/education/tax-optimization.jpg'
    }
  ],
  
  marketInsights: [
    {
      id: 'market1',
      title: 'Global Shipping Industry Outlook 2023',
      description: 'Analysis of current trends and future projections for the global shipping industry.',
      category: 'market',
      type: 'report',
      duration: '20 min read',
      thumbnail: '/images/education/shipping-outlook.jpg',
      featured: true
    },
    {
      id: 'market2',
      title: 'Impact of Supply Chain Disruptions',
      description: 'How supply chain disruptions affect global trade and investment opportunities.',
      category: 'market',
      type: 'article',
      duration: '10 min read',
      thumbnail: '/images/education/supply-chain.jpg'
    },
    {
      id: 'market3',
      title: 'Emerging Markets in Global Trade',
      description: 'Discover new opportunities in emerging markets for global trade investments.',
      category: 'market',
      type: 'webinar',
      duration: '45 min',
      thumbnail: '/images/education/emerging-markets.jpg'
    },
    {
      id: 'market4',
      title: 'Cryptocurrency and Global Trade',
      description: 'How cryptocurrency is transforming international trade and payments.',
      category: 'market',
      type: 'article',
      duration: '12 min read',
      thumbnail: '/images/education/crypto-trade.jpg'
    }
  ],
  
  faq: [
    {
      id: 'faq1',
      question: 'How does Shipping Finance generate returns?',
      answer: 'Shipping Finance generates returns by investing in global trade operations, purchasing products from low-cost countries and selling them in high-demand regions. The platform leverages economies of scale, established supply chains, and market expertise to generate consistent profits, which are then distributed to investors as daily returns.'
    },
    {
      id: 'faq2',
      question: 'How often are returns distributed?',
      answer: 'Returns are distributed daily to your account. You can see these returns reflected in your dashboard every 24 hours. The daily return rate is 1% of your invested amount.'
    },
    {
      id: 'faq3',
      question: 'What is the minimum investment amount?',
      answer: 'The minimum investment amount is $100 or equivalent in other supported cryptocurrencies. There is no maximum limit on investments.'
    },
    {
      id: 'faq4',
      question: 'How does the referral system work?',
      answer: 'Our referral system allows you to earn a 3% commission on the investments made by users you refer to the platform. When someone registers using your referral code and makes an investment, you automatically receive 3% of their investment amount as a commission in your account.'
    },
    {
      id: 'faq5',
      question: 'How secure are my investments?',
      answer: 'We implement multiple security measures including cold storage for cryptocurrencies, multi-signature wallets, regular security audits, and an insurance fund to protect against unforeseen circumstances. Additionally, all smart contracts are audited by leading security firms.'
    },
    {
      id: 'faq6',
      question: 'How can I withdraw my funds?',
      answer: 'You can withdraw your funds at any time through the withdrawal section in your dashboard. Withdrawals are processed within 24 hours and sent to the cryptocurrency wallet address you provide.'
    }
  ]
};

// Video content for the selected guide
const videoContent = {
  guide3: 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Placeholder
  advanced3: 'https://www.youtube.com/embed/dQw4w9WgXcQ' // Placeholder
};

interface EducationCenterProps {
  defaultTab?: string;
}

const EducationCenter: React.FC<EducationCenterProps> = ({
  defaultTab = 'guides'
}) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedContent, setSelectedContent] = useState(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";
  
  // Handle content selection
  const handleContentSelect = (content) => {
    setSelectedContent(content);
    onOpen();
  };
  
  // Filter content based on search query
  const filterContent = (content) => {
    if (!searchQuery) return content;
    
    return content.filter(item => 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.type.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };
  
  // Render content card
  const renderContentCard = (content) => (
    <Box
      key={content.id}
      bg={cardBgColor}
      borderRadius="md"
      borderWidth="1px"
      borderColor={borderColor}
      overflow="hidden"
      transition="all 0.3s"
      _hover={{
        transform: "translateY(-4px)",
        boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
        borderColor: primaryColor
      }}
      onClick={() => handleContentSelect(content)}
      cursor="pointer"
    >
      <Box position="relative">
        <Box
          bg={`url(${content.thumbnail || '/images/education/default.jpg'})`}
          bgSize="cover"
          bgPosition="center"
          h="160px"
          position="relative"
        >
          <Box
            position="absolute"
            top={0}
            left={0}
            right={0}
            bottom={0}
            bg="rgba(0, 0, 0, 0.4)"
          />
          
          {content.featured && (
            <Badge
              position="absolute"
              top={2}
              right={2}
              colorScheme="yellow"
              variant="solid"
            >
              {t('education.featured', 'Featured')}
            </Badge>
          )}
          
          <Badge
            position="absolute"
            bottom={2}
            left={2}
            colorScheme={content.type === 'article' ? 'blue' : content.type === 'video' ? 'red' : content.type === 'webinar' ? 'purple' : 'green'}
            variant="solid"
          >
            <HStack spacing={1}>
              <Icon 
                as={
                  content.type === 'article' ? FaRegFileAlt : 
                  content.type === 'video' ? FaRegPlayCircle : 
                  content.type === 'webinar' ? FaRegCommentDots : 
                  FaRegNewspaper
                } 
                boxSize={3} 
              />
              <Text fontSize="xs">{content.type}</Text>
            </HStack>
          </Badge>
        </Box>
      </Box>
      
      <Box p={4}>
        <Heading size="sm" color={textColor} mb={2} noOfLines={2}>
          {content.title}
        </Heading>
        
        <Text color={secondaryTextColor} fontSize="sm" mb={3} noOfLines={2}>
          {content.description}
        </Text>
        
        <HStack justify="space-between">
          <Badge variant="outline" colorScheme="yellow">
            {content.category}
          </Badge>
          
          <HStack color={secondaryTextColor} fontSize="xs">
            <Icon as={FaRegClock} />
            <Text>{content.duration}</Text>
          </HStack>
        </HStack>
      </Box>
    </Box>
  );
  
  return (
    <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
      <Flex align="center" mb={6}>
        <Icon as={FaGraduationCap} color={primaryColor} boxSize={5} mr={2} />
        <Heading size="md" color={textColor}>{t('education.title', 'Education Center')}</Heading>
      </Flex>
      
      {/* Search Bar */}
      <InputGroup mb={6}>
        <InputLeftElement pointerEvents="none">
          <SearchIcon color={secondaryTextColor} />
        </InputLeftElement>
        <Input
          placeholder={t('education.searchPlaceholder', 'Search for guides, articles, videos...')}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          bg={cardBgColor}
          borderColor={borderColor}
          color={textColor}
          _hover={{ borderColor: primaryColor }}
          _focus={{ borderColor: primaryColor, boxShadow: "none" }}
        />
      </InputGroup>
      
      {/* Content Tabs */}
      <Tabs variant="soft-rounded" colorScheme="yellow" defaultIndex={defaultTab === 'guides' ? 0 : defaultTab === 'advanced' ? 1 : defaultTab === 'market' ? 2 : 3}>
        <TabList mb={6} overflowX="auto" css={{ scrollbarWidth: 'none' }} whiteSpace="nowrap">
          <Tab color={textColor} _selected={{ color: primaryColor, bg: "rgba(240, 185, 11, 0.1)" }} mr={2}>
            <Icon as={FaBook} mr={2} />
            {t('education.beginnerGuides', 'Beginner Guides')}
          </Tab>
          <Tab color={textColor} _selected={{ color: primaryColor, bg: "rgba(240, 185, 11, 0.1)" }} mr={2}>
            <Icon as={FaChartLine} mr={2} />
            {t('education.advancedTopics', 'Advanced Topics')}
          </Tab>
          <Tab color={textColor} _selected={{ color: primaryColor, bg: "rgba(240, 185, 11, 0.1)" }} mr={2}>
            <Icon as={FaLightbulb} mr={2} />
            {t('education.marketInsights', 'Market Insights')}
          </Tab>
          <Tab color={textColor} _selected={{ color: primaryColor, bg: "rgba(240, 185, 11, 0.1)" }}>
            <Icon as={FaRegQuestionCircle} mr={2} />
            {t('education.faq', 'FAQ')}
          </Tab>
        </TabList>
        
        <TabPanels>
          {/* Beginner Guides */}
          <TabPanel p={0}>
            <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={6}>
              {filterContent(educationalContent.beginnerGuides).map(guide => renderContentCard(guide))}
            </Grid>
            
            {filterContent(educationalContent.beginnerGuides).length === 0 && (
              <Box textAlign="center" py={10}>
                <Icon as={FaRegFileAlt} boxSize={10} color={secondaryTextColor} mb={4} />
                <Text color={secondaryTextColor}>
                  {t('education.noResults', 'No guides found matching your search')}
                </Text>
              </Box>
            )}
          </TabPanel>
          
          {/* Advanced Topics */}
          <TabPanel p={0}>
            <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={6}>
              {filterContent(educationalContent.advancedTopics).map(topic => renderContentCard(topic))}
            </Grid>
            
            {filterContent(educationalContent.advancedTopics).length === 0 && (
              <Box textAlign="center" py={10}>
                <Icon as={FaRegFileAlt} boxSize={10} color={secondaryTextColor} mb={4} />
                <Text color={secondaryTextColor}>
                  {t('education.noResults', 'No topics found matching your search')}
                </Text>
              </Box>
            )}
          </TabPanel>
          
          {/* Market Insights */}
          <TabPanel p={0}>
            <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={6}>
              {filterContent(educationalContent.marketInsights).map(insight => renderContentCard(insight))}
            </Grid>
            
            {filterContent(educationalContent.marketInsights).length === 0 && (
              <Box textAlign="center" py={10}>
                <Icon as={FaRegFileAlt} boxSize={10} color={secondaryTextColor} mb={4} />
                <Text color={secondaryTextColor}>
                  {t('education.noResults', 'No insights found matching your search')}
                </Text>
              </Box>
            )}
          </TabPanel>
          
          {/* FAQ */}
          <TabPanel p={0}>
            <Accordion allowToggle>
              {educationalContent.faq
                .filter(item => 
                  !searchQuery || 
                  item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  item.answer.toLowerCase().includes(searchQuery.toLowerCase())
                )
                .map((faq, index) => (
                <AccordionItem 
                  key={faq.id} 
                  border="none" 
                  mb={4}
                >
                  <AccordionButton 
                    bg={cardBgColor} 
                    borderRadius="md" 
                    borderWidth="1px" 
                    borderColor={borderColor}
                    _hover={{ bg: cardBgColor }}
                    p={4}
                  >
                    <Box flex="1" textAlign="left" color={textColor} fontWeight="medium">
                      <HStack>
                        <Icon as={FaRegQuestionCircle} color={primaryColor} />
                        <Text>{faq.question}</Text>
                      </HStack>
                    </Box>
                    <AccordionIcon color={primaryColor} />
                  </AccordionButton>
                  <AccordionPanel 
                    pb={4} 
                    bg={cardBgColor} 
                    borderBottomRadius="md" 
                    borderWidth="1px" 
                    borderTop="none"
                    borderColor={borderColor}
                    p={4}
                  >
                    <Text color={secondaryTextColor}>{faq.answer}</Text>
                  </AccordionPanel>
                </AccordionItem>
              ))}
            </Accordion>
            
            {educationalContent.faq.filter(item => 
              !searchQuery || 
              item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
              item.answer.toLowerCase().includes(searchQuery.toLowerCase())
            ).length === 0 && (
              <Box textAlign="center" py={10}>
                <Icon as={FaRegQuestionCircle} boxSize={10} color={secondaryTextColor} mb={4} />
                <Text color={secondaryTextColor}>
                  {t('education.noFaqResults', 'No FAQ items found matching your search')}
                </Text>
              </Box>
            )}
          </TabPanel>
        </TabPanels>
      </Tabs>
      
      {/* Content Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg="#1E2329" color="#EAECEF" borderColor="#2B3139" borderWidth="1px">
          <ModalHeader borderBottomWidth="1px" borderColor="#2B3139">
            {selectedContent?.title}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody py={6}>
            {selectedContent && (
              <VStack spacing={6} align="stretch">
                {/* Video Content */}
                {selectedContent.type === 'video' && videoContent[selectedContent.id] && (
                  <AspectRatio ratio={16 / 9}>
                    <iframe
                      title={selectedContent.title}
                      src={videoContent[selectedContent.id]}
                      allowFullScreen
                    />
                  </AspectRatio>
                )}
                
                {/* Article Content */}
                {selectedContent.type === 'article' && (
                  <>
                    <Box
                      bg={`url(${selectedContent.thumbnail || '/images/education/default.jpg'})`}
                      bgSize="cover"
                      bgPosition="center"
                      h="200px"
                      borderRadius="md"
                      position="relative"
                      mb={4}
                    >
                      <Box
                        position="absolute"
                        top={0}
                        left={0}
                        right={0}
                        bottom={0}
                        bg="rgba(0, 0, 0, 0.4)"
                        borderRadius="md"
                      />
                    </Box>
                    
                    <HStack spacing={4} mb={4}>
                      <Badge colorScheme="yellow" variant="solid">
                        {selectedContent.category}
                      </Badge>
                      <HStack color={secondaryTextColor} fontSize="sm">
                        <Icon as={FaRegClock} />
                        <Text>{selectedContent.duration}</Text>
                      </HStack>
                    </HStack>
                    
                    <Text color={secondaryTextColor} mb={4}>
                      {selectedContent.description}
                    </Text>
                    
                    <Text color={textColor}>
                      This is a placeholder for the full article content. In a real application, this would contain the complete article text with proper formatting, images, and other elements.
                    </Text>
                  </>
                )}
                
                {/* Webinar Content */}
                {selectedContent.type === 'webinar' && (
                  <>
                    <Box
                      bg={`url(${selectedContent.thumbnail || '/images/education/default.jpg'})`}
                      bgSize="cover"
                      bgPosition="center"
                      h="200px"
                      borderRadius="md"
                      position="relative"
                      mb={4}
                    >
                      <Box
                        position="absolute"
                        top={0}
                        left={0}
                        right={0}
                        bottom={0}
                        bg="rgba(0, 0, 0, 0.4)"
                        borderRadius="md"
                      />
                      <Flex
                        position="absolute"
                        top={0}
                        left={0}
                        right={0}
                        bottom={0}
                        align="center"
                        justify="center"
                      >
                        <Button
                          leftIcon={<Icon as={FaRegPlayCircle} />}
                          colorScheme="yellow"
                          size="lg"
                        >
                          {t('education.watchWebinar', 'Watch Webinar')}
                        </Button>
                      </Flex>
                    </Box>
                    
                    <HStack spacing={4} mb={4}>
                      <Badge colorScheme="purple" variant="solid">
                        {selectedContent.type}
                      </Badge>
                      <HStack color={secondaryTextColor} fontSize="sm">
                        <Icon as={FaRegClock} />
                        <Text>{selectedContent.duration}</Text>
                      </HStack>
                    </HStack>
                    
                    <Text color={secondaryTextColor} mb={4}>
                      {selectedContent.description}
                    </Text>
                    
                    <Box bg="#0B0E11" p={4} borderRadius="md" mb={4}>
                      <Heading size="sm" color={textColor} mb={2}>
                        {t('education.webinarDetails', 'Webinar Details')}
                      </Heading>
                      <VStack align="start" spacing={2}>
                        <HStack>
                          <Text color={secondaryTextColor}>{t('education.presenter', 'Presenter')}:</Text>
                          <Text color={textColor}>John Smith, Chief Market Analyst</Text>
                        </HStack>
                        <HStack>
                          <Text color={secondaryTextColor}>{t('education.date', 'Date')}:</Text>
                          <Text color={textColor}>June 15, 2023</Text>
                        </HStack>
                        <HStack>
                          <Text color={secondaryTextColor}>{t('education.topics', 'Topics')}:</Text>
                          <Text color={textColor}>Market trends, Investment opportunities, Risk analysis</Text>
                        </HStack>
                      </VStack>
                    </Box>
                  </>
                )}
                
                {/* Report Content */}
                {selectedContent.type === 'report' && (
                  <>
                    <Box
                      bg={`url(${selectedContent.thumbnail || '/images/education/default.jpg'})`}
                      bgSize="cover"
                      bgPosition="center"
                      h="200px"
                      borderRadius="md"
                      position="relative"
                      mb={4}
                    >
                      <Box
                        position="absolute"
                        top={0}
                        left={0}
                        right={0}
                        bottom={0}
                        bg="rgba(0, 0, 0, 0.4)"
                        borderRadius="md"
                      />
                    </Box>
                    
                    <HStack spacing={4} mb={4}>
                      <Badge colorScheme="green" variant="solid">
                        {selectedContent.type}
                      </Badge>
                      <HStack color={secondaryTextColor} fontSize="sm">
                        <Icon as={FaRegClock} />
                        <Text>{selectedContent.duration}</Text>
                      </HStack>
                    </HStack>
                    
                    <Text color={secondaryTextColor} mb={4}>
                      {selectedContent.description}
                    </Text>
                    
                    <Button
                      leftIcon={<Icon as={FaRegFileAlt} />}
                      colorScheme="yellow"
                      mb={4}
                    >
                      {t('education.downloadReport', 'Download Full Report')}
                    </Button>
                    
                    <Text color={textColor}>
                      This is a placeholder for the report summary. In a real application, this would contain key findings, executive summary, and other important information from the report.
                    </Text>
                  </>
                )}
              </VStack>
            )}
          </ModalBody>
          <ModalFooter borderTopWidth="1px" borderColor="#2B3139">
            <Button variant="ghost" mr={3} onClick={onClose}>
              {t('common.close', 'Close')}
            </Button>
            <Button 
              leftIcon={<Icon as={FaRegBookmark} />}
              colorScheme="yellow"
            >
              {t('education.saveForLater', 'Save for Later')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default EducationCenter;
