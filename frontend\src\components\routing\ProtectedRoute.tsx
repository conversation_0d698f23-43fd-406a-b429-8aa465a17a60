import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, Spinner, Center, Alert, AlertIcon, AlertTitle, AlertDescription, Button, VStack } from '@chakra-ui/react';
import useAuth from '../../hooks/useAuth';
import { useToast } from '@chakra-ui/react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  adminOnly?: boolean;
  redirectPath?: string;
  showFeedback?: boolean;
}

/**
 * Enhanced Protected Route component
 * 
 * Features:
 * - Authentication check with loading state
 * - Optional admin-only routes
 * - Customizable redirect path
 * - Saves original URL for post-login redirect
 * - Optional feedback messages
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  adminOnly = false,
  redirectPath = '/login',
  showFeedback = true
}) => {
  const { user, loading } = useAuth();
  const location = useLocation();
  const toast = useToast();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <Center h="100vh">
        <VStack spacing={4}>
          <Spinner size="xl" color="brand.500" thickness="4px" />
          <Box color="gray.500">Verifying authentication...</Box>
        </VStack>
      </Center>
    );
  }

  // Check if user is authenticated
  if (!user) {
    // Show feedback toast if enabled
    if (showFeedback) {
      toast({
        title: "Authentication Required",
        description: "Please log in to access this page.",
        status: "warning",
        duration: 5000,
        isClosable: true,
        position: "top-right"
      });
    }

    // Redirect to login with the original URL saved in state
    return <Navigate to={redirectPath} state={{ from: location.pathname }} replace />;
  }

  // Check if admin access is required
  if (adminOnly && !user.isAdmin) {
    return (
      <Center h="100vh">
        <Alert
          status="error"
          variant="subtle"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          textAlign="center"
          height="auto"
          maxW="md"
          p={6}
          borderRadius="md"
        >
          <AlertIcon boxSize="40px" mr={0} />
          <AlertTitle mt={4} mb={1} fontSize="lg">
            Access Denied
          </AlertTitle>
          <AlertDescription maxWidth="sm" mb={4}>
            You don't have permission to access this page. This area is restricted to administrators only.
          </AlertDescription>
          <Button colorScheme="blue" as={Navigate} to="/dashboard" replace>
            Return to Dashboard
          </Button>
        </Alert>
      </Center>
    );
  }

  // User is authenticated and has proper permissions
  return <>{children}</>;
};

export default ProtectedRoute;
