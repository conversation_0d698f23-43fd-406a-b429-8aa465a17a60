import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Badge,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useToast
} from '@chakra-ui/react';
import { useCryptoPrices, useCryptoPrice, useCryptoConverter } from '../hooks/useCryptoPrices';
import { useCryptoPriceContext } from './CryptoPriceProvider';

/**
 * Component demo để test tính năng cache giá tiền tệ
 */
const CryptoPriceDemo: React.FC = () => {
  const toast = useToast();
  const { prices, loading, error, refreshPrices, cacheInfo } = useCryptoPrices();
  const { convertToUSD, convertFromUSD } = useCryptoConverter();
  const btcPrice = useCryptoPrice('BTC');
  const ethPrice = useCryptoPrice('ETH');
  const usdtPrice = useCryptoPrice('USDT');

  const handleRefresh = async () => {
    try {
      await refreshPrices();
      toast({
        title: 'Prices Refreshed',
        description: 'Cryptocurrency prices have been updated successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Refresh Failed',
        description: 'Failed to refresh cryptocurrency prices',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const formatCacheAge = (ageMs: number): string => {
    const seconds = Math.floor(ageMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const getCacheStatusColor = (): string => {
    if (!cacheInfo.hasCache) return 'red';
    if (cacheInfo.isValid) return 'green';
    return 'yellow';
  };

  const getCacheStatusText = (): string => {
    if (!cacheInfo.hasCache) return 'No Cache';
    if (cacheInfo.isValid) return 'Valid Cache';
    return 'Expired Cache';
  };

  return (
    <Box p={6} bg="gray.800" borderRadius="lg" borderWidth="1px" borderColor="gray.700">
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <Text fontSize="xl" fontWeight="bold" color="brand.500">
            💰 Crypto Price Cache Demo
          </Text>
          <Button
            onClick={handleRefresh}
            isLoading={loading}
            loadingText="Refreshing..."
            colorScheme="yellow"
            size="sm"
          >
            Refresh Prices
          </Button>
        </HStack>

        {/* Error Alert */}
        {error && (
          <Alert status="error">
            <AlertIcon />
            <AlertTitle>Error!</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Cache Info */}
        <Box p={4} bg="gray.700" borderRadius="md">
          <Text fontSize="md" fontWeight="bold" mb={3} color="gray.100">
            📊 Cache Information
          </Text>
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
            <Stat>
              <StatLabel fontSize="xs">Status</StatLabel>
              <StatNumber fontSize="sm">
                <Badge colorScheme={getCacheStatusColor()}>
                  {getCacheStatusText()}
                </Badge>
              </StatNumber>
            </Stat>
            <Stat>
              <StatLabel fontSize="xs">Price Count</StatLabel>
              <StatNumber fontSize="sm">{cacheInfo.priceCount}</StatNumber>
            </Stat>
            <Stat>
              <StatLabel fontSize="xs">Cache Age</StatLabel>
              <StatNumber fontSize="sm">{formatCacheAge(cacheInfo.cacheAge)}</StatNumber>
            </Stat>
            <Stat>
              <StatLabel fontSize="xs">Loading</StatLabel>
              <StatNumber fontSize="sm">
                {loading ? <Spinner size="sm" color="brand.500" /> : '✅'}
              </StatNumber>
            </Stat>
          </SimpleGrid>
        </Box>

        {/* Individual Price Hooks Demo */}
        <Box p={4} bg="gray.700" borderRadius="md">
          <Text fontSize="md" fontWeight="bold" mb={3} color="gray.100">
            🎯 Individual Price Hooks
          </Text>
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
            <Stat>
              <StatLabel>Bitcoin (BTC)</StatLabel>
              <StatNumber color="orange.400">
                {btcPrice.loading ? <Spinner size="sm" /> : `$${btcPrice.price.toLocaleString()}`}
              </StatNumber>
              <StatHelpText>useCryptoPrice('BTC')</StatHelpText>
            </Stat>
            <Stat>
              <StatLabel>Ethereum (ETH)</StatLabel>
              <StatNumber color="blue.400">
                {ethPrice.loading ? <Spinner size="sm" /> : `$${ethPrice.price.toLocaleString()}`}
              </StatNumber>
              <StatHelpText>useCryptoPrice('ETH')</StatHelpText>
            </Stat>
            <Stat>
              <StatLabel>Tether (USDT)</StatLabel>
              <StatNumber color="green.400">
                {usdtPrice.loading ? <Spinner size="sm" /> : `$${usdtPrice.price.toFixed(4)}`}
              </StatNumber>
              <StatHelpText>useCryptoPrice('USDT')</StatHelpText>
            </Stat>
          </SimpleGrid>
        </Box>

        {/* Conversion Demo */}
        <Box p={4} bg="gray.700" borderRadius="md">
          <Text fontSize="md" fontWeight="bold" mb={3} color="gray.100">
            🔄 Conversion Examples
          </Text>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
            <VStack align="start" spacing={2}>
              <Text fontSize="sm" color="gray.300">1 BTC to USD:</Text>
              <Text fontSize="lg" fontWeight="bold" color="brand.500">
                ${convertToUSD(1, 'BTC').toLocaleString()}
              </Text>
            </VStack>
            <VStack align="start" spacing={2}>
              <Text fontSize="sm" color="gray.300">$1000 to BTC:</Text>
              <Text fontSize="lg" fontWeight="bold" color="brand.500">
                {convertFromUSD(1000, 'BTC').toFixed(6)} BTC
              </Text>
            </VStack>
            <VStack align="start" spacing={2}>
              <Text fontSize="sm" color="gray.300">1 ETH to USD:</Text>
              <Text fontSize="lg" fontWeight="bold" color="brand.500">
                ${convertToUSD(1, 'ETH').toLocaleString()}
              </Text>
            </VStack>
            <VStack align="start" spacing={2}>
              <Text fontSize="sm" color="gray.300">$500 to ETH:</Text>
              <Text fontSize="lg" fontWeight="bold" color="brand.500">
                {convertFromUSD(500, 'ETH').toFixed(4)} ETH
              </Text>
            </VStack>
          </SimpleGrid>
        </Box>

        {/* All Prices Grid */}
        <Box p={4} bg="gray.700" borderRadius="md">
          <Text fontSize="md" fontWeight="bold" mb={3} color="gray.100">
            💎 All Cached Prices
          </Text>
          {loading && Object.keys(prices).length === 0 ? (
            <HStack justify="center" p={4}>
              <Spinner color="brand.500" />
              <Text color="gray.300">Loading prices...</Text>
            </HStack>
          ) : (
            <SimpleGrid columns={{ base: 2, md: 4, lg: 6 }} spacing={3}>
              {Object.entries(prices).map(([symbol, price]) => (
                <Box
                  key={symbol}
                  p={3}
                  bg="gray.600"
                  borderRadius="md"
                  textAlign="center"
                  transition="all 0.2s"
                  _hover={{ bg: "gray.500", transform: "translateY(-2px)" }}
                >
                  <Text fontSize="xs" color="gray.300" mb={1}>
                    {symbol}
                  </Text>
                  <Text fontSize="sm" fontWeight="bold" color="brand.500">
                    ${typeof price === 'number' ? price.toLocaleString() : price}
                  </Text>
                </Box>
              ))}
            </SimpleGrid>
          )}
        </Box>

        {/* Instructions */}
        <Box p={4} bg="blue.900" borderRadius="md" borderWidth="1px" borderColor="blue.700">
          <Text fontSize="sm" color="blue.100">
            <strong>💡 How it works:</strong><br />
            • Prices are automatically loaded when the app starts<br />
            • Data is cached in localStorage for 5 minutes<br />
            • Subsequent page loads use cached data if still valid<br />
            • All components using crypto prices share the same cache<br />
            • Manual refresh fetches fresh data from API
          </Text>
        </Box>
      </VStack>
    </Box>
  );
};

export default CryptoPriceDemo;
