import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  Flex,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Link,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Button,
  Tooltip,
  Skeleton,
  useColorModeValue,
  useToast
} from '@chakra-ui/react';
import { SearchIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import { 
  FaEthereum, 
  FaBitcoin, 
  FaExchangeAlt, 
  FaCheckCircle, 
  FaClock, 
  FaChartLine,
  FaLink,
  FaRegCopy
} from 'react-icons/fa';
import { Si<PERSON><PERSON>er, SiRipple, SiDogecoin } from 'react-icons/si';
import { useTranslation } from 'react-i18next';

// Mock transaction data
const mockTransactions = [
  {
    id: 'tx1',
    hash: '0x7d91c5d3c3e6fc3c5acf691e959bf6d8a2b1f5d5c6d7e8f9a0b1c2d3e4f5a6b7',
    from: '0x1234...5678',
    to: '0x8765...4321',
    amount: 1.25,
    currency: 'ETH',
    timestamp: '2023-05-01T12:34:56Z',
    status: 'confirmed',
    blockNumber: 12345678,
    network: 'Ethereum'
  },
  {
    id: 'tx2',
    hash: '0x8e91c5d3c3e6fc3c5acf691e959bf6d8a2b1f5d5c6d7e8f9a0b1c2d3e4f5a6b8',
    from: '0x2345...6789',
    to: '0x9876...5432',
    amount: 500,
    currency: 'USDT',
    timestamp: '2023-05-02T10:23:45Z',
    status: 'confirmed',
    blockNumber: 12345679,
    network: 'Ethereum'
  },
  {
    id: 'tx3',
    hash: '0x9f91c5d3c3e6fc3c5acf691e959bf6d8a2b1f5d5c6d7e8f9a0b1c2d3e4f5a6b9',
    from: '0x3456...7890',
    to: '0x0987...6543',
    amount: 0.05,
    currency: 'BTC',
    timestamp: '2023-05-03T09:12:34Z',
    status: 'pending',
    blockNumber: null,
    network: 'Bitcoin'
  },
  {
    id: 'tx4',
    hash: '0xaf91c5d3c3e6fc3c5acf691e959bf6d8a2b1f5d5c6d7e8f9a0b1c2d3e4f5a6ba',
    from: '0x4567...8901',
    to: '0x1098...7654',
    amount: 100,
    currency: 'XRP',
    timestamp: '2023-05-04T08:01:23Z',
    status: 'confirmed',
    blockNumber: 67890123,
    network: 'Ripple'
  },
  {
    id: 'tx5',
    hash: '0xbf91c5d3c3e6fc3c5acf691e959bf6d8a2b1f5d5c6d7e8f9a0b1c2d3e4f5a6bb',
    from: '0x5678...9012',
    to: '0x2109...8765',
    amount: 1000,
    currency: 'DOGE',
    timestamp: '2023-05-05T07:45:12Z',
    status: 'confirmed',
    blockNumber: 45678901,
    network: 'Dogecoin'
  }
];

// Mock smart contract data
const mockSmartContracts = [
  {
    id: 'contract1',
    address: '******************************************',
    name: 'Commission Distribution',
    network: 'Ethereum',
    description: 'Automatically distributes commission payments to referrers',
    deployedAt: '2023-01-15T10:00:00Z',
    transactions: 1245,
    status: 'active'
  },
  {
    id: 'contract2',
    address: '******************************************',
    name: 'Staking Rewards',
    network: 'Binance Smart Chain',
    description: 'Manages staking rewards for platform users',
    deployedAt: '2023-02-20T14:30:00Z',
    transactions: 876,
    status: 'active'
  },
  {
    id: 'contract3',
    address: '******************************************',
    name: 'Insurance Fund',
    network: 'Polygon',
    description: 'Manages the platform insurance fund for user protection',
    deployedAt: '2023-03-10T09:15:00Z',
    transactions: 432,
    status: 'active'
  }
];

interface BlockchainExplorerProps {
  userAddress?: string;
}

const BlockchainExplorer: React.FC<BlockchainExplorerProps> = ({ userAddress }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedNetwork, setSelectedNetwork] = useState('all');
  const [transactions, setTransactions] = useState(mockTransactions);
  const [smartContracts, setSmartContracts] = useState(mockSmartContracts);
  const [isLoading, setIsLoading] = useState(false);
  
  // Colors
  const bgColor = "#1E2329";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";
  
  // Currency icons
  const getCurrencyIcon = (currency: string) => {
    switch (currency.toUpperCase()) {
      case 'BTC':
        return FaBitcoin;
      case 'ETH':
        return FaEthereum;
      case 'USDT':
        return SiTether;
      case 'XRP':
        return SiRipple;
      case 'DOGE':
        return SiDogecoin;
      default:
        return FaExchangeAlt;
    }
  };
  
  // Network explorer URLs
  const getExplorerUrl = (network: string, hash: string) => {
    switch (network) {
      case 'Ethereum':
        return `https://etherscan.io/tx/${hash}`;
      case 'Bitcoin':
        return `https://www.blockchain.com/btc/tx/${hash}`;
      case 'Ripple':
        return `https://xrpscan.com/tx/${hash}`;
      case 'Dogecoin':
        return `https://dogechain.info/tx/${hash}`;
      case 'Binance Smart Chain':
        return `https://bscscan.com/tx/${hash}`;
      case 'Polygon':
        return `https://polygonscan.com/tx/${hash}`;
      default:
        return '#';
    }
  };
  
  // Contract explorer URLs
  const getContractExplorerUrl = (network: string, address: string) => {
    switch (network) {
      case 'Ethereum':
        return `https://etherscan.io/address/${address}`;
      case 'Binance Smart Chain':
        return `https://bscscan.com/address/${address}`;
      case 'Polygon':
        return `https://polygonscan.com/address/${address}`;
      default:
        return '#';
    }
  };
  
  // Handle search
  const handleSearch = () => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      if (searchQuery) {
        const filteredTransactions = mockTransactions.filter(tx => 
          tx.hash.toLowerCase().includes(searchQuery.toLowerCase()) ||
          tx.from.toLowerCase().includes(searchQuery.toLowerCase()) ||
          tx.to.toLowerCase().includes(searchQuery.toLowerCase())
        );
        setTransactions(filteredTransactions);
      } else {
        setTransactions(mockTransactions);
      }
      
      setIsLoading(false);
    }, 1000);
  };
  
  // Handle network filter
  const handleNetworkChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedNetwork(e.target.value);
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      if (e.target.value !== 'all') {
        const filteredTransactions = mockTransactions.filter(tx => 
          tx.network === e.target.value
        );
        setTransactions(filteredTransactions);
      } else {
        setTransactions(mockTransactions);
      }
      
      setIsLoading(false);
    }, 1000);
  };
  
  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: t('blockchain.copied', 'Copied to clipboard'),
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };
  
  // Truncate hash
  const truncateHash = (hash: string) => {
    return `${hash.substring(0, 6)}...${hash.substring(hash.length - 4)}`;
  };
  
  return (
    <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
      <Flex align="center" mb={6}>
        <Icon as={FaLink} color={primaryColor} boxSize={5} mr={2} />
        <Heading size="md" color={textColor}>{t('blockchain.title', 'Blockchain Explorer')}</Heading>
      </Flex>
      
      <Tabs variant="enclosed" colorScheme="yellow" mb={6}>
        <TabList borderColor={borderColor}>
          <Tab color={textColor} _selected={{ color: primaryColor, bg: "#0B0E11", borderColor: borderColor }}>
            {t('blockchain.transactions', 'Transactions')}
          </Tab>
          <Tab color={textColor} _selected={{ color: primaryColor, bg: "#0B0E11", borderColor: borderColor }}>
            {t('blockchain.smartContracts', 'Smart Contracts')}
          </Tab>
        </TabList>
        
        <TabPanels>
          {/* Transactions Tab */}
          <TabPanel p={4}>
            <VStack spacing={6} align="stretch">
              <Flex direction={{ base: "column", md: "row" }} gap={4}>
                <InputGroup flex={1}>
                  <InputLeftElement pointerEvents="none">
                    <SearchIcon color={secondaryTextColor} />
                  </InputLeftElement>
                  <Input
                    placeholder={t('blockchain.searchPlaceholder', 'Search by transaction hash, address')}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    bg="#0B0E11"
                    borderColor={borderColor}
                    color={textColor}
                    _hover={{ borderColor: primaryColor }}
                    _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                  />
                </InputGroup>
                
                <Select
                  placeholder={t('blockchain.selectNetwork', 'Select Network')}
                  value={selectedNetwork}
                  onChange={handleNetworkChange}
                  bg="#0B0E11"
                  borderColor={borderColor}
                  color={textColor}
                  _hover={{ borderColor: primaryColor }}
                  _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                  w={{ base: "100%", md: "200px" }}
                >
                  <option value="all">{t('blockchain.allNetworks', 'All Networks')}</option>
                  <option value="Ethereum">Ethereum</option>
                  <option value="Bitcoin">Bitcoin</option>
                  <option value="Ripple">Ripple (XRP)</option>
                  <option value="Dogecoin">Dogecoin</option>
                  <option value="Binance Smart Chain">Binance Smart Chain</option>
                </Select>
                
                <Button
                  colorScheme="yellow"
                  onClick={handleSearch}
                  isLoading={isLoading}
                  w={{ base: "100%", md: "auto" }}
                >
                  {t('common.search', 'Search')}
                </Button>
              </Flex>
              
              {isLoading ? (
                <VStack spacing={4}>
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} height="60px" width="100%" borderRadius="md" />
                  ))}
                </VStack>
              ) : transactions.length > 0 ? (
                <Box overflowX="auto">
                  <Table variant="simple" size="sm">
                    <Thead>
                      <Tr>
                        <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.hash', 'Hash')}</Th>
                        <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.network', 'Network')}</Th>
                        <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.from', 'From')}</Th>
                        <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.to', 'To')}</Th>
                        <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.amount', 'Amount')}</Th>
                        <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.status', 'Status')}</Th>
                        <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.time', 'Time')}</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {transactions.map((tx) => (
                        <Tr key={tx.id}>
                          <Td color={textColor} borderColor={borderColor}>
                            <HStack>
                              <Tooltip label={tx.hash}>
                                <Link href={getExplorerUrl(tx.network, tx.hash)} isExternal color={primaryColor}>
                                  {truncateHash(tx.hash)} <ExternalLinkIcon mx="2px" />
                                </Link>
                              </Tooltip>
                              <Icon 
                                as={FaRegCopy} 
                                cursor="pointer" 
                                onClick={() => copyToClipboard(tx.hash)}
                                color={secondaryTextColor}
                                _hover={{ color: primaryColor }}
                              />
                            </HStack>
                          </Td>
                          <Td color={textColor} borderColor={borderColor}>{tx.network}</Td>
                          <Td color={textColor} borderColor={borderColor}>
                            <Tooltip label={tx.from}>
                              <Text>{tx.from}</Text>
                            </Tooltip>
                          </Td>
                          <Td color={textColor} borderColor={borderColor}>
                            <Tooltip label={tx.to}>
                              <Text>{tx.to}</Text>
                            </Tooltip>
                          </Td>
                          <Td color={textColor} borderColor={borderColor}>
                            <HStack>
                              <Icon as={getCurrencyIcon(tx.currency)} color={primaryColor} />
                              <Text>{tx.amount} {tx.currency}</Text>
                            </HStack>
                          </Td>
                          <Td borderColor={borderColor}>
                            <Badge 
                              colorScheme={tx.status === 'confirmed' ? 'green' : 'yellow'} 
                              variant="subtle"
                              px={2}
                              py={1}
                              borderRadius="full"
                            >
                              <HStack spacing={1}>
                                <Icon as={tx.status === 'confirmed' ? FaCheckCircle : FaClock} boxSize={3} />
                                <Text fontSize="xs">
                                  {tx.status === 'confirmed' 
                                    ? t('blockchain.confirmed', 'Confirmed') 
                                    : t('blockchain.pending', 'Pending')}
                                </Text>
                              </HStack>
                            </Badge>
                          </Td>
                          <Td color={textColor} borderColor={borderColor} fontSize="sm">
                            {formatTimestamp(tx.timestamp)}
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </Box>
              ) : (
                <Box textAlign="center" py={10}>
                  <Text color={secondaryTextColor}>{t('blockchain.noTransactions', 'No transactions found')}</Text>
                </Box>
              )}
            </VStack>
          </TabPanel>
          
          {/* Smart Contracts Tab */}
          <TabPanel p={4}>
            <VStack spacing={6} align="stretch">
              <Box overflowX="auto">
                <Table variant="simple" size="sm">
                  <Thead>
                    <Tr>
                      <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.contract', 'Contract')}</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.address', 'Address')}</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.network', 'Network')}</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.description', 'Description')}</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.transactions', 'Transactions')}</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>{t('blockchain.status', 'Status')}</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {smartContracts.map((contract) => (
                      <Tr key={contract.id}>
                        <Td color={textColor} borderColor={borderColor} fontWeight="medium">
                          {contract.name}
                        </Td>
                        <Td color={textColor} borderColor={borderColor}>
                          <HStack>
                            <Tooltip label={contract.address}>
                              <Link href={getContractExplorerUrl(contract.network, contract.address)} isExternal color={primaryColor}>
                                {truncateHash(contract.address)} <ExternalLinkIcon mx="2px" />
                              </Link>
                            </Tooltip>
                            <Icon 
                              as={FaRegCopy} 
                              cursor="pointer" 
                              onClick={() => copyToClipboard(contract.address)}
                              color={secondaryTextColor}
                              _hover={{ color: primaryColor }}
                            />
                          </HStack>
                        </Td>
                        <Td color={textColor} borderColor={borderColor}>{contract.network}</Td>
                        <Td color={textColor} borderColor={borderColor} fontSize="sm">
                          {contract.description}
                        </Td>
                        <Td color={textColor} borderColor={borderColor}>
                          <HStack>
                            <Icon as={FaExchangeAlt} color={primaryColor} />
                            <Text>{contract.transactions.toLocaleString()}</Text>
                          </HStack>
                        </Td>
                        <Td borderColor={borderColor}>
                          <Badge 
                            colorScheme={contract.status === 'active' ? 'green' : 'red'} 
                            variant="subtle"
                            px={2}
                            py={1}
                            borderRadius="full"
                          >
                            {contract.status === 'active' 
                              ? t('blockchain.active', 'Active') 
                              : t('blockchain.inactive', 'Inactive')}
                          </Badge>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
              
              <Box bg="#0B0E11" p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                <Heading size="sm" color={textColor} mb={4}>
                  {t('blockchain.contractInfo', 'Smart Contract Information')}
                </Heading>
                <Text color={secondaryTextColor} fontSize="sm">
                  {t('blockchain.contractDescription', 'Our platform utilizes smart contracts to automate key processes, ensuring transparency and security. The Commission Distribution contract automatically calculates and distributes referral commissions, while the Staking Rewards contract manages daily returns. All contracts are audited by leading security firms.')}
                </Text>
                <HStack mt={4}>
                  <Badge colorScheme="green" variant="subtle">
                    {t('blockchain.audited', 'Audited')}
                  </Badge>
                  <Badge colorScheme="blue" variant="subtle">
                    {t('blockchain.transparent', 'Transparent')}
                  </Badge>
                  <Badge colorScheme="purple" variant="subtle">
                    {t('blockchain.automated', 'Automated')}
                  </Badge>
                </HStack>
              </Box>
            </VStack>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default BlockchainExplorer;
