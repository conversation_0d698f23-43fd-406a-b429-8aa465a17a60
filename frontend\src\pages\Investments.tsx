import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  VStack,
  HStack,
  Text,
  Heading,
  Badge,
  Button,
  Icon,
  SimpleGrid,
  Flex,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Progress,
  Select,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Divider,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Center
} from '@chakra-ui/react';
import {
  FaChartLine,
  FaClock,
  FaFileExport,
  FaCopy,
  FaEye,
  FaDownload,
  FaCoins,
  FaArrowUp,
  FaHistory,
  FaWallet
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import useAuth from '../hooks/useAuth';
import { investmentBalanceService, InvestmentBalance } from '../services/investmentBalanceService';
import { investmentPackageService } from '../services/api';

interface InvestmentPackage {
  _id: string;
  packageId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  accumulatedInterest: number;
  totalEarned: number;
  activeDays: number;
  totalDays: number;
  roi: number;
  createdAt: string;
  activatedAt?: string;
  interestRate: number;
  transactionId?: string;
}

interface InterestDistribution {
  _id: string;
  distributionId: string;
  cryptocurrency: string;
  amount: number;
  usdValue: number;
  distributionDate: string;
  type: 'daily' | 'bonus' | 'completion';
  status: 'completed' | 'pending' | 'failed';
  transactionHash: string;
  packageId: {
    packageId: string;
    currency: string;
    amount: number;
  };
}

interface WithdrawalEligibility {
  eligible: boolean;
  currentBalance: number;
  minimumRequired: number;
  availableForWithdrawal: number;
  withdrawalFee: number;
  usdEquivalent: number;
  status: 'eligible' | 'insufficient_balance' | 'cooldown';
}

const Investments: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();
  const { isOpen: isDistributionOpen, onOpen: onDistributionOpen, onClose: onDistributionClose } = useDisclosure();
  const { isOpen: isWithdrawalOpen, onOpen: onWithdrawalOpen, onClose: onWithdrawalClose } = useDisclosure();
  const { isOpen: isDepositOpen, onOpen: onDepositOpen, onClose: onDepositClose } = useDisclosure();

  const [investmentPackages, setInvestmentPackages] = useState<InvestmentPackage[]>([]);
  const [distributions, setDistributions] = useState<InterestDistribution[]>([]);
  const [withdrawalEligibility, setWithdrawalEligibility] = useState<WithdrawalEligibility | null>(null);
  const [loading, setLoading] = useState(true);
  const [distributionsLoading, setDistributionsLoading] = useState(false);
  const [sortBy, setSortBy] = useState('date');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedPackage, setSelectedPackage] = useState<InvestmentPackage | null>(null);
  const [investmentBalances, setInvestmentBalances] = useState<InvestmentBalance[]>([]);
  const [balancesLoading, setBalancesLoading] = useState(false);

  // Fetch comprehensive investment data
  const fetchInvestmentData = async () => {
    try {
      setLoading(true);
      const response = await investmentPackageService.getComprehensive();

      if (response.data) {
        setInvestmentPackages(response.data.data.packages || []);
        setDistributions(response.data.data.distributions || []);
        setWithdrawalEligibility(response.data.data.withdrawalEligibility);
      }
    } catch (error) {
      console.error('Error fetching investment data:', error);
      toast({
        title: t('common.error', 'Error'),
        description: t('investments.fetchError', 'Failed to load investment data'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch investment balances
  const fetchInvestmentBalances = async () => {
    try {
      setBalancesLoading(true);
      const balances = await investmentBalanceService.getInvestmentBalances();
      setInvestmentBalances(balances);
    } catch (error) {
      console.error('Error fetching investment balances:', error);
      setInvestmentBalances([]);
    } finally {
      setBalancesLoading(false);
    }
  };

  // Fetch interest distributions
  const fetchDistributions = async (packageId?: string) => {
    try {
      setDistributionsLoading(true);
      const params = packageId ? { packageId } : undefined;
      const response = await investmentPackageService.getDistributions(params);

      if (response.data) {
        setDistributions(response.data.data.distributions || []);
      }
    } catch (error) {
      console.error('Error fetching distributions:', error);
    } finally {
      setDistributionsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchInvestmentData();
      fetchInvestmentBalances();
    }
  }, [user]);

  // Calculate summary statistics grouped by currency
  const summaryByCurrency = investmentPackages.reduce((acc, pkg) => {
    if (!acc[pkg.currency]) {
      acc[pkg.currency] = {
        totalInvested: 0,
        totalEarned: 0,
        activePackages: 0,
        totalPackages: 0,
        totalROI: 0
      };
    }

    acc[pkg.currency].totalInvested += pkg.amount;
    acc[pkg.currency].totalEarned += pkg.totalEarned;
    acc[pkg.currency].totalPackages += 1;
    acc[pkg.currency].totalROI += pkg.roi;

    if (pkg.status === 'active') {
      acc[pkg.currency].activePackages += 1;
    }

    return acc;
  }, {} as Record<string, {
    totalInvested: number;
    totalEarned: number;
    activePackages: number;
    totalPackages: number;
    totalROI: number;
  }>);

  // Legacy calculations for backward compatibility
  const totalInvested = investmentPackages.reduce((sum, pkg) => sum + pkg.amount, 0);
  const totalEarned = investmentPackages.reduce((sum, pkg) => sum + pkg.totalEarned, 0);
  const activePackages = investmentPackages.filter(pkg => pkg.status === 'active').length;
  const averageROI = investmentPackages.length > 0
    ? investmentPackages.reduce((sum, pkg) => sum + pkg.roi, 0) / investmentPackages.length
    : 0;

  const formatCryptocurrencyAmount = (amount: number, currency: string) => {
    return `${amount.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    })} ${currency}`;
  };

  const formatUSDValue = (amount: number) => {
    return `$${amount.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  const getPackageProgress = (pkg: InvestmentPackage) => {
    return (pkg.activeDays / pkg.totalDays) * 100;
  };

  const getPackageStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#02C076';
      case 'completed': return '#F0B90B';
      case 'withdrawn': return '#F84960';
      case 'pending': return '#848E9C';
      default: return '#848E9C';
    }
  };

  const calculateDailyInterest = (principal: number, rate: number) => {
    return principal * (rate / 100);
  };

  if (loading) {
    return (
      <Container maxW="7xl" py={8}>
        <Center py={10}>
          <VStack spacing={4}>
            <Spinner color="#F0B90B" size="xl" thickness="4px" />
            <Text color="#848E9C">
              {t('investments.loading', 'Loading investment data...')}
            </Text>
          </VStack>
        </Center>
      </Container>
    );
  }

  return (
    <Container maxW="7xl" py={8} bg="#0B0E11" minH="100vh">
      <Box>
        <Box mb={6}>
          <Heading size="lg" color="#EAECEF" mb={2}>
            {t('investments.title', 'Investment Packages')}
          </Heading>
          <Text color="#848E9C" fontSize="sm">
            {t('investments.description', 'Manage your cryptocurrency investment packages and track earnings with 1% daily interest.')}
          </Text>
        </Box>

        <VStack spacing={6} align="stretch">
          {/* Investment Summary by Currency */}
          <Box
            bg="#1E2026"
            p={{ base: 4, md: 6 }}
            borderRadius="lg"
            borderWidth="1px"
            borderColor="#2B3139"
          >
            <Heading size={{ base: "xs", md: "sm" }} color="#F0B90B" mb={4} display="flex" alignItems="center">
              <Icon as={FaChartLine} mr={2} />
              {t('investments.summary', 'Investment Summary by Currency')}
            </Heading>

            {Object.keys(summaryByCurrency).length === 0 ? (
              <Box bg="#0B0E11" p={6} borderRadius="md" textAlign="center">
                <Icon as={FaCoins} color="#848E9C" boxSize={12} mb={3} />
                <Text color="#848E9C" fontSize="md">
                  No investment packages found. Create your first investment to see summary statistics.
                </Text>
              </Box>
            ) : (
              <VStack spacing={4} align="stretch">
                {Object.entries(summaryByCurrency).map(([currency, summary]) => (
                  <Box
                    key={currency}
                    bg="#0B0E11"
                    p={4}
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor="#2B3139"
                  >
                    <Flex
                      direction={{ base: "column", md: "row" }}
                      justify="space-between"
                      align={{ base: "stretch", md: "center" }}
                      mb={3}
                    >
                      <Text color="#F0B90B" fontSize="lg" fontWeight="bold">
                        {currency} Portfolio
                      </Text>
                      <Badge bg="#02C076" color="white" px={2} py={1} borderRadius="full" alignSelf={{ base: "flex-start", md: "center" }}>
                        {summary.activePackages} / {summary.totalPackages} Active
                      </Badge>
                    </Flex>

                    <SimpleGrid columns={{ base: 1, sm: 2, lg: 4 }} spacing={3}>
                      <Box textAlign="center">
                        <Text color="#848E9C" fontSize="xs" mb={1}>
                          Total Invested
                        </Text>
                        <Text color="#F0B90B" fontSize="md" fontWeight="bold">
                          {formatCryptocurrencyAmount(summary.totalInvested, currency)}
                        </Text>
                      </Box>

                      <Box textAlign="center">
                        <Text color="#848E9C" fontSize="xs" mb={1}>
                          Total Earned
                        </Text>
                        <Text color="#02C076" fontSize="md" fontWeight="bold">
                          {formatCryptocurrencyAmount(summary.totalEarned, currency)}
                        </Text>
                      </Box>

                      <Box textAlign="center">
                        <Text color="#848E9C" fontSize="xs" mb={1}>
                          Active Packages
                        </Text>
                        <Text color="#F0B90B" fontSize="md" fontWeight="bold">
                          {summary.activePackages}
                        </Text>
                      </Box>

                      <Box textAlign="center">
                        <Text color="#848E9C" fontSize="xs" mb={1}>
                          Average ROI
                        </Text>
                        <Text color="#F0B90B" fontSize="md" fontWeight="bold">
                          {summary.totalPackages > 0 ? (summary.totalROI / summary.totalPackages).toFixed(2) : '0.00'}%
                        </Text>
                      </Box>
                    </SimpleGrid>
                  </Box>
                ))}
              </VStack>
            )}
          </Box>

          {/* Withdrawal Eligibility */}
          {withdrawalEligibility && (
            <Box
              bg="#1E2026"
              p={{ base: 4, md: 6 }}
              borderRadius="lg"
              borderWidth="1px"
              borderColor="#2B3139"
            >
              <Flex
                direction={{ base: "column", md: "row" }}
                justify="space-between"
                align={{ base: "stretch", md: "center" }}
                mb={4}
                gap={3}
              >
                <Heading size={{ base: "xs", md: "sm" }} color="#F0B90B" display="flex" alignItems="center">
                  <Icon as={FaWallet} mr={2} />
                  {t('investments.withdrawalEligibility', 'Withdrawal Eligibility')}
                </Heading>
                <Badge
                  bg={withdrawalEligibility.status === 'eligible' ? '#02C076' :
                      withdrawalEligibility.status === 'insufficient_balance' ? '#F84960' : '#F0B90B'}
                  color="white"
                  px={3}
                  py={1}
                  borderRadius="full"
                  fontSize={{ base: "xs", md: "sm" }}
                  alignSelf={{ base: "flex-start", md: "center" }}
                >
                  {withdrawalEligibility.status === 'eligible' ? t('investments.eligible', 'Eligible') :
                   withdrawalEligibility.status === 'insufficient_balance' ? t('investments.insufficient', 'Insufficient') :
                   t('investments.cooldown', 'Cooldown')}
                </Badge>
              </Flex>

              <Box bg="#0B0E11" p={4} borderRadius="md" textAlign="center" mb={4}>
                <Icon as={FaCoins} color="#02C076" boxSize={8} mb={2} />
                <Text color="#EAECEF" fontSize="md" fontWeight="bold" mb={1}>
                  Native Cryptocurrency Withdrawals
                </Text>
                <Text color="#848E9C" fontSize="sm">
                  Withdrawal amounts are displayed in their native cryptocurrency units below.
                  Each currency has its own minimum withdrawal requirements and available balances.
                </Text>
              </Box>

              {/* Investment Package Balances */}
              {investmentBalances.length > 0 && (
                <Box bg="#0ECB8122" p={4} borderRadius="md" mb={4}>
                  <Text color="#02C076" fontWeight="bold" mb={3} display="flex" alignItems="center">
                    <Icon as={FaCoins} mr={2} />
                    📊 Available Investment Earnings by Currency:
                  </Text>
                  <SimpleGrid columns={{ base: 1, sm: 2, lg: 3 }} spacing={3}>
                    {investmentBalances
                      .filter(balance => balance.availableForWithdrawal > 0)
                      .map((balance, index) => (
                        <Box key={index} bg="#1E2026" p={3} borderRadius="md" borderWidth="1px" borderColor="#2B3139">
                          <HStack justify="space-between" mb={2}>
                            <Text color="#F0B90B" fontWeight="bold" fontSize="sm">
                              {balance.currency}
                            </Text>
                            <Badge colorScheme="green" fontSize="xs">
                              {balance.activePackages} packages
                            </Badge>
                          </HStack>
                          <VStack align="start" spacing={1}>
                            <HStack justify="space-between" w="100%">
                              <Text color="#848E9C" fontSize="xs">Available:</Text>
                              <Text color="#02C076" fontWeight="bold" fontSize="xs">
                                {formatCryptocurrencyAmount(balance.availableForWithdrawal, balance.currency)}
                              </Text>
                            </HStack>
                            <HStack justify="space-between" w="100%">
                              <Text color="#848E9C" fontSize="xs">Total Earned:</Text>
                              <Text color="#EAECEF" fontSize="xs">
                                {formatCryptocurrencyAmount(balance.totalEarnings, balance.currency)}
                              </Text>
                            </HStack>
                            {balance.totalWithdrawn > 0 && (
                              <HStack justify="space-between" w="100%">
                                <Text color="#848E9C" fontSize="xs">Withdrawn:</Text>
                                <Text color="#F84960" fontSize="xs">
                                  {formatCryptocurrencyAmount(balance.totalWithdrawn, balance.currency)}
                                </Text>
                              </HStack>
                            )}
                          </VStack>
                        </Box>
                      ))
                    }
                  </SimpleGrid>
                  {investmentBalances.filter(balance => balance.availableForWithdrawal > 0).length === 0 && (
                    <Text color="#848E9C" fontSize="sm" textAlign="center" py={4}>
                      No investment earnings available for withdrawal yet.
                    </Text>
                  )}
                </Box>
              )}

              <VStack spacing={3} align="stretch">
                <Button
                  leftIcon={<Icon as={FaCoins} />}
                  bg="#F0B90B"
                  color="#0B0E11"
                  _hover={{ bg: "#F8D12F" }}
                  size={{ base: "md", md: "lg" }}
                  onClick={onDepositOpen}
                  minH={{ base: "44px", md: "auto" }}
                  fontSize={{ base: "14px", md: "16px" }}
                >
                  {t('investments.createPackage', 'Create New Investment')}
                </Button>

                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={3}>
                  <Button
                    leftIcon={<Icon as={FaArrowUp} />}
                    variant="outline"
                    borderColor={withdrawalEligibility.eligible ? "#02C076" : "#F84960"}
                    color={withdrawalEligibility.eligible ? "#02C076" : "#F84960"}
                    _hover={{ bg: withdrawalEligibility.eligible ? "#02C07622" : "#F8496022" }}
                    size={{ base: "md", md: "lg" }}
                    isDisabled={!withdrawalEligibility.eligible}
                    onClick={onWithdrawalOpen}
                    minH={{ base: "44px", md: "auto" }}
                    fontSize={{ base: "14px", md: "16px" }}
                  >
                    {t('investments.withdraw', 'Withdraw Earnings')}
                  </Button>

                  <Button
                    leftIcon={<Icon as={FaHistory} />}
                    variant="outline"
                    borderColor="#F0B90B"
                    color="#F0B90B"
                    _hover={{ bg: "#F0B90B22" }}
                    size={{ base: "md", md: "lg" }}
                    onClick={onDistributionOpen}
                    minH={{ base: "44px", md: "auto" }}
                    fontSize={{ base: "14px", md: "16px" }}
                  >
                    {t('investments.viewHistory', 'View History')}
                  </Button>
                </SimpleGrid>
              </VStack>
            </Box>
          )}

          {/* Active Investment Packages */}
          <Box
            bg="#1E2026"
            p={{ base: 4, md: 6 }}
            borderRadius="lg"
            borderWidth="1px"
            borderColor="#2B3139"
          >
            <Flex
              direction={{ base: "column", md: "row" }}
              justify="space-between"
              align={{ base: "stretch", md: "center" }}
              mb={4}
              gap={4}
            >
              <Heading size={{ base: "xs", md: "sm" }} color="#F0B90B" display="flex" alignItems="center">
                <Icon as={FaCoins} mr={2} />
                {t('investments.activePackages', 'Active Investment Packages')}
              </Heading>
              <VStack spacing={2} align={{ base: "stretch", md: "flex-end" }}>
                <Badge bg="#02C076" color="white" px={3} py={1} borderRadius="full" alignSelf={{ base: "flex-start", md: "center" }}>
                  {activePackages} {t('investments.active', 'Active')}
                </Badge>
                <Select
                  size="sm"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  bg="#0B0E11"
                  borderColor="#2B3139"
                  color="#EAECEF"
                  w={{ base: "100%", md: "150px" }}
                  minH={{ base: "44px", md: "auto" }}
                  fontSize={{ base: "16px", md: "14px" }}
                >
                  <option value="date">{t('investments.sortByDate', 'Sort by Date')}</option>
                  <option value="amount">{t('investments.sortByAmount', 'Sort by Amount')}</option>
                  <option value="roi">{t('investments.sortByROI', 'Sort by ROI')}</option>
                </Select>
              </VStack>
            </Flex>

            {investmentPackages.length === 0 ? (
              <VStack spacing={4} py={8}>
                <Icon as={FaCoins} color="#848E9C" boxSize={16} />
                <Text color="#848E9C" fontSize="lg">
                  {t('investments.noPackages', 'No investment packages found')}
                </Text>
                <Text color="#848E9C" fontSize="sm" textAlign="center">
                  {t('investments.createFirstPackage', 'Create your first investment package to start earning 1% daily interest on your cryptocurrency.')}
                </Text>
                <Button
                  leftIcon={<Icon as={FaCoins} />}
                  bg="#F0B90B"
                  color="#0B0E11"
                  _hover={{ bg: "#F8D12F" }}
                  onClick={onDepositOpen}
                >
                  {t('investments.createPackage', 'Create New Investment')}
                </Button>
              </VStack>
            ) : (
              <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                {investmentPackages
                  .filter(pkg => filterStatus === 'all' || pkg.status === filterStatus)
                  .sort((a, b) => {
                    switch (sortBy) {
                      case 'amount':
                        return b.amount - a.amount;
                      case 'roi':
                        return b.roi - a.roi;
                      case 'date':
                      default:
                        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                    }
                  })
                  .map((pkg) => {
                    const progress = getPackageProgress(pkg);
                    const dailyEarnings = calculateDailyInterest(pkg.amount, pkg.interestRate * 100);
                    const totalValue = pkg.amount + pkg.accumulatedInterest;

                    return (
                      <Box
                        key={pkg._id}
                        bg="#0B0E11"
                        p={{ base: 4, md: 5 }}
                        borderRadius="lg"
                        borderWidth="1px"
                        borderColor="#2B3139"
                        _hover={{ borderColor: "#F0B90B", transform: { base: "none", md: "translateY(-2px)" } }}
                        transition="all 0.3s ease"
                        cursor="pointer"
                        onClick={() => setSelectedPackage(pkg)}
                      >
                        {/* Package Header */}
                        <Flex justify="space-between" align="center" mb={3}>
                          <VStack align="start" spacing={0}>
                            <Text color="#EAECEF" fontSize="sm" fontWeight="bold">
                              {pkg.currency}
                            </Text>
                            <Text color="#848E9C" fontSize="xs">
                              {pkg.packageId?.slice(0, 12)}...
                            </Text>
                          </VStack>
                          <Badge
                            bg={getPackageStatusColor(pkg.status) + '22'}
                            color={getPackageStatusColor(pkg.status)}
                            px={2}
                            py={1}
                            borderRadius="full"
                            fontSize="xs"
                            textTransform="capitalize"
                          >
                            {pkg.status}
                          </Badge>
                        </Flex>

                        {/* Principal Amount */}
                        <VStack align="start" spacing={1} mb={3}>
                          <Text color="#848E9C" fontSize="xs">
                            {t('investments.principalAmount', 'Principal Amount')}
                          </Text>
                          <Text color="#EAECEF" fontSize="lg" fontWeight="bold">
                            {formatCryptocurrencyAmount(pkg.amount, pkg.currency)}
                          </Text>
                        </VStack>

                        {/* Current Interest */}
                        <VStack align="start" spacing={1} mb={3}>
                          <Text color="#848E9C" fontSize="xs">
                            {t('investments.accumulatedInterest', 'Accumulated Interest')}
                          </Text>
                          <Text color="#02C076" fontSize="md" fontWeight="bold">
                            {formatCryptocurrencyAmount(pkg.accumulatedInterest, pkg.currency)}
                          </Text>
                        </VStack>

                        {/* Total Value */}
                        <VStack align="start" spacing={1} mb={3}>
                          <Text color="#848E9C" fontSize="xs">
                            {t('investments.totalValue', 'Total Value')}
                          </Text>
                          <Text color="#F0B90B" fontSize="lg" fontWeight="bold">
                            {formatCryptocurrencyAmount(totalValue, pkg.currency)}
                          </Text>
                        </VStack>

                        {/* Performance Metrics */}
                        <SimpleGrid columns={2} spacing={3} mb={3}>
                          <VStack align="start" spacing={0}>
                            <Text color="#848E9C" fontSize="xs">
                              {t('investments.dailyRate', 'Daily Rate')}
                            </Text>
                            <Text color="#F0B90B" fontSize="sm" fontWeight="bold">
                              1.00%
                            </Text>
                          </VStack>
                          <VStack align="start" spacing={0}>
                            <Text color="#848E9C" fontSize="xs">
                              {t('investments.roi', 'ROI')}
                            </Text>
                            <Text color="#02C076" fontSize="sm" fontWeight="bold">
                              +{pkg.roi.toFixed(2)}%
                            </Text>
                          </VStack>
                          <VStack align="start" spacing={0}>
                            <Text color="#848E9C" fontSize="xs">
                              {t('investments.dailyEarnings', 'Daily Earnings')}
                            </Text>
                            <Text color="#02C076" fontSize="sm" fontWeight="bold">
                              {formatCryptocurrencyAmount(dailyEarnings, pkg.currency)}
                            </Text>
                          </VStack>
                          <VStack align="start" spacing={0}>
                            <Text color="#848E9C" fontSize="xs">
                              {t('investments.daysActive', 'Days Active')}
                            </Text>
                            <Text color="#EAECEF" fontSize="sm" fontWeight="bold">
                              {pkg.activeDays}/{pkg.totalDays}
                            </Text>
                          </VStack>
                        </SimpleGrid>

                        {/* Progress Bar */}
                        <VStack spacing={2} mb={3}>
                          <Progress
                            value={progress}
                            size="sm"
                            colorScheme="yellow"
                            bg="#2B3139"
                            borderRadius="full"
                            w="100%"
                          />
                          <Text color="#848E9C" fontSize="xs" textAlign="center">
                            {progress.toFixed(1)}% {t('investments.complete', 'complete')}
                          </Text>
                        </VStack>

                        {/* Package Actions */}
                        <HStack spacing={2} justify={{ base: "center", md: "flex-start" }}>
                          <Button
                            size={{ base: "sm", md: "xs" }}
                            variant="ghost"
                            color="#F0B90B"
                            _hover={{ bg: "#F0B90B22" }}
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedPackage(pkg);
                            }}
                            minH={{ base: "36px", md: "auto" }}
                            minW={{ base: "36px", md: "auto" }}
                          >
                            <Icon as={FaEye} />
                          </Button>
                          {pkg.status === 'active' && (
                            <Button
                              size={{ base: "sm", md: "xs" }}
                              variant="ghost"
                              color="#02C076"
                              _hover={{ bg: "#02C07622" }}
                              onClick={(e) => {
                                e.stopPropagation();
                                fetchDistributions(pkg._id);
                                onDistributionOpen();
                              }}
                              minH={{ base: "36px", md: "auto" }}
                              minW={{ base: "36px", md: "auto" }}
                            >
                              <Icon as={FaHistory} />
                            </Button>
                          )}
                          <Button
                            size={{ base: "sm", md: "xs" }}
                            variant="ghost"
                            color="#848E9C"
                            _hover={{ bg: "#2B3139" }}
                            onClick={(e) => {
                              e.stopPropagation();
                              // Export package details
                            }}
                            minH={{ base: "36px", md: "auto" }}
                            minW={{ base: "36px", md: "auto" }}
                          >
                            <Icon as={FaFileExport} />
                          </Button>
                        </HStack>
                      </Box>
                    );
                  })}
              </SimpleGrid>
            )}
          </Box>

          {/* Interest Distribution History Modal */}
          <Modal isOpen={isDistributionOpen} onClose={onDistributionClose} size="6xl">
            <ModalOverlay bg="blackAlpha.800" />
            <ModalContent bg="#1E2026" borderColor="#2B3139" borderWidth="1px">
              <ModalHeader color="#F0B90B" display="flex" alignItems="center">
                <Icon as={FaHistory} mr={2} />
                {t('investments.distributionHistory', 'Interest Distribution History')}
              </ModalHeader>
              <ModalCloseButton color="#848E9C" />
              <ModalBody pb={6}>
                {distributionsLoading ? (
                  <Center py={10}>
                    <Spinner color="#F0B90B" size="lg" />
                  </Center>
                ) : distributions.length === 0 ? (
                  <VStack spacing={4} py={8}>
                    <Icon as={FaClock} color="#848E9C" boxSize={12} />
                    <Text color="#848E9C">
                      {t('investments.noDistributions', 'No interest distributions yet')}
                    </Text>
                    <Text color="#848E9C" fontSize="sm" textAlign="center">
                      {t('investments.distributionsWillAppear', 'Interest distributions will appear here after 03:00 UTC+3 daily')}
                    </Text>
                  </VStack>
                ) : (
                  <TableContainer>
                    <Table variant="simple" size="sm">
                      <Thead>
                        <Tr>
                          <Th color="#848E9C" borderColor="#2B3139">
                            {t('investments.distributionTable.date', 'Date')}
                          </Th>
                          <Th color="#848E9C" borderColor="#2B3139">
                            {t('investments.distributionTable.amount', 'Amount')}
                          </Th>
                          <Th color="#848E9C" borderColor="#2B3139">
                            {t('investments.distributionTable.type', 'Type')}
                          </Th>
                          <Th color="#848E9C" borderColor="#2B3139">
                            {t('investments.distributionTable.status', 'Status')}
                          </Th>
                          <Th color="#848E9C" borderColor="#2B3139">
                            {t('investments.distributionTable.txHash', 'Transaction')}
                          </Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {distributions.map((distribution) => (
                          <Tr key={distribution._id} _hover={{ bg: "#2B3139" }}>
                            <Td borderColor="#2B3139">
                              <VStack align="start" spacing={0}>
                                <Text color="#EAECEF" fontSize="sm">
                                  {new Date(distribution.distributionDate).toLocaleDateString()}
                                </Text>
                                <Text color="#848E9C" fontSize="xs">
                                  {new Date(distribution.distributionDate).toLocaleTimeString()}
                                </Text>
                              </VStack>
                            </Td>
                            <Td borderColor="#2B3139">
                              <Text color="#02C076" fontSize="sm" fontWeight="bold">
                                {formatCryptocurrencyAmount(distribution.amount, distribution.cryptocurrency)}
                              </Text>
                            </Td>
                            <Td borderColor="#2B3139">
                              <Badge
                                bg={distribution.type === 'daily' ? '#F0B90B22' : '#02C07622'}
                                color={distribution.type === 'daily' ? '#F0B90B' : '#02C076'}
                                px={2}
                                py={1}
                                borderRadius="full"
                                fontSize="xs"
                                textTransform="capitalize"
                              >
                                {distribution.type}
                              </Badge>
                            </Td>
                            <Td borderColor="#2B3139">
                              <Badge
                                bg={distribution.status === 'completed' ? '#02C076' : '#F0B90B'}
                                color="white"
                                px={2}
                                py={1}
                                borderRadius="full"
                                fontSize="xs"
                                textTransform="capitalize"
                              >
                                {distribution.status}
                              </Badge>
                            </Td>
                            <Td borderColor="#2B3139">
                              <HStack spacing={2}>
                                <Text fontSize="xs" color="#848E9C" maxW="100px" isTruncated>
                                  {distribution.transactionHash.slice(0, 10)}...
                                </Text>
                                <Button
                                  size="xs"
                                  variant="ghost"
                                  color="#848E9C"
                                  _hover={{ color: "#F0B90B" }}
                                  onClick={() => {
                                    navigator.clipboard.writeText(distribution.transactionHash);
                                    toast({
                                      title: t('common.copied', 'Transaction hash copied!'),
                                      status: "success",
                                      duration: 2000,
                                      isClosable: true,
                                    });
                                  }}
                                >
                                  <Icon as={FaCopy} />
                                </Button>
                              </HStack>
                            </Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </TableContainer>
                )}
              </ModalBody>
            </ModalContent>
          </Modal>
        </VStack>
      </Box>
    </Container>
  );
};

export default Investments;