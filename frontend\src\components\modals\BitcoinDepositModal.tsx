import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON>dal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  Divider,
  useToast,
  Textarea,
  Image,
  Badge,
  useColorModeValue,
  Icon,
  Tooltip,
  InputGroup,
  InputRightElement,
  Alert,
  AlertIcon,
  Progress,
  useClipboard,
  FormHelperText,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Spinner
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FaClipboard, FaCheck, FaInfoCircle, FaUpload, FaTrash, FaExclamationTriangle, FaBitcoin } from 'react-icons/fa';
import useAuth from '../../hooks/useAuth';
import useSystemConfig from '../../hooks/useSystemConfig';
import { investmentService } from '../../services/investmentService';
import NetworkSelector from '../common/NetworkSelector';
import { CRYPTO_NETWORKS, getDefaultNetwork, NetworkOption } from '../../utils/cryptoNetworks';

// Default Bitcoin address - Will be replaced with address from backend
const DEFAULT_BITCOIN_ADDRESS = '**********************************';

interface BitcoinDepositModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const BitcoinDepositModal: React.FC<BitcoinDepositModalProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { } = useAuth(); // We don't need user here
  const {
    isCurrencyEnabled,
    getNetworkAddressesForCurrency,
    getAvailableNetworksForCurrency,
    loading: configLoading
  } = useSystemConfig();

  // State variables
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [receipt, setReceipt] = useState<File | null>(null);
  const [receiptPreview, setReceiptPreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [investmentId, setInvestmentId] = useState<string | null>(null);
  const [cryptoAddress, setCryptoAddress] = useState<string>(DEFAULT_BITCOIN_ADDRESS);
  const [error, setError] = useState<string | null>(null);
  const [selectedNetwork, setSelectedNetwork] = useState<string>('');
  const [networkOptions, setNetworkOptions] = useState<NetworkOption[]>([]);
  const [currentNetworkDetails, setCurrentNetworkDetails] = useState<NetworkOption | undefined>(undefined);
  const [availableWallets, setAvailableWallets] = useState<{[network: string]: string[]}>({});
  const [disabledNetworks, setDisabledNetworks] = useState<string[]>([]);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Clipboard functionality
  const { hasCopied, onCopy } = useClipboard(cryptoAddress);

  // Colors
  const bgColor = useColorModeValue('#FFFFFF', '#1E2329');
  const borderColor = useColorModeValue('#E2E8F0', '#2B3139');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#4A5568', '#848E9C');
  const bitcoinColor = '#F7931A';

  // Helper function to toggle fallback mode in development
  const toggleFallbackMode = () => {
    if (process.env.NODE_ENV === 'development') {
      // Always disable fallback mode to ensure real API calls
      localStorage.setItem('useFallbackMode', 'false');
      toast({
        title: 'Fallback Mode Disabled',
        description: 'Bitcoin deposit will now use real API calls',
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Lấy thông tin ví có sẵn và khởi tạo network options
  useEffect(() => {
    // Tạo một biến để theo dõi xem component có còn mounted hay không
    let isMounted = true;

    const initializeNetworks = async () => {
      try {
        // Kiểm tra xem tiền điện tử có được bật không
        if (!isCurrencyEnabled('BTC')) {
          if (isMounted) setError('Bitcoin deposits are currently disabled');
          return;
        }

        // Lấy danh sách ví có sẵn từ system config
        let walletData: {[network: string]: string[]} = {};

        // Lấy thông tin network từ system config
        const networkAddresses = getNetworkAddressesForCurrency('BTC');

        // Nếu có dữ liệu từ system config, sử dụng nó
        if (networkAddresses.length > 0) {
          networkAddresses.forEach(na => {
            walletData[na.network] = na.addresses;
          });
        } else {
          // Nếu không có dữ liệu từ system config, thử lấy từ API
          try {
            const response = await investmentService.getAvailableWallets('BTC');

            if (response && response.data && response.data.data) {
              const responseData = response.data.data;

              // Xử lý cấu trúc dữ liệu từ API
              if (responseData && typeof responseData === 'object') {
                Object.keys(responseData).forEach(networkId => {
                  const networkInfo = responseData[networkId];
                  if (networkInfo && networkInfo.addresses) {
                    walletData[networkId] = networkInfo.addresses;
                  }
                });
              }
            }
          } catch (apiError) {
            console.warn('Could not fetch wallet data from API:', apiError);

            // Nếu không có dữ liệu từ cả API và system config, thử lấy từ getDepositAddress
            try {
              const addressResponse = await investmentService.getDepositAddress('BTC');
              if (addressResponse && addressResponse.data && addressResponse.data.data) {
                const networkId = addressResponse.data.data.network || 'default';
                walletData[networkId] = [addressResponse.data.data.address];
              }
            } catch (addressError) {
              console.error('Error fetching deposit address:', addressError);
            }
          }
        }

        // Kiểm tra xem component còn mounted không trước khi cập nhật state
        if (!isMounted) return;

        // Nếu vẫn không có dữ liệu, hiển thị thông báo lỗi
        if (Object.keys(walletData).length === 0) {
          setError('No wallet addresses available for Bitcoin');
          return;
        }

        setAvailableWallets(walletData);

        // Xác định các network không có địa chỉ ví
        const networks = CRYPTO_NETWORKS['BTC'] || [];
        const disabledNetworkIds: string[] = [];

        networks.forEach(network => {
          // Nếu network không có trong danh sách ví hoặc không có địa chỉ nào
          if (!walletData[network.id] || walletData[network.id].length === 0) {
            disabledNetworkIds.push(network.id);
          }
        });

        setDisabledNetworks(disabledNetworkIds);
        setNetworkOptions(networks);

        // Chọn network mặc định có sẵn địa chỉ ví
        const defaultNetwork = getDefaultNetwork('BTC');
        if (defaultNetwork && !disabledNetworkIds.includes(defaultNetwork.id)) {
          setSelectedNetwork(defaultNetwork.id);
          setCurrentNetworkDetails(defaultNetwork);
        } else {
          // Nếu network mặc định không có địa chỉ, chọn network đầu tiên có địa chỉ
          const availableNetwork = networks.find(network => !disabledNetworkIds.includes(network.id));
          if (availableNetwork) {
            setSelectedNetwork(availableNetwork.id);
            setCurrentNetworkDetails(availableNetwork);
          }
        }
      } catch (error) {
        console.error('Error initializing networks:', error);
        if (isMounted) setError('Failed to initialize network options');

        // Trong trường hợp lỗi, vẫn hiển thị tất cả các network
        const networks = CRYPTO_NETWORKS['BTC'] || [];
        if (isMounted) {
          setNetworkOptions(networks);

          const defaultNetwork = getDefaultNetwork('BTC');
          if (defaultNetwork) {
            setSelectedNetwork(defaultNetwork.id);
            setCurrentNetworkDetails(defaultNetwork);
          }
        }
      }
    };

    // Chỉ gọi khi modal được mở và configLoading đã hoàn tất
    if (isOpen && !configLoading) {
      initializeNetworks();
    }

    // Cleanup function để tránh memory leak và cập nhật state sau khi component unmounted
    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, configLoading]);

  // Update current network details when selected network changes
  useEffect(() => {
    if (selectedNetwork && networkOptions.length > 0) {
      const networkDetails = networkOptions.find(network => network.id === selectedNetwork);
      setCurrentNetworkDetails(networkDetails);
    }
  }, [selectedNetwork, networkOptions]);

  // Handle network selection change
  const handleNetworkChange = (networkId: string) => {
    setSelectedNetwork(networkId);
  };

  // Calculate commission (1%)
  const commission = amount ? parseFloat(amount) * 0.01 : 0;

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: t('depositModal.fileTooLarge', 'File Too Large'),
          description: t('depositModal.fileSizeLimit', 'Maximum file size should be 10MB.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Check file type
      const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
      if (!validTypes.includes(file.type)) {
        toast({
          title: t('depositModal.invalidFileType', 'Invalid File Type'),
          description: t('depositModal.supportedFormats', 'Supported formats: JPG, PNG, PDF'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      setReceipt(file);

      // Create preview for images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setReceiptPreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        // For PDFs, just show an icon or text
        setReceiptPreview(null);
      }
    }
  };

  // Remove uploaded file
  const handleRemoveFile = () => {
    setReceipt(null);
    setReceiptPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };



  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: t('depositModal.invalidAmount', 'Invalid Amount'),
        description: t('depositModal.enterValidAmount', 'Please enter a valid amount.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!receipt) {
      toast({
        title: t('depositModal.receiptRequired', 'Receipt Required'),
        description: t('depositModal.uploadReceipt', 'Please upload your transaction receipt.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Check if we're in fallback mode - always disable in development
    localStorage.setItem('useFallbackMode', 'false');
    const isInFallbackMode = false;

    if (!investmentId) {
      // Don't create mock investment ID, always use real API
      if (false) {
        // This block will never execute
        const mockId = `mock_${Date.now()}`;
        console.log('Creating mock investment ID for receipt upload:', mockId);
        setInvestmentId(mockId);
      } else {
        toast({
          title: 'Error',
          description: 'Investment not created. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
    }

    // Start submission process
    setIsSubmitting(true);
    setError(null);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('receipt', receipt);

      // Add transaction details to the form data
      const txHash = 'tx_' + Date.now(); // In a real app, user would provide this
      console.log('Using transaction hash:', txHash);
      formData.append('txHash', txHash);
      formData.append('amount', amount);
      formData.append('currency', 'BTC');
      formData.append('network', selectedNetwork);

      if (description) {
        formData.append('description', description);
      }

      // Always disable fallback mode to ensure real API calls
      localStorage.setItem('useFallbackMode', 'false');
      const isInFallbackMode = false;

      // Never use fallback mode, always use real API
      if (false) {
        // This block will never execute
        console.log('Simulating receipt upload in development/fallback mode');
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1500));
      } else if (investmentId) {
        try {
          // Upload receipt to the backend
          console.log('Uploading receipt to backend with investment ID:', investmentId);
          console.log('FormData contents:', Array.from(formData.entries()));
          const response = await investmentService.uploadReceipt(investmentId, formData);

          // Log the response for debugging
          console.log('Receipt upload response:', response);
          console.log('Receipt uploaded successfully to backend!');

          // If we have a transaction hash from the user, update it in the backend
          if (formData.get('txHash')) {
            await investmentService.updateTransactionHash(
              investmentId,
              formData.get('txHash') as string
            );
          }
        } catch (uploadError) {
          console.error('Error uploading receipt:', uploadError);

          // Show detailed error in development mode
          if (process.env.NODE_ENV === 'development') {
            console.error('Error uploading receipt in development mode:', uploadError);

            toast({
              title: 'API Error',
              description: `Failed to upload receipt: ${uploadError.message}. Check if the backend server is running correctly.`,
              status: 'error',
              duration: 3000,
              isClosable: true,
            });

            // In development, propagate the error
            throw uploadError;
          } else {
            // In production, propagate the error
            throw uploadError;
          }
        }
      } else {
        throw new Error('Investment ID is missing');
      }

      // Show success message
      toast({
        title: t('depositModal.successTitle', 'Transaction Successful'),
        description: t('depositModal.successDescription', 'Your Bitcoin investment request has been received. After the receipt is verified, 1% commission will be added to your account.'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Reset form and close modal
      setAmount('');
      setDescription('');
      setReceipt(null);
      setReceiptPreview(null);
      setActiveStep(0);
      setInvestmentId(null);
      setCryptoAddress(DEFAULT_BITCOIN_ADDRESS);
      onClose();
    } catch (err: any) {
      console.error('Error uploading receipt:', err);
      setError(err.response?.data?.message || 'Failed to upload receipt');

      toast({
        title: 'Error',
        description: err.response?.data?.message || 'Failed to upload receipt',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Next step in the wizard
  const nextStep = async () => {
    // Clear any previous errors
    setError(null);

    // Validate amount for step 1
    if (activeStep === 0 && (!amount || parseFloat(amount) <= 0)) {
      toast({
        title: t('depositModal.invalidAmount', 'Invalid Amount'),
        description: t('depositModal.enterValidAmount', 'Please enter a valid amount.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Validate network selection for step 1
    if (activeStep === 0 && !selectedNetwork) {
      toast({
        title: t('depositModal.networkRequired', 'Network Required'),
        description: t('depositModal.selectNetwork', 'Please select a network for your transaction.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // For development mode, always disable fallback mode
    localStorage.setItem('useFallbackMode', 'false');
    const useFallbackMode = false;

    // If moving from step 1 to step 2, create the investment
    if (activeStep === 0) {
      try {
        setIsSubmitting(true);

        // Never use fallback mode
        if (false) {
          // This block will never execute
          console.log('Using fallback mode for development');
          // Create a mock investment ID
          const mockId = `mock_${Date.now()}`;
          setInvestmentId(mockId);
          setCryptoAddress(DEFAULT_BITCOIN_ADDRESS);

          // Simulate network delay
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Move to the next step
          setActiveStep(activeStep + 1);
          setIsSubmitting(false);
          return;
        }

        // Create investment in the backend
        console.log('Creating investment with data:', {
          currency: 'BTC',
          amount: parseFloat(amount),
          description: description || undefined,
          network: selectedNetwork
        });

        const response = await investmentService.createInvestment({
          currency: 'BTC',
          amount: parseFloat(amount),
          description: description || undefined,
          network: selectedNetwork
        });

        // Lấy địa chỉ ví cho network đã chọn
        const addressResponse = await investmentService.getDepositAddress('BTC', selectedNetwork);

        // Store the investment ID for later use
        if (response && typeof response === 'object' && 'investment' in response) {
          const investment = response.investment as { _id: string };
          console.log('Investment created successfully with ID:', investment._id);
          setInvestmentId(investment._id);

          // Set the crypto address from the address API response
          if (addressResponse && addressResponse.data && addressResponse.data.data) {
            console.log('Got crypto address from API:', addressResponse.data.data.address);
            setCryptoAddress(addressResponse.data.data.address);
          } else if (availableWallets[selectedNetwork] && availableWallets[selectedNetwork].length > 0) {
            // Nếu API không trả về địa chỉ cụ thể, chọn ngẫu nhiên một địa chỉ từ danh sách có sẵn
            const randomIndex = Math.floor(Math.random() * availableWallets[selectedNetwork].length);
            const randomAddress = availableWallets[selectedNetwork][randomIndex];
            console.log('Using random address from available wallets:', randomAddress);
            setCryptoAddress(randomAddress);
          } else {
            // Fallback to default address if API fails and no available wallets
            console.warn('Could not get address from API or available wallets, using default');
            setCryptoAddress(DEFAULT_BITCOIN_ADDRESS);
          }

          // Move to the next step
          setActiveStep(activeStep + 1);
        } else {
          // Handle case where response doesn't have expected structure
          throw new Error('Invalid response from server');
        }
      } catch (err: any) {
        console.error('Error creating investment:', err);

        // Improved error handling with more detailed messages
        let errorMessage = 'Failed to create investment';

        if (err.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          errorMessage = err.response.data?.message || `Server error: ${err.response.status}`;
          console.log('Response error data:', err.response.data);
        } else if (err.request) {
          // The request was made but no response was received
          errorMessage = 'No response from server. Please check your internet connection.';
        } else {
          // Something happened in setting up the request that triggered an Error
          errorMessage = err.message || 'An unexpected error occurred';
        }

        // For development mode, add more details to help with debugging
        if (process.env.NODE_ENV === 'development') {
          console.log('Full error details:', err);

          // In development mode, show detailed error
          console.error('API error in development mode:', err);

          toast({
            title: 'API Connection Error',
            description: `Failed to connect to the backend API: ${err.message}. Check if the backend server is running.`,
            status: 'error',
            duration: 5000,
            isClosable: true,
          });

          setActiveStep(activeStep + 1);
          setIsSubmitting(false);
          return; // Exit early to avoid showing error message in dev mode
        }

        setError(errorMessage);

        toast({
          title: 'Error',
          description: errorMessage,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsSubmitting(false);
      }
    }
    // If moving from step 2 to step 3
    else if (activeStep === 1) {
      // For development/mock mode, ensure we have a valid investment ID
      if (!investmentId) {
        // Never use mock IDs, always require real investment ID
        // In production, show an error
        setError('Investment not created properly. Please try again.');
        toast({
          title: 'Error',
          description: 'Investment not created properly. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      } else {
        // We have a valid investment ID, proceed to next step
        setActiveStep(activeStep + 1);
      }
    }
    // For other steps, just move forward
    else {
      setActiveStep(activeStep + 1);
    }
  };

  // Previous step in the wizard
  const prevStep = () => {
    setActiveStep(activeStep - 1);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size={{ base: "full", md: "xl" }} isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent
        bg={bgColor}
        borderColor={borderColor}
        borderWidth="1px"
        mx={{ base: 2, md: "auto" }}
        maxH={{ base: "calc(100vh - 40px)", md: "calc(100vh - 80px)" }}
        overflowY="auto"
        height="auto"
      >
        <ModalHeader
          color={bitcoinColor}
          borderBottomWidth="1px"
          borderColor={borderColor}
          p={{ base: 3, md: 6 }}
        >
          <Flex align="center">
            <Icon as={FaBitcoin} mr={2} boxSize={{ base: 5, md: 6 }} />
            <Text fontSize={{ base: "lg", md: "xl" }}>
              {t('depositModal.bitcoinTitle', 'Bitcoin Investment')}
            </Text>
          </Flex>
        </ModalHeader>
        <ModalCloseButton color={textColor} />

        <ModalBody py={{ base: 4, md: 6 }} px={{ base: 3, md: 6 }}>
          {configLoading ? (
            <Flex justify="center" align="center" py={10}>
              <Spinner size="xl" color={bitcoinColor} thickness="4px" />
            </Flex>
          ) : !isCurrencyEnabled('BTC') ? (
            <Alert status="error" borderRadius="md" flexDirection="column" alignItems="center" textAlign="center" py={6}>
              <AlertIcon boxSize={10} mr={0} mb={4} />
              <Text fontWeight="bold" fontSize="lg">
                {t('depositModal.currencyDisabled', 'Bitcoin Deposits Disabled')}
              </Text>
              <Text mt={2}>
                {t('depositModal.currencyDisabledMessage', 'Bitcoin deposits are currently disabled. Please try again later or contact support for more information.')}
              </Text>
              <Button mt={6} colorScheme="red" onClick={onClose}>
                {t('common.close', 'Close')}
              </Button>
            </Alert>
          ) : (
          <Tabs index={activeStep} onChange={setActiveStep} variant="enclosed" colorScheme="yellow">
            <TabList
              mb={4}
              overflowX={{ base: "auto", md: "visible" }}
              overflowY="hidden"
              css={{
                scrollbarWidth: "none",
                "::-webkit-scrollbar": { display: "none" },
                WebkitOverflowScrolling: "touch"
              }}
            >
              <Tab
                _selected={{ color: bitcoinColor, borderColor: bitcoinColor }}
                fontSize={{ base: "xs", md: "sm" }}
                py={{ base: 2, md: 3 }}
                px={{ base: 2, md: 4 }}
                whiteSpace="nowrap"
              >
                {t('depositModal.step1', '1. Investment Details')}
              </Tab>
              <Tab
                _selected={{ color: bitcoinColor, borderColor: bitcoinColor }}
                isDisabled={activeStep < 1}
                fontSize={{ base: "xs", md: "sm" }}
                py={{ base: 2, md: 3 }}
                px={{ base: 2, md: 4 }}
                whiteSpace="nowrap"
              >
                {t('depositModal.step2', '2. Bitcoin Address')}
              </Tab>
              <Tab
                _selected={{ color: bitcoinColor, borderColor: bitcoinColor }}
                isDisabled={activeStep < 2}
                fontSize={{ base: "xs", md: "sm" }}
                py={{ base: 2, md: 3 }}
                px={{ base: 2, md: 4 }}
                whiteSpace="nowrap"
              >
                {t('depositModal.step3', '3. Receipt Upload')}
              </Tab>
            </TabList>

            <TabPanels>
              {/* Step 1: Investment Details */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {error && (
                    <Alert status="error" borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">Error</Text>
                        <Text fontSize="sm">{error}</Text>

                        {/* Development mode helper - only shown in development */}
                        {process.env.NODE_ENV === 'development' && (
                          <Button
                            size="xs"
                            mt={2}
                            colorScheme="blue"
                            onClick={toggleFallbackMode}
                          >
                            Disable Fallback Mode
                          </Button>
                        )}
                      </Box>
                    </Alert>
                  )}

                  <Box
                    p={4}
                    bg="rgba(247, 147, 26, 0.1)"
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor="rgba(247, 147, 26, 0.3)"
                  >
                    <Flex align="center">
                      <Icon as={FaBitcoin} color={bitcoinColor} mr={2} boxSize={5} />
                      <Text fontWeight="bold" color={textColor}>
                        {t('depositModal.bitcoinInvestment', 'Bitcoin Investment')}
                      </Text>
                    </Flex>
                  </Box>

                  <FormControl isRequired>
                    <FormLabel>{t('depositModal.amount', 'Investment Amount')}</FormLabel>
                    <InputGroup>
                      <Input
                        type="number"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        placeholder="0.00"
                        bg={useColorModeValue('white', '#0B0E11')}
                        borderColor={borderColor}
                        color={textColor}
                      />
                      <InputRightElement width="4.5rem">
                        <Text color={secondaryTextColor} fontSize="sm">BTC</Text>
                      </InputRightElement>
                    </InputGroup>
                  </FormControl>

                  {/* Network Selection */}
                  <NetworkSelector
                    networks={networkOptions}
                    selectedNetwork={selectedNetwork}
                    onChange={handleNetworkChange}
                    isRequired={true}
                    label={t('depositModal.network', 'Select Network')}
                    helperText={t('depositModal.networkHelperText', 'Choose the network for your Bitcoin deposit')}
                    currency="BTC"
                    disabledNetworks={disabledNetworks}
                  />

                  <FormControl>
                    <FormLabel>{t('depositModal.description', 'Description (Optional)')}</FormLabel>
                    <Textarea
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder={t('depositModal.descriptionPlaceholder', 'Notes about your investment...')}
                      bg={useColorModeValue('white', '#0B0E11')}
                      borderColor={borderColor}
                      color={textColor}
                      rows={3}
                    />
                  </FormControl>

                  {/* Commission calculation */}
                  {amount && parseFloat(amount) > 0 && (
                    <Box
                      p={4}
                      bg="rgba(247, 147, 26, 0.05)"
                      borderRadius="md"
                      borderWidth="1px"
                      borderColor="rgba(247, 147, 26, 0.2)"
                    >
                      <Text fontWeight="bold" mb={2} color={textColor}>
                        {t('depositModal.commissionCalculation.title', 'Transaction Details')}
                      </Text>
                      <HStack justify="space-between" mb={1}>
                        <Text color={secondaryTextColor}>{t('depositModal.commissionCalculation.investmentAmount', 'Investment Amount')}</Text>
                        <Text color={textColor}>{parseFloat(amount).toFixed(8)} BTC</Text>
                      </HStack>

                      {currentNetworkDetails && (
                        <HStack justify="space-between" mb={1}>
                          <Text color={secondaryTextColor}>{t('depositModal.networkFee', 'Network Fee')}</Text>
                          <Text color={secondaryTextColor}>{currentNetworkDetails.fee.toFixed(8)} BTC</Text>
                        </HStack>
                      )}

                      <HStack justify="space-between">
                        <Text color={secondaryTextColor}>{t('depositModal.commissionCalculation.commissionYouWillEarn', 'Commission (1%)')}</Text>
                        <Text color={bitcoinColor} fontWeight="bold">{commission.toFixed(8)} BTC</Text>
                      </HStack>

                      <Divider my={2} />

                      {currentNetworkDetails && (
                        <HStack justify="space-between" mb={1}>
                          <Text color={secondaryTextColor} fontWeight="medium">{t('depositModal.processingTime', 'Processing Time')}</Text>
                          <Text color={textColor}>{currentNetworkDetails.processingTime}</Text>
                        </HStack>
                      )}

                      <Text fontSize="xs" color={secondaryTextColor} mt={1}>
                        {t('depositModal.commissionCalculation.commissionRate', '* Commission rate: 1% - Automatically added to your account after confirmation')}
                      </Text>
                    </Box>
                  )}
                </VStack>

                <Flex justify="flex-end" mt={6}>
                  <Button
                    colorScheme="yellow"
                    bg={bitcoinColor}
                    color="white"
                    _hover={{ bg: "#e68a19" }}
                    onClick={nextStep}
                    isLoading={isSubmitting}
                    loadingText={t('common.processing', 'Processing...')}
                    isDisabled={!amount || parseFloat(amount) <= 0 || isSubmitting}
                  >
                    {t('common.next', 'Next')}
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 2: Bitcoin Address */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {error && (
                    <Alert status="error" borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">Error</Text>
                        <Text fontSize="sm">{error}</Text>

                        {/* Development mode helper - only shown in development */}
                        {process.env.NODE_ENV === 'development' && (
                          <Button
                            size="xs"
                            mt={2}
                            colorScheme="blue"
                            onClick={toggleFallbackMode}
                          >
                            Disable Fallback Mode
                          </Button>
                        )}
                      </Box>
                    </Alert>
                  )}

                  <Alert status="info" borderRadius="md">
                    <AlertIcon />
                    <Box>
                      <Text fontWeight="bold">{t('depositModal.transferInstructions', 'Transfer Instructions')}</Text>
                      <Text fontSize="sm">
                        {t('depositModal.transferInstructionsDetail', 'Please send exactly the amount you specified to the Bitcoin address below and then upload your receipt.')}
                      </Text>
                      {currentNetworkDetails && (
                        <Text fontSize="sm" fontWeight="bold" mt={2} color={bitcoinColor}>
                          {t('depositModal.selectedNetwork', 'Selected Network')}: {currentNetworkDetails.name}
                        </Text>
                      )}
                    </Box>
                  </Alert>

                  {currentNetworkDetails?.warningMessage && (
                    <Alert status="warning" borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">{t('depositModal.networkWarning', 'Important Network Information')}</Text>
                        <Text fontSize="sm">{currentNetworkDetails.warningMessage}</Text>
                      </Box>
                    </Alert>
                  )}

                  <Box
                    p={6}
                    bg={useColorModeValue('gray.50', '#0B0E11')}
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor={borderColor}
                    textAlign="center"
                  >
                    <Text fontWeight="bold" mb={4} color={textColor}>
                      {t('depositModal.bitcoinAddress', 'Make Payment to Bitcoin Address')}
                    </Text>

                    <Box
                      p={{ base: 2, md: 3 }}
                      bg={useColorModeValue('white', '#1E2329')}
                      borderRadius="md"
                      borderWidth="1px"
                      borderColor={borderColor}
                      mb={4}
                      position="relative"
                    >
                      <Flex
                        alignItems="center"
                        justifyContent="space-between"
                        flexDirection={{ base: "column", sm: "row" }}
                        gap={{ base: 2, sm: 0 }}
                      >
                        <Text
                          fontFamily="monospace"
                          color={textColor}
                          fontSize={{ base: "xs", md: "sm" }}
                          isTruncated
                          maxWidth={{ base: "100%", sm: "calc(100% - 80px)" }}
                          title={cryptoAddress}
                          wordBreak="break-all"
                          textAlign={{ base: "center", sm: "left" }}
                          width={{ base: "100%", sm: "auto" }}
                        >
                          {cryptoAddress}
                        </Text>
                        <Button
                          size="sm"
                          onClick={onCopy}
                          leftIcon={hasCopied ? <FaCheck /> : <FaClipboard />}
                          colorScheme={hasCopied ? "green" : "gray"}
                          variant="ghost"
                          ml={{ base: 0, sm: 2 }}
                          minWidth="70px"
                          width={{ base: "100%", sm: "auto" }}
                        >
                          {hasCopied ? t('common.copied', 'Copied!') : t('depositModal.copyButton', 'Copy')}
                        </Button>
                      </Flex>
                    </Box>

                    <Flex
                      justify="center"
                      flexDirection={{ base: "column", sm: "row" }}
                      gap={{ base: 2, sm: 4 }}
                      width="100%"
                      flexWrap="wrap"
                    >
                      <Badge
                        colorScheme="yellow"
                        p={{ base: 1.5, md: 2 }}
                        borderRadius="md"
                        fontSize={{ base: "xs", md: "sm" }}
                        width={{ base: "100%", sm: "auto" }}
                        textAlign="center"
                      >
                        {t('depositModal.amount', 'Amount')}: {parseFloat(amount).toFixed(8)} BTC
                      </Badge>
                      <Badge
                        colorScheme="green"
                        p={{ base: 1.5, md: 2 }}
                        borderRadius="md"
                        fontSize={{ base: "xs", md: "sm" }}
                        width={{ base: "100%", sm: "auto" }}
                        textAlign="center"
                      >
                        {t('depositModal.commission', 'Commission')}: {commission.toFixed(8)} BTC
                      </Badge>
                      {currentNetworkDetails && (
                        <Badge
                          colorScheme="blue"
                          p={{ base: 1.5, md: 2 }}
                          borderRadius="md"
                          fontSize={{ base: "xs", md: "sm" }}
                          width={{ base: "100%", sm: "auto" }}
                          textAlign="center"
                        >
                          {t('depositModal.network', 'Network')}: {currentNetworkDetails.name}
                        </Badge>
                      )}
                    </Flex>
                  </Box>

                  <Box
                    p={4}
                    bg="rgba(247, 147, 26, 0.05)"
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor="rgba(247, 147, 26, 0.2)"
                  >
                    <HStack align="flex-start">
                      <Icon as={FaExclamationTriangle} color={bitcoinColor} boxSize={5} mt={1} />
                      <Box>
                        <Text fontWeight="bold" color={textColor}>
                          {t('depositModal.important', 'Important')}
                        </Text>
                        <Text fontSize="sm" color={secondaryTextColor}>
                          {t('depositModal.warning', '* Please send exactly the amount you specified to this address and then upload your receipt.')}
                        </Text>
                        <Text fontSize="sm" color={secondaryTextColor} mt={1}>
                          {t('depositModal.warningDetail', '* Transfers made to the wrong address or over the wrong network cannot be recovered.')}
                        </Text>
                        {currentNetworkDetails && (
                          <Text fontSize="sm" color="red.400" fontWeight="medium" mt={2}>
                            * {t('depositModal.networkWarningDetail', `Make sure to use the ${currentNetworkDetails.name} for this transaction. Using a different network may result in permanent loss of funds.`)}
                          </Text>
                        )}
                      </Box>
                    </HStack>
                  </Box>
                </VStack>

                <Flex
                  justify="space-between"
                  mt={6}
                  flexDirection={{ base: "column", sm: "row" }}
                  gap={{ base: 2, sm: 0 }}
                >
                  <Button
                    variant="ghost"
                    onClick={prevStep}
                    width={{ base: "100%", sm: "auto" }}
                    order={{ base: 2, sm: 1 }}
                  >
                    {t('common.back', 'Back')}
                  </Button>
                  <Button
                    colorScheme="yellow"
                    bg={bitcoinColor}
                    color="white"
                    _hover={{ bg: "#e68a19" }}
                    onClick={nextStep}
                    isLoading={isSubmitting}
                    loadingText={t('common.processing', 'Processing...')}
                    width={{ base: "100%", sm: "auto" }}
                    order={{ base: 1, sm: 2 }}
                  >
                    {t('common.next', 'Next')}
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 3: Receipt Upload */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {error && (
                    <Alert status="error" borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">Error</Text>
                        <Text fontSize="sm">{error}</Text>

                        {/* Development mode helper - only shown in development */}
                        {process.env.NODE_ENV === 'development' && (
                          <Button
                            size="xs"
                            mt={2}
                            colorScheme="blue"
                            onClick={toggleFallbackMode}
                          >
                            Disable Fallback Mode
                          </Button>
                        )}
                      </Box>
                    </Alert>
                  )}

                  <FormControl isRequired>
                    <FormLabel>
                      {t('depositModal.uploadReceipt.title', 'Upload Receipt')}
                      <Tooltip
                        label={t('depositModal.uploadReceipt.tooltip', 'Upload your transaction receipt or screenshot')}
                        placement="top"
                      >
                        <Icon as={FaInfoCircle} ml={2} boxSize={4} color={secondaryTextColor} />
                      </Tooltip>
                    </FormLabel>

                    {!receipt ? (
                      <Box
                        borderWidth="2px"
                        borderRadius="md"
                        borderColor={borderColor}
                        borderStyle="dashed"
                        p={6}
                        textAlign="center"
                        bg={useColorModeValue('gray.50', '#0B0E11')}
                        cursor="pointer"
                        onClick={() => fileInputRef.current?.click()}
                        _hover={{ borderColor: bitcoinColor }}
                        transition="all 0.2s"
                      >
                        <Input
                          type="file"
                          accept="image/jpeg,image/png,image/jpg,application/pdf"
                          onChange={handleFileChange}
                          ref={fileInputRef}
                          display="none"
                        />
                        <Icon as={FaUpload} boxSize={8} color={secondaryTextColor} mb={4} />
                        <Text fontWeight="bold" color={textColor}>
                          {t('depositModal.dragAndDrop', 'Drag and drop file or click here')}
                        </Text>
                        <Text fontSize="sm" color={secondaryTextColor} mt={2}>
                          {t('depositModal.uploadReceipt.supportedFormats', 'Supported formats: JPG, PNG, PDF (Max. 10MB)')}
                        </Text>
                      </Box>
                    ) : (
                      <Box
                        borderWidth="1px"
                        borderRadius="md"
                        borderColor={borderColor}
                        p={4}
                        bg={useColorModeValue('white', '#0B0E11')}
                      >
                        <Flex justify="space-between" align="center">
                          <HStack>
                            {receiptPreview ? (
                              <Image
                                src={receiptPreview}
                                alt="Receipt preview"
                                boxSize="60px"
                                objectFit="cover"
                                borderRadius="md"
                              />
                            ) : (
                              <Box
                                bg={useColorModeValue('gray.100', '#1E2329')}
                                p={3}
                                borderRadius="md"
                              >
                                <Icon as={FaUpload} boxSize={5} color={secondaryTextColor} />
                              </Box>
                            )}
                            <Box>
                              <Text fontWeight="bold" color={textColor}>{receipt.name}</Text>
                              <Text fontSize="sm" color={secondaryTextColor}>
                                {(receipt.size / 1024 / 1024).toFixed(2)} MB
                              </Text>
                            </Box>
                          </HStack>
                          <Button
                            size="sm"
                            colorScheme="red"
                            variant="ghost"
                            onClick={handleRemoveFile}
                          >
                            <Icon as={FaTrash} />
                          </Button>
                        </Flex>
                      </Box>
                    )}
                    <FormHelperText>
                      {t('depositModal.uploadReceipt.helperText', 'The receipt you upload will be used to verify your transaction.')}
                    </FormHelperText>
                  </FormControl>

                  <Accordion allowToggle>
                    <AccordionItem borderColor={borderColor}>
                      <AccordionButton py={3}>
                        <Box flex="1" textAlign="left" fontWeight="bold" color={textColor}>
                          {t('depositModal.tips.title', 'Receipt Upload Tips')}
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                      <AccordionPanel pb={4}>
                        <VStack align="start" spacing={2} color={secondaryTextColor}>
                          <Text>{t('depositModal.tips.tip1', '• Make sure the receipt image is clear and legible')}</Text>
                          <Text>{t('depositModal.tips.tip2', '• Transaction amount and Bitcoin address should be visible')}</Text>
                          <Text>{t('depositModal.tips.tip3', '• Transaction hash should be visible')}</Text>
                          <Text>{t('depositModal.tips.tip4', '• Verification process is usually completed within 24 hours')}</Text>
                        </VStack>
                      </AccordionPanel>
                    </AccordionItem>
                  </Accordion>
                </VStack>

                <Flex
                  justify="space-between"
                  mt={6}
                  flexDirection={{ base: "column", sm: "row" }}
                  gap={{ base: 2, sm: 0 }}
                >
                  <Button
                    variant="ghost"
                    onClick={prevStep}
                    width={{ base: "100%", sm: "auto" }}
                    order={{ base: 2, sm: 1 }}
                  >
                    {t('common.back', 'Back')}
                  </Button>
                  <Button
                    colorScheme="yellow"
                    bg={bitcoinColor}
                    color="white"
                    _hover={{ bg: "#e68a19" }}
                    onClick={handleSubmit}
                    isLoading={isSubmitting}
                    loadingText={t('common.processing', 'Processing...')}
                    isDisabled={!receipt}
                    width={{ base: "100%", sm: "auto" }}
                    order={{ base: 1, sm: 2 }}
                    fontSize={{ base: "xs", md: "sm" }}
                  >
                    {t('depositModal.submitButton', 'Submit Receipt and Earn')}
                  </Button>
                </Flex>

                {isSubmitting && (
                  <Box mt={4}>
                    <Text fontSize="sm" color={secondaryTextColor} mb={2}>
                      {t('depositModal.uploading', 'Uploading receipt...')}
                    </Text>
                    <Progress isIndeterminate size="sm" colorScheme="yellow" borderRadius="full" />
                  </Box>
                )}
              </TabPanel>
            </TabPanels>
          </Tabs>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default BitcoinDepositModal;
