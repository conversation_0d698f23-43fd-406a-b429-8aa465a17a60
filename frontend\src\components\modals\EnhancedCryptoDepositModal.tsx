import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  Divider,
  useToast,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Badge,
  Icon,
  Progress,
  useClipboard,
  Image,
  Alert,
  AlertIcon,
  Spinner,
  Card,
  CardBody,
  Heading,
  Step,
  StepDescription,
  StepIcon,
  StepIndicator,
  StepNumber,
  StepSeparator,
  StepStatus,
  StepTitle,
  Stepper
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FaCoins,
  FaQrcode,
  FaCopy,
  FaCheck,
  FaArrowRight,
  FaArrowLeft,
  FaCalculator,
  FaWallet,
  FaClock
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
// import QRCode from 'qrcode';
import useAuth from '../../hooks/useAuth';
import { useResponsive } from '../../hooks/useResponsive';

const MotionBox = motion(Box);

interface EnhancedCryptoDepositModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCurrency?: string;
  onDepositSuccess?: () => void;
}

interface CryptoOption {
  symbol: string;
  name: string;
  icon: string;
  minDeposit: number;
  network: string;
}

const SUPPORTED_CRYPTOS: CryptoOption[] = [
  { symbol: 'BTC', name: 'Bitcoin', icon: '₿', minDeposit: 0.001, network: 'Bitcoin' },
  { symbol: 'ETH', name: 'Ethereum', icon: 'Ξ', minDeposit: 0.01, network: 'Ethereum' },
  { symbol: 'USDT', name: 'Tether', icon: '₮', minDeposit: 10, network: 'TRC20' },
  { symbol: 'BNB', name: 'Binance Coin', icon: 'BNB', minDeposit: 0.1, network: 'BSC' },
  { symbol: 'SOL', name: 'Solana', icon: 'SOL', minDeposit: 0.1, network: 'Solana' },
  { symbol: 'DOGE', name: 'Dogecoin', icon: 'Ð', minDeposit: 10, network: 'Dogecoin' },
  { symbol: 'TRX', name: 'TRON', icon: 'T', minDeposit: 100, network: 'TRON' }
];

const steps = [
  { title: 'Amount', description: 'Enter deposit amount' },
  { title: 'Address', description: 'Get deposit address' },
  { title: 'Confirm', description: 'Complete deposit' }
];

const EnhancedCryptoDepositModal: React.FC<EnhancedCryptoDepositModalProps> = ({
  isOpen,
  onClose,
  selectedCurrency = 'USDT',
  onDepositSuccess
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const toast = useToast();
  const { isMobile } = useResponsive();
  // Manual step management
  const [activeStep, setActiveStep] = useState(0);

  // State
  const [selectedCrypto, setSelectedCrypto] = useState<CryptoOption>(
    SUPPORTED_CRYPTOS.find(c => c.symbol === selectedCurrency) || SUPPORTED_CRYPTOS[2]
  );
  const [amount, setAmount] = useState<number>(0);
  const [depositAddress, setDepositAddress] = useState<string>('');
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [interestPreview, setInterestPreview] = useState({
    daily: 0,
    weekly: 0,
    monthly: 0
  });

  const { hasCopied, onCopy } = useClipboard(depositAddress);

  // Colors
  const bgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const borderColor = "#2B3139";

  // Calculate interest preview
  useEffect(() => {
    if (amount > 0) {
      const dailyInterest = amount * 0.01; // 1% daily
      setInterestPreview({
        daily: dailyInterest,
        weekly: dailyInterest * 7,
        monthly: dailyInterest * 30
      });
    } else {
      setInterestPreview({ daily: 0, weekly: 0, monthly: 0 });
    }
  }, [amount]);

  // Generate QR code when address changes
  useEffect(() => {
    if (depositAddress) {
      // Simple QR code URL generation (you can replace with actual QR service)
      const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${isMobile ? '200x200' : '300x300'}&data=${encodeURIComponent(depositAddress)}`;
      setQrCodeUrl(qrUrl);
    }
  }, [depositAddress, isMobile]);

  const handleCryptoChange = (symbol: string) => {
    const crypto = SUPPORTED_CRYPTOS.find(c => c.symbol === symbol);
    if (crypto) {
      setSelectedCrypto(crypto);
      setAmount(crypto.minDeposit);
    }
  };

  const handleNext = async () => {
    if (activeStep === 0) {
      // Validate amount
      if (amount < selectedCrypto.minDeposit) {
        toast({
          title: 'Invalid Amount',
          description: `Minimum deposit is ${selectedCrypto.minDeposit} ${selectedCrypto.symbol}`,
          status: 'error',
          duration: 3000,
        });
        return;
      }
      setActiveStep(1);
      await generateDepositAddress();
    } else if (activeStep === 1) {
      setActiveStep(2);
    }
  };

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep(activeStep - 1);
    }
  };

  const generateDepositAddress = async () => {
    setLoading(true);
    try {
      // Simulate API call to generate address
      // In real implementation, this would call your wallet service
      const mockAddress = `${selectedCrypto.symbol.toLowerCase()}1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh`;
      setDepositAddress(mockAddress);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to generate deposit address',
        status: 'error',
        duration: 3000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmDeposit = async () => {
    setLoading(true);
    try {
      // Create investment package
      // In real implementation, this would call your API
      toast({
        title: 'Deposit Initiated',
        description: 'Your deposit has been initiated. You will receive notifications when confirmed.',
        status: 'success',
        duration: 5000,
      });

      onDepositSuccess?.();
      onClose();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to initiate deposit',
        status: 'error',
        duration: 3000,
      });
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <VStack spacing={6}>
            <Box w="full">
              <Text mb={2} color={textColor}>Select Cryptocurrency</Text>
              <Select
                value={selectedCrypto.symbol}
                onChange={(e) => handleCryptoChange(e.target.value)}
                bg={bgColor}
                borderColor={borderColor}
                color={textColor}
              >
                {SUPPORTED_CRYPTOS.map((crypto) => (
                  <option key={crypto.symbol} value={crypto.symbol}>
                    {crypto.icon} {crypto.name} ({crypto.symbol})
                  </option>
                ))}
              </Select>
            </Box>

            <Box w="full">
              <Text mb={2} color={textColor}>Amount</Text>
              <NumberInput
                value={amount}
                onChange={(_, value) => setAmount(value)}
                min={selectedCrypto.minDeposit}
                precision={selectedCrypto.symbol === 'BTC' ? 8 : selectedCrypto.symbol === 'ETH' ? 6 : 2}
              >
                <NumberInputField
                  bg={bgColor}
                  borderColor={borderColor}
                  color={textColor}
                  placeholder={`Min: ${selectedCrypto.minDeposit} ${selectedCrypto.symbol}`}
                />
                <NumberInputStepper>
                  <NumberIncrementStepper color={primaryColor} />
                  <NumberDecrementStepper color={primaryColor} />
                </NumberInputStepper>
              </NumberInput>
            </Box>

            {amount > 0 && (
              <Card w="full" bg={bgColor} borderColor={borderColor}>
                <CardBody>
                  <Heading size="sm" color={primaryColor} mb={3}>
                    <Icon as={FaCalculator} mr={2} />
                    Interest Preview
                  </Heading>
                  <VStack spacing={2} align="stretch">
                    <Flex justify="space-between">
                      <Text color={textColor}>Daily (1%):</Text>
                      <Text color={primaryColor} fontWeight="bold">
                        {interestPreview.daily.toFixed(4)} {selectedCrypto.symbol}
                      </Text>
                    </Flex>
                    <Flex justify="space-between">
                      <Text color={textColor}>Weekly (7%):</Text>
                      <Text color={primaryColor} fontWeight="bold">
                        {interestPreview.weekly.toFixed(4)} {selectedCrypto.symbol}
                      </Text>
                    </Flex>
                    <Flex justify="space-between">
                      <Text color={textColor}>Monthly (30%):</Text>
                      <Text color={primaryColor} fontWeight="bold">
                        {interestPreview.monthly.toFixed(4)} {selectedCrypto.symbol}
                      </Text>
                    </Flex>
                  </VStack>
                </CardBody>
              </Card>
            )}
          </VStack>
        );

      case 1:
        return (
          <VStack spacing={6}>
            <Alert status="info" bg={`${primaryColor}20`} borderColor={primaryColor}>
              <AlertIcon color={primaryColor} />
              <Text color={textColor}>
                Send exactly {amount} {selectedCrypto.symbol} to the address below
              </Text>
            </Alert>

            {loading ? (
              <Spinner size="xl" color={primaryColor} />
            ) : (
              <>
                {qrCodeUrl && (
                  <Box textAlign="center">
                    <Image src={qrCodeUrl} alt="QR Code" mx="auto" />
                    <Text mt={2} color={textColor} fontSize="sm">
                      Scan QR code or copy address below
                    </Text>
                  </Box>
                )}

                <Box w="full">
                  <Text mb={2} color={textColor}>Deposit Address</Text>
                  <Flex>
                    <Box
                      flex={1}
                      p={3}
                      bg={bgColor}
                      borderColor={borderColor}
                      borderWidth="1px"
                      borderRadius="md"
                      fontFamily="mono"
                      fontSize="sm"
                      color={textColor}
                      wordBreak="break-all"
                    >
                      {depositAddress}
                    </Box>
                    <Button
                      ml={2}
                      onClick={onCopy}
                      colorScheme={hasCopied ? "green" : "yellow"}
                      variant="outline"
                    >
                      <Icon as={hasCopied ? FaCheck : FaCopy} />
                    </Button>
                  </Flex>
                </Box>

                <Badge colorScheme="yellow" p={2}>
                  Network: {selectedCrypto.network}
                </Badge>
              </>
            )}
          </VStack>
        );

      case 2:
        return (
          <VStack spacing={6}>
            <Alert status="warning" bg={`${primaryColor}20`} borderColor={primaryColor}>
              <AlertIcon color={primaryColor} />
              <Text color={textColor}>
                Please confirm your deposit details before proceeding
              </Text>
            </Alert>

            <Card w="full" bg={bgColor} borderColor={borderColor}>
              <CardBody>
                <VStack spacing={3} align="stretch">
                  <Flex justify="space-between">
                    <Text color={textColor}>Currency:</Text>
                    <Text color={primaryColor} fontWeight="bold">
                      {selectedCrypto.name} ({selectedCrypto.symbol})
                    </Text>
                  </Flex>
                  <Flex justify="space-between">
                    <Text color={textColor}>Amount:</Text>
                    <Text color={primaryColor} fontWeight="bold">
                      {amount} {selectedCrypto.symbol}
                    </Text>
                  </Flex>
                  <Flex justify="space-between">
                    <Text color={textColor}>Network:</Text>
                    <Text color={textColor}>{selectedCrypto.network}</Text>
                  </Flex>
                  <Divider />
                  <Flex justify="space-between">
                    <Text color={textColor}>Daily Interest:</Text>
                    <Text color="#0ECB81" fontWeight="bold">
                      {interestPreview.daily.toFixed(4)} {selectedCrypto.symbol}
                    </Text>
                  </Flex>
                </VStack>
              </CardBody>
            </Card>

            <Alert status="info" bg={`${primaryColor}20`} borderColor={primaryColor}>
              <AlertIcon color={primaryColor} />
              <VStack align="start" spacing={1}>
                <Text color={textColor} fontWeight="bold">Important Notes:</Text>
                <Text color={textColor} fontSize="sm">• Interest starts at 03:00 UTC+3 after deposit confirmation</Text>
                <Text color={textColor} fontSize="sm">• Minimum withdrawal: 50 USDT equivalent</Text>
                <Text color={textColor} fontSize="sm">• Withdrawals only allowed after 03:00 UTC+3</Text>
              </VStack>
            </Alert>
          </VStack>
        );

      default:
        return null;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size={isMobile ? "full" : "lg"} isCentered>
      <ModalOverlay bg="blackAlpha.800" />
      <ModalContent bg={bgColor} borderColor={borderColor} borderWidth="1px">
        <ModalHeader color={textColor}>
          <Icon as={FaCoins} mr={2} color={primaryColor} />
          Crypto Deposit
        </ModalHeader>
        <ModalCloseButton color={textColor} />

        <ModalBody pb={6}>
          <VStack spacing={6}>
            <Stepper index={activeStep} colorScheme="yellow" w="full">
              {steps.map((step, index) => (
                <Step key={index}>
                  <StepIndicator>
                    <StepStatus
                      complete={<StepIcon />}
                      incomplete={<StepNumber />}
                      active={<StepNumber />}
                    />
                  </StepIndicator>

                  <Box flexShrink="0">
                    <StepTitle>{step.title}</StepTitle>
                    <StepDescription>{step.description}</StepDescription>
                  </Box>

                  <StepSeparator />
                </Step>
              ))}
            </Stepper>

            <Box w="full">
              {renderStepContent()}
            </Box>

            <HStack w="full" justify="space-between">
              <Button
                onClick={handleBack}
                isDisabled={activeStep === 0}
                variant="outline"
                colorScheme="gray"
                leftIcon={<FaArrowLeft />}
              >
                Back
              </Button>

              {activeStep < steps.length - 1 ? (
                <Button
                  onClick={handleNext}
                  bg={primaryColor}
                  color="#0B0E11"
                  _hover={{ bg: "#F8D12F" }}
                  rightIcon={<FaArrowRight />}
                  isLoading={loading}
                >
                  Next
                </Button>
              ) : (
                <Button
                  onClick={handleConfirmDeposit}
                  bg={primaryColor}
                  color="#0B0E11"
                  _hover={{ bg: "#F8D12F" }}
                  rightIcon={<FaCheck />}
                  isLoading={loading}
                >
                  Confirm Deposit
                </Button>
              )}
            </HStack>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default React.memo(EnhancedCryptoDepositModal);
