import mongoose, { Document, Schema } from 'mongoose';
import { logger } from '../utils/logger';
import { getSocketService } from './socketService';

/**
 * Comprehensive Audit Logging Service
 * Tracks all security-sensitive operations and user activities
 */

export interface AuditLogEntry {
  userId?: string;
  action: string;
  category: 'AUTH' | 'FINANCIAL' | 'SECURITY' | 'ADMIN' | 'USER' | 'SYSTEM';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
}

export interface IAuditLog extends Document {
  userId?: mongoose.Types.ObjectId;
  action: string;
  category: string;
  severity: string;
  description: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
}

// Audit Log Schema
const auditLogSchema = new Schema<IAuditLog>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  action: {
    type: String,
    required: true,
    index: true
  },
  category: {
    type: String,
    required: true,
    enum: ['AUTH', 'FINANCIAL', 'SECURITY', 'ADMIN', 'USER', 'SYSTEM'],
    index: true
  },
  severity: {
    type: String,
    required: true,
    enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'],
    index: true
  },
  description: {
    type: String,
    required: true
  },
  metadata: {
    type: Schema.Types.Mixed
  },
  ipAddress: {
    type: String,
    index: true
  },
  userAgent: {
    type: String
  },
  sessionId: {
    type: String,
    index: true
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  success: {
    type: Boolean,
    required: true,
    index: true
  },
  errorMessage: {
    type: String
  }
}, {
  collection: 'audit_logs'
});

// Compound indexes for efficient queries
auditLogSchema.index({ userId: 1, timestamp: -1 });
auditLogSchema.index({ category: 1, severity: 1, timestamp: -1 });
auditLogSchema.index({ action: 1, success: 1, timestamp: -1 });
auditLogSchema.index({ ipAddress: 1, timestamp: -1 });

const AuditLog = mongoose.model<IAuditLog>('AuditLog', auditLogSchema);

export class AuditLoggingService {
  private static instance: AuditLoggingService;

  public static getInstance(): AuditLoggingService {
    if (!AuditLoggingService.instance) {
      AuditLoggingService.instance = new AuditLoggingService();
    }
    return AuditLoggingService.instance;
  }

  /**
   * Log an audit entry
   */
  async logEntry(entry: AuditLogEntry): Promise<void> {
    try {
      const auditLog = new AuditLog({
        userId: entry.userId ? new mongoose.Types.ObjectId(entry.userId) : undefined,
        action: entry.action,
        category: entry.category,
        severity: entry.severity,
        description: entry.description,
        metadata: entry.metadata,
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent,
        sessionId: entry.sessionId,
        timestamp: entry.timestamp,
        success: entry.success,
        errorMessage: entry.errorMessage
      });

      await auditLog.save();

      // Log to application logger as well
      const logLevel = this.getLogLevel(entry.severity);
      logger[logLevel]('Audit log entry created', {
        auditId: auditLog._id,
        userId: entry.userId,
        action: entry.action,
        category: entry.category,
        severity: entry.severity,
        success: entry.success
      });

      // Send real-time notifications for critical events
      if (entry.severity === 'CRITICAL' || entry.category === 'SECURITY') {
        await this.sendRealTimeAlert(entry);
      }

    } catch (error) {
      logger.error('Failed to create audit log entry:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Authentication audit logs
   */
  async logAuthentication(userId: string, action: string, success: boolean, metadata?: any, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.logEntry({
      userId,
      action,
      category: 'AUTH',
      severity: success ? 'LOW' : 'MEDIUM',
      description: `User ${action} ${success ? 'successful' : 'failed'}`,
      metadata,
      ipAddress,
      userAgent,
      timestamp: new Date(),
      success,
      errorMessage: success ? undefined : metadata?.error
    });
  }

  /**
   * Financial operation audit logs
   */
  async logFinancialOperation(userId: string, action: string, amount: number, currency: string, success: boolean, metadata?: any, ipAddress?: string): Promise<void> {
    await this.logEntry({
      userId,
      action,
      category: 'FINANCIAL',
      severity: success ? 'MEDIUM' : 'HIGH',
      description: `Financial operation: ${action} ${amount} ${currency}`,
      metadata: {
        amount,
        currency,
        ...metadata
      },
      ipAddress,
      timestamp: new Date(),
      success,
      errorMessage: success ? undefined : metadata?.error
    });
  }

  /**
   * Security event audit logs
   */
  async logSecurityEvent(userId: string | undefined, action: string, severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL', description: string, metadata?: any, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.logEntry({
      userId,
      action,
      category: 'SECURITY',
      severity,
      description,
      metadata,
      ipAddress,
      userAgent,
      timestamp: new Date(),
      success: false // Security events are typically failures or alerts
    });
  }

  /**
   * Admin operation audit logs
   */
  async logAdminOperation(adminUserId: string, action: string, targetUserId?: string, success: boolean = true, metadata?: any, ipAddress?: string): Promise<void> {
    await this.logEntry({
      userId: adminUserId,
      action,
      category: 'ADMIN',
      severity: 'HIGH',
      description: `Admin operation: ${action}${targetUserId ? ` on user ${targetUserId}` : ''}`,
      metadata: {
        targetUserId,
        ...metadata
      },
      ipAddress,
      timestamp: new Date(),
      success
    });
  }

  /**
   * User activity audit logs
   */
  async logUserActivity(userId: string, action: string, description: string, metadata?: any, ipAddress?: string): Promise<void> {
    await this.logEntry({
      userId,
      action,
      category: 'USER',
      severity: 'LOW',
      description,
      metadata,
      ipAddress,
      timestamp: new Date(),
      success: true
    });
  }

  /**
   * System event audit logs
   */
  async logSystemEvent(action: string, severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL', description: string, metadata?: any): Promise<void> {
    await this.logEntry({
      action,
      category: 'SYSTEM',
      severity,
      description,
      metadata,
      timestamp: new Date(),
      success: true
    });
  }

  /**
   * Get audit logs with filtering and pagination
   */
  async getAuditLogs(filters: any = {}, page: number = 1, limit: number = 50): Promise<{ logs: IAuditLog[], total: number, page: number, totalPages: number }> {
    try {
      const query: any = {};

      // Apply filters
      if (filters.userId) {
        query.userId = new mongoose.Types.ObjectId(filters.userId);
      }
      if (filters.category) {
        query.category = filters.category;
      }
      if (filters.severity) {
        query.severity = filters.severity;
      }
      if (filters.action) {
        query.action = { $regex: filters.action, $options: 'i' };
      }
      if (filters.success !== undefined) {
        query.success = filters.success;
      }
      if (filters.ipAddress) {
        query.ipAddress = filters.ipAddress;
      }
      if (filters.dateFrom || filters.dateTo) {
        query.timestamp = {};
        if (filters.dateFrom) {
          query.timestamp.$gte = new Date(filters.dateFrom);
        }
        if (filters.dateTo) {
          query.timestamp.$lte = new Date(filters.dateTo);
        }
      }

      const skip = (page - 1) * limit;

      const [logs, total] = await Promise.all([
        AuditLog.find(query)
          .populate('userId', 'email firstName lastName')
          .sort({ timestamp: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        AuditLog.countDocuments(query)
      ]);

      return {
        logs: logs as IAuditLog[],
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };

    } catch (error) {
      logger.error('Failed to retrieve audit logs:', error);
      throw error;
    }
  }

  /**
   * Get audit statistics
   */
  async getAuditStatistics(timeframe: 'day' | 'week' | 'month' = 'day'): Promise<any> {
    try {
      const now = new Date();
      let startDate: Date;

      switch (timeframe) {
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
      }

      const [categoryStats, severityStats, successStats, topActions] = await Promise.all([
        // Category statistics
        AuditLog.aggregate([
          { $match: { timestamp: { $gte: startDate } } },
          { $group: { _id: '$category', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),

        // Severity statistics
        AuditLog.aggregate([
          { $match: { timestamp: { $gte: startDate } } },
          { $group: { _id: '$severity', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),

        // Success/failure statistics
        AuditLog.aggregate([
          { $match: { timestamp: { $gte: startDate } } },
          { $group: { _id: '$success', count: { $sum: 1 } } }
        ]),

        // Top actions
        AuditLog.aggregate([
          { $match: { timestamp: { $gte: startDate } } },
          { $group: { _id: '$action', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ])
      ]);

      return {
        timeframe,
        period: { from: startDate, to: now },
        categoryStats,
        severityStats,
        successStats,
        topActions
      };

    } catch (error) {
      logger.error('Failed to get audit statistics:', error);
      throw error;
    }
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs(retentionDays: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);

      const result = await AuditLog.deleteMany({
        timestamp: { $lt: cutoffDate },
        severity: { $nin: ['HIGH', 'CRITICAL'] } // Keep high and critical logs longer
      });

      logger.info('Audit log cleanup completed', {
        deletedCount: result.deletedCount,
        retentionDays,
        cutoffDate
      });

      return result.deletedCount;

    } catch (error) {
      logger.error('Failed to cleanup audit logs:', error);
      throw error;
    }
  }

  /**
   * Private helper methods
   */

  private getLogLevel(severity: string): 'debug' | 'info' | 'warn' | 'error' {
    switch (severity) {
      case 'LOW': return 'debug';
      case 'MEDIUM': return 'info';
      case 'HIGH': return 'warn';
      case 'CRITICAL': return 'error';
      default: return 'info';
    }
  }

  private async sendRealTimeAlert(entry: AuditLogEntry): Promise<void> {
    try {
      const socketService = getSocketService();

      // Send to admin room
      socketService.sendSystemNotification({
        type: 'audit_alert',
        severity: entry.severity,
        category: entry.category,
        action: entry.action,
        description: entry.description,
        userId: entry.userId,
        ipAddress: entry.ipAddress,
        timestamp: entry.timestamp.toISOString()
      });

      // Send to specific user if it's a security event affecting them
      if (entry.userId && entry.category === 'SECURITY') {
        socketService.broadcastToUser(entry.userId, {
          type: 'security_alert',
          payload: {
            message: entry.description,
            severity: entry.severity,
            timestamp: entry.timestamp.toISOString()
          }
        });
      }

    } catch (error) {
      logger.warn('Failed to send real-time audit alert:', error);
    }
  }
}

export { AuditLog };
