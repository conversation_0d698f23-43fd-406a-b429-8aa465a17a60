// Configuration for supported cryptocurrencies in the system
// This file centralizes all cryptocurrency configurations
//
// To add a new cryptocurrency:
// 1. Add it to ADDITIONAL_CURRENCIES array
// 2. Set isActive: true to enable it
// 3. Update validation rules in cryptoUtils.ts
// 4. Add icon mapping in cryptoIconMap.ts
// 5. Add color in cryptoUtils.ts getCryptoColor function

export interface CurrencyConfig {
  symbol: string;
  name: string;
  networks: string[];
  addressFormat: string;
  isActive: boolean;
  priority: number; // Lower number = higher priority in UI
}

// Main cryptocurrencies actively used in the system
export const SUPPORTED_CURRENCIES: CurrencyConfig[] = [
  {
    symbol: 'BTC',
    name: 'Bitcoin',
    networks: ['mainnet'],
    addressFormat: 'Legacy (1...) or SegWit (bc1...)',
    isActive: true,
    priority: 1
  },
  {
    symbol: 'ETH',
    name: 'Ethereum',
    networks: ['mainnet', 'arbitrum', 'optimism'],
    addressFormat: '0x... format (42 characters)',
    isActive: true,
    priority: 2
  },
  {
    symbol: 'USDT',
    name: 'Tether USD',
    networks: ['ethereum', 'tron', 'bsc'],
    addressFormat: '0x... (ERC-20/BEP-20) or T... (TRC-20)',
    isActive: true,
    priority: 3
  },
  {
    symbol: 'BNB',
    name: 'Binance Coin',
    networks: ['bsc'],
    addressFormat: '0x... format (42 characters)',
    isActive: true,
    priority: 4
  },
  {
    symbol: 'ADA',
    name: 'Cardano',
    networks: ['mainnet'],
    addressFormat: 'addr1... format (Cardano address)',
    isActive: true,
    priority: 5
  },
  {
    symbol: 'DOT',
    name: 'Polkadot',
    networks: ['mainnet'],
    addressFormat: '1... format (Polkadot address)',
    isActive: true,
    priority: 6
  }
];

// Additional cryptocurrencies that can be enabled in the future
export const ADDITIONAL_CURRENCIES: CurrencyConfig[] = [
  {
    symbol: 'LINK',
    name: 'Chainlink',
    networks: ['ethereum', 'bsc', 'arbitrum'],
    addressFormat: '0x... format (42 characters)',
    isActive: false,
    priority: 7
  },
  {
    symbol: 'UNI',
    name: 'Uniswap',
    networks: ['ethereum', 'arbitrum', 'optimism'],
    addressFormat: '0x... format (42 characters)',
    isActive: false,
    priority: 8
  },
  {
    symbol: 'MATIC',
    name: 'Polygon',
    networks: ['polygon', 'ethereum'],
    addressFormat: '0x... format (42 characters)',
    isActive: false,
    priority: 9
  },
  {
    symbol: 'AVAX',
    name: 'Avalanche',
    networks: ['avalanche', 'ethereum'],
    addressFormat: '0x... format (42 characters)',
    isActive: false,
    priority: 10
  },
  {
    symbol: 'SOL',
    name: 'Solana',
    networks: ['solana'],
    addressFormat: 'Base58 format (32-44 characters)',
    isActive: false,
    priority: 11
  }
];

// Get only active currencies
export const getActiveCurrencies = (): CurrencyConfig[] => {
  return SUPPORTED_CURRENCIES
    .filter(currency => currency.isActive)
    .sort((a, b) => a.priority - b.priority);
};

// Get all currencies (active + inactive)
export const getAllCurrencies = (): CurrencyConfig[] => {
  return [...SUPPORTED_CURRENCIES, ...ADDITIONAL_CURRENCIES]
    .sort((a, b) => a.priority - b.priority);
};

// Get currency symbols for filter
export const getActiveCurrencySymbols = (): string[] => {
  return getActiveCurrencies().map(currency => currency.symbol);
};

// Get networks for a specific currency
export const getNetworksForCurrency = (symbol: string): string[] => {
  const currency = [...SUPPORTED_CURRENCIES, ...ADDITIONAL_CURRENCIES]
    .find(c => c.symbol.toUpperCase() === symbol.toUpperCase());
  return currency?.networks || ['mainnet'];
};

// Check if a currency is supported
export const isCurrencySupported = (symbol: string): boolean => {
  return SUPPORTED_CURRENCIES.some(
    currency => currency.symbol.toUpperCase() === symbol.toUpperCase() && currency.isActive
  );
};

// Get currency configuration
export const getCurrencyConfig = (symbol: string): CurrencyConfig | undefined => {
  return [...SUPPORTED_CURRENCIES, ...ADDITIONAL_CURRENCIES]
    .find(c => c.symbol.toUpperCase() === symbol.toUpperCase());
};
