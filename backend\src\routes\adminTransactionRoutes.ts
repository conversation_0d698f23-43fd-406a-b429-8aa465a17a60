import express from 'express';
import { protect, admin } from '../middleware/authMiddleware';
import adminTransactionController from '../controllers/adminTransactionController';

const router = express.Router();

// Apply auth middleware to all routes
router.use(protect, admin);

// Transaction routes
router.get('/', adminTransactionController.getAllTransactions);
router.get('/:id', adminTransactionController.getTransactionById);

// Deposit routes
router.patch('/deposits/:id/status', adminTransactionController.updateDepositStatus);
router.get('/deposits/summary', adminTransactionController.getDepositsSummary);

// Withdrawal routes
router.patch('/withdrawals/:id/status', adminTransactionController.updateWithdrawalStatus);
router.get('/withdrawals/summary', adminTransactionController.getWithdrawalsSummary);

export default router;