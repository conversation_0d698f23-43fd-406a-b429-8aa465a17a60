import { CryptoAddressService } from '../services/CryptoAddressService';
import { loginSchema, registerSchema, investmentSchema } from '../../frontend/src/schemas/validationSchemas';
import { z } from 'zod';

/**
 * Comprehensive Security Audit Script
 * Tests all critical security implementations
 */

interface AuditResult {
  category: string;
  test: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
}

class SecurityAuditor {
  private results: AuditResult[] = [];

  private addResult(category: string, test: string, status: 'PASS' | 'FAIL' | 'WARNING', message: string, details?: any) {
    this.results.push({ category, test, status, message, details });
  }

  /**
   * Test crypto address generation security
   */
  async testCryptoAddressGeneration(): Promise<void> {
    console.log('\n🔐 Testing Crypto Address Generation...');
    
    try {
      const cryptoService = CryptoAddressService.getInstance();
      
      // Test Bitcoin address generation
      try {
        const btcAddress = await cryptoService.generateBitcoinAddress('test-user-id');
        if (btcAddress.address && btcAddress.address.length > 20) {
          this.addResult('Crypto', 'Bitcoin Address Generation', 'PASS', 'Bitcoin address generated successfully');
        } else {
          this.addResult('Crypto', 'Bitcoin Address Generation', 'FAIL', 'Invalid Bitcoin address format');
        }
      } catch (error) {
        this.addResult('Crypto', 'Bitcoin Address Generation', 'FAIL', `Bitcoin generation failed: ${error.message}`);
      }

      // Test Ethereum address generation
      try {
        const ethAddress = await cryptoService.generateEthereumAddress('test-user-id');
        if (ethAddress.address && ethAddress.address.startsWith('0x') && ethAddress.address.length === 42) {
          this.addResult('Crypto', 'Ethereum Address Generation', 'PASS', 'Ethereum address generated successfully');
        } else {
          this.addResult('Crypto', 'Ethereum Address Generation', 'FAIL', 'Invalid Ethereum address format');
        }
      } catch (error) {
        this.addResult('Crypto', 'Ethereum Address Generation', 'FAIL', `Ethereum generation failed: ${error.message}`);
      }

      // Test address validation
      const validBtcAddress = '**********************************';
      const validEthAddress = '******************************************';
      const invalidAddress = 'invalid-address';

      const btcValidation = cryptoService.validateAddress(validBtcAddress, 'BTC');
      const ethValidation = cryptoService.validateAddress(validEthAddress, 'ETH');
      const invalidValidation = cryptoService.validateAddress(invalidAddress, 'BTC');

      if (btcValidation.isValid && ethValidation.isValid && !invalidValidation.isValid) {
        this.addResult('Crypto', 'Address Validation', 'PASS', 'Address validation working correctly');
      } else {
        this.addResult('Crypto', 'Address Validation', 'FAIL', 'Address validation not working properly');
      }

    } catch (error) {
      this.addResult('Crypto', 'Service Initialization', 'FAIL', `Crypto service failed: ${error.message}`);
    }
  }

  /**
   * Test input validation schemas
   */
  testInputValidation(): void {
    console.log('\n🛡️ Testing Input Validation...');

    // Test login validation
    try {
      const validLogin = { email: '<EMAIL>', password: 'SecurePass123!' };
      const invalidLogin = { email: 'invalid-email', password: '123' };

      const validResult = loginSchema.safeParse(validLogin);
      const invalidResult = loginSchema.safeParse(invalidLogin);

      if (validResult.success && !invalidResult.success) {
        this.addResult('Validation', 'Login Schema', 'PASS', 'Login validation working correctly');
      } else {
        this.addResult('Validation', 'Login Schema', 'FAIL', 'Login validation not working properly');
      }
    } catch (error) {
      this.addResult('Validation', 'Login Schema', 'FAIL', `Login validation error: ${error.message}`);
    }

    // Test registration validation
    try {
      const validRegister = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'SecurePass123!',
        confirmPassword: 'SecurePass123!',
        termsAccepted: true,
        privacyAccepted: true
      };

      const invalidRegister = {
        firstName: '',
        lastName: '',
        email: 'invalid',
        password: '123',
        confirmPassword: '456',
        termsAccepted: false,
        privacyAccepted: false
      };

      const validResult = registerSchema.safeParse(validRegister);
      const invalidResult = registerSchema.safeParse(invalidRegister);

      if (validResult.success && !invalidResult.success) {
        this.addResult('Validation', 'Registration Schema', 'PASS', 'Registration validation working correctly');
      } else {
        this.addResult('Validation', 'Registration Schema', 'FAIL', 'Registration validation not working properly');
      }
    } catch (error) {
      this.addResult('Validation', 'Registration Schema', 'FAIL', `Registration validation error: ${error.message}`);
    }

    // Test investment validation
    try {
      const validInvestment = {
        packageId: '550e8400-e29b-41d4-a716-************',
        amount: 100.50,
        currency: 'USDT' as const,
        termsAccepted: true,
        riskAccepted: true
      };

      const invalidInvestment = {
        packageId: 'invalid-uuid',
        amount: 10, // Below minimum
        currency: 'INVALID' as any,
        termsAccepted: false,
        riskAccepted: false
      };

      const validResult = investmentSchema.safeParse(validInvestment);
      const invalidResult = investmentSchema.safeParse(invalidInvestment);

      if (validResult.success && !invalidResult.success) {
        this.addResult('Validation', 'Investment Schema', 'PASS', 'Investment validation working correctly');
      } else {
        this.addResult('Validation', 'Investment Schema', 'FAIL', 'Investment validation not working properly');
      }
    } catch (error) {
      this.addResult('Validation', 'Investment Schema', 'FAIL', `Investment validation error: ${error.message}`);
    }
  }

  /**
   * Test environment security
   */
  testEnvironmentSecurity(): void {
    console.log('\n🔧 Testing Environment Security...');

    // Check for required environment variables
    const requiredEnvVars = [
      'JWT_SECRET',
      'MASTER_SEED_PHRASE',
      'API_KEY',
      'MONGO_URI'
    ];

    let missingVars: string[] = [];
    requiredEnvVars.forEach(varName => {
      if (!process.env[varName]) {
        missingVars.push(varName);
      }
    });

    if (missingVars.length === 0) {
      this.addResult('Environment', 'Required Variables', 'PASS', 'All required environment variables are set');
    } else {
      this.addResult('Environment', 'Required Variables', 'FAIL', `Missing variables: ${missingVars.join(', ')}`);
    }

    // Check JWT secret strength
    const jwtSecret = process.env.JWT_SECRET;
    if (jwtSecret && jwtSecret.length >= 32) {
      this.addResult('Environment', 'JWT Secret Strength', 'PASS', 'JWT secret is sufficiently strong');
    } else {
      this.addResult('Environment', 'JWT Secret Strength', 'WARNING', 'JWT secret should be at least 32 characters');
    }

    // Check master seed phrase
    const masterSeed = process.env.MASTER_SEED_PHRASE;
    if (masterSeed && masterSeed.split(' ').length === 12) {
      this.addResult('Environment', 'Master Seed Format', 'PASS', 'Master seed phrase format is correct');
    } else {
      this.addResult('Environment', 'Master Seed Format', 'FAIL', 'Master seed phrase should be 12 words');
    }

    // Check NODE_ENV
    const nodeEnv = process.env.NODE_ENV;
    if (nodeEnv === 'production' || nodeEnv === 'development' || nodeEnv === 'test') {
      this.addResult('Environment', 'NODE_ENV Setting', 'PASS', `NODE_ENV is properly set to ${nodeEnv}`);
    } else {
      this.addResult('Environment', 'NODE_ENV Setting', 'WARNING', 'NODE_ENV should be set to production, development, or test');
    }
  }

  /**
   * Test security headers and middleware
   */
  testSecurityMiddleware(): void {
    console.log('\n🛡️ Testing Security Middleware...');

    // Test rate limiting configuration
    const rateLimitWindow = process.env.RATE_LIMIT_WINDOW_MS;
    const rateLimitMax = process.env.RATE_LIMIT_MAX_REQUESTS;

    if (rateLimitWindow && rateLimitMax) {
      this.addResult('Middleware', 'Rate Limiting Config', 'PASS', 'Rate limiting is configured');
    } else {
      this.addResult('Middleware', 'Rate Limiting Config', 'WARNING', 'Rate limiting configuration missing');
    }

    // Test CORS configuration
    const allowedOrigins = process.env.ALLOWED_ORIGINS;
    if (allowedOrigins) {
      this.addResult('Middleware', 'CORS Configuration', 'PASS', 'CORS origins are configured');
    } else {
      this.addResult('Middleware', 'CORS Configuration', 'WARNING', 'CORS origins should be explicitly configured');
    }

    // Test file upload limits
    const maxFileSize = process.env.MAX_FILE_SIZE;
    const allowedFileTypes = process.env.ALLOWED_FILE_TYPES;

    if (maxFileSize && allowedFileTypes) {
      this.addResult('Middleware', 'File Upload Security', 'PASS', 'File upload limits are configured');
    } else {
      this.addResult('Middleware', 'File Upload Security', 'WARNING', 'File upload security should be configured');
    }
  }

  /**
   * Generate comprehensive audit report
   */
  generateReport(): void {
    console.log('\n📊 SECURITY AUDIT REPORT');
    console.log('=' .repeat(50));

    const categories = [...new Set(this.results.map(r => r.category))];
    
    categories.forEach(category => {
      console.log(`\n📁 ${category.toUpperCase()}`);
      console.log('-'.repeat(30));
      
      const categoryResults = this.results.filter(r => r.category === category);
      categoryResults.forEach(result => {
        const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'FAIL' ? '❌' : '⚠️';
        console.log(`${statusIcon} ${result.test}: ${result.message}`);
        if (result.details) {
          console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
        }
      });
    });

    // Summary
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARNING').length;
    const total = this.results.length;

    console.log('\n📈 SUMMARY');
    console.log('=' .repeat(20));
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`❌ Failed: ${failed}/${total}`);
    console.log(`⚠️  Warnings: ${warnings}/${total}`);
    
    const score = Math.round((passed / total) * 100);
    console.log(`\n🎯 Security Score: ${score}%`);

    if (score >= 90) {
      console.log('🟢 Excellent security posture!');
    } else if (score >= 75) {
      console.log('🟡 Good security, but room for improvement');
    } else {
      console.log('🔴 Security needs immediate attention!');
    }
  }

  /**
   * Run complete security audit
   */
  async runAudit(): Promise<void> {
    console.log('🔍 Starting Comprehensive Security Audit...');
    
    try {
      await this.testCryptoAddressGeneration();
      this.testInputValidation();
      this.testEnvironmentSecurity();
      this.testSecurityMiddleware();
      
      this.generateReport();
    } catch (error) {
      console.error('❌ Audit failed:', error);
      this.addResult('System', 'Audit Execution', 'FAIL', `Audit execution failed: ${error.message}`);
    }
  }
}

// Run audit if called directly
if (require.main === module) {
  const auditor = new SecurityAuditor();
  auditor.runAudit().catch(console.error);
}

export { SecurityAuditor };
