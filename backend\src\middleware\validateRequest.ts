import { Request, Response, NextFunction } from 'express';
import { validationResult, Validation<PERSON>hain } from 'express-validator';

export const validate = (validations: ValidationChain[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Run all validations
    await Promise.all(validations.map(validation => validation.run(req)));

    // Check if there are validation errors
    const errors = validationResult(req);
    if (errors.isEmpty()) {
      return next();
    }

    // Format validation errors
    const formattedErrors = errors.array().reduce((acc: any, error: any) => {
      acc[error.path] = error.msg;
      return acc;
    }, {});

    // Return validation errors
    return res.status(400).json({
      status: 'fail',
      message: 'Validation error',
      errors: formattedErrors
    });
  };
};

// Helper function to validate request body
export const validateBody = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body);

    if (error) {
      const formattedErrors = error.details.reduce((acc: any, detail: any) => {
        acc[detail.path[0]] = detail.message.replace(/"/g, '');
        return acc;
      }, {});

      return res.status(400).json({
        status: 'fail',
        message: 'Validation error',
        errors: formattedErrors
      });
    }

    next();
  };
};
