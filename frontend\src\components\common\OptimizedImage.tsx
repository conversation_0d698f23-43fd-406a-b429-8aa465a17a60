import { useState, useEffect } from 'react';
import { Box, Image, Skeleton, useBreakpointValue } from '@chakra-ui/react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: string | number;
  height?: string | number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  borderRadius?: string | number;
  fallbackSrc?: string;
  priority?: boolean;
  sizes?: string;
  loading?: 'lazy' | 'eager';
  [x: string]: any; // For any other props
}

/**
 * OptimizedImage component that:
 * 1. Uses modern image formats (WebP/AVIF) with fallback
 * 2. Lazy loads images by default
 * 3. Provides responsive images based on screen size
 * 4. Shows a skeleton loader while loading
 * 5. Handles errors gracefully
 */
const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  objectFit = 'cover',
  borderRadius = 'none',
  fallbackSrc = '/images/placeholder.jpg',
  priority = false,
  sizes = '100vw',
  loading = 'lazy',
  ...rest
}: OptimizedImageProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>(src);
  
  // Determine image size based on breakpoint
  const breakpointSize = useBreakpointValue({ base: 'small', md: 'medium', lg: 'large' }) || 'medium';
  
  useEffect(() => {
    // Reset states when src changes
    setIsLoaded(false);
    setError(false);
    setImageSrc(src);
  }, [src]);
  
  // Generate optimized image path
  const getOptimizedSrc = (originalSrc: string, format: string): string => {
    // Skip for external URLs or SVGs
    if (originalSrc.startsWith('http') || originalSrc.endsWith('.svg')) {
      return originalSrc;
    }
    
    // Extract path without extension
    const basePath = originalSrc.substring(0, originalSrc.lastIndexOf('.')) || originalSrc;
    
    // Use size-specific image if available
    return `/images/optimized${basePath.replace('/images', '')}-${breakpointSize}.${format}`;
  };
  
  // Try to load WebP or AVIF first, then fallback to original format
  const srcSet = `
    ${getOptimizedSrc(src, 'avif')} type="image/avif",
    ${getOptimizedSrc(src, 'webp')} type="image/webp",
    ${src}
  `;
  
  const handleLoad = () => {
    setIsLoaded(true);
  };
  
  const handleError = () => {
    setError(true);
    setImageSrc(fallbackSrc);
  };
  
  return (
    <Box position="relative" width={width} height={height} overflow="hidden" borderRadius={borderRadius}>
      <Skeleton isLoaded={isLoaded} startColor="gray.100" endColor="gray.300" borderRadius={borderRadius} height="100%" width="100%">
        <picture>
          <source srcSet={getOptimizedSrc(src, 'avif')} type="image/avif" />
          <source srcSet={getOptimizedSrc(src, 'webp')} type="image/webp" />
          <Image
            src={error ? fallbackSrc : imageSrc}
            alt={alt}
            width="100%"
            height="100%"
            objectFit={objectFit}
            borderRadius={borderRadius}
            onLoad={handleLoad}
            onError={handleError}
            loading={priority ? 'eager' : loading}
            sizes={sizes}
            {...rest}
          />
        </picture>
      </Skeleton>
    </Box>
  );
};

export default OptimizedImage;
