import express from 'express';
import { protect, admin } from '../middleware/authMiddleware';
import referralCommissionController from '../controllers/referralCommissionController';

const router = express.Router();

// User routes
router.get('/', protect, referralCommissionController.getUserCommissions);
router.get('/total', protect, referralCommissionController.getUserTotalCommission);

// Admin routes
router.get('/admin', protect, admin, referralCommissionController.getAllCommissions);
router.get('/admin/config', protect, admin, referralCommissionController.getCommissionConfigs);
router.post('/admin/config', protect, admin, referralCommissionController.createOrUpdateCommissionConfig);
router.delete('/admin/config/:id', protect, admin, referralCommissionController.deleteCommissionConfig);

export default router;
