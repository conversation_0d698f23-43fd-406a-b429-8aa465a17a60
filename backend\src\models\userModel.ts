import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcrypt';
import crypto from 'crypto';

export interface IUser extends Document {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  username?: string;
  birthDate?: Date;
  phoneNumber?: string;
  country?: string;
  city?: string;
  walletAddress?: string;
  kycVerified: boolean;
  twoFactorEnabled: boolean;
  referralCode: string;
  referredBy?: string;
  referrerId?: mongoose.Types.ObjectId; // ID của người giới thiệu
  referralCount: number;
  referralEarnings: number;
  totalCommission?: number; // Tổng hoa hồng đã nhận
  level?: number; // Level của người dùng (ảnh hưởng đến tỷ lệ hoa hồng)
  hasFirstDepositApproved?: boolean; // Track if user's first deposit has been approved
  firstDepositApprovedAt?: Date; // When the first deposit was approved
  lastLogin?: Date;
  isAdmin: boolean;
  marketingConsent?: boolean;
  balances?: Record<string, number>;

  // Email Verification Fields
  emailVerified: boolean;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  emailVerificationSentAt?: Date;
  emailVerificationAttempts: number;
  emailVerificationLastAttempt?: Date;

  comparePassword(password: string): Promise<boolean>;
  generateEmailVerificationToken(): string;
  isEmailVerificationTokenValid(token: string): boolean;
}

const userSchema = new Schema<IUser>(
  {
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      trim: true,
      lowercase: true,
      validate: {
        validator: function(email: string) {
          return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        },
        message: 'Please enter a valid email'
      }
    },
    password: {
      type: String,
      required: [true, 'Password is required'],
      minlength: [8, 'Password must be at least 8 characters'],
      validate: {
        validator: function(password: string) {
          // Simpler regex that should work with Test123!@#
          return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/.test(password);
        },
        message: 'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character'
      }
    },
    firstName: {
      type: String,
      required: [true, 'First name is required'],
      trim: true,
      maxlength: [50, 'First name cannot exceed 50 characters']
    },
    lastName: {
      type: String,
      required: [true, 'Last name is required'],
      trim: true,
      maxlength: [50, 'Last name cannot exceed 50 characters']
    },
    username: {
      type: String,
      trim: true,
      sparse: true,
      maxlength: [20, 'Username cannot exceed 20 characters'],
      validate: {
        validator: function(username: string) {
          return !username || /^[a-zA-Z0-9_]{3,20}$/.test(username);
        },
        message: 'Username must be 3-20 characters and contain only letters, numbers, and underscores'
      }
    },
    birthDate: {
      type: Date,
      validate: {
        validator: function(birthDate: Date) {
          if (!birthDate) return true; // Optional field
          const today = new Date();
          const age = today.getFullYear() - birthDate.getFullYear();
          return age >= 18;
        },
        message: 'You must be at least 18 years old'
      }
    },
    walletAddress: {
      type: String,
      trim: true,
      sparse: true,
      index: true, // Thêm index ở đây thay vì sử dụng userSchema.index
      validate: {
        validator: function(address: string) {
          return !address || /^0x[a-fA-F0-9]{40}$/.test(address);
        },
        message: 'Invalid Ethereum wallet address'
      }
    },
    kycVerified: {
      type: Boolean,
      default: false
    },
    twoFactorEnabled: {
      type: Boolean,
      default: false
    },
    referralCode: {
      type: String,
      unique: true,
      sparse: true, // Thêm sparse để cho phép null
      default: function() {
        // Generate a unique referral code
        return crypto.randomBytes(4).toString('hex').toUpperCase();
      }
    },
    referredBy: {
      type: String,
      default: null
    },
    referrerId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    referralCount: {
      type: Number,
      default: 0
    },
    referralEarnings: {
      type: Number,
      default: 0
    },
    totalCommission: {
      type: Number,
      default: 0
    },
    level: {
      type: Number,
      default: 1,
      min: 1
    },
    hasFirstDepositApproved: {
      type: Boolean,
      default: false
    },
    firstDepositApprovedAt: {
      type: Date,
      default: null
    },
    lastLogin: {
      type: Date,
      default: null
    },
    phoneNumber: {
      type: String,
      trim: true,
      sparse: true
    },
    country: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    isAdmin: {
      type: Boolean,
      default: false
    },
    marketingConsent: {
      type: Boolean,
      default: false
    },
    balances: {
      type: Map,
      of: Number,
      default: {}
    },

    // Email Verification Fields
    emailVerified: {
      type: Boolean,
      default: false,
      index: true
    },
    emailVerificationToken: {
      type: String,
      sparse: true,
      index: true
    },
    emailVerificationExpires: {
      type: Date,
      sparse: true,
      index: true
    },
    emailVerificationSentAt: {
      type: Date,
      sparse: true
    },
    emailVerificationAttempts: {
      type: Number,
      default: 0,
      min: 0,
      max: 5 // Limit verification attempts
    },
    emailVerificationLastAttempt: {
      type: Date,
      sparse: true
    }
  },
  {
    timestamps: true
  }
);

// Hash password before saving with stronger salt rounds
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error: any) {
    next(error);
  }
});

// Compare password method - production-grade security
userSchema.methods.comparePassword = async function(password: string): Promise<boolean> {
  try {
    // Always use bcrypt for secure password comparison
    return await bcrypt.compare(password, this.password);
  } catch (error) {
    console.error('Password comparison error:', error);
    throw new Error('Password comparison failed');
  }
};

// Generate email verification token
userSchema.methods.generateEmailVerificationToken = function(): string {
  // Generate a secure random token
  const token = crypto.randomBytes(32).toString('hex');

  // Set token and expiration (24 hours)
  this.emailVerificationToken = token;
  this.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
  this.emailVerificationSentAt = new Date();

  return token;
};

// Check if email verification token is valid
userSchema.methods.isEmailVerificationTokenValid = function(token: string): boolean {
  if (!this.emailVerificationToken || !this.emailVerificationExpires) {
    return false;
  }

  // Check if token matches and hasn't expired
  return this.emailVerificationToken === token &&
         this.emailVerificationExpires > new Date();
};

// Create indexes
// Note: We don't need to create an index for email, referralCode, and walletAddress
// because they are already defined with index in the schema
userSchema.index({ referredBy: 1 });

const User = mongoose.models.User || mongoose.model<IUser>('User', userSchema);

export default User;
