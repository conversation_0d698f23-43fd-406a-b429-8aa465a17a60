/**
 * CORS Testing Utility
 * This file helps test CORS configuration with different browsers
 */

import { Request, Response } from 'express';
import { logger } from './logger';

export interface CorsTestResult {
  browser: string;
  origin: string;
  method: string;
  headers: Record<string, string>;
  status: 'success' | 'blocked' | 'error';
  message: string;
}

/**
 * Test CORS configuration for different browsers
 */
export const testCorsConfiguration = (req: Request, res: Response): CorsTestResult => {
  const origin = req.get('Origin') || 'unknown';
  const userAgent = req.get('User-Agent') || '';
  const method = req.method;
  
  // Detect browser
  let browser = 'Unknown';
  if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
    browser = 'Safari';
  } else if (userAgent.includes('Chrome')) {
    browser = 'Chrome';
  } else if (userAgent.includes('Firefox')) {
    browser = 'Firefox';
  } else if (userAgent.includes('Edge')) {
    browser = 'Edge';
  }
  
  // Get response headers
  const responseHeaders: Record<string, string> = {};
  const headerNames = [
    'Access-Control-Allow-Origin',
    'Access-Control-Allow-Methods',
    'Access-Control-Allow-Headers',
    'Access-Control-Allow-Credentials',
    'Access-Control-Max-Age',
    'Access-Control-Expose-Headers',
    'Vary',
    'Cross-Origin-Resource-Policy'
  ];
  
  headerNames.forEach(headerName => {
    const value = res.getHeader(headerName);
    if (value) {
      responseHeaders[headerName] = String(value);
    }
  });
  
  // Determine status
  let status: 'success' | 'blocked' | 'error' = 'success';
  let message = 'CORS headers set successfully';
  
  if (!responseHeaders['Access-Control-Allow-Origin']) {
    status = 'blocked';
    message = 'Access-Control-Allow-Origin header not set';
  } else if (responseHeaders['Access-Control-Allow-Origin'] === 'null') {
    status = 'blocked';
    message = 'Origin blocked by CORS policy';
  }
  
  const result: CorsTestResult = {
    browser,
    origin,
    method,
    headers: responseHeaders,
    status,
    message
  };
  
  logger.info('CORS Test Result:', result);
  
  return result;
};

/**
 * Generate CORS test report
 */
export const generateCorsTestReport = (results: CorsTestResult[]): string => {
  let report = '# CORS Configuration Test Report\n\n';
  
  report += `Generated at: ${new Date().toISOString()}\n\n`;
  
  // Group by browser
  const browserGroups = results.reduce((groups, result) => {
    if (!groups[result.browser]) {
      groups[result.browser] = [];
    }
    groups[result.browser].push(result);
    return groups;
  }, {} as Record<string, CorsTestResult[]>);
  
  Object.entries(browserGroups).forEach(([browser, browserResults]) => {
    report += `## ${browser}\n\n`;
    
    browserResults.forEach((result, index) => {
      report += `### Test ${index + 1}\n`;
      report += `- **Origin**: ${result.origin}\n`;
      report += `- **Method**: ${result.method}\n`;
      report += `- **Status**: ${result.status}\n`;
      report += `- **Message**: ${result.message}\n\n`;
      
      report += '**Response Headers:**\n';
      Object.entries(result.headers).forEach(([header, value]) => {
        report += `- ${header}: ${value}\n`;
      });
      report += '\n';
    });
  });
  
  // Summary
  const successCount = results.filter(r => r.status === 'success').length;
  const blockedCount = results.filter(r => r.status === 'blocked').length;
  const errorCount = results.filter(r => r.status === 'error').length;
  
  report += '## Summary\n\n';
  report += `- **Total Tests**: ${results.length}\n`;
  report += `- **Successful**: ${successCount}\n`;
  report += `- **Blocked**: ${blockedCount}\n`;
  report += `- **Errors**: ${errorCount}\n\n`;
  
  if (blockedCount > 0 || errorCount > 0) {
    report += '## Recommendations\n\n';
    
    if (blockedCount > 0) {
      report += '- Check allowed origins configuration\n';
      report += '- Verify development vs production settings\n';
    }
    
    if (errorCount > 0) {
      report += '- Review CORS middleware implementation\n';
      report += '- Check for conflicting CORS configurations\n';
    }
  }
  
  return report;
};

/**
 * Common CORS issues and solutions
 */
export const corsDebuggingGuide = {
  safari: {
    issues: [
      'Stricter preflight request handling',
      'Requires explicit Vary headers',
      'Sensitive to Cross-Origin-Resource-Policy'
    ],
    solutions: [
      'Set Vary: Origin, Access-Control-Request-Method, Access-Control-Request-Headers',
      'Use Cross-Origin-Resource-Policy: cross-origin',
      'Return 204 for OPTIONS requests'
    ]
  },
  chrome: {
    issues: [
      'Strict credential handling',
      'Requires exact origin match',
      'Sensitive to mixed content'
    ],
    solutions: [
      'Ensure Access-Control-Allow-Credentials: true',
      'Use exact origin in Access-Control-Allow-Origin',
      'Avoid wildcard with credentials'
    ]
  },
  firefox: {
    issues: [
      'Different preflight caching behavior',
      'Strict header validation'
    ],
    solutions: [
      'Set appropriate Access-Control-Max-Age',
      'Include all required headers in Access-Control-Allow-Headers'
    ]
  }
};
