import React, { useState, useEffect, useCallback, memo, useMemo } from 'react';
import {
  Box,
  SimpleGrid,
  VStack,
  HStack,
  Text,
  Button,
  Icon,
  Badge,
  Flex,
  useToast,
  Spinner,
  Center,
  useDisclosure,
  Tooltip
} from '@chakra-ui/react';
import {
  FaBitcoin,
  FaEthereum,
  FaArrowUp,
  FaArrowDown,
  FaShip
} from 'react-icons/fa';
import { Si<PERSON>ogecoin, SiTether } from 'react-icons/si';
import { useTranslation } from 'react-i18next';
import useAuth from '../../hooks/useAuth';
import useMobileResponsive from '../../hooks/useMobileResponsive';
import { SocketService } from '../../utils/socketService';
import { useCryptoPrices } from '../../hooks/useCryptoPrices';

// Import modals
import DepositModal from '../modals/DepositModal';

// Import investment balance service for Total Earned integration
import { investmentBalanceService } from '../../services/investmentBalanceService';

// Interface for cryptocurrency price data
interface CryptoPriceData {
  symbol: string;
  price: number;
  change24h: number;
  changePercent24h: number;
  lastUpdated: string;
}



interface CryptocurrencyAddress {
  _id: string;
  cryptocurrency: string;
  address: string;
  network: string;
  isActive: boolean;
  isMainAddress: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CryptocurrencyInfo {
  symbol: string;
  name: string;
  icon: React.ComponentType;
  color: string;
  bgColor: string;
}

// Interface for investment data integration
interface CryptoInvestmentData {
  currency: string;
  totalEarned: number;
  activePackages: number;
  availableForWithdrawal: number;
  principalAmount: number;
}

const cryptocurrencyInfo: Record<string, CryptocurrencyInfo> = {
  BTC: {
    symbol: 'BTC',
    name: 'Bitcoin',
    icon: FaBitcoin,
    color: '#F7931A',
    bgColor: '#F7931A22'
  },
  ETH: {
    symbol: 'ETH',
    name: 'Ethereum',
    icon: FaEthereum,
    color: '#627EEA',
    bgColor: '#627EEA22'
  },
  USDT: {
    symbol: 'USDT',
    name: 'Tether',
    icon: SiTether,
    color: '#26A17B',
    bgColor: '#26A17B22'
  },
  TRX: {
    symbol: 'TRX',
    name: 'TRON',
    icon: () => <Text fontWeight="bold" fontSize="lg">T</Text>,
    color: '#FF060A',
    bgColor: '#FF060A22'
  },
  DOGE: {
    symbol: 'DOGE',
    name: 'Dogecoin',
    icon: SiDogecoin,
    color: '#C2A633',
    bgColor: '#C2A63322'
  },

};

// Removed PriceDisplay component as it's no longer needed

// Enhanced display component that shows investment data with shipping theme
const EnhancedCryptoDisplay = memo(({
  crypto,
  investmentData,
  loadingInvestmentData,
  user
}: {
  crypto: string;
  investmentData: CryptoInvestmentData[];
  loadingInvestmentData: boolean;
  user: any;
}) => {
  // Simple lookup without useMemo to avoid hook order issues
  const cryptoInvestment = investmentData.find(inv => inv.currency === crypto);
  const hasInvestmentData = user && cryptoInvestment && cryptoInvestment.totalEarned > 0;

  return (
    <Box w="100%" bg="#0B0E11" p={2} borderRadius="sm" borderWidth="1px" borderColor="#2B3139">
      <VStack spacing={2}>
        {/* Shipping Vessel Icon */}
        <VStack spacing={1} w="100%">
          <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            w="100%"
            minH={{ base: "40px", md: "48px" }}
            transition="all 0.3s ease"
          >
            <Icon
              as={FaShip}
              boxSize={{ base: 6, md: 8 }}
              color="#F0B90B"
              opacity={0.8}
              _hover={{ opacity: 1, transform: "scale(1.1)" }}
              transition="all 0.3s ease"
            />
          </Box>
        </VStack>

        {/* Investment Data - Total Earned */}
        {hasInvestmentData && (
          <VStack spacing={1} w="100%">
            <Text color="#848E9C" fontSize="2xs" textAlign="center">
              Total Earned
            </Text>
            <Text
              color="#0ECB81"
              fontSize={{ base: "md", md: "lg" }}
              fontWeight="bold"
              textAlign="center"
              w="100%"
              minH={{ base: "24px", md: "28px" }}
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              {loadingInvestmentData ? (
                <Spinner size="sm" color="#0ECB81" />
              ) : (
                `${cryptoInvestment?.totalEarned?.toFixed(2) || '0.00'} ${crypto}`
              )}
            </Text>
            {cryptoInvestment && cryptoInvestment.activePackages > 0 && (
              <Text color="#848E9C" fontSize="3xs" textAlign="center">
                {cryptoInvestment.activePackages} Investment{cryptoInvestment.activePackages > 1 ? 's' : ''}
              </Text>
            )}
          </VStack>
        )}

        {/* Daily Earnings Info */}
        {hasInvestmentData && (
          <HStack justify="space-between" w="100%">
            <Text color="#848E9C" fontSize="2xs">
              Daily Earnings
            </Text>
            <HStack spacing={1}>
              <Icon
                as={FaArrowUp}
                boxSize={2.5}
                color="#0ECB81"
              />
              <Text
                color="#0ECB81"
                fontSize="2xs"
                fontWeight="bold"
              >
                1% daily
              </Text>
            </HStack>
          </HStack>
        )}
      </VStack>
    </Box>
  );
});

EnhancedCryptoDisplay.displayName = 'EnhancedCryptoDisplay';

// Enhanced Price Display Component with modern Binance-inspired design
const EnhancedPriceDisplay = memo(({
  crypto,
  priceData,
  priceLoading
}: {
  crypto: string;
  priceData: Record<string, CryptoPriceData>;
  priceLoading: boolean;
}) => {
  const price = priceData[crypto];
  const isPositive = price?.changePercent24h >= 0;

  return (
    <Tooltip
      label={`Last updated: ${price?.lastUpdated ? new Date(price.lastUpdated).toLocaleTimeString() : 'N/A'}`}
      bg="rgba(11, 14, 17, 0.95)"
      color="#EAECEF"
      borderRadius="lg"
      fontSize="sm"
      hasArrow
      placement="top"
    >
      <Box
        w="100%"
        bg="rgba(11, 14, 17, 0.8)"
        backdropFilter="blur(10px)"
        p={2}
        borderRadius="lg"
        borderWidth="1px"
        borderColor="rgba(43, 49, 57, 0.6)"
        position="relative"
        overflow="hidden"
        _hover={{
          borderColor: isPositive ? "rgba(2, 192, 118, 0.4)" : "rgba(248, 73, 96, 0.4)",
          boxShadow: `0 4px 20px ${isPositive ? "rgba(2, 192, 118, 0.1)" : "rgba(248, 73, 96, 0.1)"}`,
          transform: "translateY(-1px)"
        }}
        transition="all 0.3s ease"
        cursor="pointer"
      >
      {/* Glassmorphism background effect */}
      <Box
        position="absolute"
        top={0}
        left={0}
        right={0}
        bottom={0}
        bg={isPositive
          ? "linear-gradient(135deg, rgba(2, 192, 118, 0.1) 0%, rgba(2, 192, 118, 0.05) 100%)"
          : "linear-gradient(135deg, rgba(248, 73, 96, 0.1) 0%, rgba(248, 73, 96, 0.05) 100%)"
        }
        opacity={0.6}
      />

      <VStack spacing={1} position="relative" zIndex={1}>
        {/* Price Section */}
        <HStack justify="space-between" w="100%" align="center">
          <VStack align="start" spacing={0} flex={1}>
            <Text color="#848E9C" fontSize="2xs" fontWeight="500">
              Current Price
            </Text>
            {priceLoading ? (
              <Spinner size="sm" color="#F0B90B" />
            ) : (
              <Text
                color="#EAECEF"
                fontSize={{ base: "md", md: "lg" }}
                fontWeight="800"
                lineHeight="1.2"
              >
                ${price?.price?.toLocaleString('en-US', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: price?.price > 1 ? 2 : 6
                }) || '0.00'}
              </Text>
            )}
          </VStack>

          {/* 24h Change */}
          <VStack align="end" spacing={0}>
            <Text color="#848E9C" fontSize="2xs" fontWeight="500">
              24h Change
            </Text>
            <HStack spacing={1}>
              <Icon
                as={isPositive ? FaArrowUp : FaArrowDown}
                boxSize={3}
                color={isPositive ? "#02C076" : "#F84960"}
              />
              <Text
                color={isPositive ? "#02C076" : "#F84960"}
                fontSize="sm"
                fontWeight="700"
              >
                {Math.abs(price?.changePercent24h || 0).toFixed(2)}%
              </Text>
            </HStack>
          </VStack>
        </HStack>

        {/* Market Trend Indicator with Animation */}
        <Box w="100%" h="2px" bg="rgba(43, 49, 57, 0.5)" borderRadius="full" overflow="hidden">
          <Box
            h="100%"
            bg={isPositive ? "#02C076" : "#F84960"}
            borderRadius="full"
            w={`${Math.min(Math.abs(price?.changePercent24h || 0) * 10, 100)}%`}
            transition="all 0.8s cubic-bezier(0.4, 0, 0.2, 1)"
            position="relative"
            _after={{
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bg: `linear-gradient(90deg, transparent 0%, ${isPositive ? "#02C076" : "#F84960"}40 50%, transparent 100%)`,
              animation: 'shimmer 2s infinite',
            }}
            sx={{
              '@keyframes shimmer': {
                '0%': { transform: 'translateX(-100%)' },
                '100%': { transform: 'translateX(100%)' }
              }
            }}
          />
        </Box>
      </VStack>
    </Box>
    </Tooltip>
  );
});

EnhancedPriceDisplay.displayName = 'EnhancedPriceDisplay';

const CryptocurrencyCards: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();
  const { shouldUseHoverEffects } = useMobileResponsive();

  // Use crypto prices hook for cached price data
  const { prices, getPrice, isLoading: pricesLoading, error: pricesError } = useCryptoPrices();

  // State
  const [addresses, setAddresses] = useState<CryptocurrencyAddress[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCrypto, setSelectedCrypto] = useState<string>('');

  // Modal states
  const { isOpen: isDepositOpen, onOpen: onDepositOpen, onClose: onDepositClose } = useDisclosure();

  // Investment data state for Total Earned integration
  const [investmentData, setInvestmentData] = useState<CryptoInvestmentData[]>([]);
  const [loadingInvestmentData, setLoadingInvestmentData] = useState(false);

  // Convert cached prices to component format
  const priceData = useMemo(() => {
    const convertedPrices: Record<string, CryptoPriceData> = {};

    Object.keys(cryptocurrencyInfo).forEach(symbol => {
      const price = getPrice(symbol);
      if (price > 0) {
        convertedPrices[symbol] = {
          symbol,
          price,
          change24h: 0, // We don't have 24h change data from our cache
          changePercent24h: 0, // We don't have 24h change data from our cache
          lastUpdated: new Date().toISOString()
        };
      }
    });

    return convertedPrices;
  }, [prices, getPrice]);

  // Memoized values - MUST be called before any conditional returns
  const addressMap = useMemo(() => {
    return addresses.reduce((acc, address) => {
      acc[address.cryptocurrency] = address;
      return acc;
    }, {} as Record<string, CryptocurrencyAddress>);
  }, [addresses]);

  const cryptocurrencyEntries = useMemo(() => {
    return Object.entries(cryptocurrencyInfo);
  }, []);



  // Note: Price fetching is now handled by useCryptoPrices hook with smart caching

  // Fetch cryptocurrency addresses
  const fetchAddresses = useCallback(async () => {
    try {
      setLoading(true);

      // Try to fetch from API using shared cache service
      try {
        const { cryptoAddressCache } = await import('../../services/cryptoAddressCache');
        const addresses = await cryptoAddressCache.getAllAddresses();
        console.log('Cached crypto addresses:', addresses);

        // Transform the cached data to match the component's expected format
        if (addresses) {
          console.log('Found crypto addresses:', addresses);

          // Process addresses for simple display (no network complexity needed for price display)
          const transformedAddresses: CryptocurrencyAddress[] = [];

          // Convert addresses object to array format for processing
          Object.entries(addresses)
            .filter(([currency, addresses]) => addresses && Array.isArray(addresses) && addresses.length > 0)
            .forEach(([currency, addresses], index: number) => {
              const traditionalEntry: CryptocurrencyAddress = {
                _id: `${currency.toLowerCase()}_${index}`,
                cryptocurrency: currency,
                address: Array.isArray(addresses) ? addresses[0] || 'placeholder' : 'placeholder', // Use first address
                network: 'mainnet',
                isActive: true, // Assume enabled if present in cache
                isMainAddress: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              };
              transformedAddresses.push(traditionalEntry);
            });

          console.log('Transformed addresses:', transformedAddresses);
          setAddresses(transformedAddresses);
          setLoading(false);
          return;
        } else {
          throw new Error('Invalid response format from API');
        }
      } catch (apiError) {
        console.error('Error fetching from API:', apiError);
      }
    } catch (error) {
      console.error('Error fetching cryptocurrency addresses:', error);

      // Provide detailed error information
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      toast({
        title: t('common.error', 'Error'),
        description: t('home.cryptoCards.fetchError', 'Failed to load cryptocurrency addresses') + `: ${errorMessage}`,
        status: 'error',
        duration: 7000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  }, []); // Remove dependencies to prevent loops

  // Fetch investment data for Total Earned integration
  const fetchInvestmentData = useCallback(async () => {
    if (!user) {
      console.log('No user logged in, skipping investment data fetch');
      return;
    }

    setLoadingInvestmentData(true);
    try {
      console.log('Fetching investment data for user...');
      const balances = await investmentBalanceService.getInvestmentBalances();

      // Transform investment balances to crypto investment data
      const cryptoInvestmentData: CryptoInvestmentData[] = balances.map(balance => ({
        currency: balance.currency,
        totalEarned: balance.totalEarnings || 0,
        activePackages: balance.activePackages || 0,
        availableForWithdrawal: balance.availableForWithdrawal || 0,
        principalAmount: 0 // Will be calculated from packages if needed
      }));

      setInvestmentData(cryptoInvestmentData);
      console.log('Investment data loaded for cryptocurrency cards:', cryptoInvestmentData);
    } catch (error) {
      console.error('Error fetching investment data for crypto cards:', error);
      setInvestmentData([]); // Reset on error
    } finally {
      setLoadingInvestmentData(false);
    }
  }, []); // Remove user dependency to prevent loops

  // Handle real-time crypto address updates
  const handleCryptoAddressUpdate = useCallback((data: any) => {
    console.log('Received crypto address update:', data);

    if (data.payload && data.payload.address) {
      const updatedAddress = data.payload.address;

      // Only process updates for enabled addresses
      if (updatedAddress.enabled === false) {
        // Remove disabled address from display
        setAddresses(prevAddresses =>
          prevAddresses.filter(addr => addr.cryptocurrency !== updatedAddress.cryptocurrency)
        );

        toast({
          title: t('home.cryptoCards.disabled', 'Address Disabled'),
          description: t('home.cryptoCards.addressDisabled', `${updatedAddress.cryptocurrency} address has been disabled`),
          status: 'warning',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      setAddresses(prevAddresses => {
        const existingIndex = prevAddresses.findIndex(
          addr => addr.cryptocurrency === updatedAddress.cryptocurrency
        );

        if (existingIndex !== -1) {
          // Update existing address
          const newAddresses = [...prevAddresses];
          newAddresses[existingIndex] = {
            ...newAddresses[existingIndex],
            ...updatedAddress,
            isActive: updatedAddress.enabled
          };
          return newAddresses;
        } else if (updatedAddress.enabled) {
          // Add new enabled address - handle both old and new address formats
          let addressString = '';
          let networkString = 'mainnet';

          if (updatedAddress.addresses && Array.isArray(updatedAddress.addresses) && updatedAddress.addresses.length > 0) {
            const addressAtIndex = updatedAddress.addresses[updatedAddress.currentIndex] || updatedAddress.addresses[0];

            if (typeof addressAtIndex === 'object' && addressAtIndex !== null) {
              // New format: { address: string, network: string }
              const addressObj = addressAtIndex as { address: string; network: string };
              addressString = addressObj.address || '';
              networkString = addressObj.network || 'mainnet';
            } else if (typeof addressAtIndex === 'string') {
              // Old format: string
              addressString = addressAtIndex;
              networkString = updatedAddress.network || 'mainnet';
            }
          } else if (updatedAddress.address) {
            // Direct address property (fallback)
            addressString = updatedAddress.address;
            networkString = updatedAddress.network || 'mainnet';
          }

          const newAddress = {
            _id: `${updatedAddress.cryptocurrency.toLowerCase()}_${Date.now()}`,
            cryptocurrency: updatedAddress.cryptocurrency,
            address: addressString,
            network: networkString,
            isActive: updatedAddress.enabled,
            isMainAddress: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          return [...prevAddresses, newAddress];
        }

        return prevAddresses;
      });

      toast({
        title: t('home.cryptoCards.updated', 'Address Updated'),
        description: t('home.cryptoCards.addressUpdated', `${updatedAddress.cryptocurrency} address has been updated`),
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
    }
  }, []); // Remove dependencies to prevent loops

  // Separate useEffect for initial data loading - runs only once
  useEffect(() => {
    console.log('Initial data loading...');
    fetchAddresses();
    fetchInvestmentData();
    // Note: Crypto prices are automatically loaded by useCryptoPrices hook
  }, []); // Empty dependency array - runs only once on mount

  // Separate useEffect for WebSocket setup - runs only once
  useEffect(() => {
    let unsubscribe: (() => void) | null = null;

    const setupWebSocket = async () => {
      try {
        // Get SocketService instance
        const socketService = SocketService.getInstance();

        // Connect to WebSocket service
        await socketService.connect();

        // Subscribe to crypto address updates
        unsubscribe = socketService.subscribe('crypto_address_updated', handleCryptoAddressUpdate);

        console.log('WebSocket connected for crypto address updates');
      } catch (error) {
        console.error('Failed to connect to WebSocket:', error);
        // Continue without WebSocket - addresses will still work without real-time updates
      }
    };

    setupWebSocket();

    // Cleanup function
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
      // Don't disconnect as other components might be using the socket
    };
  }, []); // Empty dependency array - runs only once on mount

  // Separate useEffect for user-dependent investment data
  useEffect(() => {
    if (user) {
      console.log('User logged in, fetching investment data...');
      fetchInvestmentData();
    } else {
      console.log('User logged out, clearing investment data...');
      setInvestmentData([]);
    }
  }, [user]); // Only depends on user changes

  // Handle invest now button click
  const handleInvestNowClick = (crypto: string) => {
    console.log('=== INVEST NOW CLICKED ===');
    console.log('Selected cryptocurrency:', crypto);
    console.log('User authenticated:', !!user);
    console.log('Current selectedCrypto state:', selectedCrypto);

    // Set selected crypto first and log the change
    setSelectedCrypto(crypto);
    console.log('Setting selectedCrypto to:', crypto);

    if (!user) {
      // Show login required message and redirect to login
      toast({
        title: t('home.investment.loginRequired', 'Login Required'),
        description: t('home.investment.loginRequiredDesc', 'Please log in to make an investment.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // User is logged in, open modal immediately
    console.log('User authenticated - opening DepositModal immediately with crypto:', crypto);
    onDepositOpen();
  };



  if (loading) {
    return (
      <Box py={8}>
        <Center>
          <VStack spacing={4}>
            <Spinner color="#F0B90B" size="xl" thickness="4px" />
            <Text color="#848E9C" textAlign="center">
              {t('home.cryptoCards.loading', 'Loading cryptocurrency addresses...')}
            </Text>
            <Text color="#848E9C" fontSize="sm" textAlign="center">
              {t('home.cryptoCards.loadingDesc', 'Fetching enabled cryptocurrencies from local API')}
            </Text>
          </VStack>
        </Center>
      </Box>
    );
  }

  // Show message if no enabled addresses are available
  if (addresses.length === 0) {
    return (
      <Box py={8}>
        <Center>
          <VStack spacing={4}>
            <Text color="#F0B90B" fontSize="lg" fontWeight="bold">
              {t('home.cryptoCards.noAddresses', 'No Cryptocurrency Addresses Available')}
            </Text>
            <Text color="#848E9C" textAlign="center">
              {t('home.cryptoCards.noAddressesDesc', 'No enabled cryptocurrency addresses found. Please check your system configuration.')}
            </Text>
            <Button
              colorScheme="yellow"
              variant="outline"
              onClick={fetchAddresses}
              size="sm"
            >
              {t('common.retry', 'Retry')}
            </Button>
          </VStack>
        </Center>
      </Box>
    );
  }



  return (
    <Box py={{ base: 2, md: 3 }}>
      <VStack spacing={{ base: 3, md: 4 }} align="stretch">
        <Box textAlign="center">
          <Text
            fontSize={{ base: "lg", md: "xl" }}
            fontWeight="bold"
            color="#F0B90B"
            mb={0.5}
          >
            {t('home.cryptoCards.title', 'Supported Cryptocurrencies')}
          </Text>
          <Text color="#848E9C" fontSize={{ base: "xs", md: "sm" }}>
            {t('home.cryptoCards.description', 'Invest in your favorite cryptocurrencies and earn daily returns')}
          </Text>
        </Box>

        <Box
          w="100%"
          sx={{
            display: 'grid',
            // Auto-fit grid that wraps based on available space and height
            gridTemplateColumns: {
              base: '1fr',
              sm: 'repeat(auto-fit, minmax(280px, 1fr))',
              md: 'repeat(auto-fit, minmax(300px, 1fr))',
              lg: 'repeat(auto-fit, minmax(320px, 1fr))',
              xl: 'repeat(auto-fit, minmax(280px, 1fr))'
            },
            // Auto rows with consistent height
            gridAutoRows: { base: '420px', sm: '400px', md: '380px' },
            gap: { base: 3, md: 4 },
            // Prevent layout shifts and ensure stable grid
            minHeight: '380px',
            // Mobile-specific grid optimizations
            '@media (max-width: 479px)': {
              // Single column on very small screens
              gridTemplateColumns: '1fr',
              gap: '12px',
            },
            '@media (min-width: 480px) and (max-width: 767px)': {
              // Auto-fit with smaller minimum width on tablets
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '16px',
            },
            '@media (max-width: 767px)': {
              // Prevent horizontal overflow
              width: '100%',
              maxWidth: '100%',
              overflow: 'hidden',
            },
            // Ensure all cards have consistent height - mobile optimized
            '& > *': {
              height: { base: '420px', sm: '400px', md: '380px' },
              transition: 'none', // Disable transitions during price updates
            }
          }}
        >
          {cryptocurrencyEntries.map(([crypto, info]) => {
            const address = addressMap[crypto];
            // Only display cryptocurrency cards where enabled status is true
            if (!address || !address.isActive) return null;

            return (
              <Box
                key={crypto}
                data-crypto={crypto}
                bg="#1E2026"
                p={{ base: 3, md: 4 }}
                borderRadius="2xl"
                borderWidth="1px"
                borderColor="#2B3139"
                boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
                _hover={shouldUseHoverEffects() ? {
                  borderColor: info.color,
                  transform: "translateY(-4px)",
                  boxShadow: `0 12px 24px rgba(0, 0, 0, 0.15), 0 0 0 1px ${info.color}40`
                } : {}}
                transition="all 0.3s ease"
                position="relative"
                overflow="hidden"
                h={{ base: "420px", sm: "400px", md: "380px" }}
                className="crypto-card"
                sx={{
                  contain: 'layout style',
                  display: 'flex',
                  flexDirection: 'column',
                  '@media (max-width: 767px)': {
                    touchAction: 'manipulation',
                    WebkitTapHighlightColor: 'transparent',
                    WebkitTouchCallout: 'none',
                    WebkitUserSelect: 'none',
                    userSelect: 'none',
                    transform: 'translateZ(0)',
                    backfaceVisibility: 'hidden',
                    willChange: 'transform',
                  }
                }}
              >
                {/* Enhanced top gradient border */}
                <Box
                  position="absolute"
                  top={0}
                  left={0}
                  right={0}
                  h="4px"
                  bg={`linear-gradient(90deg, ${info.color} 0%, ${info.color}80 50%, ${info.color}40 100%)`}
                  zIndex={2}
                />

                {/* Enhanced background gradient */}
                <Box
                  position="absolute"
                  top={0}
                  left={0}
                  right={0}
                  bottom={0}
                  bg={`linear-gradient(135deg, ${info.bgColor} 0%, ${info.color}10 30%, transparent 70%)`}
                  opacity={0.4}
                  zIndex={0}
                />

                <VStack
                  spacing={3}
                  position="relative"
                  zIndex={1}
                  h="100%"
                  justify="space-between"
                  align="stretch"
                  p={1}
                >
                  {/* Enhanced Cryptocurrency header */}
                  <HStack spacing={3} w="100%" align="center" flexShrink={0}>
                    <Flex
                      align="center"
                      justify="center"
                      w={10}
                      h={10}
                      bg={`${info.color}15`}
                      borderRadius="xl"
                      border="2px solid"
                      borderColor={`${info.color}30`}
                      position="relative"
                      overflow="hidden"
                    >
                      <Box
                        position="absolute"
                        top={0}
                        left={0}
                        right={0}
                        bottom={0}
                        bg={`linear-gradient(135deg, ${info.color}20 0%, transparent 100%)`}
                      />
                      <Icon as={info.icon} color={info.color} boxSize={5} zIndex={1} />
                    </Flex>
                    <VStack align="start" spacing={0} flex={1} minW={0}>
                      <Text
                        fontWeight="800"
                        color="#EAECEF"
                        fontSize={{ base: "sm", md: "md" }}
                        lineHeight="1.2"
                        letterSpacing="-0.02em"
                      >
                        {info.symbol}
                      </Text>
                      <Text
                        color="#848E9C"
                        fontSize={{ base: "2xs", md: "xs" }}
                        fontWeight="500"
                        lineHeight="1.2"
                        noOfLines={1}
                      >
                        {info.name}
                      </Text>
                    </VStack>
                    <Badge
                      bg="linear-gradient(135deg, #02C076 0%, #02C07680 100%)"
                      color="white"
                      px={2}
                      py={1}
                      borderRadius="full"
                      fontSize="2xs"
                      fontWeight="600"
                      boxShadow="0 2px 8px rgba(2, 192, 118, 0.3)"
                    >
                      {t('home.cryptoCards.active', 'Active')}
                    </Badge>
                  </HStack>

                  {/* Enhanced Price Display */}
                  <Box w="100%" flexShrink={0}>
                    <EnhancedPriceDisplay
                      crypto={crypto}
                      priceData={priceData}
                      priceLoading={pricesLoading}
                    />
                  </Box>

                  {/* Enhanced Display - Shows investment data with shipping theme */}
                  <Box flex={1} w="100%" minH="140px">
                    <EnhancedCryptoDisplay
                      crypto={crypto}
                      investmentData={investmentData}
                      loadingInvestmentData={loadingInvestmentData}
                      user={user}
                    />
                  </Box>

                  {/* Enhanced Action button with professional design */}
                  <VStack spacing={2} w="100%" flexShrink={0}>
                    <Button
                      leftIcon={<Icon as={FaShip} boxSize={{ base: 4, md: 3 }} />}
                      bg={user ? "#F0B90B" : "#848E9C"}
                      color={user ? "#0B0E11" : "#FFFFFF"}
                      _hover={user ? {
                        bg: "#FCD535",
                        boxShadow: "0 4px 16px rgba(240, 185, 11, 0.4)",
                        transform: "translateY(-1px)"
                      } : {
                        bg: "#6B7280",
                        cursor: "pointer"
                      }}
                      _active={user ? {
                        bg: "#E6A609",
                        transform: 'translateY(0px)',
                      } : {
                        bg: "#6B7280"
                      }}
                      size={{ base: "md", md: "sm" }}
                      w="100%"
                      h={{ base: "48px", md: "40px" }}
                      fontSize={{ base: "md", md: "sm" }}
                      fontWeight="700"
                      borderRadius="lg"
                      onClick={() => handleInvestNowClick(crypto)}
                      transition="all 0.2s ease"
                      boxShadow={user ? "0 2px 8px rgba(240, 185, 11, 0.3)" : "0 2px 8px rgba(132, 142, 156, 0.3)"}
                      sx={{
                        '@media (max-width: 767px)': {
                          touchAction: 'manipulation',
                          WebkitTapHighlightColor: 'transparent',
                          WebkitTouchCallout: 'none',
                          cursor: 'pointer',
                          minHeight: '48px !important',
                          minWidth: '100% !important',
                          fontSize: '16px !important', // Prevent zoom on iOS
                          padding: '12px 16px !important',
                        },
                        '@media (max-width: 480px)': {
                          minHeight: '52px !important',
                          fontSize: '17px !important',
                          fontWeight: '600 !important',
                        }
                      }}
                    >
                      {user ? t('common.investNow', 'Invest Now') : t('common.signInToInvest', t('common.loginToInvest', 'Login to Invest'))}
                    </Button>
                  </VStack>
                </VStack>
              </Box>
            );
          })}
        </Box>
      </VStack>

      {/* Deposit Modal */}
      <DepositModal
        isOpen={isDepositOpen}
        onClose={() => {
          console.log('DepositModal closing...');
          onDepositClose();
        }}
        defaultAsset={selectedCrypto}
        onSuccess={() => {
          console.log('DepositModal success callback triggered');
          // Refresh investment data after successful deposit
          fetchInvestmentData();
        }}
      />


    </Box>
  );
};

export default CryptocurrencyCards;
