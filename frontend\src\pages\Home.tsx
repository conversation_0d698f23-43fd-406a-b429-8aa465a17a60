import { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Stack,
  Grid,
  GridItem,
  Icon,
  useDisclosure,
  useToast,
  Flex,
  Divider,
  SimpleGrid
} from '@chakra-ui/react';
import {
  FaShip,
  FaArrowRight,
  FaWallet,
  FaInfoCircle,
  FaSignInAlt,
  FaUserPlus,
  FaArrowUp
} from 'react-icons/fa';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import useAuth from '../hooks/useAuth';

import useMobileResponsive from '../hooks/useMobileResponsive';

// Import custom modals
import DepositModal from '../components/modals/DepositModal';

// Import cryptocurrency cards component
import CryptocurrencyCards from '../components/home/<USER>';

const Home = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();
  const {
    buttonHeight,
    shouldUseHoverEffects,
    getAnimationDuration
  } = useMobileResponsive();

  // Modal states
  const {
    isOpen: isDepositOpen,
    onOpen: onDepositOpen,
    onClose: onDepositClose
  } = useDisclosure();

  // Withdraw modal removed from homepage



  // Selected cryptocurrency
  const [selectedCrypto, setSelectedCrypto] = useState('bitcoin');

  // Colors - Use Chakra UI theme colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";



  // When investment button is clicked
  const handleInvestClick = () => {
    // Check if user is logged in
    if (user) {
      // If user is logged in, open the modal
      onDepositOpen();
    } else {
      // If user is not logged in, show warning and redirect to login page
      toast({
        title: t('home.deposit.loginRequired', 'Login Required'),
        description: t('home.deposit.loginRequiredDesc', 'Please log in or create an account to make an investment.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      navigate('/login');
    }
  };

  // Withdraw functionality removed from homepage

  return (
    <Box className="bg-overlay" minH="100vh">
      {/* Custom Modals */}
      <DepositModal isOpen={isDepositOpen} onClose={onDepositClose} />



      {/* Enhanced Deposit Now Section */}
      <Box
        py={{ base: 6, md: 8 }}
        px={{ base: 0, md: 0 }}
        bg="linear-gradient(90deg, rgba(240, 185, 11, 0.9), rgba(240, 185, 11, 0.85), rgba(240, 185, 11, 0.9))"
        backdropFilter="blur(8px)"
        boxShadow="0 4px 20px rgba(0, 0, 0, 0.4)"
        position="relative"
        overflow="hidden"
        className="safe-area-top safe-area-left safe-area-right home-hero-section"
        _before={{
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: "url('/images/ship-transparent.svg')",
          backgroundSize: { base: "cover", md: "contain" },
          backgroundPosition: { base: "center", md: "right center" },
          backgroundRepeat: "no-repeat",
          opacity: { base: 0.05, md: 0.1 },
          zIndex: 0
        }}
      >
        <Container
          maxW={{ base: "100%", md: "container.lg" }}
          mx="auto"
          px={{ base: 4, md: 4 }}
          className="mobile-full-width"
        >
          {user ? (
            <Flex
              direction={{ base: "column", md: "row" }}
              justify="space-between"
              align="center"
              gap={4}
            >
              <Box
                position="relative"
                zIndex={1}
                w={{ base: "100%", md: "auto" }}
                textAlign={{ base: "center", md: "left" }}
                className="mobile-full-width"
              >
                <Heading
                  as="h2"
                  size={{ base: "md", md: "lg" }}
                  color={bgColor}
                  fontWeight="800"
                  textShadow="0 1px 2px rgba(0,0,0,0.1)"
                  className="responsive-heading"
                  lineHeight={{ base: "1.2", md: "1.1" }}
                  mb={{ base: 3, md: 2 }}
                >
                  {t('home.deposit.title', 'Deposit Now and Start Earning!')}
                </Heading>
                <Text
                  color={bgColor}
                  fontWeight="500"
                  fontSize={{ base: "sm", md: "md" }}
                  mt={2}
                  maxW={{ base: "100%", md: "600px" }}
                  textShadow="0 1px 1px rgba(0,0,0,0.05)"
                  className="responsive-text text-wrap"
                  lineHeight={{ base: "1.6", md: "1.5" }}
                >
                  {t('home.deposit.description', 'Deposit your crypto assets and start earning 1% daily returns immediately.')}
                </Text>
              </Box>

              <Flex
                direction={{ base: "column", sm: "row" }}
                gap={{ base: 3, sm: 4 }}
                align="center"
                w={{ base: "100%", sm: "auto" }}
                className="mobile-stack-vertical"
                justify={{ base: "center", sm: "flex-start" }}
              >
                <Button
                  onClick={handleInvestClick}
                  bg={bgColor}
                  color={primaryColor}
                  _hover={shouldUseHoverEffects() ? {
                    bg: "rgba(11, 14, 17, 0.8)",
                    transform: "translateY(-2px)"
                  } : {}}
                  _active={{ bg: "rgba(11, 14, 17, 0.9)", transform: "scale(0.98)" }}
                  fontWeight="700"
                  size={{ base: "md", md: "lg" }}
                  minH={buttonHeight}
                  w={{ base: "100%", sm: "auto" }}
                  leftIcon={<FaWallet />}
                  boxShadow="0 4px 10px rgba(0, 0, 0, 0.2)"
                  transition={`all ${getAnimationDuration()} ease`}
                  position="relative"
                  zIndex={1}
                  fontSize={{ base: "sm", md: "md" }}
                  px={{ base: 6, md: 8 }}
                >
                  {t('home.deposit.button', 'Investment Transaction')}
                </Button>

                {/* Withdraw button removed from homepage */}
              </Flex>
            </Flex>
          ) : (
            <Flex
              direction={{ base: "column", md: "row" }}
              justify="space-between"
              align="center"
              gap={4}
            >
              <Box position="relative" zIndex={1}>
                <Heading
                  as="h2"
                  size="lg"
                  color={bgColor}
                  fontWeight="800"
                  textShadow="0 1px 2px rgba(0,0,0,0.1)"
                >
                  {t('home.deposit.loginRequiredTitle', 'Login to Make an Investment')}
                </Heading>
                <Text
                  color={bgColor}
                  fontWeight="500"
                  fontSize="md"
                  mt={2}
                  maxW="600px"
                  textShadow="0 1px 1px rgba(0,0,0,0.05)"
                >
                  {t('home.deposit.loginRequiredDesc', 'Login to your account to deposit your crypto assets and earn 1% daily returns.')}
                </Text>
              </Box>

              <Flex
                direction={{ base: "column", sm: "row" }}
                gap={4}
                align="center"
              >
                <Button
                  as={RouterLink}
                  to="/login"
                  bg={bgColor}
                  color={primaryColor}
                  _hover={{ bg: "rgba(11, 14, 17, 0.8)", transform: "translateY(-2px)" }}
                  _active={{ bg: "rgba(11, 14, 17, 0.9)" }}
                  fontWeight="700"
                  size="lg"
                  leftIcon={<FaSignInAlt />}
                  boxShadow="0 4px 10px rgba(0, 0, 0, 0.2)"
                  transition="all 0.3s ease"
                  position="relative"
                  zIndex={1}
                >
                  {t('common.login', 'Login')}
                </Button>

                <Button
                  as={RouterLink}
                  to="/register"
                  variant="outline"
                  borderColor={bgColor}
                  borderWidth="2px"
                  color={bgColor}
                  _hover={{ bg: "rgba(255, 255, 255, 0.1)", transform: "translateY(-2px)" }}
                  _active={{ bg: "rgba(255, 255, 255, 0.15)" }}
                  fontWeight="700"
                  size="lg"
                  leftIcon={<FaUserPlus />}
                  boxShadow="0 4px 10px rgba(0, 0, 0, 0.2)"
                  transition="all 0.3s ease"
                  position="relative"
                  zIndex={1}
                >
                  {t('register', 'Register')}
                </Button>
              </Flex>
            </Flex>
          )}
        </Container>
      </Box>

      {/* Enhanced Hero Section - Now First and Bigger */}
      <Box
        py={{ base: 16, md: 24 }}
        px={{ base: 0, md: 0 }}
        bg={`linear-gradient(rgba(11, 14, 17, 0.6), rgba(11, 14, 17, 0.7)), url('/images/shipping-bg.jpg')`}
        bgSize="cover"
        bgPosition="center"
        position="relative"
        className="home-hero-section"
        minH={{ base: "80vh", md: "85vh" }}
        _before={{
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: "url('/images/ship-transparent.svg')",
          backgroundSize: "contain",
          backgroundPosition: "right center",
          backgroundRepeat: "no-repeat",
          opacity: 0.05,
          zIndex: 0
        }}
        _after={{
          content: '""',
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          height: "120px",
          bgGradient: `linear(to-t, ${bgColor}, transparent)`,
          zIndex: 0
        }}
      >
        <Container
          maxW={{ base: "100%", md: "container.xl" }}
          px={{ base: 4, md: 4 }}
          className="mobile-full-width"
        >
          <Grid
            templateColumns={{ base: "1fr", lg: "1fr 1fr" }}
            gap={{ base: 6, md: 10 }}
            alignItems="center"
            className="home-feature-grid"
          >
            <GridItem>
              <VStack
                align={{ base: "center", lg: "flex-start" }}
                spacing={{ base: 4, md: 6 }}
                textAlign={{ base: "center", lg: "left" }}
                w="100%"
              >
                <Box position="relative" w="100%">
                  <Heading
                    as="h1"
                    size={{ base: "xl", md: "2xl" }}
                    color={primaryColor}
                    lineHeight={{ base: "1.1", md: "1.0" }}
                    fontSize={{ base: "2.5rem", md: "3.5rem", lg: "4.5rem" }}
                    mb={{ base: 6, md: 8 }}
                    fontWeight="900"
                    textShadow="0 4px 8px rgba(0,0,0,0.4)"
                    position="relative"
                    className="responsive-heading text-wrap"
                    _after={{
                      content: '""',
                      position: "absolute",
                      bottom: "-15px",
                      left: "0",
                      width: "120px",
                      height: "6px",
                      bg: primaryColor,
                      borderRadius: "full",
                      boxShadow: "0 4px 8px rgba(240, 185, 11, 0.4)"
                    }}
                  >
                    {t('home.hero.title', 'Shipping Finance | Earn from the Power of Global Trade')}
                  </Heading>
                </Box>

                <Text
                  fontSize={{ base: "1.3rem", md: "1.5rem", lg: "1.7rem" }}
                  color="#FFFFFF"
                  fontWeight="700"
                  lineHeight={{ base: "1.4", md: "1.4" }}
                  textShadow="0 1px 3px rgba(0,0,0,0.8)"
                  maxW={{ base: "100%", md: "750px" }}
                  position="relative"
                  zIndex={1}
                  className="responsive-text text-wrap"
                  mb={{ base: 6, md: 8 }}
                  bg="rgba(255, 255, 255, 0.08)"
                  p={{ base: 4, md: 6 }}
                  borderRadius="xl"
                  border="1px solid rgba(255, 255, 255, 0.15)"
                  backdropFilter="blur(4px)"
                  boxShadow="0 4px 16px rgba(0, 0, 0, 0.1)"
                  _after={{
                    content: '""',
                    position: "absolute",
                    bottom: "-20px",
                    left: "0",
                    width: "80px",
                    height: "4px",
                    bg: primaryColor,
                    borderRadius: "full",
                    boxShadow: "0 2px 8px rgba(240, 185, 11, 0.5)"
                  }}
                >
                  {t('home.hero.mainDescription', 'Instead of keeping your crypto assets passively in exchanges, earn active returns with')} <Text as="span" color={primaryColor} fontWeight="900">{t('home.hero.dailyReturn', 'daily 1% returns')}</Text>. {t('home.hero.globalTrade', 'Grow your investment by leveraging the power of international trade.')}
                </Text>

                <VStack spacing={4} align="flex-start" maxW={{ base: "100%", md: "750px" }}>
                  <Box
                    bg="rgba(255, 255, 255, 0.12)"
                    p={{ base: 5, md: 7 }}
                    borderRadius="xl"
                    borderLeft="6px solid"
                    borderColor={primaryColor}
                    position="relative"
                    w="100%"
                    boxShadow="0 6px 20px rgba(0, 0, 0, 0.08)"
                    backdropFilter="blur(6px)"
                    border="1px solid rgba(255, 255, 255, 0.18)"
                    _before={{
                      content: '""',
                      position: "absolute",
                      top: "20px",
                      right: "20px",
                      width: "40px",
                      height: "40px",
                      bg: `rgba(240, 185, 11, 0.12)`,
                      borderRadius: "full",
                      zIndex: 0
                    }}
                  >
                    <HStack spacing={4} align="flex-start">
                      <Box
                        w="60px"
                        h="60px"
                        bg={primaryColor}
                        borderRadius="full"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        flexShrink={0}
                        boxShadow="0 4px 12px rgba(240, 185, 11, 0.4)"
                      >
                        <Text color="#0B0E11" fontWeight="900" fontSize="xl">%1</Text>
                      </Box>
                      <VStack align="flex-start" spacing={2} flex={1}>
                        <Text
                          color="#FFFFFF"
                          fontWeight="800"
                          fontSize={{ base: "1.1rem", md: "1.3rem" }}
                          lineHeight="1.3"
                        >
                          {t('home.hero.dailyReturnTitle', 'Daily Guaranteed Returns')}
                        </Text>
                        <Text
                          color="#B7BDC6"
                          fontWeight="500"
                          fontSize={{ base: "1rem", md: "1.1rem" }}
                          lineHeight="1.5"
                        >
                          {t('home.hero.dailyReturnDesc', 'Earn 1% of your investment every day. Growing returns with compound interest system.')}
                        </Text>
                      </VStack>
                    </HStack>
                  </Box>

                  <Box
                    bg="rgba(255, 255, 255, 0.12)"
                    p={{ base: 5, md: 7 }}
                    borderRadius="xl"
                    borderLeft="6px solid"
                    borderColor="#10B981"
                    position="relative"
                    w="100%"
                    boxShadow="0 6px 20px rgba(0, 0, 0, 0.08)"
                    backdropFilter="blur(6px)"
                    border="1px solid rgba(255, 255, 255, 0.18)"
                    _before={{
                      content: '""',
                      position: "absolute",
                      top: "20px",
                      right: "20px",
                      width: "40px",
                      height: "40px",
                      bg: "rgba(16, 185, 129, 0.12)",
                      borderRadius: "full",
                      zIndex: 0
                    }}
                  >
                    <HStack spacing={4} align="flex-start">
                      <Box
                        w="60px"
                        h="60px"
                        bg="#10B981"
                        borderRadius="full"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        flexShrink={0}
                        boxShadow="0 4px 12px rgba(16, 185, 129, 0.4)"
                      >
                        <Icon as={FaShip} color="#FFFFFF" boxSize={6} />
                      </Box>
                      <VStack align="flex-start" spacing={2} flex={1}>
                        <Text
                          color="#FFFFFF"
                          fontWeight="800"
                          fontSize={{ base: "1.1rem", md: "1.3rem" }}
                          lineHeight="1.3"
                        >
                          {t('home.hero.globalTradePowerTitle', 'Global Trade Power')}
                        </Text>
                        <Text
                          color="#B7BDC6"
                          fontWeight="500"
                          fontSize={{ base: "1rem", md: "1.1rem" }}
                          lineHeight="1.5"
                        >
                          {t('home.hero.globalTradePowerDesc', 'Stable returns by leveraging opportunities in international shipping and trade sectors.')}
                        </Text>
                      </VStack>
                    </HStack>
                  </Box>
                </VStack>

                {!user ? (
                  <HStack
                    spacing={{ base: 3, md: 4 }}
                    pt={{ base: 4, md: 6 }}
                    mt={2}
                    direction={{ base: "column", sm: "row" }}
                    w={{ base: "100%", sm: "auto" }}
                    align="center"
                    className="mobile-stack-vertical"
                  >
                    <Button
                      as={RouterLink}
                      to="/register"
                      size={{ base: "lg", md: "xl" }}
                      bg={primaryColor}
                      color="#0B0E11"
                      rightIcon={<FaArrowRight />}
                      px={{ base: 8, md: 12 }}
                      py={{ base: 8, md: 10 }}
                      fontSize={{ base: "1.1rem", md: "1.3rem" }}
                      fontWeight="800"
                      w={{ base: "100%", sm: "auto" }}
                      minW={{ base: "250px", sm: "auto" }}
                      minH={{ base: "56px", md: "64px" }}
                      boxShadow="0 4px 15px rgba(240, 185, 11, 0.3)"
                      position="relative"
                      _hover={{
                        bg: "brand.400",
                        transform: "translateY(-3px)",
                        boxShadow: "0 6px 20px rgba(240, 185, 11, 0.4)"
                      }}
                      _active={{
                        bg: "rgba(240, 185, 11, 0.9)",
                        transform: "translateY(-1px)",
                        boxShadow: "0 4px 12px rgba(240, 185, 11, 0.3)"
                      }}
                      transition="all 0.3s ease"
                      _before={{
                        content: '""',
                        position: "absolute",
                        top: "-2px",
                        left: "-2px",
                        right: "-2px",
                        bottom: "-2px",
                        bg: "transparent",
                        borderRadius: "lg",
                        border: "2px solid",
                        borderColor: `${primaryColor}50`,
                        opacity: 0,
                        transition: "opacity 0.3s ease",
                        zIndex: -1
                      }}
                    >
                      {t('home.hero.registerButton', 'Register Now')}
                    </Button>

                    <Button
                      as={RouterLink}
                      to="/login"
                      size={{ base: "lg", md: "xl" }}
                      variant="outline"
                      borderColor={primaryColor}
                      borderWidth="3px"
                      color={primaryColor}
                      px={{ base: 8, md: 12 }}
                      py={{ base: 8, md: 10 }}
                      fontSize={{ base: "1.1rem", md: "1.3rem" }}
                      fontWeight="700"
                      w={{ base: "100%", sm: "auto" }}
                      minW={{ base: "250px", sm: "auto" }}
                      minH={{ base: "56px", md: "64px" }}
                      position="relative"
                      _hover={{
                        bg: "rgba(240, 185, 11, 0.1)",
                        transform: "translateY(-3px)",
                        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)"
                      }}
                      _active={{
                        bg: "rgba(240, 185, 11, 0.15)",
                        transform: "translateY(-1px)",
                        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)"
                      }}
                      transition="all 0.3s ease"
                      _before={{
                        content: '""',
                        position: "absolute",
                        top: "-4px",
                        left: "-4px",
                        right: "-4px",
                        bottom: "-4px",
                        bg: "transparent",
                        borderRadius: "lg",
                        border: "1px solid",
                        borderColor: `${primaryColor}30`,
                        opacity: 0,
                        transition: "opacity 0.3s ease",
                        zIndex: -1
                      }}
                    >
                      {t('home.hero.loginButton', 'Login')}
                    </Button>
                  </HStack>
                ) : (
                  <Stack
                    spacing={{ base: 3, md: 4 }}
                    pt={{ base: 4, md: 6 }}
                    mt={2}
                    direction={{ base: "column", sm: "row" }}
                    w={{ base: "100%", sm: "auto" }}
                    align="center"
                    className="mobile-stack-vertical"
                  >
                    <Button
                      as={RouterLink}
                      to="/profile"
                      size={{ base: "xl", md: "xl" }}
                      bg={primaryColor}
                      color="#0B0E11"
                      rightIcon={<FaArrowRight />}
                      px={{ base: 10, md: 12 }}
                      py={{ base: 8, md: 10 }}
                      fontSize={{ base: "1.2rem", md: "1.3rem" }}
                      fontWeight="800"
                      w={{ base: "100%", sm: "auto" }}
                      minW={{ base: "100%", sm: "250px" }}
                      minH={{ base: "60px", md: "64px" }}
                      boxShadow="0 4px 15px rgba(240, 185, 11, 0.3)"
                      position="relative"
                      _hover={{
                        bg: "brand.400",
                        transform: "translateY(-3px)",
                        boxShadow: "0 6px 20px rgba(240, 185, 11, 0.4)"
                      }}
                      _active={{
                        bg: "rgba(240, 185, 11, 0.9)",
                        transform: "translateY(-1px)",
                        boxShadow: "0 4px 12px rgba(240, 185, 11, 0.3)"
                      }}
                      transition="all 0.3s ease"
                      sx={{
                        '@media (max-width: 767px)': {
                          touchAction: 'manipulation',
                          WebkitTapHighlightColor: 'transparent',
                          fontSize: '17px !important',
                          minHeight: '52px !important',
                          display: 'flex !important',
                          visibility: 'visible !important',
                          opacity: '1 !important',
                          zIndex: '999 !important',
                          position: 'relative !important',
                        }
                      }}
                    >
                      {t('profile', 'My Profile')}
                    </Button>

                    <Button
                      onClick={handleInvestClick}
                      size={{ base: "xl", md: "xl" }}
                      variant="outline"
                      borderColor={primaryColor}
                      borderWidth="3px"
                      color={primaryColor}
                      px={{ base: 10, md: 12 }}
                      py={{ base: 8, md: 10 }}
                      fontSize={{ base: "1.2rem", md: "1.3rem" }}
                      fontWeight="700"
                      w={{ base: "100%", sm: "auto" }}
                      minW={{ base: "100%", sm: "250px" }}
                      minH={{ base: "60px", md: "64px" }}
                      position="relative"
                      _hover={{
                        bg: "rgba(240, 185, 11, 0.1)",
                        transform: "translateY(-3px)",
                        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)"
                      }}
                      _active={{
                        bg: "rgba(240, 185, 11, 0.15)",
                        transform: "translateY(-1px)",
                        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)"
                      }}
                      transition="all 0.3s ease"
                      sx={{
                        '@media (max-width: 767px)': {
                          touchAction: 'manipulation',
                          WebkitTapHighlightColor: 'transparent',
                          fontSize: '17px !important',
                          minHeight: '52px !important',
                          display: 'flex !important',
                          visibility: 'visible !important',
                          opacity: '1 !important',
                          zIndex: '999 !important',
                          position: 'relative !important',
                        }
                      }}
                    >
                      {t('home.deposit.button', 'Investment Transaction')}
                    </Button>
                  </Stack>
                )}
              </VStack>
            </GridItem>

            <GridItem display={{ base: "none", lg: "block" }}>
              <Box
                className="transparent-container dark-overlay"
                bg="rgba(0, 0, 0, 0.6)"
                p={8}
                borderRadius="xl"
                borderWidth="1px"
                borderColor="rgba(240, 185, 11, 0.3)"
                position="relative"
                overflow="hidden"
                boxShadow="0 8px 24px rgba(0, 0, 0, 0.4)"
                backgroundImage="url('/images/bitcoin-shipping.jpg')"
                backgroundSize="cover"
                backgroundPosition="center"
                backgroundBlendMode="overlay"
                _before={{
                  content: '""',
                  position: "absolute",
                  top: "-50px",
                  right: "-50px",
                  width: "100px",
                  height: "100px",
                  borderRadius: "full",
                  bg: `${primaryColor}10`,
                  zIndex: 0
                }}
                _after={{
                  content: '""',
                  position: "absolute",
                  bottom: "-30px",
                  left: "-30px",
                  width: "80px",
                  height: "80px",
                  borderRadius: "full",
                  bg: `${primaryColor}10`,
                  zIndex: 0
                }}
                transition="all 0.3s ease"
                _hover={{
                  borderColor: "rgba(240, 185, 11, 0.3)"
                }}
              >
                <VStack spacing={5} position="relative" zIndex={1}>
                  <Flex
                    w="90px"
                    h="90px"
                    borderRadius="full"
                    bg="rgba(0, 0, 0, 0.7)"
                    border="2px solid"
                    borderColor={`${primaryColor}60`}
                    justify="center"
                    align="center"
                    mb={2}
                    boxShadow="0 6px 18px rgba(0, 0, 0, 0.3)"
                  >
                    <Icon as={FaShip} color={primaryColor} boxSize={12} />
                  </Flex>

                  <Heading
                    size="md"
                    color={primaryColor}
                    textAlign="center"
                    fontSize={{ base: "1.4rem", md: "1.7rem" }}
                    mb={2}
                    fontWeight="700"
                    position="relative"
                    _after={{
                      content: '""',
                      position: "absolute",
                      bottom: "-10px",
                      left: "50%",
                      transform: "translateX(-50%)",
                      width: "60px",
                      height: "3px",
                      bg: `${primaryColor}70`,
                      borderRadius: "full"
                    }}
                  >
                    {t('home.dailyReturn.title', '1% Daily Return Opportunity')}
                  </Heading>

                  <Text
                    fontSize={{ base: "sm", md: "md" }}
                    color={textColor}
                    textAlign="center"
                    fontWeight="500"
                    lineHeight="1.6"
                    mt={4}
                  >
                    {t('home.dailyReturn.description', 'Instead of keeping your crypto assets passively in exchanges, you can earn an active return of 1% daily by investing through Shipping Finance.')}
                  </Text>

                  <Box
                    mt={4}
                    p={4}
                    bg="rgba(0, 0, 0, 0.7)"
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor="rgba(240, 185, 11, 0.4)"
                    maxW="300px"
                    mx="auto"
                    position="relative"
                    overflow="hidden"
                    boxShadow="0 6px 18px rgba(0, 0, 0, 0.35)"
                    _hover={{
                      borderColor: "rgba(240, 185, 11, 0.3)"
                    }}
                    _before={{
                      content: '""',
                      position: "absolute",
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundImage: "url('/images/ship-transparent.svg')",
                      backgroundSize: "cover",
                      backgroundPosition: "center",
                      backgroundRepeat: "no-repeat",
                      opacity: 0.05,
                      zIndex: 0
                    }}
                  >
                    <VStack spacing={3} position="relative" zIndex={1}>
                      <Flex
                        w="60px"
                        h="60px"
                        borderRadius="full"
                        bg="rgba(0, 0, 0, 0.7)"
                        border="2px solid"
                        borderColor={`${primaryColor}60`}
                        justify="center"
                        align="center"
                        mb={1}
                        boxShadow="0 4px 12px rgba(0, 0, 0, 0.3)"
                      >
                        <Icon as={FaShip} color={primaryColor} boxSize={8} />
                      </Flex>

                      <Text color={primaryColor} fontWeight="700" fontSize="lg" textAlign="center">
                        {t('home.dailyReturn.quote', '"Earn with the Power of Global Trade"')}
                      </Text>

                      <Divider borderColor={`${primaryColor}40`} />

                      <HStack spacing={6} justify="center">
                        <VStack spacing={0}>
                          <Text color={primaryColor} fontWeight="800" fontSize="xl">%1</Text>
                          <Text color={textColor} fontSize="xs">{t('home.dailyReturn.dailyInterest', 'Daily Interest')}</Text>
                        </VStack>

                        <Box w="1px" h="30px" bg={`${primaryColor}40`}></Box>

                        <VStack spacing={0}>
                          <Text color={primaryColor} fontWeight="800" fontSize="xl">%3</Text>
                          <Text color={textColor} fontSize="xs">{t('home.dailyReturn.referralRate', 'Referral Rate')}</Text>
                        </VStack>
                      </HStack>

                      <Divider borderColor={`${primaryColor}40`} />

                      <SimpleGrid columns={3} spacing={2} w="full">
                        <VStack spacing={0}>
                          <Text color={primaryColor} fontWeight="700" fontSize="sm">1,000 USDT</Text>
                          <Text color={textColor} fontSize="xs">→ 1,010 USDT</Text>
                          <Text color={textColor} fontSize="2xs" opacity={0.7}>{t('home.dailyReturn.periods.daily', 'Daily')}</Text>
                        </VStack>

                        <VStack spacing={0}>
                          <Text color={primaryColor} fontWeight="700" fontSize="sm">1,000 USDT</Text>
                          <Text color={textColor} fontSize="xs">→ 1,070 USDT</Text>
                          <Text color={textColor} fontSize="2xs" opacity={0.7}>{t('home.dailyReturn.periods.weekly', 'Weekly')}</Text>
                        </VStack>

                        <VStack spacing={0}>
                          <Text color={primaryColor} fontWeight="700" fontSize="sm">1,000 USDT</Text>
                          <Text color={textColor} fontSize="xs">→ 1,305 USDT</Text>
                          <Text color={textColor} fontSize="2xs" opacity={0.7}>{t('home.dailyReturn.periods.monthly', 'Monthly')}</Text>
                        </VStack>
                      </SimpleGrid>
                    </VStack>
                  </Box>
                </VStack>
              </Box>
            </GridItem>
          </Grid>
        </Container>
      </Box>

      {/* Cryptocurrency Cards Section - After Hero Section */}
      <Box
        py={{ base: 6, md: 8 }}
        bg="#0B0E11"
        position="relative"
      >
        <Container maxW="container.xl">
          <CryptocurrencyCards />
        </Container>
      </Box>

      {/* Nasıl Çalışır? */}
      <Box
        py={{ base: 14, md: 20 }}
        bg={`linear-gradient(rgba(11, 14, 17, 0.7), rgba(11, 14, 17, 0.8)), url('/images/how-it-works-bg.jpg')`}
        bgSize="cover"
        bgPosition="center"
        position="relative"
        className="bitcoin-bg dark-overlay"
        _before={{
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "100px",
          bgGradient: `linear(to-b, ${bgColor}, transparent)`,
          zIndex: 0
        }}
        _after={{
          content: '""',
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          height: "100px",
          bgGradient: `linear(to-t, ${bgColor}, transparent)`,
          zIndex: 0
        }}
      >
        <Container maxW="container.lg" mx="auto" position="relative" zIndex={1}>
          <VStack spacing={12}>
            <Box textAlign="center" mb={10}>
              <Heading
                as="h2"
                size="xl"
                color={primaryColor}
                mb={6}
                fontWeight="800"
                fontSize={{ base: "2rem", md: "2.5rem" }}
                textShadow="0 2px 4px rgba(0,0,0,0.3)"
                position="relative"
                display="inline-block"
                _after={{
                  content: '""',
                  position: "absolute",
                  bottom: "-15px",
                  left: "50%",
                  transform: "translateX(-50%)",
                  width: "80px",
                  height: "4px",
                  bg: primaryColor,
                  borderRadius: "full"
                }}
              >
                {t('home:howItWorks.title', 'How It Works?')}
              </Heading>
              <Text
                fontSize={{ base: "md", md: "xl" }}
                color={textColor}
                maxW="800px"
                mx="auto"
                lineHeight="1.7"
                mt={8}
                fontWeight="500"
                textShadow="0 1px 2px rgba(0,0,0,0.2)"
              >
                {t('home:howItWorks.description', 'Investing and earning with Shipping Finance is very easy. You can start in just a few steps.')}
              </Text>
            </Box>

            <Grid
              templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)" }}
              gap={8}
              w="full"
            >
              {/* Adım 1 */}
              <GridItem>
                <Box
                  bg={`rgba(30, 35, 41, 0.5)`}
                  borderRadius="xl"
                  p={6}
                  borderWidth="1px"
                  borderColor={borderColor}
                  height="100%"
                  position="relative"
                  overflow="hidden"
                  boxShadow="0 8px 20px rgba(0, 0, 0, 0.15)"
                  transition="all 0.3s ease"
                  _hover={{
                    transform: "translateY(-8px)",
                    boxShadow: "0 15px 30px rgba(0, 0, 0, 0.25)",
                    borderColor: primaryColor
                  }}
                >
                  <VStack spacing={5} align="center">
                    <Flex
                      w="90px"
                      h="90px"
                      borderRadius="full"
                      bg={`${primaryColor}`}
                      color={bgColor}
                      justify="center"
                      align="center"
                      fontSize="3xl"
                      fontWeight="bold"
                      position="relative"
                      boxShadow="0 5px 15px rgba(240, 185, 11, 0.3)"
                    >
                      1
                      <Box
                        position="absolute"
                        right="-30px"
                        top="50%"
                        transform="translateY(-50%)"
                        display={{ base: "none", lg: "block" }}
                      >
                        <Icon as={FaArrowRight} color={primaryColor} boxSize={7} />
                      </Box>
                    </Flex>
                    <Heading
                      size="md"
                      color={textColor}
                      textAlign="center"
                      fontWeight="700"
                      fontSize="1.3rem"
                    >
                      {t('home:howItWorks.step1.title', 'Create Account')}
                    </Heading>
                    <Text
                      color={secondaryTextColor}
                      textAlign="center"
                      fontSize="md"
                      lineHeight="1.6"
                    >
                      {t('home:howItWorks.step1.description', 'Create your free account quickly and easily and gain access to our platform.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>

              {/* Adım 2 */}
              <GridItem>
                <Box
                  bg={`rgba(30, 35, 41, 0.5)`}
                  borderRadius="xl"
                  p={6}
                  borderWidth="1px"
                  borderColor={borderColor}
                  height="100%"
                  position="relative"
                  overflow="hidden"
                  boxShadow="0 8px 20px rgba(0, 0, 0, 0.15)"
                  transition="all 0.3s ease"
                  _hover={{
                    transform: "translateY(-8px)",
                    boxShadow: "0 15px 30px rgba(0, 0, 0, 0.25)",
                    borderColor: primaryColor
                  }}
                >
                  <VStack spacing={5} align="center">
                    <Flex
                      w="90px"
                      h="90px"
                      borderRadius="full"
                      bg={`${primaryColor}`}
                      color={bgColor}
                      justify="center"
                      align="center"
                      fontSize="3xl"
                      fontWeight="bold"
                      position="relative"
                      boxShadow="0 5px 15px rgba(240, 185, 11, 0.3)"
                    >
                      2
                      <Box
                        position="absolute"
                        right="-30px"
                        top="50%"
                        transform="translateY(-50%)"
                        display={{ base: "none", lg: "block" }}
                      >
                        <Icon as={FaArrowRight} color={primaryColor} boxSize={7} />
                      </Box>
                    </Flex>
                    <Heading
                      size="md"
                      color={textColor}
                      textAlign="center"
                      fontWeight="700"
                      fontSize="1.3rem"
                    >
                      {t('home:howItWorks.step2.title', 'Deposit Cryptocurrency')}
                    </Heading>
                    <Text
                      color={secondaryTextColor}
                      textAlign="center"
                      fontSize="md"
                      lineHeight="1.6"
                    >
                      {t('home:howItWorks.step2.description', 'Invest in your account with Bitcoin, Ethereum, Tether, Dogecoin or XRP.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>

              {/* Adım 3 */}
              <GridItem>
                <Box
                  bg={`rgba(30, 35, 41, 0.5)`}
                  borderRadius="xl"
                  p={6}
                  borderWidth="1px"
                  borderColor={borderColor}
                  height="100%"
                  position="relative"
                  overflow="hidden"
                  boxShadow="0 8px 20px rgba(0, 0, 0, 0.15)"
                  transition="all 0.3s ease"
                  _hover={{
                    transform: "translateY(-8px)",
                    boxShadow: "0 15px 30px rgba(0, 0, 0, 0.25)",
                    borderColor: primaryColor
                  }}
                >
                  <VStack spacing={5} align="center">
                    <Flex
                      w="90px"
                      h="90px"
                      borderRadius="full"
                      bg={`${primaryColor}`}
                      color={bgColor}
                      justify="center"
                      align="center"
                      fontSize="3xl"
                      fontWeight="bold"
                      position="relative"
                      boxShadow="0 5px 15px rgba(240, 185, 11, 0.3)"
                    >
                      3
                      <Box
                        position="absolute"
                        right="-30px"
                        top="50%"
                        transform="translateY(-50%)"
                        display={{ base: "none", lg: "block" }}
                      >
                        <Icon as={FaArrowRight} color={primaryColor} boxSize={7} />
                      </Box>
                    </Flex>
                    <Heading
                      size="md"
                      color={textColor}
                      textAlign="center"
                      fontWeight="700"
                      fontSize="1.3rem"
                    >
                      {t('home:howItWorks.step3.title', 'Earn Daily Returns')}
                    </Heading>
                    <Text
                      color={secondaryTextColor}
                      textAlign="center"
                      fontSize="md"
                      lineHeight="1.6"
                    >
                      {t('home:howItWorks.step3.description', 'Start earning 1% daily returns on your investment.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>

              {/* Adım 4 */}
              <GridItem>
                <Box
                  bg={`rgba(30, 35, 41, 0.5)`}
                  borderRadius="xl"
                  p={6}
                  borderWidth="1px"
                  borderColor={borderColor}
                  height="100%"
                  position="relative"
                  overflow="hidden"
                  boxShadow="0 8px 20px rgba(0, 0, 0, 0.15)"
                  transition="all 0.3s ease"
                  _hover={{
                    transform: "translateY(-8px)",
                    boxShadow: "0 15px 30px rgba(0, 0, 0, 0.25)",
                    borderColor: primaryColor
                  }}
                >
                  <VStack spacing={5} align="center">
                    <Flex
                      w="90px"
                      h="90px"
                      borderRadius="full"
                      bg={`${primaryColor}`}
                      color={bgColor}
                      justify="center"
                      align="center"
                      fontSize="3xl"
                      fontWeight="bold"
                      position="relative"
                      boxShadow="0 5px 15px rgba(240, 185, 11, 0.3)"
                    >
                      4
                    </Flex>
                    <Heading
                      size="md"
                      color={textColor}
                      textAlign="center"
                      fontWeight="700"
                      fontSize="1.3rem"
                    >
                      {t('home:howItWorks.step4.title', 'Withdraw Your Earnings')}
                    </Heading>
                    <Text
                      color={secondaryTextColor}
                      textAlign="center"
                      fontSize="md"
                      lineHeight="1.6"
                    >
                      {t('home:howItWorks.step4.description', 'You can withdraw your earnings as cryptocurrency anytime you want.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>
            </Grid>

            {!user ? (
              <Button
                as={RouterLink}
                to="/register"
                size="lg"
                bg={primaryColor}
                color="#0B0E11"
                rightIcon={<FaArrowRight />}
                fontSize={{ base: "md", md: "lg" }}
                py={7}
                px={12}
                fontWeight="700"
                mt={10}
                boxShadow="0 8px 20px rgba(240, 185, 11, 0.3)"
                borderRadius="lg"
                _hover={{
                  bg: "brand.400",
                  transform: "translateY(-5px)",
                  boxShadow: "0 12px 25px rgba(240, 185, 11, 0.4)"
                }}
                transition="all 0.3s ease"
              >
                {t('home:howItWorks.startButton', 'Get Started Now')}
              </Button>
            ) : (
              <Button
                onClick={handleInvestClick}
                size="lg"
                bg={primaryColor}
                color="#0B0E11"
                rightIcon={<FaArrowRight />}
                fontSize={{ base: "md", md: "lg" }}
                py={7}
                px={12}
                fontWeight="700"
                mt={10}
                boxShadow="0 8px 20px rgba(240, 185, 11, 0.3)"
                borderRadius="lg"
                _hover={{
                  bg: "brand.400",
                  transform: "translateY(-5px)",
                  boxShadow: "0 12px 25px rgba(240, 185, 11, 0.4)"
                }}
                transition="all 0.3s ease"
              >
                {t('home.deposit.button', 'Investment Transaction')}
              </Button>
            )}
          </VStack>
        </Container>
      </Box>

      {/* Neden Shipping Finance? */}
      <Box
        py={{ base: 10, md: 16 }}
        bg={bgColor}
        position="relative"
      >
        <Container maxW="container.xl" position="relative" zIndex={1}>
          <VStack spacing={12}>
            <Box textAlign="center" mb={10}>
              <Heading
                as="h2"
                size="xl"
                color={primaryColor}
                mb={6}
                fontWeight="800"
                fontSize={{ base: "2rem", md: "2.5rem" }}
                textShadow="0 2px 4px rgba(0,0,0,0.3)"
                position="relative"
                display="inline-block"
                _after={{
                  content: '""',
                  position: "absolute",
                  bottom: "-15px",
                  left: "50%",
                  transform: "translateX(-50%)",
                  width: "80px",
                  height: "4px",
                  bg: primaryColor,
                  borderRadius: "full"
                }}
              >
                {t('home:whyChooseUs.title', 'Why Choose Shipping Finance?')}
              </Heading>
              <Text
                fontSize={{ base: "md", md: "xl" }}
                color={textColor}
                maxW="800px"
                mx="auto"
                lineHeight="1.7"
                mt={8}
                fontWeight="500"
              >
                {t('home:whyChooseUs.description', 'Shipping Finance is an innovative financial platform that provides investors with stable and reliable returns by evaluating the opportunities offered by the international trade and maritime sector.')}
              </Text>
              <Text
                fontSize={{ base: "sm", md: "md" }}
                color={secondaryTextColor}
                maxW="800px"
                mx="auto"
                lineHeight="1.7"
                mt={4}
                fontWeight="400"
                opacity="0.9"
              >
                {t('home:whyChooseUs.extendedDescription', 'Our business model is based on the principle of purchasing products from low-cost countries (such as China, Egypt) and selling them in markets with high demand and price advantages (especially in Europe and other developed regions). Through this trade volume, high profitability is achieved and our investors are provided with an average daily return of 1%.')}
              </Text>
            </Box>

            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8} w="full">
              <Box
                bg={cardBgColor}
                p={8}
                borderRadius="xl"
                borderWidth="1px"
                borderColor={borderColor}
                height="100%"
                boxShadow="0 8px 20px rgba(0, 0, 0, 0.15)"
                transition="all 0.3s ease"
                _hover={{
                  transform: "translateY(-8px)",
                  boxShadow: "0 15px 30px rgba(0, 0, 0, 0.25)",
                  borderColor: primaryColor
                }}
                position="relative"
                overflow="hidden"
                _before={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "5px",
                  height: "100%",
                  bg: primaryColor,
                  borderTopLeftRadius: "xl",
                  borderBottomLeftRadius: "xl"
                }}
              >
                <VStack spacing={4} align="flex-start">
                  <Heading
                    size="md"
                    color={primaryColor}
                    fontWeight="700"
                    fontSize="1.3rem"
                    position="relative"
                    pl={2}
                  >
                    {t('home:whyChooseUs.benefits.title1', 'Real Commercial Earnings')}
                  </Heading>
                  <Text
                    color={secondaryTextColor}
                    fontSize="md"
                    lineHeight="1.7"
                    pl={2}
                  >
                    {t('home:whyChooseUs.benefits.description1', 'Solid profits through the model of purchasing products from low-cost countries and selling to high-demand regions.')}
                  </Text>
                </VStack>
              </Box>

              <Box
                bg={cardBgColor}
                p={8}
                borderRadius="xl"
                borderWidth="1px"
                borderColor={borderColor}
                height="100%"
                boxShadow="0 8px 20px rgba(0, 0, 0, 0.15)"
                transition="all 0.3s ease"
                _hover={{
                  transform: "translateY(-8px)",
                  boxShadow: "0 15px 30px rgba(0, 0, 0, 0.25)",
                  borderColor: primaryColor
                }}
                position="relative"
                overflow="hidden"
                _before={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "5px",
                  height: "100%",
                  bg: primaryColor,
                  borderTopLeftRadius: "xl",
                  borderBottomLeftRadius: "xl"
                }}
              >
                <VStack spacing={4} align="flex-start">
                  <Heading
                    size="md"
                    color={primaryColor}
                    fontWeight="700"
                    fontSize="1.3rem"
                    position="relative"
                    pl={2}
                  >
                    {t('home:whyChooseUs.benefits.title2', 'Daily Income Advantage')}
                  </Heading>
                  <Text
                    color={secondaryTextColor}
                    fontSize="md"
                    lineHeight="1.7"
                    pl={2}
                  >
                    {t('home:whyChooseUs.benefits.description2', 'Earn 1% daily instead of keeping your crypto assets passive.')}
                  </Text>
                </VStack>
              </Box>

              <Box
                bg={cardBgColor}
                p={8}
                borderRadius="xl"
                borderWidth="1px"
                borderColor={borderColor}
                height="100%"
                boxShadow="0 8px 20px rgba(0, 0, 0, 0.15)"
                transition="all 0.3s ease"
                _hover={{
                  transform: "translateY(-8px)",
                  boxShadow: "0 15px 30px rgba(0, 0, 0, 0.25)",
                  borderColor: primaryColor
                }}
                position="relative"
                overflow="hidden"
                _before={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "5px",
                  height: "100%",
                  bg: primaryColor,
                  borderTopLeftRadius: "xl",
                  borderBottomLeftRadius: "xl"
                }}
              >
                <VStack spacing={4} align="flex-start">
                  <Heading
                    size="md"
                    color={primaryColor}
                    fontWeight="700"
                    fontSize="1.3rem"
                    position="relative"
                    pl={2}
                  >
                    {t('home:whyChooseUs.benefits.title3', 'Transparent and Secure System')}
                  </Heading>
                  <Text
                    color={secondaryTextColor}
                    fontSize="md"
                    lineHeight="1.7"
                    pl={2}
                  >
                    {t('home:whyChooseUs.benefits.description3', 'All transactions are based on international trade standards, contracts and transparency.')}
                  </Text>
                </VStack>
              </Box>

              <Box
                bg={cardBgColor}
                p={8}
                borderRadius="xl"
                borderWidth="1px"
                borderColor={borderColor}
                height="100%"
                boxShadow="0 8px 20px rgba(0, 0, 0, 0.15)"
                transition="all 0.3s ease"
                _hover={{
                  transform: "translateY(-8px)",
                  boxShadow: "0 15px 30px rgba(0, 0, 0, 0.25)",
                  borderColor: primaryColor
                }}
                position="relative"
                overflow="hidden"
                _before={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  width: "5px",
                  height: "100%",
                  bg: primaryColor,
                  borderTopLeftRadius: "xl",
                  borderBottomLeftRadius: "xl"
                }}
              >
                <VStack spacing={4} align="flex-start">
                  <Heading
                    size="md"
                    color={primaryColor}
                    fontWeight="700"
                    fontSize="1.3rem"
                    position="relative"
                    pl={2}
                  >
                    {t('home:whyChooseUs.benefits.title4', 'Flexible Investment Processes')}
                  </Heading>
                  <Text
                    color={secondaryTextColor}
                    fontSize="md"
                    lineHeight="1.7"
                    pl={2}
                  >
                    {t('home:whyChooseUs.benefits.description4', 'Opportunity to earn by utilizing your cryptocurrencies with flexible, fast and transparent investment processes instead of waiting passively.')}
                  </Text>
                </VStack>
              </Box>
            </SimpleGrid>

            <Grid
              templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }}
              gap={8}
              w="full"
            >
              {/* Kart 1 */}
              <GridItem>
                <Box
                  bg={cardBgColor}
                  p={6}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor={borderColor}
                  height="100%"
                  position="relative"
                  overflow="hidden"
                >
                  <VStack spacing={4} align="flex-start">
                    <Icon as={FaShip} color={primaryColor} boxSize={10} />
                    <Heading size="md" color={textColor} fontWeight="700">
                      {t('home:whyChooseUs.card1.title', 'Global Trade Opportunities')}
                    </Heading>
                    <Text color={secondaryTextColor} fontSize="md" lineHeight="1.7">
                      {t('home:whyChooseUs.card1.description', 'Shipping Finance provides investors with 1% daily earnings by evaluating opportunities in the global trade and maritime sector.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>

              {/* Kart 2 */}
              <GridItem>
                <Box
                  bg={cardBgColor}
                  p={6}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor={borderColor}
                  height="100%"
                  position="relative"
                  overflow="hidden"
                >
                  <VStack spacing={4} align="flex-start">
                    <Icon as={FaArrowUp} color={primaryColor} boxSize={10} />
                    <Heading size="md" color={textColor} fontWeight="700">
                      {t('home:whyChooseUs.card2.title', 'Stable Daily Returns')}
                    </Heading>
                    <Text color={secondaryTextColor} fontSize="md" lineHeight="1.7">
                      {t('home:whyChooseUs.card2.description', 'Our platform offers investors a stable daily return of 1%. This means a potential return of up to 30% monthly.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>

              {/* Kart 3 */}
              <GridItem>
                <Box
                  bg={cardBgColor}
                  p={6}
                  borderRadius="lg"
                  borderWidth="1px"
                  borderColor={borderColor}
                  height="100%"
                  position="relative"
                  overflow="hidden"
                >
                  <VStack spacing={4} align="flex-start">
                    <Icon as={FaArrowRight} color={primaryColor} boxSize={10} />
                    <Heading size="md" color={textColor} fontWeight="700">
                      {t('home:whyChooseUs.card3.title', 'Easy and Secure Investment')}
                    </Heading>
                    <Text color={secondaryTextColor} fontSize="md" lineHeight="1.7">
                      {t('home:whyChooseUs.card3.description', 'Investing in Shipping Finance is extremely easy and secure. You can quickly invest with your cryptocurrencies.')}
                    </Text>
                  </VStack>
                </Box>
              </GridItem>
            </Grid>
          </VStack>
        </Container>
      </Box>






      {/* About Us Summary */}
      <Box
        py={{ base: 14, md: 20 }}
        bg={`linear-gradient(rgba(11, 14, 17, 0.75), rgba(11, 14, 17, 0.85)), url('/images/about-bg.jpg')`}
        bgSize="cover"
        bgPosition="center"
        position="relative"
        _before={{
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          height: "100px",
          bgGradient: `linear(to-b, ${bgColor}, transparent)`,
          zIndex: 0
        }}
        _after={{
          content: '""',
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          height: "100px",
          bgGradient: `linear(to-t, ${bgColor}, transparent)`,
          zIndex: 0
        }}
      >
        <Container maxW="container.lg" mx="auto">
          <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={10} alignItems="center">
            <GridItem>
              <VStack align="flex-start" spacing={6}>
                <Heading
                  as="h2"
                  size="xl"
                  color={primaryColor}
                  mb={6}
                  fontWeight="800"
                  fontSize={{ base: "2rem", md: "2.5rem" }}
                  textShadow="0 2px 4px rgba(0,0,0,0.3)"
                  position="relative"
                  _after={{
                    content: '""',
                    position: "absolute",
                    bottom: "-15px",
                    left: "0",
                    width: "80px",
                    height: "4px",
                    bg: primaryColor,
                    borderRadius: "full"
                  }}
                >
                  {t('home:about.title', 'About Us (Summary)')}
                </Heading>
                <Text
                  fontSize={{ base: "md", md: "lg" }}
                  color={textColor}
                  lineHeight="1.7"
                  fontWeight="500"
                  textShadow="0 1px 2px rgba(0,0,0,0.2)"
                  mt={4}
                >
                  {t('home:about.description1', 'Shipping Finance is an innovative financial platform that offers investors the power of international trade.')}
                </Text>
                <Text
                  fontSize={{ base: "md", md: "lg" }}
                  color={textColor}
                  lineHeight="1.7"
                  fontWeight="500"
                  textShadow="0 1px 2px rgba(0,0,0,0.2)"
                >
                  {t('home:about.description2', 'Instead of keeping your cryptocurrencies idle on exchanges, we offer the opportunity to grow them through real commercial activities.')}
                </Text>
                <Text
                  fontSize={{ base: "md", md: "lg" }}
                  color={textColor}
                  lineHeight="1.7"
                  fontWeight="500"
                  textShadow="0 1px 2px rgba(0,0,0,0.2)"
                >
                  {t('home:about.description3', 'With our global logistics network and professional trading operations, we take your investments further every day.')}
                </Text>
                <Button
                  as={RouterLink}
                  to="/about"
                  size="lg"
                  bg={primaryColor}
                  color="#0B0E11"
                  rightIcon={<FaInfoCircle />}
                  fontWeight="700"
                  mt={4}
                  px={8}
                  py={7}
                  boxShadow="0 8px 20px rgba(240, 185, 11, 0.3)"
                  borderRadius="lg"
                  _hover={{
                    bg: "brand.400",
                    transform: "translateY(-5px)",
                    boxShadow: "0 12px 25px rgba(240, 185, 11, 0.4)"
                  }}
                  transition="all 0.3s ease"
                >
                  {t('home:about.button', 'More Details')}
                </Button>
              </VStack>
            </GridItem>
            <GridItem display={{ base: "none", lg: "block" }}>
              <Box
                bg={cardBgColor}
                p={8}
                borderRadius="xl"
                borderWidth="1px"
                borderColor={borderColor}
                position="relative"
                overflow="hidden"
                boxShadow="0 15px 35px rgba(0, 0, 0, 0.3)"
                transition="all 0.3s ease"
                _hover={{
                  transform: "translateY(-8px)",
                  boxShadow: "0 20px 40px rgba(0, 0, 0, 0.4)",
                  borderColor: primaryColor
                }}
                _before={{
                  content: '""',
                  position: "absolute",
                  top: "-50px",
                  right: "-50px",
                  width: "100px",
                  height: "100px",
                  borderRadius: "full",
                  bg: `${primaryColor}30`,
                  zIndex: 0
                }}
                _after={{
                  content: '""',
                  position: "absolute",
                  bottom: "-30px",
                  left: "-30px",
                  width: "80px",
                  height: "80px",
                  borderRadius: "full",
                  bg: `${primaryColor}20`,
                  zIndex: 0
                }}
              >
                <VStack spacing={5} align="flex-start">
                  <Heading
                    size="md"
                    color={primaryColor}
                    fontWeight="700"
                    fontSize="1.4rem"
                    position="relative"
                    zIndex={1}
                    mb={2}
                    _after={{
                      content: '""',
                      position: "absolute",
                      bottom: "-10px",
                      left: "0",
                      width: "60px",
                      height: "3px",
                      bg: primaryColor,
                      borderRadius: "full"
                    }}
                  >
                    {t('home.about.joinTitle', 'Join Now and Start Investing')}
                  </Heading>
                  <Text
                    color={textColor}
                    fontSize="md"
                    lineHeight="1.7"
                    mt={4}
                    fontWeight="500"
                    position="relative"
                    zIndex={1}
                  >
                    {t('home.about.joinDescription', 'Registration is free, and your investments start growing immediately. With Shipping Finance, take strong steps toward your future by growing your capital not only in digital assets but also in the growing volume of world trade.')}
                  </Text>
                  {!user ? (
                    <HStack spacing={4} pt={4} w="full" position="relative" zIndex={1}>
                      <Button
                        as={RouterLink}
                        to="/register"
                        size="md"
                        w="full"
                        bg={primaryColor}
                        color="#0B0E11"
                        rightIcon={<FaArrowRight />}
                        fontWeight="700"
                        py={6}
                        boxShadow="0 8px 15px rgba(240, 185, 11, 0.3)"
                        borderRadius="lg"
                        _hover={{
                          bg: "brand.400",
                          transform: "translateY(-3px)",
                          boxShadow: "0 12px 20px rgba(240, 185, 11, 0.4)"
                        }}
                        transition="all 0.3s ease"
                      >
                        {t('home.about.registerButton', 'Register')}
                      </Button>
                      <Button
                        as={RouterLink}
                        to="/login"
                        size="md"
                        w="full"
                        variant="outline"
                        borderColor={primaryColor}
                        color={primaryColor}
                        fontWeight="600"
                        py={6}
                        borderWidth="2px"
                        _hover={{
                          bg: "rgba(240, 185, 11, 0.1)",
                          transform: "translateY(-3px)",
                          boxShadow: "0 8px 15px rgba(0, 0, 0, 0.1)"
                        }}
                        transition="all 0.3s ease"
                      >
                        {t('home.about.loginButton', 'Login')}
                      </Button>
                    </HStack>
                  ) : (
                    <HStack spacing={4} pt={4} w="full" position="relative" zIndex={1}>
                      <Button
                        as={RouterLink}
                        to="/profile"
                        size="md"
                        w="full"
                        bg={primaryColor}
                        color="#0B0E11"
                        rightIcon={<FaArrowRight />}
                        fontWeight="700"
                        py={6}
                        boxShadow="0 8px 15px rgba(240, 185, 11, 0.3)"
                        borderRadius="lg"
                        _hover={{
                          bg: "brand.400",
                          transform: "translateY(-3px)",
                          boxShadow: "0 12px 20px rgba(240, 185, 11, 0.4)"
                        }}
                        transition="all 0.3s ease"
                      >
                        {t('common.profile', 'My Profile')}
                      </Button>
                      <Button
                        onClick={handleInvestClick}
                        size="md"
                        w="full"
                        variant="outline"
                        borderColor={primaryColor}
                        color={primaryColor}
                        fontWeight="600"
                        py={6}
                        borderWidth="2px"
                        _hover={{
                          bg: "rgba(240, 185, 11, 0.1)",
                          transform: "translateY(-3px)",
                          boxShadow: "0 8px 15px rgba(0, 0, 0, 0.1)"
                        }}
                        transition="all 0.3s ease"
                      >
                        {t('home.deposit.button', 'Investment Transaction')}
                      </Button>
                    </HStack>
                  )}
                </VStack>
              </Box>
            </GridItem>
          </Grid>
        </Container>
      </Box>


    </Box>
  );
};

export default Home;
