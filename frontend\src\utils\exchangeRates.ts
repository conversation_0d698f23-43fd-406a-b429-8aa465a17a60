/**
 * Exchange rates utility for cryptocurrency to USD conversion
 * Fetches real-time rates from API endpoint
 */

export interface ExchangeRate {
  symbol: string;
  name: string;
  usdRate: number;
  lastUpdated: string;
}

export interface CryptoPricesResponse {
  status: string;
  data: { [symbol: string]: number };
  timestamp: string;
  note?: string;
}

// Current exchange rates (should be updated regularly or fetched from API)
export const exchangeRates: Record<string, ExchangeRate> = {
  BTC: {
    symbol: 'BTC',
    name: 'Bitcoin',
    usdRate: 43250.00,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  ETH: {
    symbol: 'ETH',
    name: 'Ethereum',
    usdRate: 2650.00,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  USDT: {
    symbol: 'USDT',
    name: 'Tether',
    usdRate: 1.00,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  USDC: {
    symbol: 'USDC',
    name: 'USD Coin',
    usdRate: 1.00,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  BNB: {
    symbol: 'BNB',
    name: 'Binance Coin',
    usdRate: 315.50,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  XRP: {
    symbol: 'XRP',
    name: 'Ripple',
    usdRate: 0.52,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  ADA: {
    symbol: 'ADA',
    name: 'Cardano',
    usdRate: 0.48,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  SOL: {
    symbol: 'SOL',
    name: 'Solana',
    usdRate: 98.75,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  DOT: {
    symbol: 'DOT',
    name: 'Polkadot',
    usdRate: 7.25,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  DOGE: {
    symbol: 'DOGE',
    name: 'Dogecoin',
    usdRate: 0.085,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  LTC: {
    symbol: 'LTC',
    name: 'Litecoin',
    usdRate: 72.50,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  LINK: {
    symbol: 'LINK',
    name: 'Chainlink',
    usdRate: 14.80,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  XLM: {
    symbol: 'XLM',
    name: 'Stellar',
    usdRate: 0.12,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  XMR: {
    symbol: 'XMR',
    name: 'Monero',
    usdRate: 158.00,
    lastUpdated: '2024-01-15T10:00:00Z'
  },
  TRX: {
    symbol: 'TRX',
    name: 'TRON',
    usdRate: 0.112,
    lastUpdated: '2024-01-15T10:00:00Z'
  }
};

// Cache for API prices
let cachedPrices: { [symbol: string]: number } = {};
let lastFetchTime: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Fetch real-time cryptocurrency prices from API
 * @returns Promise with prices object
 */
export const fetchCryptoPrices = async (): Promise<{ [symbol: string]: number }> => {
  try {
    const response = await fetch(`${import.meta.env.VITE_API_URL}/crypto/prices`);
    const data: CryptoPricesResponse = await response.json();

    if (data.status === 'success' && data.data) {
      cachedPrices = data.data;
      lastFetchTime = Date.now();
      console.log('Fetched crypto prices from API:', data.data);
      return data.data;
    } else {
      throw new Error('Invalid API response');
    }
  } catch (error) {
    console.error('Error fetching crypto prices:', error);
    // Return fallback prices if API fails
    return {
      BTC: 43250.00,
      ETH: 2650.00,
      USDT: 1.00,
      USDC: 1.00,
      BNB: 315.50,
      XRP: 0.52,
      ADA: 0.48,
      SOL: 98.75,
      DOT: 7.25,
      DOGE: 0.085,
      LTC: 72.50,
      LINK: 14.80,
      XLM: 0.12,
      XMR: 158.00,
      TRX: 0.112
    };
  }
};

/**
 * Get USD exchange rate for a cryptocurrency (with API integration)
 * @param symbol Cryptocurrency symbol (e.g., BTC, ETH, TRX)
 * @returns USD exchange rate or 0 if not found
 */
export const getUSDRate = async (symbol: string): Promise<number> => {
  const now = Date.now();

  // Check if we need to fetch new data
  if (now - lastFetchTime > CACHE_DURATION || Object.keys(cachedPrices).length === 0) {
    await fetchCryptoPrices();
  }

  // Return cached price or fallback
  const price = cachedPrices[symbol.toUpperCase()];
  if (price !== undefined) {
    return price;
  }

  // Fallback to static rates if not found in API
  const fallbackRate = exchangeRates[symbol.toUpperCase()];
  return fallbackRate ? fallbackRate.usdRate : 0;
};

/**
 * Get USD exchange rate for a cryptocurrency (synchronous version with cache)
 * @param symbol Cryptocurrency symbol (e.g., BTC, ETH, TRX)
 * @returns USD exchange rate or 0 if not found
 */
export const getUSDRateSync = (symbol: string): number => {
  // Try cached API prices first
  const apiPrice = cachedPrices[symbol.toUpperCase()];
  if (apiPrice !== undefined) {
    return apiPrice;
  }

  // Fallback to static rates
  const rate = exchangeRates[symbol.toUpperCase()];
  return rate ? rate.usdRate : 0;
};

/**
 * Convert cryptocurrency amount to USD
 * @param amount Amount in cryptocurrency
 * @param symbol Cryptocurrency symbol
 * @returns USD equivalent value
 */
export const convertToUSD = (amount: number, symbol: string): number => {
  const rate = getUSDRate(symbol);
  return amount * rate;
};

/**
 * Format USD amount with proper currency formatting
 * @param amount USD amount
 * @param options Formatting options
 * @returns Formatted USD string
 */
export const formatUSD = (
  amount: number, 
  options: {
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    compact?: boolean;
  } = {}
): string => {
  const {
    minimumFractionDigits = 2,
    maximumFractionDigits = 2,
    compact = false
  } = options;

  if (compact && amount >= 1000000) {
    return `$${(amount / 1000000).toFixed(1)}M`;
  } else if (compact && amount >= 1000) {
    return `$${(amount / 1000).toFixed(1)}K`;
  }

  return `$${amount.toLocaleString('en-US', {
    minimumFractionDigits,
    maximumFractionDigits
  })}`;
};

/**
 * Get exchange rate info for a cryptocurrency
 * @param symbol Cryptocurrency symbol
 * @returns Exchange rate information or null if not found
 */
export const getExchangeRateInfo = (symbol: string): ExchangeRate | null => {
  return exchangeRates[symbol.toUpperCase()] || null;
};

/**
 * Check if exchange rate data is stale (older than 1 hour)
 * @param symbol Cryptocurrency symbol
 * @returns True if data is stale
 */
export const isRateStale = (symbol: string): boolean => {
  const rate = exchangeRates[symbol.toUpperCase()];
  if (!rate) return true;

  const lastUpdated = new Date(rate.lastUpdated);
  const now = new Date();
  const hoursDiff = (now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60);
  
  return hoursDiff > 1;
};

/**
 * Get all supported cryptocurrency symbols
 * @returns Array of supported symbols
 */
export const getSupportedSymbols = (): string[] => {
  return Object.keys(exchangeRates);
};

/**
 * Calculate percentage change (placeholder for future implementation)
 * @param symbol Cryptocurrency symbol
 * @returns Percentage change (currently returns 0)
 */
export const getPriceChange24h = (symbol: string): number => {
  // TODO: Implement real-time price change calculation
  // This would require historical price data or API integration
  return 0;
};

// Export default rates for backward compatibility
export default exchangeRates;
