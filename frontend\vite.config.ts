import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import compression from 'vite-plugin-compression'
import autoprefixer from 'autoprefixer'
import cssnano from 'cssnano'

export default defineConfig(({ mode }) => {
  // Load env variables
  const env = loadEnv(mode, process.cwd(), '')

  // Check if we're analyzing the bundle
  const isAnalyze = mode === 'analyze'

  // Check if we're in production-no-optimize mode
  const isProductionNoOptimize = mode === 'production-no-optimize'

  return {
    plugins: [
      react({
        jsxRuntime: 'automatic',
      }),
      // Only use compression when not in production-no-optimize mode
      !isProductionNoOptimize && compression({ algorithm: 'gzip', ext: '.gz' }),
      !isProductionNoOptimize && compression({ algorithm: 'brotliCompress', ext: '.br' }),
      isAnalyze && visualizer({
        open: true,
        filename: 'dist/stats.html',
        gzipSize: true,
        brotliSize: true,
      }),
    ].filter(<PERSON><PERSON>an),

    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },

    server: {
      port: 3000, // Changed to port 3000 as per user preference
      host: true,
      open: false,
      strictPort: false,
      hmr: {
        overlay: true,
        timeout: 5000,
        clientPort: 3000, // Port exposed to the host
        host: 'localhost', // Use 'localhost' for local development
      },
      watch: {
        usePolling: true,
        interval: 1000,
      },
      proxy: {
        '/api': {
          target: 'http://localhost:5000', // Local backend for development
          changeOrigin: true,
          secure: false,
        },
        '/uploads': {
          target: 'http://localhost:5000',
          changeOrigin: true,
          secure: false,
        },
        '/ws': {
          target: 'http://localhost:5000',
          changeOrigin: true,
          ws: true, // Important: Allow WebSocket and Socket.IO proxy
          secure: false,
        },
        '/socket.io': {
          target: 'http://localhost:5000',
          changeOrigin: true,
          ws: true, // Important: Allow Socket.IO proxy
          secure: false,
        },
      },
    },

    build: {
      // Always create sourcemap in development or production-no-optimize mode
      sourcemap: mode !== 'production' || isProductionNoOptimize,

      // Minify in production mode
      minify: mode === 'production' && !isProductionNoOptimize ? 'terser' : false,

      // Configure terser only when minify is enabled
      terserOptions: mode === 'production' && !isProductionNoOptimize ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.debug', 'console.info'],
        },
        format: {
          comments: false,
        }
      } : undefined,

      // Increase chunk size warning limit in non-optimized mode
      chunkSizeWarningLimit: isProductionNoOptimize ? 5000 : 1200,

      // Don't inline assets in non-optimized mode
      assetsInlineLimit: isProductionNoOptimize ? 0 : 4096,

      // Don't split CSS into chunks in non-optimized mode
      cssCodeSplit: !isProductionNoOptimize,

      // Report compressed size
      reportCompressedSize: isProductionNoOptimize,

      // Include all types of assets
      assetsInclude: ['**/*.jpg', '**/*.jpeg', '**/*.png', '**/*.gif', '**/*.svg', '**/*.webp', '**/*.avif'],

      // Configure rollup options
      rollupOptions: {
        output: isProductionNoOptimize ? {
          // Turn off manualChunks to not split bundle in non-optimized mode
          manualChunks: undefined,

          // Don't compress file names in non-optimized mode
          entryFileNames: 'assets/[name].js',
          chunkFileNames: 'assets/[name].js',
          assetFileNames: 'assets/[name].[ext]'
        } : {
          // Default configuration for production mode
        }
      }
    },

    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@chakra-ui/react',
        'react-icons',
        'i18next',
        'react-i18next',
        'recharts',
        'ethers',
      ],
      esbuildOptions: {
        target: 'es2020',
      },
    },

    css: {
      devSourcemap: true,
      // CSS optimization
      postcss: {
        plugins: [
          mode === 'production' && autoprefixer(),
          mode === 'production' && cssnano({
            preset: ['default', {
              discardComments: { removeAll: true },
              normalizeWhitespace: true,
              minifyFontValues: true,
              colormin: true,
            }]
          })
        ].filter(Boolean)
      },
    },
  }
})
