import { useState, useEffect, useCallback } from 'react';

interface ImpersonationState {
  isImpersonating: boolean;
  isHidden: boolean;
  adminInfo: any;
}

export const useImpersonation = () => {
  const [state, setState] = useState<ImpersonationState>({
    isImpersonating: false,
    isHidden: false,
    adminInfo: null
  });

  const checkImpersonationStatus = useCallback(() => {
    // Check if impersonation cookie exists
    const isImpersonatingCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('isImpersonating='));

    const isImpersonating = isImpersonatingCookie && isImpersonatingCookie.split('=')[1] === 'true';

    // Check if banner was previously hidden
    const bannerHidden = localStorage.getItem('impersonationBannerHidden') === 'true';

    // Get admin info
    let adminInfo = null;
    if (isImpersonating) {
      const storedAdminInfo = localStorage.getItem('impersonationAdminInfo');
      if (storedAdminInfo) {
        try {
          adminInfo = JSON.parse(storedAdminInfo);
        } catch (error) {
          console.error('Error parsing admin info:', error);
        }
      }
    }

    // Only update state if values have actually changed
    setState(prevState => {
      if (
        prevState.isImpersonating === isImpersonating &&
        prevState.isHidden === bannerHidden &&
        JSON.stringify(prevState.adminInfo) === JSON.stringify(adminInfo)
      ) {
        return prevState; // No change, return previous state
      }

      return {
        isImpersonating,
        isHidden: bannerHidden,
        adminInfo
      };
    });
  }, []);

  useEffect(() => {
    // Initial check
    checkImpersonationStatus();

    // Listen for storage changes (when banner is hidden/shown)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'impersonationBannerHidden' || e.key === 'impersonationAdminInfo') {
        checkImpersonationStatus();
      }
    };

    // Listen for focus events to check for cookie changes when user returns to tab
    const handleFocus = () => {
      checkImpersonationStatus();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('focus', handleFocus);

    // Check for cookie changes periodically (reduced frequency)
    const cookieCheckInterval = setInterval(checkImpersonationStatus, 10000); // Every 10 seconds

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('focus', handleFocus);
      clearInterval(cookieCheckInterval);
    };
  }, [checkImpersonationStatus]); // Include checkImpersonationStatus in dependencies

  // Computed values
  const isBannerVisible = state.isImpersonating && !state.isHidden;
  const shouldAdjustLayout = isBannerVisible;

  // Memoize the refresh function to prevent unnecessary re-renders
  const refreshStatus = useCallback(() => {
    checkImpersonationStatus();
  }, [checkImpersonationStatus]);

  return {
    ...state,
    isBannerVisible,
    shouldAdjustLayout,
    refreshStatus
  };
};
