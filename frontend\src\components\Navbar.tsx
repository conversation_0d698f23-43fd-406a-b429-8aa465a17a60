import { lazy, Suspense, useEffect, useRef, memo } from 'react';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import useAuth from '../hooks/useAuth';

// Core UI components - always loaded
import {
  Box,
  Flex,
  Text,
  Button,
  Stack,
  useDisclosure,
  IconButton,
  HStack,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Avatar,
  Drawer,
  DrawerBody,
  DrawerHeader,
  DrawerOverlay,
  DrawerContent,
  DrawerCloseButton,
  VStack,
  useBreakpointValue,
  Divider,
  Tooltip,
  Spinner,
  Icon
} from '@chakra-ui/react';
import { HamburgerIcon, ChevronDownIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { FaCoins, FaExchangeAlt, FaUserFriends, FaWallet } from 'react-icons/fa';

// Lazy loaded components
const TransactionNotifications = lazy(() => import('./TransactionNotifications'));
const ThemeSwitcher = lazy(() => import('./theme/ThemeSwitcher'));
// Import the wrapper instead of the direct component
import LanguageSwitcherWrapper from './LanguageSwitcherWrapper';

// Fallback for lazy loaded components
const LazyComponentFallback = () => (
  <Box display="flex" alignItems="center" justifyContent="center" h="24px" w="24px">
    <Spinner size="sm" color="#F0B90B" />
  </Box>
);

// Optimize with memo to prevent unnecessary re-renders
const Navbar = memo(() => {
  // Hooks
  const { isOpen, onToggle, onClose, onOpen } = useDisclosure();
  const { user, logout } = useAuth();
  const { t } = useTranslation("translation");
  const location = useLocation();
  const btnRef = useRef(null);

  // Responsive adjustments
  const isMobile = useBreakpointValue({ base: true, md: false });
  const logoSize = useBreakpointValue({ base: "20px", md: "24px" });
  const innerLogoSize = useBreakpointValue({ base: "12px", md: "16px" });
  const logoFontSize = useBreakpointValue({ base: "lg", md: "xl" });

  // Navigation items
  const navItems = [
    { name: t('common.home', 'Home'), path: '/' },
    { name: t('common.about', 'About Us'), path: '/about' },
    { name: t('common.contact', 'Contact'), path: '/contact' }
  ];

  // Check if path is active
  const isActive = (path) => location.pathname === path;

  // Close mobile menu when route changes
  useEffect(() => {
    // if (isOpen && isMobile) {
    //   onClose();
    // }
  }, [location.pathname, isOpen, isMobile, onClose]);

  // Handle logout with confirmation
  const handleLogout = () => {
    if (window.confirm(t('common.logoutConfirm', 'Are you sure you want to log out?'))) {
      logout();
    }
  };

  return (
    <Box position="sticky" top="0" zIndex="1000">
      <Flex
        bg="#0B0E11"
        color="#EAECEF"
        minH={{ base: '50px', md: '60px' }}
        py={{ base: 1, md: 2 }}
        px={{ base: 2, md: 4 }}
        borderBottom={1}
        borderStyle={'solid'}
        borderColor="#1E2329"
        align={'center'}
        justify={'space-between'}
        boxShadow="0 2px 10px rgba(0, 0, 0, 0.3)"
      >
        {/* Mobile hamburger menu button - Only on mobile */}
        <Flex
          flex={{ base: 1, md: 'auto' }}
          ml={{ base: -2 }}
          display={{ base: 'flex', md: 'none' }}
        >
          <IconButton
            ref={btnRef}
            onClick={onOpen}
            icon={<HamburgerIcon w={{ base: 4, md: 5 }} h={{ base: 4, md: 5 }} />}
            variant={'ghost'}
            aria-label={'Toggle Navigation'}
            _hover={{ bg: 'rgba(240, 185, 11, 0.1)' }}
            size={{ base: 'sm', md: 'md' }}
            color="#F0B90B"
          />
        </Flex>

        {/* Logo and desktop navigation */}
        <Flex flex={{ base: 1 }} justify={{ base: 'center', md: 'start' }} align="center">
          {/* Logo */}
          <Flex align="center">
            <Text
              as={RouterLink}
              to="/"
              textAlign={{ base: 'center', md: 'left' }}
              fontFamily={'heading'}
              color="#F0B90B"
              fontWeight="bold"
              fontSize={logoFontSize}
              display="flex"
              alignItems="center"
              _hover={{ textDecoration: 'none' }}
            >
              <Box
                w={{ base: '24px', md: logoSize }}
                h={{ base: '24px', md: logoSize }}
                bg="#F0B90B"
                borderRadius="md"
                mr={{ base: 1, md: 2 }}
                display="flex"
                alignItems="center"
                justifyContent="center"
                boxShadow="0 0 10px rgba(240, 185, 11, 0.3)"
              >
                <Box
                  w={{ base: '16px', md: innerLogoSize }}
                  h={{ base: '16px', md: innerLogoSize }}
                  bg="#0B0E11"
                  borderRadius="sm"
                />
              </Box>
              <Text display={{ base: isMobile ? 'none' : 'block', md: 'block' }}>
                Shipping Finance
              </Text>
            </Text>
          </Flex>

          {/* Desktop Navigation Links */}
          <HStack
            as={'nav'}
            spacing={{ md: 2, lg: 6 }}
            display={{ base: 'none', md: 'flex' }}
            ml={{ md: 2, lg: 10 }}
            flexWrap={{ md: 'wrap', lg: 'nowrap' }}
          >
            {navItems.map((item) => (
              <Tooltip
                key={item.path}
                label={item.name}
                placement="bottom"
                openDelay={500}
                bg="#1E2329"
                color="#EAECEF"
                hasArrow
                display={{ base: 'none', lg: 'block' }}
              >
                <Button
                  as={RouterLink}
                  to={item.path}
                  px={{ md: 1, lg: 2 }}
                  py={{ md: 0.5, lg: 1 }}
                  rounded={'md'}
                  variant="ghost"
                  color={isActive(item.path) ? "#F0B90B" : "#EAECEF"}
                  fontWeight={isActive(item.path) ? 600 : 500}
                  borderBottom={isActive(item.path) ? "2px solid #F0B90B" : "none"}
                  _hover={{
                    textDecoration: 'none',
                    color: '#F0B90B',
                    bg: 'rgba(240, 185, 11, 0.05)'
                  }}
                  size={{ md: "xs", lg: "sm" }}
                  fontSize={{ md: "xs", lg: "sm" }}
                >
                  {item.name}
                </Button>
              </Tooltip>
            ))}

            {/* Converter link */}
            <Tooltip
              label={t('common.converter', 'Converter')}
              placement="bottom"
              openDelay={500}
              bg="#1E2329"
              color="#EAECEF"
              hasArrow
              display={{ base: 'none', lg: 'block' }}
            >
              <Button
                as="a"
                href="/converter.html"
                px={{ md: 1, lg: 2 }}
                py={{ md: 0.5, lg: 1 }}
                rounded={'md'}
                variant="ghost"
                color="#EAECEF"
                fontWeight={500}
                _hover={{
                  textDecoration: 'none',
                  color: '#F0B90B',
                  bg: 'rgba(240, 185, 11, 0.05)'
                }}
                size={{ md: "xs", lg: "sm" }}
                fontSize={{ md: "xs", lg: "sm" }}
              >
                {t('common.converter', 'Converter')}
              </Button>
            </Tooltip>
          </HStack>
        </Flex>

        {/* Right side - Language switcher and user menu */}
        <Stack
          flex={{ base: 1, md: 0 }}
          justify={'flex-end'}
          direction={'row'}
          spacing={{ base: 2, md: 6 }}
          align="center"
        >
          {/* Theme Switcher */}
          <Box display={{ base: 'none', md: 'block' }}>
            <Suspense fallback={<LazyComponentFallback />}>
              <ThemeSwitcher variant="icon" size="sm" />
            </Suspense>
          </Box>

          {/* Language Switcher */}
          <Box display={{ base: 'none', md: 'block' }}>
            <LanguageSwitcherWrapper />
          </Box>

          {/* Desktop User Menu or Login/Register Buttons */}
          {user ? (
            <Menu>
              <MenuButton
                as={Button}
                variant={'ghost'}
                cursor={'pointer'}
                minW={0}
                rightIcon={<ChevronDownIcon color="#848E9C" />}
                color="#EAECEF"
                _hover={{
                  bg: 'rgba(240, 185, 11, 0.05)',
                }}
                borderRadius="sm"
                display={{ base: 'none', md: 'flex' }}
              >
                <HStack>
                  <Avatar
                    size={'xs'}
                    name={`${user.firstName} ${user.lastName}`}
                    bg="#F0B90B"
                    color="#0B0E11"
                    mr={1}
                    boxShadow="0 0 0 2px rgba(240, 185, 11, 0.3)"
                  />
                  <Text fontSize="sm" display={{ base: 'none', md: 'block' }}>{user.firstName}</Text>
                </HStack>
              </MenuButton>
              <MenuList bg="#1E2329" borderColor="#2B3139" boxShadow="xl">
                <MenuItem
                  as={RouterLink}
                  to="/profile"
                  bg="#1E2329"
                  _hover={{ bg: '#2B3139' }}
                  icon={<Avatar size="xs" name={`${user.firstName} ${user.lastName}`} bg="#F0B90B" color="#0B0E11" />}
                >
                  {t('common.profile')}
                </MenuItem>

                <MenuItem
                  as={RouterLink}
                  to="/payments"
                  bg="#1E2329"
                  _hover={{ bg: '#2B3139' }}
                >
                  {t('common.payments', 'Payment History')}
                </MenuItem>

                <MenuItem
                  as={RouterLink}
                  to="/wallet"
                  bg="#1E2329"
                  _hover={{ bg: '#2B3139' }}
                  icon={<Icon as={FaWallet} color="#F0B90B" />}
                >
                  {t('common.wallet', 'Wallet')}
                </MenuItem>

                <MenuItem
                  as={RouterLink}
                  to="/investments"
                  bg="#1E2329"
                  _hover={{ bg: '#2B3139' }}
                  icon={<Icon as={FaCoins} color="#F0B90B" />}
                >
                  {t('common.investments', 'Investments')}
                </MenuItem>

                <MenuItem
                  as={RouterLink}
                  to="/transactions"
                  bg="#1E2329"
                  _hover={{ bg: '#2B3139' }}
                  icon={<Icon as={FaExchangeAlt} color="#F0B90B" />}
                >
                  {t('common.transactions', 'Transactions')}
                </MenuItem>

                <MenuItem
                  as={RouterLink}
                  to="/referrals"
                  bg="#1E2329"
                  _hover={{ bg: '#2B3139' }}
                  icon={<Icon as={FaUserFriends} color="#F0B90B" />}
                >
                  {t('common.referrals', 'Referrals')}
                </MenuItem>

                <Divider borderColor="#2B3139" />
                <MenuItem
                  onClick={handleLogout}
                  bg="#1E2329"
                  _hover={{ bg: '#2B3139', color: 'red.400' }}
                  color="#EAECEF"
                >
                  {t('common.logout')}
                </MenuItem>
              </MenuList>
            </Menu>
          ) : (
            <>
              <Button
                as={RouterLink}
                fontSize={{ base: 'xs', md: 'sm' }}
                fontWeight={500}
                variant={'ghost'}
                color="#EAECEF"
                to={'/login'}
                _hover={{
                  bg: 'rgba(240, 185, 11, 0.05)',
                }}
                size={{ base: 'xs', md: 'sm' }}
                px={{ base: 1, md: 2 }}
                minW={{ base: "40px", md: "auto" }}
                display={{ base: 'none', md: 'inline-flex' }}
              >
                {t('common.login')}
              </Button>
              <Button
                as={RouterLink}
                display={{ base: 'none', md: 'inline-flex' }}
                fontSize={{ base: 'xs', md: 'sm' }}
                fontWeight={600}
                color={'#0B0E11'}
                bg={'#F0B90B'}
                to={'/register'}
                _hover={{
                  bg: '#F8D12F',
                  transform: { base: "none", lg: "translateY(-2px)" },
                  boxShadow: { base: "none", lg: "0 4px 8px rgba(0, 0, 0, 0.1)" }
                }}
                _active={{
                  bg: '#E0A800',
                  transform: "translateY(0)",
                }}
                transition="all 0.2s"
                borderRadius="sm"
                px={{ base: 4, md: 6 }}
                size={{ base: 'xs', md: 'sm' }}
                height={{ base: "28px", md: "32px" }}
              >
                {t('common.register')}
              </Button>
            </>
          )}
        </Stack>
      </Flex>

      {/* Mobile Navigation Drawer */}
      <Drawer
        isOpen={isOpen}
        placement="left"
        onClose={onClose}
        finalFocusRef={btnRef}
        size={{ base: "xs", sm: "sm" }}
      >
        <DrawerOverlay />
        <DrawerContent bg="#0B0E11" color="#EAECEF">
          <DrawerCloseButton color="#F0B90B" />
          <DrawerHeader borderBottomWidth="1px" borderColor="#1E2329">
            <Flex align="center">
              <Box
                w="24px"
                h="24px"
                bg="#F0B90B"
                borderRadius="md"
                mr={2}
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Box w="16px" h="16px" bg="#0B0E11" borderRadius="sm" />
              </Box>
              Shipping Finance
            </Flex>
          </DrawerHeader>
          <DrawerBody p={0}>
            <VStack align="stretch" spacing={0}>
              {/* Clean professional menu - no theme switcher or notifications */}

              {/* Mobile Navigation Items - Simplified */}
              {navItems.map((item) => (
                <Button
                  key={item.path}
                  as={RouterLink}
                  to={item.path}
                  variant="ghost"
                  justifyContent="flex-start"
                  bg={isActive(item.path) ? "rgba(240, 185, 11, 0.1)" : "transparent"}
                  color={isActive(item.path) ? "#F0B90B" : "#EAECEF"}
                  borderRadius={0}
                  borderLeft={isActive(item.path) ? "4px solid #F0B90B" : "4px solid transparent"}
                  _hover={{
                    bg: "rgba(240, 185, 11, 0.05)",
                    color: "#F0B90B"
                  }}
                  py={4}
                  onClick={onClose}
                  leftIcon={<ChevronRightIcon />}
                  fontSize="md"
                  fontWeight="500"
                >
                  {item.name}
                </Button>
              ))}

              <Divider borderColor="#1E2329" />

              {/* Auth Buttons for Mobile */}
              {!user ? (
                <>
                  <Button
                    as={RouterLink}
                    to="/login"
                    variant="ghost"
                    justifyContent="flex-start"
                    color="#EAECEF"
                    borderRadius={0}
                    _hover={{
                      bg: "rgba(240, 185, 11, 0.05)",
                      color: "#F0B90B"
                    }}
                    py={{ base: 4, sm: 6 }}
                    onClick={onClose}
                    leftIcon={<ChevronRightIcon />}
                    fontSize={{ base: "sm", sm: "md" }}
                    height={{ base: "auto", sm: "auto" }}
                  >
                    {t('common.login')}
                  </Button>
                  <Button
                    as={RouterLink}
                    to="/register"
                    variant="ghost"
                    justifyContent="flex-start"
                    color="#F0B90B"
                    fontWeight="600"
                    borderRadius={0}
                    _hover={{
                      bg: "rgba(240, 185, 11, 0.1)",
                      color: "#F0B90B"
                    }}
                    py={{ base: 4, sm: 6 }}
                    onClick={onClose}
                    leftIcon={<ChevronRightIcon />}
                    fontSize={{ base: "sm", sm: "md" }}
                    height={{ base: "auto", sm: "auto" }}
                  >
                    {t('common.register')}
                  </Button>
                </>
              ) : (
                <>
                  {/* User Profile Header */}
                  <Box p={4} borderBottom="1px solid #1E2329" bg="rgba(240, 185, 11, 0.05)">
                    <HStack spacing={3}>
                      <Avatar
                        size="md"
                        name={`${user.firstName} ${user.lastName}`}
                        bg="#F0B90B"
                        color="#0B0E11"
                        boxShadow="0 0 0 2px rgba(240, 185, 11, 0.3)"
                      />
                      <VStack align="start" spacing={0}>
                        <Text fontSize="md" fontWeight="600" color="#EAECEF">
                          {user.firstName} {user.lastName}
                        </Text>
                        <Text fontSize="sm" color="#848E9C">
                          {user.email}
                        </Text>
                      </VStack>
                    </HStack>
                  </Box>

                  {/* Essential Menu Items Only */}
                  <Button
                    as={RouterLink}
                    to="/profile"
                    variant="ghost"
                    justifyContent="flex-start"
                    color="#EAECEF"
                    borderRadius={0}
                    _hover={{
                      bg: "rgba(240, 185, 11, 0.05)",
                      color: "#F0B90B"
                    }}
                    py={4}
                    onClick={onClose}
                    leftIcon={<Icon as={FaUserFriends} color="#F0B90B" />}
                    fontSize="md"
                    fontWeight="500"
                  >
                    {t('common.profile')}
                  </Button>

                  <Button
                    as={RouterLink}
                    to="/wallet"
                    variant="ghost"
                    justifyContent="flex-start"
                    color="#EAECEF"
                    borderRadius={0}
                    _hover={{
                      bg: "rgba(240, 185, 11, 0.05)",
                      color: "#F0B90B"
                    }}
                    py={4}
                    onClick={onClose}
                    leftIcon={<Icon as={FaWallet} color="#F0B90B" />}
                    fontSize="md"
                    fontWeight="500"
                  >
                    {t('common.wallet', 'Wallet')}
                  </Button>

                  <Button
                    as={RouterLink}
                    to="/investments"
                    variant="ghost"
                    justifyContent="flex-start"
                    color="#EAECEF"
                    borderRadius={0}
                    _hover={{
                      bg: "rgba(240, 185, 11, 0.05)",
                      color: "#F0B90B"
                    }}
                    py={4}
                    onClick={onClose}
                    leftIcon={<Icon as={FaCoins} color="#F0B90B" />}
                    fontSize="md"
                    fontWeight="500"
                  >
                    {t('common.investments', 'Investments')}
                  </Button>
                  {/* Logout Button - Professional Style */}
                  <Box mt={6} pt={4} borderTop="1px solid #1E2329">
                    <Button
                      variant="ghost"
                      justifyContent="center"
                      color="#F84960"
                      borderRadius="md"
                      _hover={{
                        bg: "rgba(248, 73, 96, 0.1)",
                        color: "#F84960"
                      }}
                      py={3}
                      onClick={() => {
                        onClose();
                        handleLogout();
                      }}
                      fontSize="md"
                      fontWeight="500"
                      w="100%"
                      border="1px solid rgba(248, 73, 96, 0.2)"
                    >
                      {t('common.logout')}
                    </Button>
                  </Box>
                </>
              )}
            </VStack>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </Box>
  );
});

export default Navbar;
