import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ner,
  Alert,
  AlertIcon,
  useToast,
  Flex,
  Icon,
  Tooltip,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Divider
} from '@chakra-ui/react';
import { FaEye, FaCopy, FaExternalLinkAlt, FaSync } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import withdrawalService from '../services/withdrawalService';
import { getCryptoIcon, getCryptoColor } from '../utils/cryptoIcons';
import {
  getStatusColorScheme,
  getStatusLabel,
  getStatusDescription,
  getStatusBadgeProps,
  getTransactionTypeStatusMessage,
  getStatusAlert
} from '../utils/transactionStatusUtils';
import TransactionStatusBadge from './TransactionStatusBadge';

interface WithdrawalHistoryItem {
  id: string;
  cryptocurrency: string;
  withdrawalType: string;
  amount: number;
  usdValue: number;
  networkFee: number;
  netAmount: number;
  walletAddress: string;
  network: string;
  status: string;
  txHash?: string;
  adminNotes?: string;
  createdAt: string;
  updatedAt: string;
}

interface WithdrawalHistoryProps {
  limit?: number;
  showTitle?: boolean;
}

const WithdrawalHistory: React.FC<WithdrawalHistoryProps> = ({
  limit = 10,
  showTitle = true
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [withdrawals, setWithdrawals] = useState<WithdrawalHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<WithdrawalHistoryItem | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
    pages: 0
  });

  useEffect(() => {
    loadWithdrawals();
  }, []);

  const loadWithdrawals = async () => {
    try {
      setLoading(true);
      const result = await withdrawalService.getWithdrawalHistory(1, limit);
      setWithdrawals(result.withdrawals);
      setPagination({
        page: result.pagination.page,
        total: result.pagination.total,
        pages: result.pagination.pages
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const openDetailModal = (withdrawal: WithdrawalHistoryItem) => {
    setSelectedWithdrawal(withdrawal);
    onOpen();
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied',
      description: `${label} copied to clipboard`,
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  // Remove old getStatusColor function - now using utility

  const formatCurrency = (amount: number, currency: string) => {
    return `${amount.toFixed(6)} ${currency}`;
  };

  const formatUSD = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  const formatWithdrawalType = (type: string) => {
    const types: { [key: string]: string } = {
      balance: 'Main Balance',
      interest: 'Interest Earnings',
      commission: 'Commission Earnings'
    };
    return types[type] || type;
  };

  const getBlockExplorerUrl = (txHash: string, network: string) => {
    const explorers: { [key: string]: string } = {
      'Bitcoin': `https://blockstream.info/tx/${txHash}`,
      'Ethereum': `https://etherscan.io/tx/${txHash}`,
      'BSC': `https://bscscan.com/tx/${txHash}`,
      'Tron': `https://tronscan.org/#/transaction/${txHash}`,
      'Solana': `https://explorer.solana.com/tx/${txHash}`,
      'Polygon': `https://polygonscan.com/tx/${txHash}`
    };
    return explorers[network];
  };

  if (loading) {
    return (
      <Box p={6}>
        <Flex justify="center" align="center" h="200px">
          <Spinner size="lg" color="blue.500" />
        </Flex>
      </Box>
    );
  }

  return (
    <Box>
      {showTitle && (
        <HStack justify="space-between" mb={4}>
          <Text fontSize="xl" fontWeight="bold" color="white">
            Withdrawal History
          </Text>
          <Button
            leftIcon={<FaSync />}
            onClick={loadWithdrawals}
            size="sm"
            variant="outline"
            colorScheme="blue"
          >
            Refresh
          </Button>
        </HStack>
      )}

      {withdrawals.length === 0 ? (
        <Alert status="info" bg="blue.900" borderColor="blue.600">
          <AlertIcon />
          <Text>No withdrawal history found</Text>
        </Alert>
      ) : (
        <Box bg="gray.800" borderRadius="md" borderWidth="1px" borderColor="gray.600" overflow="hidden">
          <Table variant="simple" size="sm">
            <Thead bg="gray.700">
              <Tr>
                <Th color="gray.300">Crypto</Th>
                <Th color="gray.300">Type</Th>
                <Th color="gray.300">Amount</Th>
                <Th color="gray.300">Status</Th>
                <Th color="gray.300">Date</Th>
                <Th color="gray.300">Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {withdrawals.map((withdrawal) => (
                <Tr key={withdrawal.id} _hover={{ bg: "gray.700" }}>
                  <Td>
                    <HStack>
                      <Icon
                        as={getCryptoIcon(withdrawal.cryptocurrency)}
                        color={getCryptoColor(withdrawal.cryptocurrency)}
                        boxSize={4}
                      />
                      <Text color="white" fontSize="sm">
                        {withdrawal.cryptocurrency}
                      </Text>
                    </HStack>
                  </Td>
                  <Td>
                    <Text color="white" fontSize="sm">
                      {formatWithdrawalType(withdrawal.withdrawalType)}
                    </Text>
                  </Td>
                  <Td>
                    <VStack align="start" spacing={0}>
                      <Text color="white" fontSize="sm">
                        {formatCurrency(withdrawal.amount, withdrawal.cryptocurrency)}
                      </Text>
                      <Text color="gray.400" fontSize="xs">
                        {formatUSD(withdrawal.usdValue)}
                      </Text>
                    </VStack>
                  </Td>
                  <Td>
                    <TransactionStatusBadge
                      status={withdrawal.status}
                      transactionType="withdrawal"
                      size="sm"
                      showIcon={true}
                      showTooltip={true}
                    />
                  </Td>
                  <Td>
                    <Text color="white" fontSize="sm">
                      {new Date(withdrawal.createdAt).toLocaleDateString()}
                    </Text>
                  </Td>
                  <Td>
                    <Button
                      leftIcon={<FaEye />}
                      onClick={() => openDetailModal(withdrawal)}
                      size="xs"
                      variant="ghost"
                      colorScheme="blue"
                    >
                      Details
                    </Button>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>
      )}

      {/* Detail Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay bg="blackAlpha.800" />
        <ModalContent bg="gray.800" borderColor="gray.600">
          <ModalHeader color="white">
            Withdrawal Details
          </ModalHeader>
          <ModalCloseButton color="white" />
          <ModalBody pb={6}>
            {selectedWithdrawal && (
              <VStack spacing={4} align="stretch">
                {/* Status Alert */}
                {(() => {
                  const alertConfig = getStatusAlert(selectedWithdrawal.status);
                  return (
                    <Alert status={alertConfig.status} borderRadius="md" bg={alertConfig.bg}>
                      <AlertIcon color={alertConfig.color} />
                      <Box>
                        <Text fontWeight="bold" color={alertConfig.color}>
                          {alertConfig.title}
                        </Text>
                        <Text color="white" fontSize="sm">
                          {getTransactionTypeStatusMessage('withdrawal', selectedWithdrawal.status)}
                        </Text>
                      </Box>
                    </Alert>
                  );
                })()}

                {/* Basic Information */}
                <Box p={4} bg="gray.700" borderRadius="md">
                  <Text fontWeight="bold" color="white" mb={3}>
                    Transaction Information
                  </Text>
                  <VStack align="start" spacing={2}>
                    <HStack justify="space-between" w="full">
                      <Text color="gray.300">Cryptocurrency:</Text>
                      <HStack>
                        <Icon
                          as={getCryptoIcon(selectedWithdrawal.cryptocurrency)}
                          color={getCryptoColor(selectedWithdrawal.cryptocurrency)}
                          boxSize={4}
                        />
                        <Text color="white">{selectedWithdrawal.cryptocurrency}</Text>
                      </HStack>
                    </HStack>
                    <HStack justify="space-between" w="full">
                      <Text color="gray.300">Type:</Text>
                      <Text color="white">
                        {formatWithdrawalType(selectedWithdrawal.withdrawalType)}
                      </Text>
                    </HStack>
                    <HStack justify="space-between" w="full">
                      <Text color="gray.300">Network:</Text>
                      <Text color="white">{selectedWithdrawal.network}</Text>
                    </HStack>
                    <HStack justify="space-between" w="full">
                      <Text color="gray.300">Status:</Text>
                      <TransactionStatusBadge
                        status={selectedWithdrawal.status}
                        transactionType="withdrawal"
                        size="md"
                        showIcon={true}
                        showTooltip={false}
                      />
                    </HStack>
                  </VStack>
                </Box>

                {/* Amount Information */}
                <Box p={4} bg="gray.700" borderRadius="md">
                  <Text fontWeight="bold" color="white" mb={3}>
                    Amount Details
                  </Text>
                  <VStack align="start" spacing={2}>
                    <HStack justify="space-between" w="full">
                      <Text color="gray.300">Amount:</Text>
                      <Text color="white">
                        {formatCurrency(selectedWithdrawal.amount, selectedWithdrawal.cryptocurrency)}
                      </Text>
                    </HStack>
                    <HStack justify="space-between" w="full">
                      <Text color="gray.300">USD Value:</Text>
                      <Text color="white">{formatUSD(selectedWithdrawal.usdValue)}</Text>
                    </HStack>
                    <HStack justify="space-between" w="full">
                      <Text color="gray.300">Network Fee:</Text>
                      <Text color="white">
                        {formatCurrency(selectedWithdrawal.networkFee, selectedWithdrawal.cryptocurrency)}
                      </Text>
                    </HStack>
                    <Divider />
                    <HStack justify="space-between" w="full">
                      <Text color="gray.300" fontWeight="bold">Net Amount:</Text>
                      <Text color="white" fontWeight="bold">
                        {formatCurrency(selectedWithdrawal.netAmount, selectedWithdrawal.cryptocurrency)}
                      </Text>
                    </HStack>
                  </VStack>
                </Box>

                {/* Wallet Information */}
                <Box p={4} bg="gray.700" borderRadius="md">
                  <Text fontWeight="bold" color="white" mb={3}>
                    Destination Wallet
                  </Text>
                  <HStack justify="space-between" w="full">
                    <Text color="gray.300">Address:</Text>
                    <HStack>
                      <Text color="white" fontSize="sm" fontFamily="mono">
                        {selectedWithdrawal.walletAddress.substring(0, 10)}...
                        {selectedWithdrawal.walletAddress.substring(selectedWithdrawal.walletAddress.length - 10)}
                      </Text>
                      <Tooltip label="Copy address">
                        <Button
                          size="xs"
                          variant="ghost"
                          onClick={() => copyToClipboard(selectedWithdrawal.walletAddress, 'Wallet address')}
                        >
                          <FaCopy />
                        </Button>
                      </Tooltip>
                    </HStack>
                  </HStack>
                </Box>

                {/* Transaction Hash */}
                {selectedWithdrawal.txHash && (
                  <Box p={4} bg="gray.700" borderRadius="md">
                    <Text fontWeight="bold" color="white" mb={3}>
                      Transaction Hash
                    </Text>
                    <HStack justify="space-between" w="full">
                      <Text color="white" fontSize="sm" fontFamily="mono">
                        {selectedWithdrawal.txHash.substring(0, 10)}...
                        {selectedWithdrawal.txHash.substring(selectedWithdrawal.txHash.length - 10)}
                      </Text>
                      <HStack>
                        <Tooltip label="Copy transaction hash">
                          <Button
                            size="xs"
                            variant="ghost"
                            onClick={() => copyToClipboard(selectedWithdrawal.txHash!, 'Transaction hash')}
                          >
                            <FaCopy />
                          </Button>
                        </Tooltip>
                        {getBlockExplorerUrl(selectedWithdrawal.txHash, selectedWithdrawal.network) && (
                          <Tooltip label="View on block explorer">
                            <Button
                              size="xs"
                              variant="ghost"
                              as="a"
                              href={getBlockExplorerUrl(selectedWithdrawal.txHash, selectedWithdrawal.network)}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <FaExternalLinkAlt />
                            </Button>
                          </Tooltip>
                        )}
                      </HStack>
                    </HStack>
                  </Box>
                )}

                {/* Timestamps */}
                <Box p={4} bg="gray.700" borderRadius="md">
                  <Text fontWeight="bold" color="white" mb={3}>
                    Timeline
                  </Text>
                  <VStack align="start" spacing={2}>
                    <HStack justify="space-between" w="full">
                      <Text color="gray.300">Created:</Text>
                      <Text color="white">
                        {new Date(selectedWithdrawal.createdAt).toLocaleString()}
                      </Text>
                    </HStack>
                    <HStack justify="space-between" w="full">
                      <Text color="gray.300">Last Updated:</Text>
                      <Text color="white">
                        {new Date(selectedWithdrawal.updatedAt).toLocaleString()}
                      </Text>
                    </HStack>
                  </VStack>
                </Box>

                {/* Admin Notes */}
                {selectedWithdrawal.adminNotes && (
                  <Box p={4} bg="gray.700" borderRadius="md">
                    <Text fontWeight="bold" color="white" mb={3}>
                      Admin Notes
                    </Text>
                    <Text color="gray.300">{selectedWithdrawal.adminNotes}</Text>
                  </Box>
                )}
              </VStack>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default WithdrawalHistory;
