import axios, { AxiosResponse } from 'axios';
import DepositTransaction from '../models/depositTransactionModel';
import Wallet from '../models/walletModel';
import UserWallet from '../models/userWalletModel';
import cryptoApiService from './cryptoApiService';
import autoInvestmentService from './autoInvestmentService';
import { EventEmitter } from 'events';

interface BlockchainTransaction {
  hash: string;
  amount: number;
  confirmations: number;
  blockHeight?: number;
  timestamp: number;
  fee?: number;
}

interface BlockchainApiResponse {
  transactions: BlockchainTransaction[];
  address: string;
  balance: number;
}

class DepositMonitorService extends EventEmitter {
  private monitoringInterval: NodeJS.Timeout | null = null;
  private readonly POLL_INTERVAL = 30000; // 30 seconds
  private readonly API_ENDPOINTS = {
    BTC: 'https://blockstream.info/api',
    ETH: 'https://api.etherscan.io/api',
    USDT: 'https://api.etherscan.io/api', // USDT on Ethereum
    BNB: 'https://api.bscscan.com/api',
    ADA: 'https://cardano-mainnet.blockfrost.io/api/v0',
    DOT: 'https://polkadot.api.subscan.io/api/scan'
  };

  private readonly REQUIRED_CONFIRMATIONS = {
    BTC: 3,
    ETH: 12,
    USDT: 12,
    BNB: 15,
    ADA: 5,
    DOT: 2
  };

  constructor() {
    super();
    console.log('🔍 Deposit Monitor Service initialized');
  }

  /**
   * Start monitoring all active wallet addresses
   */
  async startMonitoring(): Promise<void> {
    if (this.monitoringInterval) {
      console.log('⚠️ Monitoring already running');
      return;
    }

    console.log('🚀 Starting deposit monitoring...');

    // Initial scan
    await this.scanAllAddresses();

    // Set up periodic scanning
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.scanAllAddresses();
      } catch (error) {
        console.error('❌ Error in monitoring cycle:', error);
        this.emit('error', error);
      }
    }, this.POLL_INTERVAL);

    console.log(`✅ Deposit monitoring started (polling every ${this.POLL_INTERVAL / 1000}s)`);
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('🛑 Deposit monitoring stopped');
    }
  }

  /**
   * Scan all active wallet addresses for new transactions
   */
  private async scanAllAddresses(): Promise<void> {
    try {
      const currencies = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA'];

      for (const currency of currencies) {
        await this.scanCurrencyAddresses(currency);
      }
    } catch (error) {
      console.error('❌ Error scanning addresses:', error);
      throw error;
    }
  }

  /**
   * Scan addresses for a specific currency
   */
  private async scanCurrencyAddresses(currency: string): Promise<void> {
    try {
      const wallets = await UserWallet.getActiveWalletsByCurrency(currency);

      for (const wallet of wallets) {
        await this.scanWalletAddress(wallet);
      }
    } catch (error) {
      console.error(`❌ Error scanning ${currency} addresses:`, error);
    }
  }

  /**
   * Scan a specific wallet address for new transactions
   */
  private async scanWalletAddress(wallet: any): Promise<void> {
    try {
      const transactions = await this.getAddressTransactions(wallet.address, wallet.currency);

      for (const tx of transactions) {
        await this.processTransaction(tx, wallet);
      }

      // Update wallet balance
      const balance = await this.getAddressBalance(wallet.address, wallet.currency);
      if (balance !== wallet.balance) {
        await wallet.updateBalance(balance);
        this.emit('balanceUpdated', {
          userId: wallet.userId,
          currency: wallet.currency,
          oldBalance: wallet.balance,
          newBalance: balance
        });
      }
    } catch (error) {
      console.error(`❌ Error scanning wallet ${wallet.address}:`, error);
    }
  }

  /**
   * Process a blockchain transaction
   */
  private async processTransaction(tx: BlockchainTransaction, wallet: any): Promise<void> {
    try {
      // Check if transaction already exists
      const existingTx = await DepositTransaction.findOne({ transactionHash: tx.hash });
      if (existingTx) {
        // Update confirmations if changed
        if (existingTx.confirmations !== tx.confirmations) {
          existingTx.confirmations = tx.confirmations;

          // Check if now confirmed
          const requiredConfirmations = this.REQUIRED_CONFIRMATIONS[wallet.currency as keyof typeof this.REQUIRED_CONFIRMATIONS] || 3;
          if (existingTx.confirmations >= requiredConfirmations && existingTx.status === 'pending') {
            await this.confirmTransaction(existingTx);
          }

          await existingTx.save();
        }
        return;
      }

      // Create new deposit transaction
      const usdtValue = await this.calculateUSDTValue(tx.amount, wallet.currency);
      const conversionRate = wallet.currency === 'USDT' ? 1 : usdtValue / tx.amount;

      const depositTx = new DepositTransaction({
        userId: wallet.userId,
        currency: wallet.currency,
        amount: tx.amount,
        walletAddress: wallet.address,
        transactionHash: tx.hash,
        confirmations: tx.confirmations,
        requiredConfirmations: this.REQUIRED_CONFIRMATIONS[wallet.currency as keyof typeof this.REQUIRED_CONFIRMATIONS] || 3,
        status: 'pending',
        blockHeight: tx.blockHeight,
        networkFee: tx.fee,
        usdtValue,
        conversionRate,
        autoInvestmentEnabled: true
      });

      await depositTx.save();

      console.log(`💰 New deposit detected: ${tx.amount} ${wallet.currency} (${usdtValue} USDT)`);

      this.emit('newDeposit', {
        transaction: depositTx,
        wallet,
        usdtValue
      });

      // Check if immediately confirmed
      if (depositTx.confirmations >= depositTx.requiredConfirmations) {
        await this.confirmTransaction(depositTx);
      }

    } catch (error) {
      console.error('❌ Error processing transaction:', error);
    }
  }

  /**
   * Confirm a transaction and trigger auto-investment
   */
  private async confirmTransaction(depositTx: any): Promise<void> {
    try {
      await depositTx.markAsConfirmed();

      console.log(`✅ Transaction confirmed: ${depositTx.transactionHash}`);

      this.emit('transactionConfirmed', {
        transaction: depositTx
      });

      // Trigger auto-investment if enabled and meets minimum
      if (depositTx.canCreateInvestment()) {
        this.emit('autoInvestmentReady', {
          transaction: depositTx
        });

        // Process auto-investment immediately
        try {
          await autoInvestmentService.processDepositForInvestment(depositTx);
        } catch (autoInvestError) {
          console.error('❌ Auto-investment processing failed:', autoInvestError);
        }
      }

    } catch (error) {
      console.error('❌ Error confirming transaction:', error);
    }
  }

  /**
   * Get transactions for an address (mock implementation)
   */
  private async getAddressTransactions(address: string, currency: string): Promise<BlockchainTransaction[]> {
    try {
      // Mock implementation - in production, this would call real blockchain APIs
      console.log(`🔍 Scanning ${currency} address: ${address}`);

      // Return empty array for now - real implementation would fetch from blockchain APIs
      return [];
    } catch (error) {
      console.error(`❌ Error fetching transactions for ${address}:`, error);
      return [];
    }
  }

  /**
   * Get balance for an address (mock implementation)
   */
  private async getAddressBalance(address: string, currency: string): Promise<number> {
    try {
      // Mock implementation - in production, this would call real blockchain APIs
      console.log(`💰 Checking ${currency} balance for: ${address}`);

      // Return mock balance
      return Math.random() * 10;
    } catch (error) {
      console.error(`❌ Error fetching balance for ${address}:`, error);
      return 0;
    }
  }

  /**
   * Calculate USDT value for an amount
   */
  private async calculateUSDTValue(amount: number, currency: string): Promise<number> {
    try {
      if (currency === 'USDT') {
        return amount;
      }

      const conversion = await cryptoApiService.convertCurrency(amount, currency, 'USDT');
      return conversion.amount;
    } catch (error) {
      console.error(`❌ Error calculating USDT value:`, error);

      // Fallback to mock rates
      const mockRates: { [key: string]: number } = {
        'BTC': 45000,
        'ETH': 3000,
        'BNB': 300,
        'ADA': 0.5,
        'DOT': 8
      };

      return amount * (mockRates[currency] || 1);
    }
  }

  /**
   * Get monitoring status
   */
  getStatus(): { isRunning: boolean; pollInterval: number; lastScan?: Date } {
    return {
      isRunning: this.monitoringInterval !== null,
      pollInterval: this.POLL_INTERVAL,
      lastScan: new Date()
    };
  }

  /**
   * Force scan for a specific address
   */
  async forceScan(address: string, currency: string): Promise<void> {
    try {
      const wallet = await UserWallet.getWalletByAddress(address);
      if (!wallet) {
        throw new Error(`Wallet not found for address: ${address}`);
      }

      await this.scanWalletAddress(wallet);
      console.log(`🔍 Force scan completed for ${address}`);
    } catch (error) {
      console.error(`❌ Error in force scan:`, error);
      throw error;
    }
  }
}

// Export singleton instance
const depositMonitorService = new DepositMonitorService();
export default depositMonitorService;
