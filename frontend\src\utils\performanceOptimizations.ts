/**
 * Performance optimizations to prevent infinite re-renders
 */

import { useCallback, useMemo, useRef, useEffect, useState } from 'react';
import { useToast } from '@chakra-ui/react';

/**
 * Debounce hook to prevent excessive function calls
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Throttle hook to limit function execution frequency
 */
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
};

/**
 * Stable callback hook to prevent unnecessary re-renders
 */
export const useStableCallback = <T extends (...args: any[]) => any>(
  callback: T
): T => {
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  });

  return useCallback(
    ((...args) => callbackRef.current(...args)) as T,
    []
  );
};

/**
 * Memoized timer hook for countdown components
 */
export const useCountdownTimer = (targetDate: Date) => {
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  const targetTime = useMemo(() => targetDate.getTime(), [targetDate]);

  useEffect(() => {
    if (!targetTime) return;

    const updateTimer = () => {
      const now = Date.now();
      const difference = targetTime - now;

      if (difference > 0) {
        const hours = Math.floor(difference / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ hours, minutes, seconds });
      } else {
        setTimeLeft({ hours: 0, minutes: 0, seconds: 0 });
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [targetTime]);

  return timeLeft;
};

/**
 * Optimized state updater to prevent unnecessary re-renders
 */
export const useOptimizedState = <T>(initialValue: T) => {
  const [state, setState] = useState<T>(initialValue);

  const setOptimizedState = useCallback((newValue: T | ((prev: T) => T)) => {
    setState(prev => {
      const nextValue = typeof newValue === 'function'
        ? (newValue as (prev: T) => T)(prev)
        : newValue;

      // Only update if value actually changed
      return JSON.stringify(prev) !== JSON.stringify(nextValue) ? nextValue : prev;
    });
  }, []);

  return [state, setOptimizedState] as const;
};

/**
 * Prevent component re-render on prop changes
 */
export const arePropsEqual = <T extends Record<string, any>>(
  prevProps: T,
  nextProps: T,
  excludeKeys: string[] = []
): boolean => {
  const prevKeys = Object.keys(prevProps).filter(key => !excludeKeys.includes(key));
  const nextKeys = Object.keys(nextProps).filter(key => !excludeKeys.includes(key));

  if (prevKeys.length !== nextKeys.length) {
    return false;
  }

  for (const key of prevKeys) {
    if (prevProps[key] !== nextProps[key]) {
      return false;
    }
  }

  return true;
};

/**
 * Optimized interval hook that cleans up properly
 */
export const useOptimizedInterval = (
  callback: () => void,
  delay: number | null,
  dependencies: any[] = []
) => {
  const savedCallback = useRef(callback);

  useEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  useEffect(() => {
    if (delay === null) return;

    const tick = () => savedCallback.current();
    const id = setInterval(tick, delay);

    return () => clearInterval(id);
  }, [delay, ...dependencies]);
};

/**
 * Memoized crypto balance calculator
 */
export const useCryptoBalanceCalculator = (
  packages: any[],
  exchangeRates: Record<string, number>
) => {
  return useMemo(() => {
    const balances = packages.reduce((acc, pkg) => {
      const currency = pkg.currency;
      const balance = pkg.amount + pkg.totalEarned;
      const usdtValue = currency === 'USDT'
        ? balance
        : balance * (exchangeRates[currency] || 0);

      if (!acc[currency]) {
        acc[currency] = {
          symbol: currency,
          balance: 0,
          usdtValue: 0,
          dailyInterest: 0
        };
      }

      acc[currency].balance += balance;
      acc[currency].usdtValue += usdtValue;
      acc[currency].dailyInterest += pkg.dailyInterest || 0;

      return acc;
    }, {} as Record<string, any>);

    return Object.values(balances);
  }, [packages, exchangeRates]);
};

/**
 * Optimized toast notifications to prevent spam
 */
export const useOptimizedToast = () => {
  const toastRef = useRef<Set<string>>(new Set());
  const { toast } = useToast();

  const showToast = useCallback((options: any) => {
    const key = `${options.title}-${options.status}`;

    if (toastRef.current.has(key)) {
      return; // Prevent duplicate toasts
    }

    toastRef.current.add(key);

    toast({
      ...options,
      onCloseComplete: () => {
        toastRef.current.delete(key);
      }
    });

    // Auto-cleanup after 5 seconds
    setTimeout(() => {
      toastRef.current.delete(key);
    }, 5000);
  }, [toast]);

  return showToast;
};
