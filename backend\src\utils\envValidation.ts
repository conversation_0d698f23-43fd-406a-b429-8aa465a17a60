import { logger } from './logger';

interface RequiredEnvVars {
  JWT_SECRET: string;
  MONGO_URI: string;
  NODE_ENV: string;
}

interface OptionalEnvVars {
  PORT?: string;
  LOG_LEVEL?: string;
  FRONTEND_URL?: string;
}

/**
 * Validate that all required environment variables are set
 */
export const validateEnvironmentVariables = (): void => {
  const requiredVars: (keyof RequiredEnvVars)[] = [
    'JWT_SECRET',
    'MONGO_URI',
    'NODE_ENV'
  ];

  const missing: string[] = [];
  const warnings: string[] = [];

  // Check required variables
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (!value || value.trim() === '') {
      missing.push(varName);
    } else {
      // Validate specific requirements
      switch (varName) {
        case 'JWT_SECRET':
          if (value.length < 32) {
            warnings.push(`JWT_SECRET should be at least 32 characters long for security`);
          }
          break;
        case 'MONGO_URI':
          if (!value.startsWith('mongodb://') && !value.startsWith('mongodb+srv://')) {
            warnings.push(`MONGO_URI should start with mongodb:// or mongodb+srv://`);
          }
          break;
        case 'NODE_ENV':
          if (!['development', 'production', 'test'].includes(value)) {
            warnings.push(`NODE_ENV should be 'development', 'production', or 'test'`);
          }
          break;
      }
    }
  });

  // Log warnings
  warnings.forEach(warning => {
    logger.warn(`Environment variable warning: ${warning}`);
  });

  // Throw error if required variables are missing
  if (missing.length > 0) {
    const errorMessage = `Missing required environment variables: ${missing.join(', ')}`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // Log successful validation
  logger.info('Environment variables validated successfully', {
    nodeEnv: process.env.NODE_ENV,
    hasJwtSecret: !!process.env.JWT_SECRET,
    hasMongoUri: !!process.env.MONGO_URI,
    port: process.env.PORT || '5000'
  });
};

/**
 * Get environment-specific configuration
 */
export const getEnvConfig = () => {
  return {
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',
    isTest: process.env.NODE_ENV === 'test',
    port: parseInt(process.env.PORT || '5000', 10),
    jwtSecret: process.env.JWT_SECRET!,
    mongoUri: process.env.MONGO_URI!,
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
    logLevel: process.env.LOG_LEVEL || 'info'
  };
};

/**
 * Setup default environment variables for development
 */
export const setupDefaultEnvVars = (): void => {
  // Only set defaults in development mode
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  const defaults = {
    JWT_SECRET: 'your-super-secret-jwt-key-that-should-be-changed-in-production-and-should-be-at-least-32-characters-long',
    MONGO_URI: 'mongodb://localhost:27017/cryptoyieldhub',
    PORT: '5000',
    LOG_LEVEL: 'debug',
    FRONTEND_URL: 'http://localhost:3000'
  };

  Object.entries(defaults).forEach(([key, value]) => {
    if (!process.env[key]) {
      process.env[key] = value;
      logger.warn(`Using default value for ${key} in development mode`);
    }
  });
};
