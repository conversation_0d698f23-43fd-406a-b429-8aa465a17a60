import { useEffect, useRef } from 'react';
import { Box, Flex, Text, Select, Button, HStack } from '@chakra-ui/react';
import ChartWrapper from './ChartWrapper';
import {
  createChart,
  CrosshairMode,
  IChartApi,
  ISeriesApi,
  LineStyle,
  SeriesType,
  Time,
  AreaData
} from 'lightweight-charts';

interface CryptoChartProps {
  symbol: string;
  interval?: string;
}

// Sample data for the chart
const generateSampleData = (days: number, startPrice: number, volatility: number): AreaData<Time>[] => {
  const data: AreaData<Time>[] = [];
  let currentPrice = startPrice;
  const now = new Date();

  for (let i = days; i >= 0; i--) {
    const time = new Date(now);
    time.setDate(now.getDate() - i);

    // Generate a random price movement
    const change = (Math.random() - 0.5) * volatility;
    currentPrice = Math.max(0.1, currentPrice * (1 + change));

    data.push({
      time: time.getTime() / 1000 as Time,
      value: currentPrice,
    });
  }

  return data;
};

// Sample data for different cryptocurrencies
const cryptoData: Record<string, AreaData<Time>[]> = {
  'BTCUSDT': generateSampleData(30, 60000, 0.03),
  'ETHUSDT': generateSampleData(30, 3000, 0.04),
  'BNBUSDT': generateSampleData(30, 500, 0.035),
  'SOLUSDT': generateSampleData(30, 120, 0.05),
  'ADAUSDT': generateSampleData(30, 0.5, 0.04),
};

const CryptoChart = ({ symbol = 'BTCUSDT', interval = '1d' }: CryptoChartProps) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const seriesRef = useRef<ISeriesApi<any> | null>(null);

  useEffect(() => {
    if (chartContainerRef.current) {
      // Clear previous chart if it exists
      if (chartRef.current) {
        chartRef.current.remove();
      }

      // Create a new chart
      const chart = createChart(chartContainerRef.current, {
        width: chartContainerRef.current.clientWidth,
        height: 400,
        layout: {
          background: { color: '#0B0E11' },
          textColor: '#EAECEF',
        },
        grid: {
          vertLines: { color: '#1E2329', style: LineStyle.Dotted },
          horzLines: { color: '#1E2329', style: LineStyle.Dotted },
        },
        crosshair: {
          mode: CrosshairMode.Normal,
          vertLine: {
            color: '#F0B90B',
            width: 1,
            style: LineStyle.Solid,
            labelBackgroundColor: '#F0B90B',
          },
          horzLine: {
            color: '#F0B90B',
            width: 1,
            style: LineStyle.Solid,
            labelBackgroundColor: '#F0B90B',
          },
        },
        timeScale: {
          borderColor: '#1E2329',
          timeVisible: true,
        },
        rightPriceScale: {
          borderColor: '#1E2329',
        },
      });

      // Create the area series
      const areaSeries = chart.addSeries({
        type: 'Area' as SeriesType,
        topColor: 'rgba(240, 185, 11, 0.4)',
        bottomColor: 'rgba(240, 185, 11, 0.1)',
        lineColor: '#F0B90B',
        lineWidth: 2,
      } as any);

      // Set the data
      const data = cryptoData[symbol as keyof typeof cryptoData] || cryptoData.BTCUSDT;
      // Convert data to the format expected by the chart
      areaSeries.setData(data);

      // Fit the chart to the data
      chart.timeScale().fitContent();

      // Save references
      chartRef.current = chart;
      seriesRef.current = areaSeries;

      // Handle resize
      const handleResize = () => {
        if (chartContainerRef.current && chartRef.current) {
          chartRef.current.applyOptions({
            width: chartContainerRef.current.clientWidth
          });
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (chartRef.current) {
          chartRef.current.remove();
        }
      };
    }
  }, [symbol, interval]);

  // Get the current price from the last data point
  const currentPrice = cryptoData[symbol as keyof typeof cryptoData]?.slice(-1)[0]?.value.toFixed(2) || '0';

  // Calculate a random 24h change
  const change24h = (Math.random() * 10 - 5).toFixed(2);
  const isPositive = parseFloat(change24h) >= 0;

  return (
    <Box
      bg="#1E2329"
      borderRadius="md"
      p={4}
      borderWidth="1px"
      borderColor="#2B3139"
      mb={6}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <HStack spacing={4}>
          <Text fontSize="xl" fontWeight="bold" color="#EAECEF">{symbol}</Text>
          <Text fontSize="xl" color="#EAECEF">${currentPrice}</Text>
          <Text
            fontSize="md"
            color={isPositive ? "#0ECB81" : "#F6465D"}
          >
            {isPositive ? "+" : ""}{change24h}%
          </Text>
        </HStack>
        <HStack spacing={2}>
          <Select
            size="sm"
            bg="#0B0E11"
            borderColor="#2B3139"
            color="#EAECEF"
            _hover={{ borderColor: "#F0B90B" }}
            _focus={{ borderColor: "#F0B90B", boxShadow: "none" }}
            w="100px"
            value={interval}
          >
            <option value="1m">1m</option>
            <option value="5m">5m</option>
            <option value="15m">15m</option>
            <option value="1h">1h</option>
            <option value="4h">4h</option>
            <option value="1d">1d</option>
            <option value="1w">1w</option>
          </Select>
          <Button
            size="sm"
            bg="#0B0E11"
            color="#F0B90B"
            borderWidth="1px"
            borderColor="#2B3139"
            _hover={{ bg: "#1E2329" }}
          >
            Indicators
          </Button>
          <Button
            size="sm"
            bg="#0B0E11"
            color="#F0B90B"
            borderWidth="1px"
            borderColor="#2B3139"
            _hover={{ bg: "#1E2329" }}
          >
            Full Screen
          </Button>
        </HStack>
      </Flex>
      <ChartWrapper height="400px">
        <Box ref={chartContainerRef} w="100%" h="400px" />
      </ChartWrapper>
    </Box>
  );
};

export default CryptoChart;
