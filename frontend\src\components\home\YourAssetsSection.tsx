import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  SimpleGrid,
  useDisclosure,
  useToast,
  Spinner,
  Center,
  Icon,
  Button,
  HStack
} from '@chakra-ui/react';
import { FaWallet, FaCoins, FaArrowRight } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import useAuth from '../../hooks/useAuth';

// Import components
import InvestmentCard from '../InvestmentCard';
import DepositModal from '../modals/DepositModal';


// Import services
import { investmentService } from '../../services/investmentService';
import { investmentBalanceService } from '../../services/investmentBalanceService';

// Import types
import { Investment, GroupedInvestment } from '../../types/investment';

const YourAssetsSection: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const toast = useToast();
  const navigate = useNavigate();

  // State
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalEarned, setTotalEarned] = useState(0);
  const [selectedCrypto, setSelectedCrypto] = useState('bitcoin');

  // Modal states
  const {
    isOpen: isDepositOpen,
    onOpen: onDepositOpen,
    onClose: onDepositClose
  } = useDisclosure();

  const {
    isOpen: isWithdrawOpen,
    onOpen: onWithdrawOpen,
    onClose: onWithdrawClose
  } = useDisclosure();

  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Fetch investments
  const fetchInvestments = useCallback(async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const [investmentsResponse, balancesData] = await Promise.all([
        investmentService.getInvestments(),
        investmentBalanceService.getInvestmentBalances()
      ]);

      // Extract investments array from response
      const investmentsData = investmentsResponse?.data?.investments || [];
      setInvestments(Array.isArray(investmentsData) ? investmentsData : []);

      // Calculate total earned from balances
      const balances = Array.isArray(balancesData) ? balancesData : [];
      const totalEarnings = balances.reduce((sum, balance) => sum + (balance.totalEarnings || 0), 0);
      setTotalEarned(totalEarnings);
    } catch (error) {
      console.error('Error fetching investments:', error);
      setInvestments([]); // Set empty array on error
      setTotalEarned(0);
      toast({
        title: t('common.error', 'Error'),
        description: t('investments.fetchError', 'Failed to load investments'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  }, [user, t, toast]);

  useEffect(() => {
    fetchInvestments();
  }, [fetchInvestments]);

  // Group investments by currency
  const groupedInvestments = React.useMemo(() => {
    // Ensure investments is an array before processing
    if (!Array.isArray(investments) || investments.length === 0) {
      return [];
    }

    const grouped = investments.reduce((acc, investment) => {
      const currency = investment.currency;
      if (!acc[currency]) {
        acc[currency] = {
          currency,
          investments: [],
          totalAmount: 0,
          firstInvestmentDate: investment.createdAt,
          networks: [],
          addresses: []
        };
      }

      acc[currency].investments.push(investment);
      acc[currency].totalAmount += investment.adminVerifiedAmount || investment.amount;

      if (new Date(investment.createdAt) < new Date(acc[currency].firstInvestmentDate)) {
        acc[currency].firstInvestmentDate = investment.createdAt;
      }

      if (investment.network && !acc[currency].networks.includes(investment.network)) {
        acc[currency].networks.push(investment.network);
      }

      if (investment.address && !acc[currency].addresses.includes(investment.address)) {
        acc[currency].addresses.push(investment.address);
      }

      return acc;
    }, {} as Record<string, GroupedInvestment>);

    return Object.values(grouped);
  }, [investments]);

  // Filter only approved investments
  const approvedGroupedInvestments = groupedInvestments.filter(group =>
    group.investments.some(inv => inv.status === 'approved')
  );

  // Handle actions
  const handleDepositClick = (currency: string) => {
    setSelectedCrypto(currency);
    onDepositOpen();
  };

  const handleWithdrawClick = (currency: string) => {
    setSelectedCrypto(currency);
    onWithdrawOpen();
  };

  const handleViewDetails = (currency: string) => {
    navigate('/profile?tab=investments');
  };

  // Don't render if user is not logged in
  if (!user) {
    return null;
  }

  return (
    <>
      {/* Modals */}
      <DepositModal isOpen={isDepositOpen} onClose={onDepositClose} />
      <ThreeStepWithdrawModal
        isOpen={isWithdrawOpen}
        onClose={onWithdrawClose}
        initialCrypto={selectedCrypto}
      />

      {/* Your Assets Section */}
      <Box
        py={{ base: 6, md: 8 }}
        bg={bgColor}
        position="relative"
      >
        <Container maxW="container.xl">
          <VStack spacing={6} align="stretch">
            {/* Section Header */}
            <VStack spacing={3} align="center" textAlign="center">
              <Heading
                as="h2"
                size={{ base: "lg", md: "xl" }}
                color={primaryColor}
                fontWeight="800"
                textShadow="0 2px 4px rgba(0,0,0,0.3)"
              >
                {t('home.yourAssets.title', 'Your Assets')}
              </Heading>
              <Text
                fontSize={{ base: "md", md: "lg" }}
                color={secondaryTextColor}
                maxW="600px"
                lineHeight="1.6"
              >
                {t('home.yourAssets.description', 'Manage your cryptocurrency investments and track your earnings')}
              </Text>
            </VStack>

            {/* Content */}
            {loading ? (
              <Center py={12}>
                <VStack spacing={4}>
                  <Spinner size="xl" color={primaryColor} thickness="4px" />
                  <Text color={secondaryTextColor}>
                    {t('investments.loading', 'Loading your investments...')}
                  </Text>
                </VStack>
              </Center>
            ) : approvedGroupedInvestments.length > 0 ? (
              <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                {approvedGroupedInvestments.map((groupedInvestment) => (
                  <InvestmentCard
                    key={groupedInvestment.currency}
                    groupedInvestment={groupedInvestment}
                    totalEarned={totalEarned}
                    onViewDetails={handleViewDetails}
                    onDepositClick={handleDepositClick}
                    onWithdrawClick={handleWithdrawClick}
                  />
                ))}
              </SimpleGrid>
            ) : (
              <Center py={12}>
                <VStack spacing={6} textAlign="center">
                  <Icon as={FaWallet} color={secondaryTextColor} boxSize={16} />
                  <VStack spacing={3}>
                    <Heading size="md" color={textColor}>
                      {t('home.yourAssets.noInvestments', 'No Investments Yet')}
                    </Heading>
                    <Text color={secondaryTextColor} maxW="400px" lineHeight="1.6">
                      {t('home.yourAssets.noInvestmentsDesc', 'Start your investment journey by depositing cryptocurrency and earning 1% daily returns.')}
                    </Text>
                  </VStack>
                  <HStack spacing={4}>
                    <Button
                      leftIcon={<Icon as={FaCoins} />}
                      bg={primaryColor}
                      color={bgColor}
                      _hover={{
                        bg: "#F8D12F",
                        transform: "translateY(-2px)"
                      }}
                      _active={{
                        bg: "#E6C200",
                        transform: "translateY(0px)"
                      }}
                      size="lg"
                      fontWeight="700"
                      onClick={() => onDepositOpen()}
                      boxShadow="0 4px 12px rgba(240, 185, 11, 0.3)"
                      transition="all 0.3s ease"
                    >
                      {t('common.startInvesting', 'Start Investing')}
                    </Button>
                    <Button
                      rightIcon={<Icon as={FaArrowRight} />}
                      variant="outline"
                      borderColor={primaryColor}
                      color={primaryColor}
                      _hover={{
                        bg: "rgba(240, 185, 11, 0.1)",
                        transform: "translateY(-2px)"
                      }}
                      size="lg"
                      fontWeight="600"
                      onClick={() => navigate('/about')}
                      transition="all 0.3s ease"
                    >
                      {t('common.learnMore', 'Learn More')}
                    </Button>
                  </HStack>
                </VStack>
              </Center>
            )}
          </VStack>
        </Container>
      </Box>
    </>
  );
};

export default YourAssetsSection;
