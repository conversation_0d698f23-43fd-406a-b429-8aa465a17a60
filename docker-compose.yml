services:
  mongodb:
    image: mongo:6.0
    container_name: cryptoyield-mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USER}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
      MONGO_INITDB_DATABASE: cryptoyield
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
      - ./mongodb-keyfile:/tmp/keyfile-source:ro
    networks:
      - cryptoyield-network
    ports:
      - "27017:27017"
    command: >
      bash -c "
        if [ ! -f /data/db/keyfile ]; then
          echo 'Generating keyfile...'
          cp /tmp/keyfile-source/mongodb-keyfile /data/db/keyfile
          chmod 400 /data/db/keyfile
          chown 999:999 /data/db/keyfile
        fi
        exec docker-entrypoint.sh mongod --replSet rs0 --keyFile /data/db/keyfile --bind_ip_all
      "
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cryptoyield-backend
    restart: always
    depends_on:
      mongodb:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - MONGO_URI=mongodb://cryptoyield_admin:${MONGO_PASSWORD}@mongodb:27017/cryptoyield?replicaSet=rs0&authSource=admin
      - JWT_SECRET=${JWT_SECRET}
      - PORT=5000
      - FRONTEND_URL=${FRONTEND_URL}
      - CONTRACT_ADDRESS=${CONTRACT_ADDRESS}
      - PROVIDER_URL=${PROVIDER_URL}
    ports:
      - "5000:5000"
    volumes:
      - uploads_data:/app/uploads
    networks:
      - cryptoyield-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/health"]
      interval: 15s
      timeout: 10s
      retries: 10

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - API_URL=${API_URL}
        - CONTRACT_ADDRESS=${CONTRACT_ADDRESS}
        - INFURA_ID=${INFURA_ID}
        - STORAGE_KEY=${STORAGE_KEY}
        - SOCKET_URL=${SOCKET_URL}
    container_name: cryptoyield-frontend
    restart: always
    depends_on:
      backend:
        condition: service_healthy
    ports:
      - "3001:80"
    networks:
      - cryptoyield-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M


networks:
  cryptoyield-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
  uploads_data:
    driver: local