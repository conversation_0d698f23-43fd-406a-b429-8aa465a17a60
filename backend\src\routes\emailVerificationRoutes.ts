import express from 'express';
import {
  sendVerificationEmail,
  verifyEmail,
  resendVerificationEmail,
  getVerificationStatus,
  adminVerifyEmail
} from '../controllers/emailVerificationController';
import { protect, admin } from '../middleware/authMiddleware';
import { 
  authRateLimit,
  sensitiveOperationRateLimit 
} from '../middleware/rateLimitMiddleware';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

/**
 * Public Routes
 */

// Verify email with token (public route)
router.get('/verify/:token', verifyEmail);

/**
 * Protected Routes (require authentication)
 */

// Send verification email
router.post(
  '/send', 
  protect, 
  authRateLimit, 
  wrapController(sendVerificationEmail)
);

// Resend verification email
router.post(
  '/resend', 
  protect, 
  authRateLimit, 
  wrapController(resendVerificationEmail)
);

// Get verification status
router.get(
  '/status', 
  protect, 
  wrapController(getVerificationStatus)
);

/**
 * Admin Routes (require admin privileges)
 */

// Admin manually verify user email
router.put(
  '/admin/:id/verify', 
  protect, 
  admin, 
  sensitiveOperationRateLimit, 
  wrapController(adminVerifyEmail)
);

export default router;
