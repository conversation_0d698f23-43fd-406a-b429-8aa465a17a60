import { useState } from 'react';
import {
  Box,
  Button,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Image,
  useColorModeValue,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import useAuth from '../hooks/useAuth';

interface WalletConnectProps {
  onConnect: (address: string) => void;
}

const WalletConnect = ({ onConnect }: WalletConnectProps) => {
  const [connecting, setConnecting] = useState(false);
  const [error, setError] = useState('');
  const { updateProfile } = useAuth();

  const connectMetaMask = async () => {
    try {
      setConnecting(true);
      setError('');

      // Check if MetaMask is installed
      if (!(window as any).ethereum) {
        throw new Error('MetaMask is not installed. Please install MetaMask to continue.');
      }

      // Request account access
      const accounts = await (window as any).ethereum.request({
        method: 'eth_requestAccounts',
      });

      const address = accounts[0];

      // Update user profile with wallet address
      await updateProfile({ walletAddress: address });

      // Notify parent component
      onConnect(address);
    } catch (err: any) {
      console.error('Error connecting wallet:', err);
      setError(err.message || 'Failed to connect wallet. Please try again.');
    } finally {
      setConnecting(false);
    }
  };



  return (
    <Container maxW="container.md" py={12}>
      <Box
        bg={useColorModeValue('white', 'gray.700')}
        p={8}
        borderRadius="lg"
        boxShadow="lg"
        textAlign="center"
      >
        <VStack spacing={6}>
          <Heading size="lg">Connect Your Wallet</Heading>
          <Text color="gray.600">
            Connect your wallet to start using CryptoYieldHub and earn commission or interest on your crypto assets.
          </Text>

          {error && (
            <Alert status="error" borderRadius="md">
              <AlertIcon />
              {error}
            </Alert>
          )}

          <Box w="full" py={4}>
            <VStack spacing={4} align="stretch">
              <Button
                size="lg"
                height="16"
                onClick={connectMetaMask}
                isLoading={connecting}
                loadingText="Connecting..."
                leftIcon={
                  <Image
                    src="https://upload.wikimedia.org/wikipedia/commons/3/36/MetaMask_Fox.svg"
                    alt="MetaMask"
                    boxSize="24px"
                  />
                }
              >
                Connect with MetaMask
              </Button>


            </VStack>
          </Box>

          <Text fontSize="sm" color="gray.500">
            By connecting your wallet, you agree to our Terms of Service and Privacy Policy.
          </Text>
        </VStack>
      </Box>
    </Container>
  );
};

export default WalletConnect;
