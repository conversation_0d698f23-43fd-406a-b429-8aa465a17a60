import { Registry, collectDefaultMetrics, Histogram, Counter, Gauge } from 'prom-client';
import { Request, Response, NextFunction } from 'express';

export const metrics = new Registry();

// Custom metrics
const httpRequestDurationMicroseconds = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
  registers: [metrics]
});

const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
  registers: [metrics]
});

const errorRate = new Counter({
  name: 'error_rate_total',
  help: 'Total number of errors',
  labelNames: ['type', 'route'],
  registers: [metrics]
});

const activeUsers = new Gauge({
  name: 'active_users',
  help: 'Number of active users in the last 5 minutes',
  registers: [metrics]
});

const walletOperations = new Counter({
  name: 'wallet_operations_total',
  help: 'Total number of wallet operations',
  labelNames: ['operation_type', 'asset', 'status'],
  registers: [metrics]
});

const transactionVolume = new Counter({
  name: 'transaction_volume_total',
  help: 'Total transaction volume',
  labelNames: ['asset', 'type'],
  registers: [metrics]
});

// System metrics
collectDefaultMetrics({ register: metrics });

// Middleware function
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const start = process.hrtime();

  // Record end time and calculate duration
  res.on('finish', () => {
    const duration = process.hrtime(start);
    const durationSeconds = duration[0] + duration[1] / 1e9;

    // Route path normalization
    const route = req.route?.path || 'unknown_route';

    // Record metrics
    httpRequestDurationMicroseconds
      .labels(req.method, route, res.statusCode.toString())
      .observe(durationSeconds);

    httpRequestsTotal
      .labels(req.method, route, res.statusCode.toString())
      .inc();

    // Record errors
    if (res.statusCode >= 400) {
      errorRate.labels(
        res.statusCode >= 500 ? 'server_error' : 'client_error',
        route
      ).inc();
    }

    // Record specific business metrics based on route
    if (req.originalUrl.includes('/api/wallets')) {
      const operation = req.method === 'POST' ? 'deposit' :
                       req.method === 'PUT' ? 'withdraw' : 'query';

      walletOperations.labels(
        operation,
        req.body?.asset || 'unknown',
        res.statusCode < 400 ? 'success' : 'failed'
      ).inc();
    }

    if (req.originalUrl.includes('/api/transactions') && req.method === 'POST') {
      transactionVolume.labels(
        req.body?.asset || 'unknown',
        req.body?.type || 'unknown'
      ).inc(Number(req.body?.amount) || 0);
    }
  });

  next();
};

// Metrics endpoint handler
export const metricsHandler = async (_req: Request, res: Response) => {
  try {
    res.set('Content-Type', metrics.contentType);
    res.end(await metrics.metrics());
  } catch (error) {
    res.status(500).end(error);
  }
};

// Export metrics object for use in other parts of the application
export {
  activeUsers,
  walletOperations,
  transactionVolume,
  errorRate
};