import express from 'express';
import { protect } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';
import userReferralController from '../controllers/userReferralController';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(protect);

// User referral history and stats routes
router.get('/history', wrapController(userReferralController.getReferralHistory));
router.get('/stats', wrapController(userReferralController.getReferralStats));

// Referral code management
router.get('/code', wrapController(userReferralController.getReferralCode));
router.post('/generate-code', wrapController(userReferralController.generateReferralCode));

// Referral commission balance and withdrawal routes (keeping referral functionality)
router.get('/commission-balance', wrapController(userReferralController.getCommissionBalance));
router.get('/commission-balance/:currency', wrapController(userReferralController.getCommissionBalanceByCurrency));

// Referral commission withdrawal routes (for referral commissions only)
router.get('/withdrawal-history', wrapController(userReferralController.getCommissionWithdrawalHistory));

// Analytics and reporting
router.get('/analytics', wrapController(userReferralController.getReferralAnalytics));

export default router;
