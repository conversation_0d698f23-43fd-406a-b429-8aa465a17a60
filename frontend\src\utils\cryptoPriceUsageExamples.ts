/**
 * Examples of how to use the new crypto price caching system
 * 
 * This file demonstrates various ways to integrate the crypto price
 * caching functionality into your components and services.
 */

// ============================================================================
// 1. BASIC USAGE IN COMPONENTS
// ============================================================================

/*
// Example 1: Using the hook in a component
import { useCryptoPrices } from '../hooks/useCryptoPrices';

const MyComponent = () => {
  const { prices, loading, error, getPrice } = useCryptoPrices();
  
  if (loading) return <div>Loading prices...</div>;
  if (error) return <div>Error: {error}</div>;
  
  return (
    <div>
      <p>BTC: ${getPrice('BTC').toLocaleString()}</p>
      <p>ETH: ${getPrice('ETH').toLocaleString()}</p>
    </div>
  );
};
*/

// ============================================================================
// 2. SINGLE PRICE USAGE
// ============================================================================

/*
// Example 2: Getting a single cryptocurrency price
import { useCryptoPrice } from '../hooks/useCryptoPrices';

const BitcoinPrice = () => {
  const { price, loading, error } = useCryptoPrice('BTC');
  
  return (
    <div>
      {loading ? 'Loading...' : `Bitcoin: $${price.toLocaleString()}`}
    </div>
  );
};
*/

// ============================================================================
// 3. CONVERSION UTILITIES
// ============================================================================

/*
// Example 3: Using conversion utilities
import { useCryptoConverter } from '../hooks/useCryptoPrices';

const ConversionExample = () => {
  const { convertToUSD, convertFromUSD } = useCryptoConverter();
  
  const btcAmount = 0.5;
  const usdValue = convertToUSD(btcAmount, 'BTC');
  const ethAmount = convertFromUSD(1000, 'ETH');
  
  return (
    <div>
      <p>{btcAmount} BTC = ${usdValue.toLocaleString()}</p>
      <p>$1000 = {ethAmount.toFixed(4)} ETH</p>
    </div>
  );
};
*/

// ============================================================================
// 4. CONTEXT PROVIDER USAGE
// ============================================================================

/*
// Example 4: Using the context provider directly
import { useCryptoPriceContext } from '../components/CryptoPriceProvider';

const AdvancedComponent = () => {
  const { 
    prices, 
    loading, 
    refreshPrices, 
    cacheInfo 
  } = useCryptoPriceContext();
  
  return (
    <div>
      <button onClick={refreshPrices}>Refresh Prices</button>
      <p>Cache valid: {cacheInfo.isValid ? 'Yes' : 'No'}</p>
      <p>Price count: {cacheInfo.priceCount}</p>
    </div>
  );
};
*/

// ============================================================================
// 5. SERVICE DIRECT USAGE
// ============================================================================

/*
// Example 5: Using the service directly (outside React)
import { cryptoPriceService } from '../services/cryptoPriceService';

// In a utility function or service
export const calculatePortfolioValue = async (holdings: any[]) => {
  const prices = await cryptoPriceService.getPrices();
  
  return holdings.reduce((total, holding) => {
    const price = prices[holding.symbol] || 0;
    return total + (holding.amount * price);
  }, 0);
};

// Subscribe to price updates
const unsubscribe = cryptoPriceService.subscribe((prices) => {
  console.log('Prices updated:', prices);
  // Update your UI or state here
});

// Don't forget to unsubscribe when done
// unsubscribe();
*/

// ============================================================================
// 6. INTEGRATION WITH EXISTING COMPONENTS
// ============================================================================

/*
// Example 6: Updating existing components to use cached prices
// Before:
const OldComponent = () => {
  const [btcPrice, setBtcPrice] = useState(0);
  
  useEffect(() => {
    fetch('/api/crypto/prices')
      .then(res => res.json())
      .then(data => setBtcPrice(data.BTC));
  }, []);
  
  return <div>BTC: ${btcPrice}</div>;
};

// After:
const NewComponent = () => {
  const { getPrice } = useCryptoPrices();
  
  return <div>BTC: ${getPrice('BTC').toLocaleString()}</div>;
};
*/

// ============================================================================
// 7. WALLET BALANCE CALCULATIONS
// ============================================================================

/*
// Example 7: Calculating wallet balances with cached prices
import { useCryptoPrices } from '../hooks/useCryptoPrices';

const WalletBalance = ({ wallets }: { wallets: any[] }) => {
  const { convertToUSD, loading } = useCryptoPrices();
  
  const totalUSD = wallets.reduce((total, wallet) => {
    return total + convertToUSD(wallet.balance, wallet.currency);
  }, 0);
  
  if (loading) return <div>Calculating...</div>;
  
  return (
    <div>
      <h3>Total Portfolio Value</h3>
      <p>${totalUSD.toLocaleString()}</p>
      
      {wallets.map(wallet => (
        <div key={wallet.currency}>
          {wallet.balance} {wallet.currency} = 
          ${convertToUSD(wallet.balance, wallet.currency).toLocaleString()}
        </div>
      ))}
    </div>
  );
};
*/

// ============================================================================
// 8. TRANSACTION VALUE CALCULATIONS
// ============================================================================

/*
// Example 8: Calculating transaction values
import { useCryptoPrices } from '../hooks/useCryptoPrices';

const TransactionList = ({ transactions }: { transactions: any[] }) => {
  const { convertToUSD } = useCryptoPrices();
  
  return (
    <div>
      {transactions.map(tx => {
        const usdValue = convertToUSD(tx.amount, tx.currency);
        
        return (
          <div key={tx.id}>
            <span>{tx.amount} {tx.currency}</span>
            <span>${usdValue.toLocaleString()}</span>
          </div>
        );
      })}
    </div>
  );
};
*/

// ============================================================================
// 9. PRICE ALERTS AND MONITORING
// ============================================================================

/*
// Example 9: Price monitoring with subscriptions
import { useEffect } from 'react';
import { cryptoPriceService } from '../services/cryptoPriceService';

const PriceAlert = ({ targetPrice, symbol }: { targetPrice: number, symbol: string }) => {
  useEffect(() => {
    const unsubscribe = cryptoPriceService.subscribe((prices) => {
      const currentPrice = prices[symbol];
      
      if (currentPrice >= targetPrice) {
        // Trigger alert
        console.log(`🚨 ${symbol} reached target price: $${currentPrice}`);
        // You could show a toast notification here
      }
    });
    
    return unsubscribe;
  }, [targetPrice, symbol]);
  
  return <div>Monitoring {symbol} for price ${targetPrice}</div>;
};
*/

// ============================================================================
// 10. CACHE DEBUGGING AND MONITORING
// ============================================================================

/*
// Example 10: Cache debugging component (development only)
import { useCryptoPrices } from '../hooks/useCryptoPrices';

const CacheDebugger = () => {
  const { cacheInfo, refreshPrices } = useCryptoPrices();
  
  if (process.env.NODE_ENV !== 'development') return null;
  
  return (
    <div style={{ 
      position: 'fixed', 
      top: 10, 
      right: 10, 
      background: 'black', 
      color: 'white', 
      padding: 10,
      fontSize: 12
    }}>
      <div>Cache Status: {cacheInfo.isValid ? '✅' : '❌'}</div>
      <div>Price Count: {cacheInfo.priceCount}</div>
      <div>Age: {Math.round(cacheInfo.cacheAge / 1000)}s</div>
      <button onClick={refreshPrices}>Refresh</button>
    </div>
  );
};
*/

// ============================================================================
// IMPLEMENTATION NOTES
// ============================================================================

export const IMPLEMENTATION_NOTES = {
  cacheStrategy: {
    duration: '5 minutes',
    storage: 'localStorage',
    fallback: 'Built-in fallback prices',
    refreshStrategy: 'Manual or automatic on cache expiry'
  },
  
  performance: {
    initialLoad: 'Loads from cache if available, otherwise fetches from API',
    subsequentLoads: 'Uses cached data until expiry',
    memoryUsage: 'Minimal - only stores price data',
    networkRequests: 'Reduced by 80-90% with effective caching'
  },
  
  errorHandling: {
    apiFailure: 'Falls back to built-in prices',
    cacheCorruption: 'Automatically clears and refetches',
    networkIssues: 'Uses stale cache if available'
  },
  
  integration: {
    existing: 'Drop-in replacement for existing price fetching',
    newComponents: 'Use hooks for easy integration',
    services: 'Direct service access for non-React code'
  }
};

export default IMPLEMENTATION_NOTES;
