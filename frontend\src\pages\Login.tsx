import { useState, useRef, useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Heading,
  Text,
  Link,
  Alert,
  AlertIcon,
  Container,
  Flex,
  InputGroup,
  InputRightElement,
  Checkbox,
  Divider,
  HStack,
  VStack,
  Icon,
  useToast,
  FormHelperText,
  keyframes,
} from '@chakra-ui/react';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  FaEye,
  FaEyeSlash,
  FaShieldAlt,
  FaLock,
  FaEnvelope,
  FaRocket,
  FaGem
} from 'react-icons/fa';
import useAuth from '../hooks/useAuth';

// Enhanced modern animations with updated color scheme
const float = keyframes`
  0% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
  100% { transform: translateY(0px) rotate(0deg); }
`;

const glow = keyframes`
  0% { box-shadow: 0 0 20px rgba(252, 213, 53, 0.3); }
  50% { box-shadow: 0 0 30px rgba(252, 213, 53, 0.6); }
  100% { box-shadow: 0 0 20px rgba(252, 213, 53, 0.3); }
`;

const shimmer = keyframes`
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
`;

const Login = () => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [captchaValue, setCaptchaValue] = useState('');
  const [captchaAnswer, setCaptchaAnswer] = useState('');
  const [captchaError, setCaptchaError] = useState(false);

  const { user, login, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const captchaRef = useRef<HTMLInputElement>(null);

  // Redirect if user is already logged in
  useEffect(() => {
    if (user) {
      // Get the redirect path from location state or default to home
      const from = location.state?.from?.pathname || '/';

      console.log('User already logged in, redirecting to:', from);

      // Show notification
      toast({
        title: t('login.alreadyLoggedIn', 'Already Logged In'),
        description: t('login.redirectingToHome', 'You are already logged in. Redirecting...'),
        status: 'info',
        duration: 3000,
        isClosable: true,
      });

      // Navigate to home or the page they were trying to access
      navigate(from, { replace: true });
    }
  }, [user, navigate, location, toast, t]);

  // Generate CAPTCHA
  const generateCaptcha = () => {
    const num1 = Math.floor(Math.random() * 10) + 1;
    const num2 = Math.floor(Math.random() * 10) + 1;
    const operators = ['+', '-', 'x'];
    const operator = operators[Math.floor(Math.random() * operators.length)];

    let answer = 0;
    switch(operator) {
      case '+':
        answer = num1 + num2;
        break;
      case '-':
        answer = num1 - num2;
        break;
      case 'x':
        answer = num1 * num2;
        break;
    }

    setCaptchaValue(`${num1} ${operator} ${num2} = ?`);
    setCaptchaAnswer(answer.toString());
    setCaptchaError(false);
  };

  // Generate CAPTCHA when page loads
  useEffect(() => {
    generateCaptcha();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setCaptchaError(false);

    // Check email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError(t('translation:login.invalidEmail', 'Please enter a valid email address'));
      return;
    }

    // Check password
    if (!password) {
      setError(t('translation:login.invalidPassword', 'Please enter a valid password'));
      return;
    }

    // CAPTCHA verification
    const userCaptchaAnswer = captchaRef.current?.value;
    if (!userCaptchaAnswer || userCaptchaAnswer !== captchaAnswer) {
      setCaptchaError(true);
      setError(t('translation:login.invalidCaptcha', 'CAPTCHA verification failed'));
      generateCaptcha(); // Generate new CAPTCHA
      if (captchaRef.current) captchaRef.current.value = '';
      return;
    }

    try {
      // Call the login function from AuthContext
      await login(email, password);

      // Show successful login notification
      toast({
        title: t('translation:login.successTitle', 'Login Successful!'),
        description: t('translation:login.successDescription', 'Welcome! Redirecting you...'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      setSuccess(t('translation:login.loginSuccessful', 'Login successful! Redirecting to dashboard...'));

      // Navigate to dashboard after a short delay
      setTimeout(() => {
        navigate('/');
      }, 1500);
    } catch (error: any) {
      console.error('Login error:', error);

      // Extract error details
      const errorMessage = error.message || t('translation:login.loginFailed', 'Login failed. Please check your credentials.');
      const errorType = error.type || 'general';
      const errorStatus = error.status;

      setError(errorMessage);

      // Determine toast configuration based on error type
      let toastConfig = {
        title: t('translation:login.errorTitle', 'Login Failed'),
        description: errorMessage,
        status: 'error' as const,
        duration: 5000,
        isClosable: true,
      };

      // Customize toast based on error type
      switch (errorType) {
        case 'network':
          toastConfig.title = t('translation:login.networkErrorTitle', 'Connection Error');
          toastConfig.description = t('translation:login.networkErrorDesc', 'Unable to connect to server. Please check your internet connection.');
          toastConfig.duration = 7000;
          break;
        case 'timeout':
          toastConfig.title = t('translation:login.timeoutErrorTitle', 'Request Timeout');
          toastConfig.description = t('translation:login.timeoutErrorDesc', 'The request took too long. Please try again.');
          break;
        case 'server':
          toastConfig.title = t('translation:login.serverErrorTitle', 'Server Error');
          toastConfig.description = t('translation:login.serverErrorDesc', 'Server is temporarily unavailable. Please try again later.');
          toastConfig.duration = 7000;
          break;
        case 'auth':
          toastConfig.title = t('translation:login.authErrorTitle', 'Invalid Credentials');
          toastConfig.description = t('translation:login.authErrorDesc', 'Email or password is incorrect. Please check and try again.');
          break;
        case 'locked':
          toastConfig.title = t('translation:login.lockedErrorTitle', 'Account Locked');
          toastConfig.description = t('translation:login.lockedErrorDesc', 'Your account is temporarily locked. Please try again later.');
          toastConfig.duration = 8000;
          break;
        case 'rateLimit':
          toastConfig.title = t('translation:login.rateLimitErrorTitle', 'Too Many Attempts');
          toastConfig.description = t('translation:login.rateLimitErrorDesc', 'Please wait before trying again.');
          toastConfig.duration = 8000;
          break;
        default:
          // Use default configuration
          break;
      }

      // Show enhanced error notification
      toast(toastConfig);

      // Generate new CAPTCHA on error
      generateCaptcha();
      if (captchaRef.current) captchaRef.current.value = '';
    }
  };



  return (
    <Box
      minH="100vh"
      bgGradient="linear(135deg, #0B0E11 0%, #1A1D29 50%, #0B0E11 100%)"
      position="relative"
      overflow="hidden"
      className="safe-area-all"
    >
      {/* Enhanced animated background elements with new color scheme and mobile optimization */}
      <Box
        position="absolute"
        top="10%"
        left="10%"
        w={{ base: "60px", md: "100px" }}
        h={{ base: "60px", md: "100px" }}
        borderRadius="full"
        bg="linear-gradient(45deg, rgba(252, 213, 53, 0.15), rgba(252, 213, 53, 0.08))"
        animation={`${float} 6s ease-in-out infinite`}
        zIndex={0}
      />
      <Box
        position="absolute"
        top="60%"
        right="15%"
        w={{ base: "50px", md: "80px" }}
        h={{ base: "50px", md: "80px" }}
        borderRadius="full"
        bg="linear-gradient(45deg, rgba(252, 213, 53, 0.12), rgba(252, 213, 53, 0.06))"
        animation={`${float} 8s ease-in-out infinite reverse`}
        zIndex={0}
      />
      <Box
        position="absolute"
        top="30%"
        right="8%"
        w={{ base: "30px", md: "50px" }}
        h={{ base: "30px", md: "50px" }}
        borderRadius="full"
        bg="linear-gradient(45deg, rgba(252, 213, 53, 0.08), rgba(252, 213, 53, 0.04))"
        animation={`${float} 10s ease-in-out infinite`}
        zIndex={0}
        display={{ base: "none", sm: "block" }}
      />

      <Container
        maxW={{ base: "100%", sm: "400px", md: "450px", lg: "lg" }}
        px={{ base: 3, sm: 4, md: 6, lg: 8 }}
        py={{ base: 4, sm: 6, md: 12, lg: 16 }}
        position="relative"
        zIndex={1}
        className="mobile-full-width"
        minH="100vh"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <VStack spacing={8} align="center">
          {/* Modern Header */}
          <VStack spacing={6} textAlign="center">
            <Box position="relative">
              <Flex align="center" justify="center" mb={4}>
                <Box
                  w={{ base: "44px", md: "48px" }}
                  h={{ base: "44px", md: "48px" }}
                  bg="linear-gradient(135deg, #FCD535 0%, #F8D12F 100%)"
                  borderRadius="xl"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  boxShadow="0 8px 32px rgba(252, 213, 53, 0.4)"
                  animation={`${glow} 3s ease-in-out infinite`}
                  position="relative"
                  _before={{
                    content: '""',
                    position: 'absolute',
                    top: '-2px',
                    left: '-2px',
                    right: '-2px',
                    bottom: '-2px',
                    borderRadius: 'xl',
                    background: 'linear-gradient(45deg, #FCD535, #F8D12F, #FCD535)',
                    backgroundSize: '200% 200%',
                    animation: `${shimmer} 2s linear infinite`,
                    zIndex: -1,
                  }}
                >
                  <Icon as={FaRocket} color="#0B0E11" boxSize={{ base: 5, md: 6 }} />
                </Box>
              </Flex>
              <Heading
                fontSize={{ base: '2xl', md: '3xl' }}
                fontWeight="800"
                bgGradient="linear(135deg, #FCD535 0%, #F8D12F 50%, #FCD535 100%)"
                bgClip="text"
                letterSpacing="tight"
                mb={2}
              >
                Shipping Finance
              </Heading>
              <Text
                fontSize={{ base: 'lg', md: 'xl' }}
                color="#EAECEF"
                fontWeight="500"
                opacity={0.9}
              >
                {t('translation:login.title', 'Welcome Back')}
              </Text>
              <Text
                fontSize="sm"
                color="#848E9C"
                mt={2}
                opacity={0.8}
              >
                {t('common:login.subtitle', 'Sign in to your account to continue')}
              </Text>
            </Box>
          </VStack>
          {/* Enhanced Modern Glassmorphism Card */}
          <Box
            w="full"
            maxW={{ base: "100%", sm: "400px", md: "450px" }}
            bg="rgba(30, 35, 41, 0.85)"
            backdropFilter="blur(20px)"
            borderRadius={{ base: "xl", md: "2xl" }}
            borderWidth="1px"
            borderColor="rgba(252, 213, 53, 0.25)"
            boxShadow="0 25px 50px -12px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(252, 213, 53, 0.15)"
            p={{ base: 4, sm: 5, md: 6, lg: 8 }}
            position="relative"
            className="mobile-full-width"
            mx={{ base: 2, sm: 0 }}
            _before={{
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius: '2xl',
              background: 'linear-gradient(135deg, rgba(252, 213, 53, 0.12) 0%, transparent 50%, rgba(252, 213, 53, 0.06) 100%)',
              pointerEvents: 'none',
            }}
          >
          {error && (
            <Alert
              status="error"
              mb={4}
              borderRadius="xl"
              bg="rgba(248, 73, 96, 0.1)"
              borderColor="#F84960"
              borderWidth="1px"
              color="#F84960"
            >
              <AlertIcon color="#F84960" />
              {error}
            </Alert>
          )}
          {success && (
            <Alert
              status="success"
              mb={4}
              borderRadius="xl"
              bg="rgba(2, 192, 118, 0.1)"
              borderColor="#02C076"
              borderWidth="1px"
              color="#02C076"
            >
              <AlertIcon color="#02C076" />
              {success}
            </Alert>
          )}
            <form onSubmit={handleSubmit}>
              <VStack spacing={6} position="relative" zIndex={1}>
                {/* Enhanced Modern Email Field */}
                <FormControl id="email" isRequired>
                  <FormLabel
                    color="#EAECEF"
                    fontSize="sm"
                    fontWeight="600"
                    mb={3}
                  >
                    <HStack spacing={2}>
                      <Box
                        p={1}
                        borderRadius="md"
                        bg="rgba(252, 213, 53, 0.15)"
                      >
                        <Icon as={FaEnvelope} color="#FCD535" boxSize={3} />
                      </Box>
                      <Text>{t('translation:common.email', 'Email Address')}</Text>
                    </HStack>
                  </FormLabel>
                  <Input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    bg="rgba(11, 14, 17, 0.7)"
                    borderColor="#2B3139"
                    borderWidth="1px"
                    color="#EAECEF"
                    placeholder="Enter your email address"
                    _placeholder={{ color: '#848E9C' }}
                    _hover={{
                      borderColor: "rgba(252, 213, 53, 0.7)",
                      boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                    }}
                    _focus={{
                      borderColor: "#FCD535",
                      boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                      bg: "rgba(11, 14, 17, 0.9)"
                    }}
                    size="lg"
                    borderRadius="xl"
                    fontSize="md"
                    transition="all 0.3s ease"
                    minH={{ base: "44px", md: "48px" }}
                  />
                </FormControl>

                {/* Enhanced Modern Password Field */}
                <FormControl id="password" isRequired>
                  <FormLabel
                    color="#EAECEF"
                    fontSize="sm"
                    fontWeight="600"
                    mb={3}
                  >
                    <HStack spacing={2}>
                      <Box
                        p={1}
                        borderRadius="md"
                        bg="rgba(252, 213, 53, 0.15)"
                      >
                        <Icon as={FaLock} color="#FCD535" boxSize={3} />
                      </Box>
                      <Text>{t('translation:common.password', 'Password')}</Text>
                    </HStack>
                  </FormLabel>
                  <InputGroup size="lg">
                    <Input
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      bg="rgba(11, 14, 17, 0.7)"
                      borderColor="#2B3139"
                      borderWidth="1px"
                      color="#EAECEF"
                      placeholder="Enter your password"
                      _placeholder={{ color: '#848E9C' }}
                      _hover={{
                        borderColor: "rgba(252, 213, 53, 0.7)",
                        boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                      }}
                      _focus={{
                        borderColor: "#FCD535",
                        boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                        bg: "rgba(11, 14, 17, 0.9)"
                      }}
                      borderRadius="xl"
                      fontSize="md"
                      transition="all 0.3s ease"
                      minH={{ base: "44px", md: "48px" }}
                    />
                    <InputRightElement width="3rem" h={{ base: "44px", md: "48px" }}>
                      <Button
                        h={{ base: "32px", md: "36px" }}
                        w={{ base: "32px", md: "36px" }}
                        minW={{ base: "32px", md: "36px" }}
                        size="sm"
                        onClick={() => setShowPassword(!showPassword)}
                        bg="transparent"
                        _hover={{
                          bg: "rgba(252, 213, 53, 0.15)",
                          transform: "scale(1.1)"
                        }}
                        _active={{ transform: "scale(0.95)" }}
                        color="#848E9C"
                        borderRadius="lg"
                        transition="all 0.2s ease"
                      >
                        <Icon as={showPassword ? FaEyeSlash : FaEye} />
                      </Button>
                    </InputRightElement>
                  </InputGroup>
                </FormControl>

                {/* Enhanced Modern CAPTCHA Section */}
                <Box
                  bg="rgba(252, 213, 53, 0.04)"
                  borderRadius="xl"
                  borderWidth="1px"
                  borderColor="rgba(252, 213, 53, 0.2)"
                  p={{ base: 4, md: 5 }}
                  position="relative"
                  _before={{
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 'xl',
                    background: 'linear-gradient(135deg, rgba(252, 213, 53, 0.08) 0%, transparent 50%, rgba(252, 213, 53, 0.04) 100%)',
                    pointerEvents: 'none',
                  }}
                >
                  <HStack mb={4} spacing={3}>
                    <Box
                      bg="linear-gradient(135deg, rgba(252, 213, 53, 0.25), rgba(252, 213, 53, 0.15))"
                      p={2}
                      borderRadius="lg"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon as={FaShieldAlt} color="#FCD535" boxSize={4} />
                    </Box>
                    <VStack align="start" spacing={0}>
                      <Text color="#EAECEF" fontSize="sm" fontWeight="600">
                        {t('translation:login.captchaTitle', 'Security Verification')}
                      </Text>
                      <Text color="#848E9C" fontSize="xs">
                        {t('common:login.captchaSubtitle', 'Solve the math problem below')}
                      </Text>
                    </VStack>
                  </HStack>

                  <FormControl isRequired isInvalid={captchaError}>
                    <Box
                      bg="rgba(11, 14, 17, 0.7)"
                      borderRadius="lg"
                      borderWidth="1px"
                      borderColor={captchaError ? "#F84960" : "#2B3139"}
                      p={4}
                      mb={4}
                      textAlign="center"
                      position="relative"
                      _before={{
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        borderRadius: 'lg',
                        background: captchaError
                          ? 'linear-gradient(135deg, rgba(248, 73, 96, 0.1), transparent)'
                          : 'linear-gradient(135deg, rgba(252, 213, 53, 0.08), transparent)',
                        pointerEvents: 'none',
                      }}
                    >
                      <Text
                        fontSize="xl"
                        fontWeight="bold"
                        letterSpacing="wider"
                        color="#EAECEF"
                        fontFamily="monospace"
                        textShadow="2px 2px 4px rgba(0,0,0,0.5)"
                        position="relative"
                        zIndex={1}
                      >
                        {captchaValue}
                      </Text>
                    </Box>

                    <InputGroup size="lg">
                      <Input
                        ref={captchaRef}
                        placeholder={t('translation:login.captchaPlaceholder', 'Enter the result')}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor={captchaError ? "#F84960" : "#2B3139"}
                        borderWidth="1px"
                        color="#EAECEF"
                        textAlign="center"
                        fontSize="lg"
                        fontWeight="600"
                        _placeholder={{ color: '#848E9C' }}
                        _hover={{
                          borderColor: captchaError ? "#F84960" : "rgba(252, 213, 53, 0.7)",
                          boxShadow: captchaError
                            ? "0 0 0 1px rgba(248, 73, 96, 0.3)"
                            : "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor: captchaError ? "#F84960" : "#FCD535",
                          boxShadow: captchaError
                            ? "0 0 0 3px rgba(248, 73, 96, 0.15)"
                            : "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        borderRadius="xl"
                        transition="all 0.3s ease"
                        minH={{ base: "44px", md: "48px" }}
                      />
                      <InputRightElement width="4rem" h={{ base: "44px", md: "48px" }}>
                        <Button
                          h={{ base: "32px", md: "36px" }}
                          w={{ base: "32px", md: "36px" }}
                          minW={{ base: "32px", md: "36px" }}
                          size="sm"
                          onClick={generateCaptcha}
                          bg="transparent"
                          _hover={{
                            bg: "rgba(252, 213, 53, 0.15)",
                            transform: "rotate(180deg)"
                          }}
                          _active={{ transform: "scale(0.95)" }}
                          color="#848E9C"
                          borderRadius="lg"
                          transition="all 0.3s ease"
                          title={t('translation:login.refreshCaptcha', 'Refresh')}
                        >
                          <Icon as={FaShieldAlt} />
                        </Button>
                      </InputRightElement>
                    </InputGroup>

                    {captchaError && (
                      <FormHelperText color="#F84960" mt={2} fontSize="sm">
                        <Icon as={FaShieldAlt} mr={1} />
                        {t('translation:login.captchaError', 'Verification failed. Please try again.')}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>

                {/* Enhanced Remember Me & Forgot Password */}
                <Flex
                  direction={{ base: 'column', sm: 'row' }}
                  align={{ base: 'start', sm: 'center' }}
                  justify="space-between"
                  gap={4}
                  mt={2}
                >
                  <Checkbox
                    isChecked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    colorScheme="yellow"
                    size="md"
                  >
                    <Text fontSize="sm" color="#EAECEF" fontWeight="500">
                      {t('translation:login.rememberMe', 'Remember me')}
                    </Text>
                  </Checkbox>
                  <Link
                    color="#FCD535"
                    fontSize="sm"
                    fontWeight="600"
                    _hover={{
                      textDecoration: 'none',
                      color: '#F8D12F',
                      transform: 'translateY(-1px)'
                    }}
                    transition="all 0.2s ease"
                    minH={{ base: "44px", md: "auto" }}
                    display="flex"
                    alignItems="center"
                  >
                    {t('translation:login.forgotPassword', 'Forgot password?')}
                  </Link>
                </Flex>

                {/* Enhanced Modern Login Button */}
                <Button
                  type="submit"
                  bg="linear-gradient(135deg, #FCD535 0%, #F8D12F 100%)"
                  color="#0B0E11"
                  _hover={{
                    bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
                    transform: "translateY(-3px)",
                    boxShadow: "0 12px 30px rgba(252, 213, 53, 0.5)"
                  }}
                  _active={{
                    transform: "translateY(-1px)",
                    boxShadow: "0 6px 20px rgba(252, 213, 53, 0.4)"
                  }}
                  transition="all 0.3s ease"
                  isLoading={loading}
                  loadingText="Signing in..."
                  size="lg"
                  borderRadius="xl"
                  fontWeight="700"
                  fontSize="md"
                  w="full"
                  h={{ base: "48px", md: "56px" }}
                  minH="44px"
                  position="relative"
                  _before={{
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 'xl',
                    background: 'linear-gradient(135deg, rgba(255,255,255,0.2), transparent)',
                    pointerEvents: 'none',
                  }}
                >
                  <HStack spacing={2}>
                    <Icon as={FaGem} />
                    <Text>{t('translation:common.login', 'Sign In')}</Text>
                  </HStack>
                </Button>


              </VStack>
            </form>

            {/* Enhanced Modern Footer */}
            <VStack spacing={4} pt={8}>
              <Divider borderColor="rgba(252, 213, 53, 0.2)" />
              <Text
                textAlign="center"
                fontSize="sm"
                color="#848E9C"
                fontWeight="500"
              >
                {t('translation:login.noAccount', "Don't have an account?")}{' '}
                <Link
                  as={RouterLink}
                  to="/register"
                  color="#FCD535"
                  fontWeight="600"
                  _hover={{
                    textDecoration: 'none',
                    color: '#F8D12F',
                    transform: 'translateY(-1px)'
                  }}
                  transition="all 0.2s ease"
                  minH={{ base: "44px", md: "auto" }}
                  display="inline-flex"
                  alignItems="center"
                >
                  {t('translation:login.registerLink', 'Create Account')}
                </Link>
              </Text>

              {/* Enhanced Additional Security Info */}
              <HStack spacing={2} opacity={0.7}>
                <Icon as={FaShieldAlt} color="#FCD535" boxSize={3} />
                <Text fontSize="xs" color="#848E9C">
                  {t('common:login.secureLogin', 'Your connection is secure and encrypted')}
                </Text>
              </HStack>
            </VStack>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default Login;
