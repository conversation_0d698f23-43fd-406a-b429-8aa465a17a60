import React from 'react';
import {
  Box,
  Flex,
  Text,
  Icon,
  Badge,
  HStack,
  VStack,
  Progress,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Divider,
  Button,
  Tooltip,
  useColorModeValue,
  useToast,
} from '@chakra-ui/react';
import {
  FaCalendarAlt,
  FaCoins,
  FaChartLine,
  FaInfoCircle,
  FaExchangeAlt,
  FaArrowUp,
  FaArrowDown,
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { cryptoIcons, cryptoNames, cryptoColors, getCryptoIcon, getCryptoName, getCryptoColor } from '../utils/cryptoIcons';
import { formatCurrency as formatCurrencyWithSymbol } from '../utils/formatters';
import useAuth from '../hooks/useAuth';

interface Investment {
  _id: string;
  userId: string;
  currency: string;
  amount: number;
  description?: string;
  status: 'pending' | 'processing' | 'approved' | 'rejected';
  receiptUrl?: string;
  cryptoAddress: string;
  txHash?: string;
  network?: string;
  adminNotes?: string;
  approvedAt?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  // Amount correction fields for admin verification
  originalAmount?: number;
  adminVerifiedAmount?: number;
  amountModifiedBy?: string;
  amountModifiedAt?: string;
  amountCorrectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

interface GroupedInvestment {
  currency: string;
  totalAmount: number;
  investments: Investment[];
  firstInvestmentDate: string;
  status: 'pending' | 'processing' | 'approved' | 'rejected' | 'mixed';
  networks: string[];
  addresses: string[];
}

interface InvestmentCardProps {
  groupedInvestment: GroupedInvestment;
  totalEarned: number;
  onViewDetails?: (currency: string) => void;
  onDepositClick?: (currency: string, investmentData?: any) => void;
  onWithdrawClick?: (currency: string, investmentData?: any) => void;
}

const InvestmentCard: React.FC<InvestmentCardProps> = ({
  groupedInvestment,
  totalEarned,
  onViewDetails,
  onDepositClick,
  onWithdrawClick,
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";

  // Calculate investment metrics
  const createdDate = new Date(groupedInvestment.firstInvestmentDate);
  const currentDate = new Date();
  const diffTime = Math.abs(currentDate.getTime() - createdDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // Calculate earnings (1% daily)
  const dailyEarning = groupedInvestment.totalAmount * 0.01;

  // Calculate total earnings for this currency using reduce instead of forEach
  const totalEarningForThisCurrency = groupedInvestment.investments.reduce((total, investment) => {
    // Chỉ tính lợi nhuận cho các khoản đầu tư đã được approve
    if (investment.status === 'approved') {
      // Sử dụng ngày approve nếu có, nếu không thì sử dụng ngày tạo
      const investmentDate = investment.approvedAt ? new Date(investment.approvedAt) : new Date(investment.createdAt);
      const investmentDiffTime = Math.abs(currentDate.getTime() - investmentDate.getTime());
      const investmentDiffDays = Math.ceil(investmentDiffTime / (1000 * 60 * 60 * 24));
      return total + (investment.amount * 0.01 * investmentDiffDays);
    }
    return total;
  }, 0);

  const earningPercentage = groupedInvestment.totalAmount > 0
    ? (totalEarningForThisCurrency / groupedInvestment.totalAmount) * 100
    : 0;

  // Format currency with crypto symbol
  const formatCurrency = (amount: number) => {
    return formatCurrencyWithSymbol(amount, groupedInvestment.currency);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'processing':
        return 'blue';
      case 'rejected':
        return 'red';
      case 'mixed':
        return 'purple';
      default:
        return 'gray';
    }
  };

  // Get status display text
  const getStatusText = (status: string) => {
    if (status === 'mixed') {
      return t('investment.mixedStatus', 'Mixed Status');
    }
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Get crypto icon
  const CryptoIcon = getCryptoIcon(groupedInvestment.currency);

  // Get crypto color
  const cryptoColor = getCryptoColor(groupedInvestment.currency, primaryColor);

  // Get crypto name
  const cryptoName = getCryptoName(groupedInvestment.currency);

  // Count investments
  const investmentCount = groupedInvestment.investments.length;

  // Enhanced deposit button click handler
  const handleDepositClick = () => {
    if (!user) {
      toast({
        title: t('common.loginRequired', 'Login Required'),
        description: t('common.loginRequiredDesc', 'Please log in to make a deposit.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const investmentData = {
      currency: groupedInvestment.currency,
      totalAmount: groupedInvestment.totalAmount,
      totalEarned: totalEarned,
      investmentCount: groupedInvestment.investments.length,
      status: groupedInvestment.status
    };

    onDepositClick?.(groupedInvestment.currency, investmentData);
  };

  // Enhanced withdraw button click handler with Total Earned data
  const handleWithdrawClick = () => {
    if (!user) {
      toast({
        title: t('common.loginRequired', 'Login Required'),
        description: t('common.loginRequiredDesc', 'Please log in to withdraw.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const investmentData = {
      currency: groupedInvestment.currency,
      totalAmount: groupedInvestment.totalAmount,
      totalEarned: totalEarningForThisCurrency, // CRITICAL FIX: Use currency-specific earnings
      investmentCount: groupedInvestment.investments.length,
      status: groupedInvestment.status
    };

    console.log(`🎯 CRITICAL FIX - Opening withdraw modal for ${groupedInvestment.currency}:`, {
      currency: groupedInvestment.currency,
      totalEarned_OLD: totalEarned, // Global total from props
      totalEarningForThisCurrency_NEW: totalEarningForThisCurrency, // Currency-specific calculation
      investmentData: investmentData
    });

    onWithdrawClick?.(groupedInvestment.currency, investmentData);
  };

  return (
    <Box
      bg={bgColor}
      borderRadius="2xl"
      borderWidth="1px"
      borderColor={borderColor}
      overflow="hidden"
      position="relative"
      transition="all 0.3s ease"
      boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
      w="100%"
      h="100%"
      _hover={{
        transform: 'translateY(-4px)',
        boxShadow: `0 12px 24px rgba(0, 0, 0, 0.15), 0 0 0 1px ${cryptoColor}40`,
        borderColor: `${cryptoColor}80`,
      }}
    >
      {/* Enhanced top gradient border */}
      <Box
        h="4px"
        bg={`linear-gradient(90deg, ${cryptoColor} 0%, ${cryptoColor}80 50%, ${cryptoColor}40 100%)`}
        position="absolute"
        top={0}
        left={0}
        right={0}
      />

      {/* Enhanced Card Header */}
      <Flex
        p={{ base: 3, md: 4, lg: 5 }}
        align="center"
        borderBottomWidth="1px"
        borderBottomColor={borderColor}
        bg={`${cryptoColor}05`}
        minH={{ base: "auto", md: "110px" }}
      >
        <Flex
          bg={`${cryptoColor}15`}
          p={{ base: 3, md: 4 }}
          borderRadius="xl"
          mr={{ base: 4, md: 5 }}
          align="center"
          justify="center"
          border="2px solid"
          borderColor={`${cryptoColor}30`}
          flexShrink={0}
        >
          <Icon as={CryptoIcon} color={cryptoColor} boxSize={{ base: 6, md: 7 }} />
        </Flex>
        <Box flex={1} minW={0} mr={4}>
          <VStack align="start" spacing={{ base: 2, md: 3 }}>
            <Flex direction={{ base: "column", sm: "row" }} align={{ base: "start", sm: "center" }} gap={2} w="100%">
              <Text
                color={textColor}
                fontWeight="800"
                fontSize={{ base: "lg", md: "xl", lg: "2xl" }}
                lineHeight="1.2"
                letterSpacing="-0.02em"
                whiteSpace="nowrap"
              >
                {groupedInvestment.currency}
              </Text>
              <Text
                color={secondaryTextColor}
                fontSize={{ base: "sm", md: "md", lg: "lg" }}
                fontWeight="500"
                lineHeight="1.2"
                whiteSpace="nowrap"
              >
                {cryptoName}
              </Text>
            </Flex>
            <Flex direction={{ base: "column", sm: "row" }} gap={2} w="100%" flexWrap="wrap">
              <Badge
                bg={`${cryptoColor}20`}
                color={cryptoColor}
                variant="solid"
                fontSize="xs"
                fontWeight="600"
                px={3}
                py={1}
                borderRadius="full"
                whiteSpace="nowrap"
              >
                {investmentCount} {t('investment.investments', 'investments')}
              </Badge>
              <Badge
                colorScheme={getStatusColor(groupedInvestment.status)}
                variant="solid"
                borderRadius="full"
                px={3}
                py={1}
                fontSize="xs"
                fontWeight="600"
                whiteSpace="nowrap"
              >
                {getStatusText(groupedInvestment.status)}
              </Badge>
            </Flex>
          </VStack>
        </Box>
        <VStack align="end" spacing={1} minW={{ base: "120px", md: "160px", lg: "180px" }} flex="0 0 auto">
          <Text
            color={secondaryTextColor}
            fontSize="xs"
            fontWeight="600"
            textTransform="uppercase"
            letterSpacing="0.05em"
            lineHeight="1.2"
            textAlign="right"
            whiteSpace="nowrap"
          >
            {t('investment.totalInvested', 'Total Invested')}
          </Text>
          <Text
            color={textColor}
            fontWeight="800"
            fontSize={{ base: "md", md: "lg", lg: "xl" }}
            lineHeight="1.1"
            letterSpacing="-0.02em"
            textAlign="right"
            whiteSpace="nowrap"
            overflow="visible"
          >
            {formatCurrency(groupedInvestment.totalAmount)}
          </Text>
        </VStack>
      </Flex>

      {/* Enhanced Card Body */}
      <Box p={{ base: 3, md: 4, lg: 5 }}>
        {/* Enhanced Investment Stats */}
        <Flex
          direction={{ base: 'column', sm: 'row' }}
          justify="space-between"
          mb={{ base: 3, md: 4 }}
          gap={{ base: 3, sm: 4 }}
        >
          {/* Enhanced Earnings Display */}
          <Box
            flex={1}
            bg={cardBgColor}
            p={{ base: 2.5, md: 3 }}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
            position="relative"
            overflow="hidden"
            minW={{ base: "0", md: "180px" }}
          >
            <Box
              position="absolute"
              top={0}
              left={0}
              right={0}
              h="2px"
              bg="linear-gradient(90deg, #02C076 0%, #02C07680 100%)"
            />
            <VStack align="start" spacing={{ base: 2, md: 3 }} w="100%">
              <Text
                color={secondaryTextColor}
                fontSize={{ base: "xs", md: "sm" }}
                fontWeight="600"
                textTransform="uppercase"
                letterSpacing="0.05em"
                whiteSpace="nowrap"
              >
                {t('investment.totalEarned', 'Total Earned')}
              </Text>
              <Text
                color="#02C076"
                fontSize={{ base: "sm", md: "md", lg: "lg" }}
                fontWeight="800"
                lineHeight="1.1"
                letterSpacing="-0.02em"
                wordBreak="break-word"
                overflow="hidden"
                textOverflow="ellipsis"
              >
                {formatCurrency(totalEarningForThisCurrency)}
              </Text>
              <HStack spacing={1}>
                <Icon as={FaArrowUp} color="#02C076" boxSize={{ base: 2, md: 3 }} />
                <Text
                  color="#02C076"
                  fontSize={{ base: "xs", md: "sm" }}
                  fontWeight="600"
                  whiteSpace="nowrap"
                >
                  {earningPercentage.toFixed(1)}%
                </Text>
              </HStack>
            </VStack>
          </Box>

          {/* Enhanced Daily Earnings Display */}
          <Box
            flex={1}
            bg={cardBgColor}
            p={{ base: 2.5, md: 3 }}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
            position="relative"
            overflow="hidden"
            minW={{ base: "0", md: "180px" }}
          >
            <Box
              position="absolute"
              top={0}
              left={0}
              right={0}
              h="2px"
              bg={`linear-gradient(90deg, ${cryptoColor} 0%, ${cryptoColor}80 100%)`}
            />
            <VStack align="start" spacing={{ base: 2, md: 3 }} w="100%">
              <Text
                color={secondaryTextColor}
                fontSize={{ base: "xs", md: "sm" }}
                fontWeight="600"
                textTransform="uppercase"
                letterSpacing="0.05em"
                whiteSpace="nowrap"
              >
                {t('investment.dailyEarnings', 'Daily Earnings')}
              </Text>
              <Text
                color={textColor}
                fontSize={{ base: "sm", md: "md", lg: "lg" }}
                fontWeight="800"
                lineHeight="1.1"
                letterSpacing="-0.02em"
                wordBreak="break-word"
                overflow="hidden"
                textOverflow="ellipsis"
              >
                {formatCurrency(dailyEarning)}
              </Text>
              <HStack spacing={1}>
                <Icon as={FaArrowUp} color={cryptoColor} boxSize={{ base: 2, md: 3 }} />
                <Text
                  color={cryptoColor}
                  fontSize={{ base: "xs", md: "sm" }}
                  fontWeight="600"
                  whiteSpace="nowrap"
                >
                  1% daily
                </Text>
              </HStack>
            </VStack>
          </Box>
        </Flex>

        {/* Enhanced Investment Summary */}
        <Box
          bg={cardBgColor}
          p={{ base: 2.5, md: 3 }}
          borderRadius="lg"
          border="1px solid"
          borderColor={borderColor}
          mb={{ base: 3, md: 4 }}
        >
          <Flex
            direction={{ base: "column", sm: "row" }}
            justify="space-between"
            align={{ base: "start", sm: "center" }}
            gap={{ base: 3, sm: 4 }}
          >
            <VStack align="start" spacing={{ base: 1, md: 2 }}>
              <HStack spacing={2}>
                <Icon as={FaCalendarAlt} color={cryptoColor} boxSize={{ base: 3, md: 4 }} />
                <Text
                  color={secondaryTextColor}
                  fontSize={{ base: "xs", md: "sm" }}
                  fontWeight="600"
                  whiteSpace="nowrap"
                >
                  {diffDays} days active
                </Text>
              </HStack>
              <Text
                color={textColor}
                fontSize={{ base: "xs", md: "sm" }}
                fontWeight="500"
                whiteSpace="nowrap"
              >
                {investmentCount} transactions
              </Text>
            </VStack>
            <VStack align={{ base: "start", sm: "end" }} spacing={{ base: 1, md: 2 }}>
              <Text
                color={secondaryTextColor}
                fontSize={{ base: "xs", md: "sm" }}
                fontWeight="600"
                whiteSpace="nowrap"
                textAlign={{ base: "left", sm: "right" }}
              >
                Since {formatDate(groupedInvestment.firstInvestmentDate)}
              </Text>
              <Badge
                bg={`${cryptoColor}20`}
                color={cryptoColor}
                borderRadius="full"
                fontSize={{ base: "xs", md: "sm" }}
                fontWeight="600"
                px={{ base: 2, md: 3 }}
                py={1}
                whiteSpace="nowrap"
              >
                {groupedInvestment.addresses.length} addresses
              </Badge>
            </VStack>
          </Flex>
        </Box>

        {/* Enhanced Action Buttons with Details */}
        <VStack spacing={{ base: 2.5, md: 3 }} align="stretch">
          {/* Primary Action Buttons Row */}
          <Flex direction={{ base: "column", sm: "row" }} gap={{ base: 2.5, sm: 3 }}>
            {/* Deposit Button */}
            <Button
              leftIcon={<Icon as={FaArrowDown} boxSize={{ base: 3, md: 4 }} />}
              bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
              color="#0B0E11"
              _hover={{
                bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
                boxShadow: "0 6px 20px rgba(240, 185, 11, 0.3)",
                transform: "translateY(-1px)"
              }}
              _active={{
                bg: "linear-gradient(135deg, #E6C200 0%, #F0B90B 100%)",
                transform: 'translateY(0px)',
              }}
              size={{ base: "md", md: "lg" }}
              flex={1}
              minH={{ base: "44px", md: "48px" }}
              fontSize={{ base: "sm", md: "md" }}
              fontWeight="700"
              borderRadius="lg"
              onClick={handleDepositClick}
              transition="all 0.3s ease"
              boxShadow="0 3px 8px rgba(240, 185, 11, 0.2)"
              whiteSpace="nowrap"
              sx={{
                '@media (max-width: 767px)': {
                  touchAction: 'manipulation',
                  WebkitTapHighlightColor: 'transparent',
                  WebkitTouchCallout: 'none',
                  cursor: 'pointer',
                  minHeight: '44px',
                  minWidth: '44px',
                }
              }}
            >
              {t('common.deposit', 'Deposit')}
            </Button>

            {/* Withdraw Button */}
            <Button
              leftIcon={<Icon as={FaArrowUp} boxSize={{ base: 3, md: 4 }} />}
              bg="rgba(2, 192, 118, 0.1)"
              color="#02C076"
              border="2px solid"
              borderColor="#02C076"
              _hover={{
                bg: "rgba(2, 192, 118, 0.2)",
                borderColor: "#02C076",
                boxShadow: "0 6px 20px rgba(2, 192, 118, 0.3)",
                transform: "translateY(-1px)"
              }}
              _active={{
                bg: "rgba(2, 192, 118, 0.3)",
                transform: 'translateY(0px)',
                borderColor: "#02C076",
              }}
              size={{ base: "md", md: "lg" }}
              flex={1}
              minH={{ base: "44px", md: "48px" }}
              fontSize={{ base: "sm", md: "md" }}
              fontWeight="700"
              borderRadius="lg"
              onClick={handleWithdrawClick}
              transition="all 0.3s ease"
              boxShadow="0 3px 8px rgba(2, 192, 118, 0.1)"
              whiteSpace="nowrap"
              sx={{
                '@media (max-width: 767px)': {
                  touchAction: 'manipulation',
                  WebkitTapHighlightColor: 'transparent',
                  WebkitTouchCallout: 'none',
                  cursor: 'pointer',
                  minHeight: '44px',
                  minWidth: '44px',
                }
              }}
            >
              {t('common.withdraw', 'Withdraw')}
            </Button>
          </Flex>

          {/* Details Button */}
          <Button
            leftIcon={<Icon as={FaInfoCircle} boxSize={{ base: 3, md: 4 }} />}
            variant="ghost"
            color={secondaryTextColor}
            _hover={{
              bg: `${cryptoColor}10`,
              color: cryptoColor,
              transform: "translateY(-1px)"
            }}
            _active={{
              transform: "translateY(0px)"
            }}
            size={{ base: "md", md: "lg" }}
            minH={{ base: "40px", md: "44px" }}
            fontSize={{ base: "sm", md: "md" }}
            fontWeight="600"
            borderRadius="lg"
            onClick={() => onViewDetails?.(groupedInvestment.currency)}
            transition="all 0.3s ease"
            whiteSpace="nowrap"
          >
            {t('investment.viewDetails', 'View Details')}
          </Button>
        </VStack>
      </Box>
    </Box>
  );
};

export default InvestmentCard;
