import mongoose from 'mongoose';
import Wallet from '../models/walletModel';
import { logger } from '../utils/logger';
import crypto from 'crypto';
import QRCode from 'qrcode';

interface MigrationResult {
  success: boolean;
  migratedUsers: number;
  migratedWallets: number;
  errors: string[];
}

interface WalletBalance {
  currency: string;
  balance: number;
  usdtValue: number;
  lastUpdated: Date;
  address?: string;
  qrCodeUrl?: string;
  network?: string;
}

class WalletMigrationService {
  private readonly SUPPORTED_CURRENCIES = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI'];

  // Mock addresses for development/testing
  private readonly MOCK_ADDRESSES = {
    BTC: [
      '******************************************',
      '******************************************',
      '**************************************************************'
    ],
    ETH: [
      '******************************************',
      '0x8ba1f109551bD432803012645Hac136c22C4',
      '******************************************'
    ],
    USDT: [
      'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
      'TA0b86a33E6441b8435b662f98C94C6C4c4b4d8b6',
      'T742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6'
    ],
    BNB: [
      'bnb1grpf0955h0ykzq3ar5nmum7y6gdfl6lxfn46h2',
      'bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m',
      'bnb136ns6lfw4zs5hg4n85vdthaad7hq5m4gtkgf23'
    ],
    ADA: [
      'addr1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh',
      'addr1qw508d6qejxtdg4y5r3zarvary0c5xw7kv8f3t4',
      'addr1qrp33g0q5c5txsp9arysrx4k6zdkfs4nce4xj0gdcccefvpysxf3qccfmv3'
    ]
  };

  /**
   * Migrate UserWallet data to enhanced Wallet model
   */
  async migrateUserWalletsToWallet(): Promise<MigrationResult> {
    const session = await mongoose.startSession();
    const result: MigrationResult = {
      success: false,
      migratedUsers: 0,
      migratedWallets: 0,
      errors: []
    };

    try {
      await session.withTransaction(async () => {
        // Get all UserWallets
        const userWallets = await UserWallet.find({}).session(session);
        logger.info(`Found ${userWallets.length} UserWallets to migrate`);

        // Group by userId
        const walletsByUser = userWallets.reduce((acc, wallet) => {
          const userId = wallet.userId.toString();
          if (!acc[userId]) {
            acc[userId] = [];
          }
          acc[userId].push(wallet);
          return acc;
        }, {} as { [key: string]: any[] });

        // Migrate each user's wallets
        for (const [userId, wallets] of Object.entries(walletsByUser)) {
          try {
            await this.migrateUserWallets(userId, wallets, session);
            result.migratedUsers++;
            result.migratedWallets += wallets.length;
          } catch (error) {
            const errorMsg = `Failed to migrate user ${userId}: ${error.message}`;
            logger.error(errorMsg);
            result.errors.push(errorMsg);
          }
        }
      });

      result.success = result.errors.length === 0;
      logger.info(`Migration completed: ${result.migratedUsers} users, ${result.migratedWallets} wallets`);
      
    } catch (error) {
      logger.error('Migration failed:', error);
      result.errors.push(`Migration failed: ${error.message}`);
    } finally {
      await session.endSession();
    }

    return result;
  }

  /**
   * Migrate wallets for a specific user
   */
  private async migrateUserWallets(userId: string, userWallets: any[], session: mongoose.ClientSession): Promise<void> {
    // Find or create Wallet document
    let wallet = await Wallet.findOne({ userId }).session(session);
    
    if (!wallet) {
      wallet = new Wallet({
        userId,
        assets: [],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });
    }

    // Migrate each UserWallet to Wallet asset
    for (const userWallet of userWallets) {
      const currency = userWallet.currency;
      
      // Find existing asset or create new one
      let asset = wallet.assets.find(a => a.symbol === currency);
      
      if (!asset) {
        asset = {
          symbol: currency,
          balance: userWallet.balance || 0,
          commissionBalance: 0,
          interestBalance: 0,
          mode: 'commission',
          network: userWallet.network || 'mainnet',
          address: userWallet.address,
          addresses: []
        };
        wallet.assets.push(asset);
      } else {
        // Update existing asset with UserWallet data
        asset.balance = (asset.balance || 0) + (userWallet.balance || 0);
        if (!asset.address && userWallet.address) {
          asset.address = userWallet.address;
        }
      }

      // Add address to addresses array
      if (userWallet.address) {
        if (!asset.addresses) {
          asset.addresses = [];
        }

        // Check if address already exists
        const existingAddress = asset.addresses.find(addr => addr.address === userWallet.address);
        
        if (!existingAddress) {
          asset.addresses.push({
            address: userWallet.address,
            network: userWallet.network || 'mainnet',
            isDefault: userWallet.isDefault || asset.addresses.length === 0,
            isActive: userWallet.isActive !== false,
            qrCodeUrl: userWallet.qrCodeUrl || this.generateQRCodeUrl(userWallet.address),
            label: userWallet.label || `${currency} Wallet`,
            addressIndex: userWallet.addressIndex || 0,
            lastUpdated: userWallet.lastUpdated || new Date(),
            withdrawalEnabled: userWallet.withdrawalEnabled !== false
          });
        }
      }
    }

    await wallet.save({ session });
    logger.info(`Migrated ${userWallets.length} wallets for user ${userId}`);
  }

  /**
   * Get wallet balances using enhanced Wallet model
   */
  async getWalletBalances(userId: string): Promise<WalletBalance[]> {
    try {
      const wallet = await Wallet.findOne({ userId });
      
      if (!wallet) {
        return [];
      }

      const balances: WalletBalance[] = [];

      for (const asset of wallet.assets) {
        const defaultAddress = asset.addresses?.find(addr => addr.isDefault && addr.isActive);
        
        balances.push({
          currency: asset.symbol,
          balance: asset.balance || 0,
          usdtValue: await this.calculateUSDTValue(asset.symbol, asset.balance || 0),
          lastUpdated: defaultAddress?.lastUpdated || new Date(),
          address: defaultAddress?.address || asset.address,
          qrCodeUrl: defaultAddress?.qrCodeUrl,
          network: defaultAddress?.network || asset.network
        });
      }

      return balances;
    } catch (error) {
      logger.error('Error getting wallet balances:', error);
      throw new Error('Failed to get wallet balances');
    }
  }

  /**
   * Get user wallets using enhanced Wallet model
   */
  async getUserWallets(userId: string): Promise<any[]> {
    try {
      const wallet = await Wallet.findOne({ userId });
      
      if (!wallet) {
        // Create default wallet with supported currencies
        return await this.createUserWallets(userId);
      }

      return wallet.assets.map(asset => {
        const defaultAddress = asset.addresses?.find(addr => addr.isDefault && addr.isActive);
        
        return {
          _id: asset._id,
          userId: wallet.userId,
          currency: asset.symbol,
          address: defaultAddress?.address || asset.address,
          balance: asset.balance || 0,
          commissionBalance: asset.commissionBalance || 0,
          interestBalance: asset.interestBalance || 0,
          totalEarnings: (asset.commissionBalance || 0) + (asset.interestBalance || 0),
          mode: asset.mode || 'commission',
          network: defaultAddress?.network || asset.network || 'mainnet',
          isActive: defaultAddress?.isActive !== false,
          qrCodeUrl: defaultAddress?.qrCodeUrl,
          label: defaultAddress?.label,
          lastUpdated: defaultAddress?.lastUpdated || new Date(),
          withdrawalEnabled: defaultAddress?.withdrawalEnabled !== false
        };
      });
    } catch (error) {
      logger.error('Error getting user wallets:', error);
      throw new Error('Failed to get user wallets');
    }
  }

  /**
   * Create wallets for all supported currencies
   */
  async createUserWallets(userId: string): Promise<any[]> {
    try {
      let wallet = await Wallet.findOne({ userId });
      
      if (!wallet) {
        wallet = new Wallet({
          userId,
          assets: [],
          totalCommissionEarned: 0,
          totalInterestEarned: 0
        });
      }

      const createdWallets = [];

      for (const currency of this.SUPPORTED_CURRENCIES) {
        try {
          // Check if asset already exists
          let asset = wallet.assets.find(a => a.symbol === currency);
          
          if (!asset) {
            const address = this.generateAddress(currency);
            
            asset = {
              symbol: currency,
              balance: 0,
              commissionBalance: 0,
              interestBalance: 0,
              mode: 'commission',
              network: 'mainnet',
              address,
              addresses: [{
                address,
                network: 'mainnet',
                isDefault: true,
                isActive: true,
                qrCodeUrl: this.generateQRCodeUrl(address),
                label: `${currency} Wallet`,
                addressIndex: 0,
                lastUpdated: new Date(),
                withdrawalEnabled: true
              }]
            };
            
            wallet.assets.push(asset);
            
            createdWallets.push({
              currency,
              address,
              balance: 0,
              network: 'mainnet'
            });
          }
        } catch (error) {
          logger.error(`Error creating ${currency} wallet for user ${userId}:`, error);
        }
      }

      await wallet.save();
      logger.info(`Created ${createdWallets.length} wallets for user ${userId}`);
      
      return await this.getUserWallets(userId);
    } catch (error) {
      logger.error('Error creating user wallets:', error);
      throw new Error('Failed to create user wallets');
    }
  }

  /**
   * Generate address for a currency
   */
  private generateAddress(currency: string): string {
    const isProduction = process.env.NODE_ENV === 'production';
    
    if (!isProduction) {
      const addresses = this.MOCK_ADDRESSES[currency as keyof typeof this.MOCK_ADDRESSES];
      if (addresses && addresses.length > 0) {
        const randomIndex = Math.floor(Math.random() * addresses.length);
        return addresses[randomIndex];
      }
    }
    
    // Fallback address generation
    switch (currency.toUpperCase()) {
      case 'BTC':
        return `bc1q${crypto.randomBytes(32).toString('hex').substring(0, 39)}`;
      case 'ETH':
      case 'USDT':
        return `0x${crypto.randomBytes(20).toString('hex')}`;
      case 'BNB':
        return `bnb1${crypto.randomBytes(20).toString('hex').substring(0, 38)}`;
      case 'ADA':
        return `addr1q${crypto.randomBytes(28).toString('hex').substring(0, 50)}`;
      default:
        return `${currency.toLowerCase()}_${crypto.randomBytes(20).toString('hex')}`;
    }
  }

  /**
   * Generate QR code URL
   */
  private generateQRCodeUrl(address: string): string {
    return `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(address)}`;
  }

  /**
   * Calculate USDT value (placeholder)
   */
  private async calculateUSDTValue(currency: string, amount: number): Promise<number> {
    if (currency === 'USDT') return amount;
    
    // Fallback rates for development
    const fallbackRates: { [key: string]: number } = {
      'BTC': 100000, 'ETH': 2500, 'BNB': 600, 'ADA': 0.4,
      'SOL': 150, 'DOGE': 0.15, 'TRX': 0.25, 'DOT': 8
    };
    
    const rate = fallbackRates[currency] || 1;
    return amount * rate;
  }
}

// Export singleton instance
const walletMigrationService = new WalletMigrationService();
export default walletMigrationService;
