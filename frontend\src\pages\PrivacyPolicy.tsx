import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  UnorderedList,
  ListItem,
  Divider,
  Button,
  Flex,
  Icon,
} from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import { FaArrowLeft, FaUserShield, FaLock, FaFileContract, FaShieldAlt, FaDatabase } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const PrivacyPolicy = () => {
  const { t } = useTranslation();
  
  return (
    <Box
      minH="100vh"
      bgGradient="linear(135deg, #0B0E11 0%, #1A1D29 25%, #0B0E11 50%, #1A1D29 75%, #0B0E11 100%)"
      py={12}
    >
      <Container maxW="4xl" px={{ base: 4, md: 8 }}>
        <Flex mb={8} align="center">
          <Button
            as={RouterLink}
            to="/register"
            variant="ghost"
            leftIcon={<FaArrowLeft />}
            color="#FCD535"
            _hover={{ bg: 'rgba(252, 213, 53, 0.1)' }}
            mr={4}
          >
            Back to Registration
          </Button>
        </Flex>

        <Box
          bg="rgba(30, 35, 41, 0.85)"
          backdropFilter="blur(20px)"
          borderRadius="2xl"
          borderWidth="1px"
          borderColor="rgba(252, 213, 53, 0.25)"
          boxShadow="0 25px 50px -12px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(252, 213, 53, 0.15)"
          p={{ base: 6, md: 10 }}
          position="relative"
          _before={{
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            borderRadius: '2xl',
            background: 'linear-gradient(135deg, rgba(252, 213, 53, 0.12) 0%, transparent 50%, rgba(252, 213, 53, 0.06) 100%)',
            pointerEvents: 'none',
          }}
        >
          <VStack spacing={8} align="start" position="relative" zIndex={1}>
            <Flex align="center" w="full" mb={2}>
              <Box
                p={3}
                borderRadius="lg"
                bg="rgba(252, 213, 53, 0.15)"
                display="flex"
                alignItems="center"
                justifyContent="center"
                mr={4}
              >
                <Icon as={FaUserShield} color="#FCD535" boxSize={6} />
              </Box>
              <Heading
                fontSize={{ base: '2xl', md: '3xl' }}
                fontWeight="900"
                bgGradient="linear(135deg, #FCD535 0%, #F8D12F 50%, #FCD535 100%)"
                bgClip="text"
                letterSpacing="tight"
              >
                Privacy Policy
              </Heading>
            </Flex>

            <Text color="#EAECEF" fontSize="md">
              Last Updated: May 30, 2025
            </Text>

            <Divider borderColor="rgba(252, 213, 53, 0.2)" />

            <VStack spacing={6} align="start" w="full">
              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  1. Introduction
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  At Shipping Finance ("Company", "we", "our", "us"), we respect your privacy and are committed to protecting your personal data. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our website and services. Please read this Privacy Policy carefully to understand our practices regarding your personal data.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  2. Information We Collect
                </Heading>
                <VStack spacing={3} align="start">
                  <Text color="#EAECEF" fontSize="md">
                    We collect several types of information from and about users of our platform, including:
                  </Text>
                  <UnorderedList spacing={2} pl={5}>
                    <ListItem color="#EAECEF">
                      <strong>Personal Identification Information:</strong> Name, email address, phone number, date of birth, country, city, and username.
                    </ListItem>
                    <ListItem color="#EAECEF">
                      <strong>Financial Information:</strong> Cryptocurrency wallet addresses, transaction history, and investment details.
                    </ListItem>
                    <ListItem color="#EAECEF">
                      <strong>Technical Data:</strong> IP address, browser type and version, time zone setting, browser plug-in types and versions, operating system, and platform.
                    </ListItem>
                    <ListItem color="#EAECEF">
                      <strong>Usage Data:</strong> Information about how you use our website, products, and services.
                    </ListItem>
                  </UnorderedList>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  3. How We Collect Your Information
                </Heading>
                <VStack spacing={3} align="start">
                  <Text color="#EAECEF" fontSize="md">
                    We collect information through:
                  </Text>
                  <UnorderedList spacing={2} pl={5}>
                    <ListItem color="#EAECEF">
                      Direct interactions when you register an account, make deposits or withdrawals, or contact our support team.
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Automated technologies or interactions, including cookies and similar tracking technologies.
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Third parties or publicly available sources, such as blockchain explorers for transaction verification.
                    </ListItem>
                  </UnorderedList>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  4. How We Use Your Information
                </Heading>
                <VStack spacing={3} align="start">
                  <Text color="#EAECEF" fontSize="md">
                    We use your information for the following purposes:
                  </Text>
                  <UnorderedList spacing={2} pl={5}>
                    <ListItem color="#EAECEF">
                      To create and manage your account
                    </ListItem>
                    <ListItem color="#EAECEF">
                      To process and manage your cryptocurrency transactions
                    </ListItem>
                    <ListItem color="#EAECEF">
                      To provide and improve our services
                    </ListItem>
                    <ListItem color="#EAECEF">
                      To communicate with you about your account, transactions, and platform updates
                    </ListItem>
                    <ListItem color="#EAECEF">
                      To comply with legal obligations, including anti-money laundering (AML) and know-your-customer (KYC) requirements
                    </ListItem>
                    <ListItem color="#EAECEF">
                      To detect and prevent fraud and unauthorized access
                    </ListItem>
                    <ListItem color="#EAECEF">
                      To send marketing communications if you have opted in to receive them
                    </ListItem>
                  </UnorderedList>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  5. Data Security
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  We have implemented appropriate security measures to prevent your personal data from being accidentally lost, used, or accessed in an unauthorized way. We use encryption, secure socket layer technology (SSL), and strict access controls. However, no method of transmission over the Internet or electronic storage is 100% secure, and we cannot guarantee absolute security.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  6. Data Retention
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  We will retain your personal data only for as long as necessary to fulfill the purposes for which we collected it, including for the purposes of satisfying any legal, regulatory, tax, accounting, or reporting requirements. In some circumstances, we may anonymize your personal data so that it can no longer be associated with you, in which case we may use such information without further notice to you.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  7. Disclosure of Your Information
                </Heading>
                <VStack spacing={3} align="start">
                  <Text color="#EAECEF" fontSize="md">
                    We may share your personal information with:
                  </Text>
                  <UnorderedList spacing={2} pl={5}>
                    <ListItem color="#EAECEF">
                      Service providers who perform services on our behalf
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Business partners with whom we jointly offer products or services
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Regulatory authorities, law enforcement agencies, and other third parties when required by law
                    </ListItem>
                    <ListItem color="#EAECEF">
                      Professional advisers including lawyers, bankers, auditors, and insurers
                    </ListItem>
                  </UnorderedList>
                  <Text color="#EAECEF" fontSize="md">
                    We require all third parties to respect the security of your personal data and to treat it in accordance with the law.
                  </Text>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  8. Your Legal Rights
                </Heading>
                <VStack spacing={3} align="start">
                  <Text color="#EAECEF" fontSize="md">
                    Depending on your location, you may have the following rights regarding your personal data:
                  </Text>
                  <UnorderedList spacing={2} pl={5}>
                    <ListItem color="#EAECEF">
                      The right to access your personal data
                    </ListItem>
                    <ListItem color="#EAECEF">
                      The right to request correction of your personal data
                    </ListItem>
                    <ListItem color="#EAECEF">
                      The right to request erasure of your personal data
                    </ListItem>
                    <ListItem color="#EAECEF">
                      The right to object to processing of your personal data
                    </ListItem>
                    <ListItem color="#EAECEF">
                      The right to request restriction of processing your personal data
                    </ListItem>
                    <ListItem color="#EAECEF">
                      The right to data portability
                    </ListItem>
                    <ListItem color="#EAECEF">
                      The right to withdraw consent
                    </ListItem>
                  </UnorderedList>
                  <Text color="#EAECEF" fontSize="md">
                    To exercise any of these rights, please contact us using the information provided in the "Contact Us" section below.
                  </Text>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  9. Cookies and Tracking Technologies
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  We use cookies and similar tracking technologies to track activity on our platform and to hold certain information. Cookies are files with a small amount of data that may include an anonymous unique identifier. You can instruct your browser to refuse all cookies or to indicate when a cookie is being sent. However, if you do not accept cookies, you may not be able to use some portions of our platform.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  10. Children's Privacy
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  Our platform is not intended for children under the age of 18. We do not knowingly collect personal information from children under 18. If you are a parent or guardian and you are aware that your child has provided us with personal information, please contact us so that we can take necessary actions.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  11. Changes to This Privacy Policy
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date. You are advised to review this Privacy Policy periodically for any changes.
                </Text>
              </Box>

              <Box>
                <Heading size="md" color="#FCD535" mb={3}>
                  12. Contact Us
                </Heading>
                <Text color="#EAECEF" fontSize="md">
                  If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
                </Text>
              </Box>
            </VStack>

            <Divider borderColor="rgba(252, 213, 53, 0.2)" my={6} />

            <Flex justify="space-between" w="full" wrap="wrap" gap={4}>
              <Button
                as={RouterLink}
                to="/register"
                variant="outline"
                borderColor="#FCD535"
                color="#FCD535"
                _hover={{
                  bg: 'rgba(252, 213, 53, 0.1)',
                }}
                leftIcon={<FaArrowLeft />}
              >
                Back to Registration
              </Button>
              <Button
                as={RouterLink}
                to="/terms"
                colorScheme="yellow"
                bg="#FCD535"
                color="#0B0E11"
                _hover={{
                  bg: '#F8D12F',
                }}
                rightIcon={<FaFileContract />}
              >
                View Terms of Service
              </Button>
            </Flex>
          </VStack>
        </Box>
      </Container>
    </Box>
  );
};

export default PrivacyPolicy;