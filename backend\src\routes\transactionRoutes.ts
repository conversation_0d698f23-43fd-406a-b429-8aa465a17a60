import express from 'express';
import {
  getTransactions,
  getTransactionById,
  calculateCommission,
  calculateInterest,
  getAdminTransactions,
  getTransactionSummary,
  // Note: getWithdrawableBalance moved to walletRoutes.ts
  // Note: updateTransactionStatus import removed as per requirements
} from '../controllers/transactionController';
import { getAllBalances } from '../controllers/walletController';
import { protect, admin } from '../middleware/authMiddleware';
import { cacheMiddleware, clearCache } from '../middleware/cacheMiddleware';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

// User routes - all are protected
router.get('/', protect, cacheMiddleware({ keyPrefix: 'api:transactions:' }), wrapController(getTransactions));
router.get('/summary', protect, cacheMiddleware({ keyPrefix: 'api:transactions:summary:' }), wrapController(getTransactionSummary));
router.get('/wallet/balances', protect, cacheMiddleware({ keyPrefix: 'api:transactions:wallet:balances:' }), wrapController(getAllBalances));
// Note: /wallet/withdrawable/:crypto route moved to /api/wallets/withdrawable-balance/:crypto in walletRoutes.ts
router.get('/:id', protect, cacheMiddleware({ keyPrefix: 'api:transaction:' }), wrapController(getTransactionById));
router.post('/calculate-commission', protect, wrapController(calculateCommission));
router.post('/calculate-interest', protect, wrapController(calculateInterest));

// Admin routes - protected and require admin role
router.get('/admin/all', protect, admin, wrapController(getAdminTransactions));
// Note: Transaction status update route removed as per requirements

export default router;
