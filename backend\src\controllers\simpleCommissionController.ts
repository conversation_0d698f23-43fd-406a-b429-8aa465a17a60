import { Request, Response } from 'express';
import mongoose from 'mongoose';
import {
  getCommissionRates,
  getUserCommissionEarnings,
  calculatePlatformCommission,
  calculateReferralCommission,
  COMMISSION_RATES
} from '../services/simpleCommissionService';
import Transaction from '../models/transactionModel';
import logger from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user: {
    _id: mongoose.Types.ObjectId;
    email: string;
    firstName: string;
    lastName: string;
    isAdmin?: boolean;
  };
}

/**
 * Komisyon oranlarını getir
 */
export const getCommissionRatesController = async (req: Request, res: Response): Promise<void> => {
  try {
    const rates = getCommissionRates();
    
    res.json({
      success: true,
      data: rates,
      message: 'Commission rates retrieved successfully'
    });
  } catch (error: any) {
    logger.error('Error getting commission rates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get commission rates',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Kullanıcının komisyon kazançlarını getir
 */
export const getUserCommissionEarningsController = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as AuthenticatedRequest).user._id;
    const earnings = await getUserCommissionEarnings(userId);
    
    res.json({
      success: true,
      data: earnings,
      message: 'Commission earnings retrieved successfully'
    });
  } catch (error: any) {
    logger.error('Error getting user commission earnings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get commission earnings',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Komisyon hesaplama (önizleme)
 */
export const calculateCommissionPreview = async (req: Request, res: Response): Promise<void> => {
  try {
    const { amount, type } = req.body;

    if (!amount || amount <= 0) {
      res.status(400).json({
        success: false,
        message: 'Valid amount is required'
      });
      return;
    }

    if (!type || !['platform', 'referral'].includes(type)) {
      res.status(400).json({
        success: false,
        message: 'Valid commission type is required (platform or referral)'
      });
      return;
    }

    let calculation;
    if (type === 'platform') {
      calculation = calculatePlatformCommission(amount);
    } else {
      calculation = calculateReferralCommission(amount);
    }

    res.json({
      success: true,
      data: {
        calculation,
        rates: getCommissionRates()
      },
      message: 'Commission calculated successfully'
    });
  } catch (error: any) {
    logger.error('Error calculating commission preview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate commission',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Kullanıcının komisyon geçmişini getir
 */
export const getUserCommissionHistory = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as AuthenticatedRequest).user._id;
    const { page = 1, limit = 10, type } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Query filter
    const filter: any = {
      userId,
      type: { $in: ['commission', 'referral_commission'] }
    };

    if (type && ['platform', 'referral'].includes(type as string)) {
      filter['metadata.commissionType'] = type;
    }

    // Get transactions
    const transactions = await Transaction.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .select('type asset amount status description createdAt metadata');

    // Get total count
    const total = await Transaction.countDocuments(filter);

    // Calculate totals by type
    const totals = await Transaction.aggregate([
      { $match: filter },
      {
        $group: {
          _id: '$metadata.commissionType',
          total: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        },
        totals,
        rates: getCommissionRates()
      },
      message: 'Commission history retrieved successfully'
    });
  } catch (error: any) {
    logger.error('Error getting commission history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get commission history',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Admin: Tüm komisyon istatistiklerini getir
 */
export const getCommissionStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = (req as AuthenticatedRequest).user;
    
    if (!user.isAdmin) {
      res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
      return;
    }

    const { startDate, endDate } = req.query;

    // Date filter
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.createdAt = { $gte: new Date(startDate as string) };
    }
    if (endDate) {
      dateFilter.createdAt = { 
        ...dateFilter.createdAt, 
        $lte: new Date(endDate as string) 
      };
    }

    // Platform commission stats
    const platformStats = await Transaction.aggregate([
      {
        $match: {
          type: 'commission',
          'metadata.commissionType': 'platform',
          ...dateFilter
        }
      },
      {
        $group: {
          _id: '$asset',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 },
          avgAmount: { $avg: '$amount' }
        }
      }
    ]);

    // Referral commission stats
    const referralStats = await Transaction.aggregate([
      {
        $match: {
          type: 'referral_commission',
          'metadata.commissionType': 'referral',
          ...dateFilter
        }
      },
      {
        $group: {
          _id: '$asset',
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 },
          avgAmount: { $avg: '$amount' }
        }
      }
    ]);

    // Daily commission trends
    const dailyTrends = await Transaction.aggregate([
      {
        $match: {
          type: { $in: ['commission', 'referral_commission'] },
          ...dateFilter
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
            type: '$metadata.commissionType'
          },
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': -1 } }
    ]);

    res.json({
      success: true,
      data: {
        platformStats,
        referralStats,
        dailyTrends,
        rates: getCommissionRates(),
        period: {
          startDate: startDate || 'All time',
          endDate: endDate || 'Now'
        }
      },
      message: 'Commission statistics retrieved successfully'
    });
  } catch (error: any) {
    logger.error('Error getting commission stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get commission statistics',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Admin: Komisyon oranlarını güncelle (sadece log için, oranlar sabit)
 */
export const updateCommissionRates = async (req: Request, res: Response): Promise<void> => {
  try {
    const user = (req as AuthenticatedRequest).user;
    
    if (!user.isAdmin) {
      res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
      return;
    }

    // Bu endpoint sadece bilgi amaçlı
    // Gerçek oranlar kod içinde sabit tanımlı
    
    logger.info('Commission rates update requested by admin', {
      adminId: user._id.toString(),
      currentRates: COMMISSION_RATES
    });

    res.json({
      success: true,
      data: {
        message: 'Commission rates are fixed in the system',
        currentRates: getCommissionRates(),
        note: 'To change rates, update COMMISSION_RATES in simpleCommissionService.ts'
      },
      message: 'Commission rates information retrieved'
    });
  } catch (error: any) {
    logger.error('Error in commission rates update:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process request',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get all commissions with pagination (for general commission listing)
 */
export const getAllCommissions = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req as AuthenticatedRequest).user._id;
    const { page = 1, limit = 10, type, status } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Build query for user's commissions
    const query: any = {
      $or: [
        { userId: userId }, // Platform commissions for this user
        { referrerId: userId }, // Referral commissions where user is referrer
        { referredId: userId } // Referral commissions where user is referred
      ]
    };

    if (type) {
      query.type = type;
    }

    if (status) {
      query.status = status;
    }

    // Get transactions that represent commissions
    const [commissions, total] = await Promise.all([
      Transaction.find({
        ...query,
        type: { $in: ['commission', 'referral_commission', 'platform_commission'] }
      })
        .populate('userId', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum),
      Transaction.countDocuments({
        ...query,
        type: { $in: ['commission', 'referral_commission', 'platform_commission'] }
      })
    ]);

    // Format response
    const formattedCommissions = commissions.map(commission => ({
      id: commission._id,
      amount: commission.amount,
      asset: commission.asset,
      type: commission.type,
      status: commission.status,
      description: commission.description,
      metadata: commission.metadata,
      user: commission.userId,
      createdAt: commission.createdAt,
      updatedAt: commission.updatedAt
    }));

    res.json({
      success: true,
      data: formattedCommissions,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum),
        hasNext: pageNum < Math.ceil(total / limitNum),
        hasPrev: pageNum > 1
      },
      message: 'Commissions retrieved successfully'
    });
  } catch (error: any) {
    logger.error('Error fetching commissions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch commissions',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export default {
  getCommissionRatesController,
  getUserCommissionEarningsController,
  calculateCommissionPreview,
  getUserCommissionHistory,
  getCommissionStats,
  updateCommissionRates,
  getAllCommissions
};
