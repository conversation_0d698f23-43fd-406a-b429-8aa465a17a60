import { Router } from 'express';
import { 
  CryptoAddressController,
  addressGenerationLimiter,
  generateAddressValidation,
  validateAddressValidation,
  getDepositAddressValidation
} from '../controllers/CryptoAddressController';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';

const router = Router();
const cryptoAddressController = new CryptoAddressController();

/**
 * @route   POST /api/crypto/addresses/generate
 * @desc    Generate new crypto address for user
 * @access  Private
 */
router.post(
  '/generate',
  authenticateToken,
  addressGenerationLimiter,
  generateAddressValidation,
  validateRequest,
  cryptoAddressController.generateAddress
);

/**
 * @route   GET /api/crypto/addresses
 * @desc    Get user's crypto addresses
 * @access  Private
 */
router.get(
  '/',
  authenticateToken,
  cryptoAddressController.getUserAddresses
);

/**
 * @route   POST /api/crypto/addresses/validate
 * @desc    Validate crypto address format
 * @access  Private
 */
router.post(
  '/validate',
  authenticateToken,
  validateAddressValidation,
  validateRequest,
  cryptoAddressController.validateAddress
);

/**
 * @route   GET /api/crypto/addresses/deposit/:currency
 * @desc    Get deposit address for specific currency
 * @access  Private
 */
router.get(
  '/deposit/:currency',
  authenticateToken,
  getDepositAddressValidation,
  validateRequest,
  cryptoAddressController.getDepositAddress
);

export default router;
