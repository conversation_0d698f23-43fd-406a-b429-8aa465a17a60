import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/userModel';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

export const protect = async (req: Request, res: Response, next: NextFunction): Promise<void> => {

  // Production-grade authentication - no development bypasses for security

  let token: string | undefined;

  // Log request details for debugging
  console.log('Request URL:', req.originalUrl);
  console.log('Request method:', req.method);
  console.log('Request headers:', JSON.stringify(req.headers, null, 2));
  console.log('Request cookies:', JSON.stringify(req.cookies, null, 2));

  // Check if token exists in cookies first (preferred method)
  if (req.cookies && req.cookies.token) {
    token = req.cookies.token;
    console.log('Token found in cookies');
  }
  // Fallback to Authorization header for backward compatibility
  else if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
    console.log('Token found in Authorization header');
  }
  console.log(token)
  if (token) {
    try {
      // Log token for debugging (only show prefix for security)
      console.log('Token received:', token ? token.substring(0, 10) + '...' : 'none');

      // Verify token
      const decoded: any = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret');

      // Log decoded token for debugging
      console.log('Decoded token:', decoded ? { id: decoded.id, iat: decoded.iat, exp: decoded.exp } : 'none');

      // Get user from the token
      const user = await User.findById(decoded.id).select('-password');

      // Log user for debugging
      console.log('User found:', user ? { _id: user._id, email: user.email, isAdmin: user.isAdmin } : 'none');

      if (!user) {
        console.error('User not found for token');
        // Clear the invalid cookies with proper options
        res.clearCookie('token', {
          path: '/',
          secure: process.env.NODE_ENV === 'production',
          sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
        });
        res.clearCookie('adminToken', {
          path: '/',
          secure: process.env.NODE_ENV === 'production',
          sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
        });
        res.status(401).json({ message: 'Not authorized, user not found' });
        return;
      }

      // Set user in request object
      req.user = user;

      // Always ensure the admin cookie is set correctly based on user.isAdmin status
      if (user.isAdmin) {
        // Set admin cookie if user is admin
        res.cookie('adminToken', 'true', {
          httpOnly: false, // Allow JavaScript access for UI state
          secure: process.env.NODE_ENV === 'production', // Only secure in production
          sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production for cross-site requests
          maxAge: 30 * 24 * 60 * 60 * 1000,
          path: '/'
        });
      } else {
        // Clear admin cookie if user is not admin
        res.clearCookie('adminToken', {
          path: '/',
          secure: process.env.NODE_ENV === 'production',
          sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
        });
      }

      return next();
    } catch (error: any) {
      console.error('Token verification error:', error);
      console.error('JWT Secret length:', (process.env.JWT_SECRET || 'fallback_secret').length);

      // Clear the invalid cookies with proper options
      res.clearCookie('token', {
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
      });
      res.clearCookie('adminToken', {
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
      });

      res.status(401).json({
        message: 'Not authorized, token failed',
        error: error.message || 'Unknown error',
        tokenInfo: token ? {
          length: token.length,
          prefix: token.substring(0, 10) + '...'
        } : 'No token'
      });
      return;
    }
  }

  console.error('No token provided');
  res.status(401).json({ message: 'Not authorized, no token' });
  return;
};

// Admin middleware - unified authentication with admin permission check
export const admin = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    console.error('Admin middleware: No user found in request');
    res.status(401).json({
      message: 'Not authorized, no user found',
      code: 'NO_USER'
    });
    return;
  }

  if (req.user.isAdmin) {
    console.log(`Admin access granted for user ${req.user._id} (${req.user.email})`);

    // Ensure admin cookie is set for frontend UI state
    res.cookie('adminToken', 'true', {
      httpOnly: false, // Allow JavaScript access for UI state
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production for cross-site requests
      maxAge: 30 * 24 * 60 * 60 * 1000,
      path: '/'
    });

    next();
  } else {
    console.log(`User ${req.user._id} (${req.user.email}) attempted to access admin route but is not an admin`);
    res.status(403).json({
      message: 'Not authorized as an admin',
      code: 'NOT_ADMIN',
      details: 'This endpoint requires admin privileges'
    });
  }
};
