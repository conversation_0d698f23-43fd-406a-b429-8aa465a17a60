import React, { createContext, useContext, useEffect, useState } from 'react';
import { cryptoPriceService, CryptoPriceData } from '../services/cryptoPriceService';

interface CryptoPriceContextType {
  prices: CryptoPriceData;
  loading: boolean;
  error: string | null;
  refreshPrices: () => Promise<void>;
  getPrice: (symbol: string) => number;
  convertToUSD: (amount: number, symbol: string) => number;
  convertFromUSD: (usdAmount: number, symbol: string) => number;
  cacheInfo: {
    hasCache: boolean;
    cacheAge: number;
    isValid: boolean;
    priceCount: number;
  };
}

const CryptoPriceContext = createContext<CryptoPriceContextType | undefined>(undefined);

interface CryptoPriceProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component để quản lý giá tiền tệ toàn cục
 * Tự động load gi<PERSON> khi ứng dụng khởi động và cache vào localStorage
 */
export const CryptoPriceProvider: React.FC<CryptoPriceProviderProps> = ({ children }) => {
  const [prices, setPrices] = useState<CryptoPriceData>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Load prices on mount
  useEffect(() => {
    let mounted = true;

    const initializePrices = async () => {
      try {
        console.log('💰 CryptoPriceProvider: Initializing crypto prices...');
        setLoading(true);
        setError(null);
        
        // Load prices from cache or API
        const priceData = await cryptoPriceService.getPrices();
        
        if (mounted) {
          setPrices(priceData);
          setLoading(false);
          console.log('💰 CryptoPriceProvider: Prices initialized successfully', {
            priceCount: Object.keys(priceData).length,
            cacheInfo: cryptoPriceService.getCacheInfo()
          });
        }
      } catch (err: any) {
        if (mounted) {
          setError(err.message || 'Failed to initialize crypto prices');
          setLoading(false);
          console.error('💰 CryptoPriceProvider: Error initializing prices:', err);
        }
      }
    };

    initializePrices();

    return () => {
      mounted = false;
    };
  }, []);

  // Subscribe to price updates
  useEffect(() => {
    const unsubscribe = cryptoPriceService.subscribe((updatedPrices) => {
      setPrices(updatedPrices);
      console.log('💰 CryptoPriceProvider: Prices updated via subscription');
    });

    return unsubscribe;
  }, []);

  // Refresh prices manually
  const refreshPrices = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const priceData = await cryptoPriceService.refreshPrices();
      setPrices(priceData);
      
      console.log('💰 CryptoPriceProvider: Prices refreshed manually');
    } catch (err: any) {
      setError(err.message || 'Failed to refresh crypto prices');
      console.error('💰 CryptoPriceProvider: Error refreshing prices:', err);
    } finally {
      setLoading(false);
    }
  };

  // Get price for specific symbol
  const getPrice = (symbol: string): number => {
    const normalizedSymbol = symbol.toUpperCase();
    return prices[normalizedSymbol] || 0;
  };

  // Convert to USD
  const convertToUSD = (amount: number, symbol: string): number => {
    const price = getPrice(symbol);
    return amount * price;
  };

  // Convert from USD
  const convertFromUSD = (usdAmount: number, symbol: string): number => {
    const price = getPrice(symbol);
    return price > 0 ? usdAmount / price : 0;
  };

  // Get cache info
  const cacheInfo = cryptoPriceService.getCacheInfo();

  const contextValue: CryptoPriceContextType = {
    prices,
    loading,
    error,
    refreshPrices,
    getPrice,
    convertToUSD,
    convertFromUSD,
    cacheInfo
  };

  return (
    <CryptoPriceContext.Provider value={contextValue}>
      {children}
    </CryptoPriceContext.Provider>
  );
};

/**
 * Hook để sử dụng CryptoPriceContext
 */
export const useCryptoPriceContext = (): CryptoPriceContextType => {
  const context = useContext(CryptoPriceContext);
  if (context === undefined) {
    throw new Error('useCryptoPriceContext must be used within a CryptoPriceProvider');
  }
  return context;
};

/**
 * Component hiển thị thông tin cache (cho debugging)
 */
export const CryptoPriceCacheInfo: React.FC = () => {
  const { cacheInfo, prices } = useCryptoPriceContext();
  
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '10px',
      right: '10px',
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999
    }}>
      <div><strong>Crypto Price Cache Info:</strong></div>
      <div>Has Cache: {cacheInfo.hasCache ? '✅' : '❌'}</div>
      <div>Cache Valid: {cacheInfo.isValid ? '✅' : '❌'}</div>
      <div>Price Count: {cacheInfo.priceCount}</div>
      <div>Cache Age: {Math.round(cacheInfo.cacheAge / 1000)}s</div>
      <div>Sample Prices:</div>
      <div style={{ fontSize: '10px' }}>
        {Object.entries(prices).slice(0, 3).map(([symbol, price]) => (
          <div key={symbol}>{symbol}: ${price}</div>
        ))}
      </div>
    </div>
  );
};

export default CryptoPriceProvider;
