import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { getSocketService } from './socketService';

/**
 * Enhanced Rate Limiting Service
 * Provides intelligent rate limiting with different strategies
 */

export interface RateLimitConfig {
  windowMs: number;
  max: number;
  message: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: Request) => string;
  onLimitReached?: (req: Request, res: Response) => void;
}

export interface RateLimitRule {
  name: string;
  pattern: string | RegExp;
  config: RateLimitConfig;
}

export class RateLimitingService {
  private static instance: RateLimitingService;
  private limiters: Map<string, any> = new Map();
  private rules: RateLimitRule[] = [];

  public static getInstance(): RateLimitingService {
    if (!RateLimitingService.instance) {
      RateLimitingService.instance = new RateLimitingService();
    }
    return RateLimitingService.instance;
  }

  constructor() {
    this.initializeDefaultRules();
  }

  /**
   * Initialize default rate limiting rules
   */
  private initializeDefaultRules(): void {
    // Authentication endpoints
    this.addRule({
      name: 'auth_login',
      pattern: /^\/api\/auth\/login$/,
      config: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 5, // 5 attempts per window
        message: 'Too many login attempts. Please try again later.',
        keyGenerator: (req) => `login_${req.ip}_${req.body?.email || 'unknown'}`,
        onLimitReached: this.handleAuthLimitReached
      }
    });

    this.addRule({
      name: 'auth_register',
      pattern: /^\/api\/auth\/register$/,
      config: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 3, // 3 registrations per hour per IP
        message: 'Too many registration attempts. Please try again later.',
        keyGenerator: (req) => `register_${req.ip}`,
        onLimitReached: this.handleAuthLimitReached
      }
    });

    this.addRule({
      name: 'password_reset',
      pattern: /^\/api\/auth\/forgot-password$/,
      config: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 3, // 3 password reset requests per hour
        message: 'Too many password reset requests. Please try again later.',
        keyGenerator: (req) => `reset_${req.ip}_${req.body?.email || 'unknown'}`,
        onLimitReached: this.handleSecurityLimitReached
      }
    });

    // 2FA endpoints
    this.addRule({
      name: '2fa_setup',
      pattern: /^\/api\/auth\/2fa\/setup$/,
      config: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 3, // 3 setup attempts per hour
        message: 'Too many 2FA setup attempts. Please try again later.',
        keyGenerator: (req) => `2fa_setup_${(req as any).user?.id || req.ip}`,
        onLimitReached: this.handleSecurityLimitReached
      }
    });

    this.addRule({
      name: '2fa_verify',
      pattern: /^\/api\/auth\/2fa\/verify/,
      config: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 10, // 10 verification attempts per 15 minutes
        message: 'Too many 2FA verification attempts. Please try again later.',
        keyGenerator: (req) => `2fa_verify_${(req as any).user?.id || req.ip}`,
        onLimitReached: this.handleSecurityLimitReached
      }
    });

    // Financial endpoints
    this.addRule({
      name: 'investment_create',
      pattern: /^\/api\/investments$/,
      config: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 10, // 10 investments per hour
        message: 'Too many investment attempts. Please try again later.',
        keyGenerator: (req) => `investment_${(req as any).user?.id || req.ip}`,
        onLimitReached: this.handleFinancialLimitReached
      }
    });

    this.addRule({
      name: 'withdrawal_request',
      pattern: /^\/api\/withdrawals$/,
      config: {
        windowMs: 24 * 60 * 60 * 1000, // 24 hours
        max: 5, // 5 withdrawal requests per day
        message: 'Too many withdrawal requests. Please try again tomorrow.',
        keyGenerator: (req) => `withdrawal_${(req as any).user?.id || req.ip}`,
        onLimitReached: this.handleFinancialLimitReached
      }
    });

    // Address generation
    this.addRule({
      name: 'address_generation',
      pattern: /^\/api\/crypto\/addresses\/generate$/,
      config: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 5, // 5 addresses per hour
        message: 'Too many address generation requests. Please try again later.',
        keyGenerator: (req) => `addr_gen_${(req as any).user?.id || req.ip}`,
        onLimitReached: this.handleSecurityLimitReached
      }
    });

    // API endpoints
    this.addRule({
      name: 'api_general',
      pattern: /^\/api\//,
      config: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100, // 100 requests per 15 minutes
        message: 'Too many API requests. Please slow down.',
        keyGenerator: (req) => `api_${(req as any).user?.id || req.ip}`,
        skipSuccessfulRequests: true
      }
    });

    // Admin endpoints
    this.addRule({
      name: 'admin_endpoints',
      pattern: /^\/api\/admin\//,
      config: {
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 50, // 50 admin requests per hour
        message: 'Too many admin requests. Please try again later.',
        keyGenerator: (req) => `admin_${(req as any).user?.id || req.ip}`,
        onLimitReached: this.handleAdminLimitReached
      }
    });
  }

  /**
   * Add a new rate limiting rule
   */
  public addRule(rule: RateLimitRule): void {
    this.rules.push(rule);

    const limiter = rateLimit({
      windowMs: rule.config.windowMs,
      max: rule.config.max,
      message: {
        error: rule.config.message,
        retryAfter: Math.ceil(rule.config.windowMs / 1000)
      },
      keyGenerator: rule.config.keyGenerator,
      skipSuccessfulRequests: rule.config.skipSuccessfulRequests,
      skipFailedRequests: rule.config.skipFailedRequests,
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        // Log rate limit hit
        logger.warn('Rate limit exceeded', {
          rule: rule.name,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          userId: (req as any).user?.id,
          path: req.path,
          method: req.method
        });

        // Call custom handler if provided
        if (rule.config.onLimitReached) {
          rule.config.onLimitReached(req, res);
        }

        res.status(429).json({
          error: rule.config.message,
          retryAfter: Math.ceil(rule.config.windowMs / 1000)
        });
      }
    });

    this.limiters.set(rule.name, limiter);
  }

  /**
   * Get rate limiter for a specific rule
   */
  public getLimiter(ruleName: string): any {
    return this.limiters.get(ruleName);
  }

  /**
   * Get appropriate rate limiter for a request path
   */
  public getLimiterForPath(path: string): any {
    for (const rule of this.rules) {
      if (typeof rule.pattern === 'string') {
        if (path === rule.pattern) {
          return this.limiters.get(rule.name);
        }
      } else if (rule.pattern instanceof RegExp) {
        if (rule.pattern.test(path)) {
          return this.limiters.get(rule.name);
        }
      }
    }
    return null;
  }

  /**
   * Create a custom rate limiter
   */
  public createCustomLimiter(config: RateLimitConfig): any {
    return rateLimit({
      windowMs: config.windowMs,
      max: config.max,
      message: {
        error: config.message,
        retryAfter: Math.ceil(config.windowMs / 1000)
      },
      keyGenerator: config.keyGenerator,
      skipSuccessfulRequests: config.skipSuccessfulRequests,
      skipFailedRequests: config.skipFailedRequests,
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        logger.warn('Custom rate limit exceeded', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          userId: (req as any).user?.id,
          path: req.path,
          method: req.method
        });

        if (config.onLimitReached) {
          config.onLimitReached(req, res);
        }

        res.status(429).json({
          error: config.message,
          retryAfter: Math.ceil(config.windowMs / 1000)
        });
      }
    });
  }

  /**
   * Event handlers for different types of rate limit violations
   */

  private handleAuthLimitReached = (req: Request, res: Response): void => {
    const userId = (req as any).user?.id;
    const email = req.body?.email;

    logger.error('Authentication rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId,
      email,
      path: req.path,
      severity: 'HIGH'
    });

    // Send security alert
    try {
      const socketService = getSocketService();
      socketService.sendSystemNotification({
        type: 'security_alert',
        severity: 'HIGH',
        message: 'Multiple failed authentication attempts detected',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.warn('Failed to send security alert:', error);
    }
  };

  private handleSecurityLimitReached = (req: Request, res: Response): void => {
    const userId = (req as any).user?.id;

    logger.error('Security endpoint rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId,
      path: req.path,
      severity: 'HIGH'
    });

    // Send security alert to user if authenticated
    if (userId) {
      try {
        const socketService = getSocketService();
        socketService.broadcastToUser(userId, {
          type: 'security_alert',
          payload: {
            message: 'Unusual security activity detected on your account',
            timestamp: new Date().toISOString(),
            action: 'rate_limit_exceeded',
            ip: req.ip
          }
        });
      } catch (error) {
        logger.warn('Failed to send user security alert:', error);
      }
    }
  };

  private handleFinancialLimitReached = (req: Request, res: Response): void => {
    const userId = (req as any).user?.id;

    logger.error('Financial endpoint rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId,
      path: req.path,
      severity: 'CRITICAL'
    });

    // Send immediate alert to admins
    try {
      const socketService = getSocketService();
      socketService.sendSystemNotification({
        type: 'financial_security_alert',
        severity: 'CRITICAL',
        message: 'Financial endpoint rate limit exceeded',
        userId,
        ip: req.ip,
        path: req.path,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.warn('Failed to send financial security alert:', error);
    }
  };

  private handleAdminLimitReached = (req: Request, res: Response): void => {
    const userId = (req as any).user?.id;

    logger.error('Admin endpoint rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId,
      path: req.path,
      severity: 'CRITICAL'
    });

    // Send immediate alert
    try {
      const socketService = getSocketService();
      socketService.sendSystemNotification({
        type: 'admin_security_alert',
        severity: 'CRITICAL',
        message: 'Admin endpoint rate limit exceeded',
        userId,
        ip: req.ip,
        path: req.path,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.warn('Failed to send admin security alert:', error);
    }
  };

  /**
   * Get rate limiting statistics
   */
  public getStatistics(): any {
    return {
      totalRules: this.rules.length,
      activeRules: this.rules.map(rule => ({
        name: rule.name,
        pattern: rule.pattern.toString(),
        windowMs: rule.config.windowMs,
        max: rule.config.max
      }))
    };
  }
}
