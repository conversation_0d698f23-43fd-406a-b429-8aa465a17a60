import React, { useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  VStack,
  Divider,
  useColorModeValue,
  Button,
  Flex,
  Icon,
  SimpleGrid,
  Card,
  CardBody,
  Link
} from '@chakra-ui/react';
import { FaQuestionCircle, FaMoneyBillWave, FaShieldAlt, FaUserAlt, FaExchangeAlt, FaInfoCircle } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { Link as RouterLink } from 'react-router-dom';
import { Helmet } from 'react-helmet';

/**
 * FAQ Page Component
 * 
 * Displays frequently asked questions organized by categories
 */
const FAQ: React.FC = () => {
  const { t } = useTranslation();
  const bgColor = useColorModeValue('white', '#0B0E11');
  const cardBgColor = useColorModeValue('gray.50', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const accentColor = '#F0B90B';
  
  // Update document title on component mount
  useEffect(() => {
    document.title = 'FAQ | Shipping Finance';
  }, []);
  
  // FAQ categories
  const categories = [
    { 
      id: 'general', 
      title: 'General Information', 
      icon: FaInfoCircle,
      questions: [
        {
          question: 'What is Shipping Finance?',
          answer: 'Shipping Finance is an innovative financial platform that offers investors stable and sustainable returns by leveraging opportunities in international trade and cryptocurrency markets. Our platform allows users to invest in various cryptocurrencies and earn competitive returns.'
        },
        {
          question: 'How does Shipping Finance work?',
          answer: 'Shipping Finance works by allowing users to deposit cryptocurrencies like Bitcoin, Ethereum, and Tether into their accounts. These funds are then used in various investment strategies to generate returns. Users can track their investments, withdraw funds, and manage their portfolios through our user-friendly platform.'
        },
        {
          question: 'Is Shipping Finance available worldwide?',
          answer: 'Yes, Shipping Finance is available to users worldwide. However, users are responsible for ensuring they comply with their local regulations regarding cryptocurrency investments.'
        }
      ]
    },
    { 
      id: 'investments', 
      title: 'Investments', 
      icon: FaMoneyBillWave,
      questions: [
        {
          question: 'What cryptocurrencies can I invest with?',
          answer: 'Currently, Shipping Finance supports investments in Bitcoin (BTC), Ethereum (ETH), and Tether (USDT). We plan to add support for more cryptocurrencies in the future.'
        },
        {
          question: 'What are the minimum and maximum investment amounts?',
          answer: 'The minimum investment amount is 0.01 BTC, 0.1 ETH, or 100 USDT, depending on your chosen cryptocurrency. There is no maximum investment amount, but larger investments may require additional verification.'
        },
        {
          question: 'How are returns calculated?',
          answer: 'Returns are calculated based on the performance of our investment strategies, market conditions, and the amount invested. Our platform provides transparent reporting of all returns and fees.'
        }
      ]
    },
    { 
      id: 'security', 
      title: 'Security', 
      icon: FaShieldAlt,
      questions: [
        {
          question: 'How secure is my investment with Shipping Finance?',
          answer: 'Shipping Finance employs industry-leading security measures, including cold storage for cryptocurrencies, two-factor authentication, and regular security audits. We prioritize the security of your investments and personal information.'
        },
        {
          question: 'Is my personal information safe?',
          answer: 'Yes, we take data protection very seriously. All personal information is encrypted and stored securely. We comply with international data protection regulations and never share your information with third parties without your consent.'
        },
        {
          question: 'What happens if I lose access to my account?',
          answer: 'If you lose access to your account, you can use our account recovery process. This involves verifying your identity through multiple channels to ensure only the rightful account owner can regain access.'
        }
      ]
    },
    { 
      id: 'account', 
      title: 'Account Management', 
      icon: FaUserAlt,
      questions: [
        {
          question: 'How do I create an account?',
          answer: 'Creating an account is simple. Click on the "Register" button, fill in your details, verify your email address, and complete the KYC process if required. Once approved, you can start investing.'
        },
        {
          question: 'Can I have multiple accounts?',
          answer: 'No, each user is allowed only one account. Multiple accounts from the same user may be suspended.'
        },
        {
          question: 'How do I verify my account?',
          answer: 'Account verification involves submitting identification documents through our secure verification system. This typically includes a government-issued ID and proof of address. The verification process usually takes 1-2 business days.'
        }
      ]
    },
    { 
      id: 'transactions', 
      title: 'Deposits and Withdrawals', 
      icon: FaExchangeAlt,
      questions: [
        {
          question: 'How long do deposits take to process?',
          answer: 'Cryptocurrency deposits are typically processed within 1-3 network confirmations, which can take from a few minutes to an hour depending on network congestion.'
        },
        {
          question: 'How do I withdraw my funds?',
          answer: 'To withdraw funds, go to your dashboard, click on "Withdraw," select the cryptocurrency and amount, and enter your wallet address. Withdrawals are typically processed within 24 hours.'
        },
        {
          question: 'Are there any fees for deposits or withdrawals?',
          answer: 'Deposits are free of charge. Withdrawals incur a small network fee to cover transaction costs on the blockchain. These fees are clearly displayed before you confirm your withdrawal.'
        }
      ]
    }
  ];
  
  return (
    <>
      <Helmet>
        <title>FAQ | Shipping Finance</title>
        <meta name="description" content="Frequently asked questions about Shipping Finance cryptocurrency investment platform. Learn about our services, security measures, and how to get started." />
      </Helmet>
      
      <Box bg={bgColor} py={10}>
        <Container maxW="container.xl">
          {/* Header */}
          <Box textAlign="center" mb={10}>
            <Heading as="h1" size="xl" mb={4} color={accentColor}>
              Frequently Asked Questions
            </Heading>
            <Text fontSize="lg" maxW="2xl" mx="auto">
              Find answers to the most common questions about Shipping Finance and our cryptocurrency investment platform.
            </Text>
          </Box>
          
          {/* Category Navigation */}
          <SimpleGrid columns={{ base: 2, md: 3, lg: 5 }} spacing={4} mb={10}>
            {categories.map((category) => (
              <Card 
                key={category.id} 
                bg={cardBgColor} 
                borderColor={borderColor} 
                borderWidth="1px"
                borderRadius="md"
                _hover={{ 
                  transform: 'translateY(-5px)', 
                  boxShadow: 'lg',
                  borderColor: accentColor
                }}
                transition="all 0.3s ease"
              >
                <CardBody>
                  <Link 
                    as={RouterLink} 
                    to={`/faq#${category.id}`}
                    _hover={{ textDecoration: 'none' }}
                  >
                    <VStack spacing={3} align="center">
                      <Icon as={category.icon} boxSize={8} color={accentColor} />
                      <Text fontWeight="600" textAlign="center">
                        {category.title}
                      </Text>
                    </VStack>
                  </Link>
                </CardBody>
              </Card>
            ))}
          </SimpleGrid>
          
          {/* FAQ Accordion */}
          <VStack spacing={10} align="stretch">
            {categories.map((category) => (
              <Box key={category.id} id={category.id}>
                <Flex align="center" mb={4}>
                  <Icon as={category.icon} boxSize={6} color={accentColor} mr={3} />
                  <Heading as="h2" size="lg">
                    {category.title}
                  </Heading>
                </Flex>
                
                <Divider mb={6} />
                
                <Accordion allowMultiple>
                  {category.questions.map((item, index) => (
                    <AccordionItem 
                      key={index} 
                      borderWidth="1px" 
                      borderRadius="md" 
                      mb={4}
                      borderColor={borderColor}
                      _hover={{ borderColor: accentColor }}
                    >
                      <h3>
                        <AccordionButton py={4}>
                          <Box flex="1" textAlign="left" fontWeight="600">
                            {item.question}
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                      </h3>
                      <AccordionPanel pb={4}>
                        <Text>{item.answer}</Text>
                      </AccordionPanel>
                    </AccordionItem>
                  ))}
                </Accordion>
              </Box>
            ))}
          </VStack>
          
          {/* Contact Section */}
          <Box mt={16} textAlign="center" p={8} borderRadius="lg" bg={cardBgColor} borderWidth="1px" borderColor={borderColor}>
            <Heading as="h3" size="md" mb={4}>
              Still have questions?
            </Heading>
            <Text mb={6}>
              If you couldn't find the answer to your question, our support team is here to help.
            </Text>
            <Button 
              as={RouterLink} 
              to="/contact" 
              colorScheme="yellow" 
              size="lg"
              _hover={{
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 8px rgba(240, 185, 11, 0.3)',
              }}
            >
              Contact Support
            </Button>
          </Box>
        </Container>
      </Box>
    </>
  );
};

export default FAQ;
