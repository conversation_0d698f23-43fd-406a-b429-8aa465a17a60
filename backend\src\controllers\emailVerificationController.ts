import { Request, Response } from 'express';
import crypto from 'crypto';
import User from '../models/userModel';
import { emailService } from '../services/emailService';
import { logger } from '../utils/logger';
import { catchAsync } from '../utils/errorHandler';
import { AppError } from '../utils/AppError';
import { cacheService } from '../services/cacheService';

/**
 * Send email verification
 * @route POST /api/users/send-verification
 * @access Private
 */
export const sendVerificationEmail = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Check if email is already verified
  if (user.emailVerified) {
    return res.status(400).json({
      status: 'error',
      message: 'Email is already verified'
    });
  }

  // Check rate limiting - max 5 attempts per day
  const now = new Date();
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  if (user.emailVerificationLastAttempt && user.emailVerificationLastAttempt > oneDayAgo) {
    if (user.emailVerificationAttempts >= 5) {
      throw new AppError('Maximum verification attempts reached. Please try again tomorrow.', 429);
    }
  } else {
    // Reset attempts if more than 24 hours have passed
    user.emailVerificationAttempts = 0;
  }

  // Check if we need to wait before sending another email (15 minutes cooldown)
  if (user.emailVerificationSentAt) {
    const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000);
    if (user.emailVerificationSentAt > fifteenMinutesAgo) {
      const waitTime = Math.ceil((user.emailVerificationSentAt.getTime() + 15 * 60 * 1000 - now.getTime()) / 60000);
      throw new AppError(`Please wait ${waitTime} minutes before requesting another verification email`, 429);
    }
  }

  try {
    // Generate new verification token
    const token = user.generateEmailVerificationToken();
    
    // Update attempt tracking
    user.emailVerificationAttempts += 1;
    user.emailVerificationLastAttempt = now;
    
    await user.save();

    // Send verification email
    const emailSent = await emailService.sendVerificationEmail(user, token);

    if (!emailSent) {
      throw new AppError('Failed to send verification email. Please try again later.', 500);
    }

    logger.info('Verification email sent', {
      userId: user._id,
      email: user.email,
      attempt: user.emailVerificationAttempts
    });

    res.status(200).json({
      status: 'success',
      message: 'Verification email sent successfully',
      data: {
        email: user.email,
        attemptsRemaining: 5 - user.emailVerificationAttempts
      }
    });

  } catch (error) {
    logger.error('Error sending verification email', {
      userId: user._id,
      email: user.email,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
});

/**
 * Verify email with token
 * @route GET /api/users/verify-email/:token
 * @access Public
 */
export const verifyEmail = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token } = req.params;

    // Validate token parameter
    if (!token || typeof token !== 'string' || token.trim() === '') {
      res.status(400).json({
        status: 'error',
        message: 'Verification token is required',
        error: 'MISSING_TOKEN'
      });
      return;
    }

    // Sanitize token (remove any whitespace)
    const sanitizedToken = token.trim();

    let user: any = null;
    try {
      // Find user with this verification token
      user = await User.findOne({
        emailVerificationToken: sanitizedToken,
        emailVerificationExpires: { $gt: new Date() }
      });
    } catch (dbError) {
      logger.error('Database error while finding user with verification token', {
        token: sanitizedToken,
        error: dbError instanceof Error ? dbError.message : 'Unknown database error'
      });

      res.status(500).json({
        status: 'error',
        message: 'Database error occurred while verifying email',
        error: 'DATABASE_ERROR'
      });
      return;
    }

    // Check if user exists with valid token
    if (!user) {
      logger.warn('Invalid or expired verification token attempted', {
        token: sanitizedToken,
        timestamp: new Date().toISOString()
      });

      res.status(404).json({
        status: 'error',
        message: 'Invalid or expired verification token',
        error: 'INVALID_TOKEN'
      });
      return;
    }

    // Check if email is already verified
    if (user.emailVerified) {
      logger.info('Email verification attempted for already verified user', {
        userId: user._id,
        email: user.email
      });

      res.status(200).json({
        status: 'success',
        message: 'Email is already verified',
        data: {
          emailVerified: true,
          user: {
            id: user._id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName
          }
        }
      });
      return;
    }

    // Additional token validation using user method
    try {
      if (!user.isEmailVerificationTokenValid || !user.isEmailVerificationTokenValid(sanitizedToken)) {
        logger.warn('Token validation failed for user', {
          userId: user._id,
          email: user.email,
          token: sanitizedToken
        });

        res.status(400).json({
          status: 'error',
          message: 'Invalid or expired verification token',
          error: 'TOKEN_VALIDATION_FAILED'
        });
        return;
      }
    } catch (tokenValidationError) {
      logger.error('Error during token validation', {
        userId: user._id,
        email: user.email,
        error: tokenValidationError instanceof Error ? tokenValidationError.message : 'Unknown validation error'
      });

      res.status(400).json({
        status: 'error',
        message: 'Token validation failed',
        error: 'TOKEN_VALIDATION_ERROR'
      });
      return;
    }

    // Mark email as verified and clear verification fields
    try {
      user.emailVerified = true;
      user.emailVerificationToken = undefined;
      user.emailVerificationExpires = undefined;
      user.emailVerificationAttempts = 0;
      user.emailVerificationLastAttempt = undefined;
      user.emailVerificationSentAt = undefined;

      await user.save();

      logger.info('User email verification fields updated successfully', {
        userId: user._id,
        email: user.email
      });
    } catch (saveError) {
      logger.error('Error saving user after email verification', {
        userId: user._id,
        email: user.email,
        error: saveError instanceof Error ? saveError.message : 'Unknown save error'
      });

      res.status(500).json({
        status: 'error',
        message: 'Failed to update user verification status',
        error: 'USER_SAVE_ERROR'
      });
      return;
    }

    // Clear user cache (non-critical operation)
    try {
      cacheService.delete(`user:profile:${user._id}`);
      logger.debug('User cache cleared successfully', { userId: user._id });
    } catch (cacheError) {
      // Log but don't fail the request for cache errors
      logger.warn('Failed to clear user cache after email verification', {
        userId: user._id,
        error: cacheError instanceof Error ? cacheError.message : 'Unknown cache error'
      });
    }

    // Send welcome email (non-critical operation)
    try {
      await emailService.sendWelcomeEmail(user);
      logger.info('Welcome email sent successfully', {
        userId: user._id,
        email: user.email
      });
    } catch (emailError) {
      // Log but don't fail the request for email errors
      logger.warn('Failed to send welcome email after verification', {
        userId: user._id,
        email: user.email,
        error: emailError instanceof Error ? emailError.message : 'Unknown email error'
      });
    }

    logger.info('Email verified successfully', {
      userId: user._id,
      email: user.email,
      timestamp: new Date().toISOString()
    });

    // Send success response
    res.status(200).json({
      status: 'success',
      message: 'Email verified successfully',
      data: {
        emailVerified: true,
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          emailVerified: user.emailVerified
        }
      }
    });

  } catch (error) {
    // Catch any unexpected errors
    logger.error('Unexpected error in email verification', {
      token: req.params.token,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });

    // Ensure we always send a response
    if (!res.headersSent) {
      res.status(500).json({
        status: 'error',
        message: 'An unexpected error occurred during email verification',
        error: 'INTERNAL_SERVER_ERROR'
      });
    }
  }
};

/**
 * Resend verification email
 * @route POST /api/users/resend-verification
 * @access Private
 */
export const resendVerificationEmail = catchAsync(async (req: Request, res: Response) => {
  // This is essentially the same as sendVerificationEmail
  // We need to call the inner function directly since sendVerificationEmail is wrapped by catchAsync
  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  if (user.emailVerified) {
    return res.status(400).json({
      status: 'error',
      message: 'Email is already verified'
    });
  }

  // Check rate limiting
  const now = new Date();
  const lastAttempt = user.emailVerificationLastAttempt;
  const timeSinceLastAttempt = lastAttempt ? now.getTime() - lastAttempt.getTime() : Infinity;
  const minInterval = 15 * 60 * 1000; // 15 minutes

  if (timeSinceLastAttempt < minInterval) {
    const nextAllowedTime = new Date(lastAttempt!.getTime() + minInterval);
    const waitTime = Math.ceil((nextAllowedTime.getTime() - now.getTime()) / 60000); // minutes

    return res.status(429).json({
      status: 'error',
      message: `Please wait ${waitTime} minutes before requesting another verification email`,
      nextAllowedTime
    });
  }

  // Check attempt limit
  if (user.emailVerificationAttempts >= 5) {
    return res.status(429).json({
      status: 'error',
      message: 'Maximum verification attempts reached. Please contact support.'
    });
  }

  // Generate new verification token
  const verificationToken = crypto.randomBytes(32).toString('hex');
  const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  // Update user with new verification data
  user.emailVerificationToken = verificationToken;
  user.emailVerificationExpires = verificationExpires;
  user.emailVerificationSentAt = now;
  user.emailVerificationLastAttempt = now;
  user.emailVerificationAttempts += 1;

  await user.save();

  // Send verification email
  try {
    const emailSent = await emailService.sendVerificationEmail(user, verificationToken);

    if (!emailSent) {
      throw new Error('Failed to send verification email');
    }

    logger.info('Verification email resent', {
      userId: user._id,
      email: user.email,
      attempt: user.emailVerificationAttempts
    });

    res.status(200).json({
      status: 'success',
      message: 'Verification email sent successfully',
      data: {
        attemptsRemaining: Math.max(0, 5 - user.emailVerificationAttempts),
        nextAllowedTime: new Date(now.getTime() + minInterval)
      }
    });
  } catch (error) {
    logger.error('Failed to send verification email:', error);

    // Revert the attempt count since email failed
    user.emailVerificationAttempts -= 1;
    await user.save();

    throw new AppError('Failed to send verification email. Please try again later.', 500);
  }
});

/**
 * Check email verification status
 * @route GET /api/users/verification-status
 * @access Private
 */
export const getVerificationStatus = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.user._id).select('emailVerified emailVerificationSentAt emailVerificationAttempts');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  const now = new Date();
  const canResend = !user.emailVerificationSentAt || 
                   (now.getTime() - user.emailVerificationSentAt.getTime()) > 15 * 60 * 1000; // 15 minutes

  let nextResendTime = null;
  if (!canResend && user.emailVerificationSentAt) {
    nextResendTime = new Date(user.emailVerificationSentAt.getTime() + 15 * 60 * 1000);
  }

  res.status(200).json({
    status: 'success',
    data: {
      emailVerified: user.emailVerified,
      canResend,
      nextResendTime,
      attemptsRemaining: Math.max(0, 5 - user.emailVerificationAttempts),
      lastSentAt: user.emailVerificationSentAt
    }
  });
});

/**
 * Admin: Manually verify user email
 * @route PUT /api/admin/users/:id/verify-email
 * @access Private (Admin only)
 */
export const adminVerifyEmail = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  const user = await User.findById(id);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  if (user.emailVerified) {
    return res.status(400).json({
      status: 'error',
      message: 'Email is already verified'
    });
  }

  // Mark email as verified and clear verification fields
  user.emailVerified = true;
  user.emailVerificationToken = undefined;
  user.emailVerificationExpires = undefined;
  user.emailVerificationAttempts = 0;
  user.emailVerificationLastAttempt = undefined;
  user.emailVerificationSentAt = undefined;

  await user.save();

  // Clear user cache
  cacheService.delete(`user:profile:${user._id}`);

  logger.info('Email verified by admin', {
    userId: user._id,
    email: user.email,
    adminId: req.user._id
  });

  res.status(200).json({
    status: 'success',
    message: 'Email verified successfully by admin',
    data: {
      user: {
        id: user._id,
        email: user.email,
        emailVerified: user.emailVerified
      }
    }
  });
});
