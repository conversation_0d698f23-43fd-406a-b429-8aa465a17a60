/**
 * Bundle Optimization Service
 * Provides comprehensive bundle analysis, optimization, and performance monitoring
 */

export interface BundleAnalysis {
  totalSize: number;
  gzippedSize: number;
  chunks: ChunkInfo[];
  dependencies: DependencyInfo[];
  recommendations: OptimizationRecommendation[];
  performanceScore: number;
}

export interface ChunkInfo {
  name: string;
  size: number;
  gzippedSize: number;
  modules: string[];
  loadTime: number;
  isLazyLoaded: boolean;
}

export interface DependencyInfo {
  name: string;
  version: string;
  size: number;
  usage: 'critical' | 'important' | 'optional' | 'unused';
  treeshakeable: boolean;
  alternatives?: string[];
}

export interface OptimizationRecommendation {
  type: 'code-splitting' | 'tree-shaking' | 'compression' | 'lazy-loading' | 'dependency';
  priority: 'high' | 'medium' | 'low';
  description: string;
  estimatedSavings: number;
  implementation: string;
}

export interface PerformanceMetrics {
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  timeToInteractive: number;
  totalBlockingTime: number;
  bundleLoadTime: number;
  memoryUsage: number;
}

export class BundleOptimizationService {
  private static instance: BundleOptimizationService;
  private performanceObserver: PerformanceObserver | null = null;
  private metrics: PerformanceMetrics = {
    firstContentfulPaint: 0,
    largestContentfulPaint: 0,
    firstInputDelay: 0,
    cumulativeLayoutShift: 0,
    timeToInteractive: 0,
    totalBlockingTime: 0,
    bundleLoadTime: 0,
    memoryUsage: 0
  };

  public static getInstance(): BundleOptimizationService {
    if (!BundleOptimizationService.instance) {
      BundleOptimizationService.instance = new BundleOptimizationService();
    }
    return BundleOptimizationService.instance;
  }

  constructor() {
    this.initializePerformanceMonitoring();
  }

  /**
   * Initialize performance monitoring
   */
  private initializePerformanceMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Web Vitals monitoring
    this.observeWebVitals();
    
    // Bundle load time monitoring
    this.monitorBundleLoadTime();
    
    // Memory usage monitoring
    this.monitorMemoryUsage();
  }

  /**
   * Observe Web Vitals metrics
   */
  private observeWebVitals(): void {
    if (!('PerformanceObserver' in window)) return;

    // First Contentful Paint
    this.observeMetric('first-contentful-paint', (entry) => {
      this.metrics.firstContentfulPaint = entry.startTime;
    });

    // Largest Contentful Paint
    this.observeMetric('largest-contentful-paint', (entry) => {
      this.metrics.largestContentfulPaint = entry.startTime;
    });

    // First Input Delay
    this.observeMetric('first-input', (entry) => {
      this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
    });

    // Cumulative Layout Shift
    this.observeMetric('layout-shift', (entry) => {
      if (!entry.hadRecentInput) {
        this.metrics.cumulativeLayoutShift += entry.value;
      }
    });
  }

  /**
   * Observe specific performance metric
   */
  private observeMetric(type: string, callback: (entry: any) => void): void {
    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach(callback);
      });
      observer.observe({ type, buffered: true });
    } catch (error) {
      console.warn(`Failed to observe ${type}:`, error);
    }
  }

  /**
   * Monitor bundle load time
   */
  private monitorBundleLoadTime(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        this.metrics.bundleLoadTime = navigation.loadEventEnd - navigation.fetchStart;
        this.metrics.timeToInteractive = navigation.domInteractive - navigation.fetchStart;
      }
    });
  }

  /**
   * Monitor memory usage
   */
  private monitorMemoryUsage(): void {
    if (typeof window === 'undefined' || !('memory' in performance)) return;

    setInterval(() => {
      const memory = (performance as any).memory;
      if (memory) {
        this.metrics.memoryUsage = memory.usedJSHeapSize;
      }
    }, 5000);
  }

  /**
   * Analyze current bundle
   */
  async analyzeBundlePerformance(): Promise<BundleAnalysis> {
    const chunks = await this.analyzeChunks();
    const dependencies = await this.analyzeDependencies();
    const recommendations = this.generateRecommendations(chunks, dependencies);
    const performanceScore = this.calculatePerformanceScore();

    return {
      totalSize: chunks.reduce((sum, chunk) => sum + chunk.size, 0),
      gzippedSize: chunks.reduce((sum, chunk) => sum + chunk.gzippedSize, 0),
      chunks,
      dependencies,
      recommendations,
      performanceScore
    };
  }

  /**
   * Analyze chunks
   */
  private async analyzeChunks(): Promise<ChunkInfo[]> {
    const chunks: ChunkInfo[] = [];

    // Analyze main bundle
    chunks.push({
      name: 'main',
      size: this.estimateChunkSize('main'),
      gzippedSize: this.estimateGzippedSize('main'),
      modules: ['react', 'react-dom', '@chakra-ui/react'],
      loadTime: this.metrics.bundleLoadTime,
      isLazyLoaded: false
    });

    // Analyze vendor chunk
    chunks.push({
      name: 'vendor',
      size: this.estimateChunkSize('vendor'),
      gzippedSize: this.estimateGzippedSize('vendor'),
      modules: ['react-router-dom', 'framer-motion', 'react-i18next'],
      loadTime: 0,
      isLazyLoaded: false
    });

    // Analyze lazy-loaded chunks
    const lazyChunks = ['profile', 'admin', 'settings'];
    lazyChunks.forEach(chunkName => {
      chunks.push({
        name: chunkName,
        size: this.estimateChunkSize(chunkName),
        gzippedSize: this.estimateGzippedSize(chunkName),
        modules: [chunkName],
        loadTime: 0,
        isLazyLoaded: true
      });
    });

    return chunks;
  }

  /**
   * Analyze dependencies
   */
  private async analyzeDependencies(): Promise<DependencyInfo[]> {
    const dependencies: DependencyInfo[] = [
      {
        name: 'react',
        version: '18.x',
        size: 42000,
        usage: 'critical',
        treeshakeable: false
      },
      {
        name: 'react-dom',
        version: '18.x',
        size: 130000,
        usage: 'critical',
        treeshakeable: false
      },
      {
        name: '@chakra-ui/react',
        version: '2.x',
        size: 250000,
        usage: 'critical',
        treeshakeable: true
      },
      {
        name: 'framer-motion',
        version: '10.x',
        size: 180000,
        usage: 'important',
        treeshakeable: true
      },
      {
        name: 'react-router-dom',
        version: '6.x',
        size: 85000,
        usage: 'critical',
        treeshakeable: true
      },
      {
        name: 'react-i18next',
        version: '12.x',
        size: 45000,
        usage: 'important',
        treeshakeable: true
      },
      {
        name: 'socket.io-client',
        version: '4.x',
        size: 95000,
        usage: 'important',
        treeshakeable: false
      }
    ];

    return dependencies;
  }

  /**
   * Generate optimization recommendations
   */
  private generateRecommendations(chunks: ChunkInfo[], dependencies: DependencyInfo[]): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = [];

    // Code splitting recommendations
    const largeChunks = chunks.filter(chunk => chunk.size > 200000 && !chunk.isLazyLoaded);
    if (largeChunks.length > 0) {
      recommendations.push({
        type: 'code-splitting',
        priority: 'high',
        description: 'Split large chunks into smaller, lazy-loaded modules',
        estimatedSavings: largeChunks.reduce((sum, chunk) => sum + chunk.size * 0.3, 0),
        implementation: 'Use React.lazy() and dynamic imports for route-based code splitting'
      });
    }

    // Tree shaking recommendations
    const treeshakeableDeps = dependencies.filter(dep => dep.treeshakeable && dep.size > 50000);
    if (treeshakeableDeps.length > 0) {
      recommendations.push({
        type: 'tree-shaking',
        priority: 'medium',
        description: 'Optimize tree-shaking for large dependencies',
        estimatedSavings: treeshakeableDeps.reduce((sum, dep) => sum + dep.size * 0.2, 0),
        implementation: 'Use named imports and configure webpack/vite for better tree-shaking'
      });
    }

    // Compression recommendations
    const totalSize = chunks.reduce((sum, chunk) => sum + chunk.size, 0);
    if (totalSize > 500000) {
      recommendations.push({
        type: 'compression',
        priority: 'high',
        description: 'Enable gzip/brotli compression for static assets',
        estimatedSavings: totalSize * 0.7,
        implementation: 'Configure server-side compression and optimize build output'
      });
    }

    // Lazy loading recommendations
    const nonLazyChunks = chunks.filter(chunk => !chunk.isLazyLoaded && chunk.name !== 'main');
    if (nonLazyChunks.length > 0) {
      recommendations.push({
        type: 'lazy-loading',
        priority: 'medium',
        description: 'Implement lazy loading for non-critical routes',
        estimatedSavings: nonLazyChunks.reduce((sum, chunk) => sum + chunk.size * 0.8, 0),
        implementation: 'Use React.lazy() for route components and Suspense boundaries'
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Calculate performance score
   */
  private calculatePerformanceScore(): number {
    let score = 100;

    // FCP penalty
    if (this.metrics.firstContentfulPaint > 2000) score -= 20;
    else if (this.metrics.firstContentfulPaint > 1000) score -= 10;

    // LCP penalty
    if (this.metrics.largestContentfulPaint > 4000) score -= 25;
    else if (this.metrics.largestContentfulPaint > 2500) score -= 15;

    // FID penalty
    if (this.metrics.firstInputDelay > 300) score -= 20;
    else if (this.metrics.firstInputDelay > 100) score -= 10;

    // CLS penalty
    if (this.metrics.cumulativeLayoutShift > 0.25) score -= 15;
    else if (this.metrics.cumulativeLayoutShift > 0.1) score -= 8;

    // Bundle size penalty
    if (this.metrics.bundleLoadTime > 5000) score -= 20;
    else if (this.metrics.bundleLoadTime > 3000) score -= 10;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Get current performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Estimate chunk size (placeholder implementation)
   */
  private estimateChunkSize(chunkName: string): number {
    const estimates = {
      main: 350000,
      vendor: 280000,
      profile: 45000,
      admin: 65000,
      settings: 35000
    };
    return estimates[chunkName] || 50000;
  }

  /**
   * Estimate gzipped size (placeholder implementation)
   */
  private estimateGzippedSize(chunkName: string): number {
    return Math.round(this.estimateChunkSize(chunkName) * 0.3);
  }

  /**
   * Optimize bundle configuration
   */
  generateOptimizedConfig(): any {
    return {
      build: {
        rollupOptions: {
          output: {
            manualChunks: {
              vendor: ['react', 'react-dom'],
              ui: ['@chakra-ui/react', 'framer-motion'],
              router: ['react-router-dom'],
              utils: ['react-i18next', 'socket.io-client']
            }
          }
        },
        chunkSizeWarningLimit: 1000,
        minify: 'terser',
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true
          }
        }
      },
      optimizeDeps: {
        include: ['react', 'react-dom', '@chakra-ui/react'],
        exclude: ['@vite/client', '@vite/env']
      }
    };
  }
}
