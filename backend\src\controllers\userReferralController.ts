import { Request, Response } from 'express';
import mongoose from 'mongoose';
import User from '../models/userModel';
import ReferralCommission from '../models/referralCommission';
import Withdrawal from '../models/withdrawalModel';
import { logger } from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user?: {
    _id: string;
    email: string;
    firstName: string;
    lastName: string;
    referralCode: string;
  };
}

class UserReferralController {
  /**
   * Get user's referral history with pagination
   */
  async getReferralHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const status = req.query.status as string;
      const sortBy = req.query.sortBy as string || 'createdAt';
      const sortOrder = req.query.sortOrder as string || 'desc';

      // Build query
      const query: any = { referrerId: new mongoose.Types.ObjectId(userId) };
      if (status) {
        query.status = status;
      }

      // Build sort object
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute query with pagination
      const [commissions, total] = await Promise.all([
        ReferralCommission.find(query)
          .populate('referredId', 'firstName lastName email createdAt')
          .populate('investmentId', 'amount currency status createdAt')
          .sort(sort)
          .skip((page - 1) * limit)
          .limit(limit)
          .lean(),
        ReferralCommission.countDocuments(query)
      ]);

      // Transform data for frontend
      const referrals = commissions.map((commission: any) => ({
        _id: commission._id,
        referredUser: {
          id: commission.referredId._id,
          name: `${commission.referredId.firstName} ${commission.referredId.lastName}`,
          email: commission.referredId.email.replace(/(.{3}).*(@.*)/, '$1***$2') // Anonymize email
        },
        registrationDate: commission.referredId.createdAt,
        commissionAmount: commission.amount,
        commissionStatus: commission.status,
        investmentAmount: commission.investmentId?.amount || 0,
        currency: commission.currency,
        level: commission.level,
        createdAt: commission.createdAt,
        updatedAt: commission.updatedAt
      }));

      const pagination = {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      };

      res.status(200).json({
        success: true,
        data: {
          referrals,
          pagination
        }
      });
    } catch (error) {
      logger.error('Error fetching referral history:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch referral history'
      });
    }
  }

  /**
   * Get user's referral statistics
   */
  async getReferralStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      const user = await User.findById(userId);
      if (!user) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      // Get commission statistics
      const [totalCommissions, pendingCommissions, approvedCommissions] = await Promise.all([
        ReferralCommission.aggregate([
          { $match: { referrerId: new mongoose.Types.ObjectId(userId) } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]),
        ReferralCommission.aggregate([
          { $match: { referrerId: new mongoose.Types.ObjectId(userId), status: 'pending' } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]),
        ReferralCommission.aggregate([
          { $match: { referrerId: new mongoose.Types.ObjectId(userId), status: 'approved' } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ])
      ]);

      // Calculate tier based on referral count
      const referralCount = user.referralCount || 0;
      let currentTier = 'Bronze';
      let nextTierRequirement = 10;

      if (referralCount >= 50) {
        currentTier = 'Platinum';
        nextTierRequirement = undefined;
      } else if (referralCount >= 25) {
        currentTier = 'Gold';
        nextTierRequirement = 50;
      } else if (referralCount >= 10) {
        currentTier = 'Silver';
        nextTierRequirement = 25;
      }

      const stats = {
        totalReferrals: referralCount,
        activeReferrals: Math.floor(referralCount * 0.85), // Estimate active referrals
        totalCommissionEarned: totalCommissions[0]?.total || 0,
        pendingCommission: pendingCommissions[0]?.total || 0,
        availableForWithdrawal: approvedCommissions[0]?.total || 0,
        referralCode: user.referralCode,
        referralLink: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/register?ref=${user.referralCode}`,
        currentTier,
        commissionRate: 3.0, // 3% commission rate
        nextTierRequirement
      };

      res.status(200).json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error fetching referral stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch referral statistics'
      });
    }
  }

  /**
   * Get user's referral code
   */
  async getReferralCode(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      const user = await User.findById(userId);
      if (!user) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          referralCode: user.referralCode,
          referralLink: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/register?ref=${user.referralCode}`
        }
      });
    } catch (error) {
      logger.error('Error fetching referral code:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch referral code'
      });
    }
  }

  /**
   * Generate new referral code
   */
  async generateReferralCode(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      const user = await User.findById(userId);
      if (!user) {
        res.status(404).json({ success: false, message: 'User not found' });
        return;
      }

      // Generate new unique referral code
      let newCode: string;
      let isUnique = false;
      let attempts = 0;

      while (!isUnique && attempts < 10) {
        newCode = Math.random().toString(36).substring(2, 8).toUpperCase();
        const existingUser = await User.findOne({ referralCode: newCode });
        if (!existingUser) {
          isUnique = true;
          user.referralCode = newCode;
          await user.save();
        }
        attempts++;
      }

      if (!isUnique) {
        res.status(500).json({
          success: false,
          message: 'Failed to generate unique referral code'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          referralCode: user.referralCode,
          referralLink: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/register?ref=${user.referralCode}`,
          message: 'New referral code generated successfully'
        }
      });
    } catch (error) {
      logger.error('Error generating referral code:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate referral code'
      });
    }
  }

  /**
   * Get commission balance for all currencies
   */
  async getCommissionBalance(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      // Get commission balances by currency
      const commissionBalances = await ReferralCommission.aggregate([
        { $match: { referrerId: new mongoose.Types.ObjectId(userId) } },
        {
          $group: {
            _id: '$currency',
            totalEarned: { $sum: { $cond: [{ $eq: ['$status', 'approved'] }, '$amount', 0] } },
            pendingAmount: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0] } },
            lastCommissionDate: { $max: '$createdAt' }
          }
        }
      ]);

      // Get withdrawal amounts by currency
      const withdrawalAmounts = await Withdrawal.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(userId),
            withdrawalType: 'commission',
            status: { $in: ['completed', 'approved'] }
          }
        },
        {
          $group: {
            _id: '$currency',
            totalWithdrawn: { $sum: '$amount' }
          }
        }
      ]);

      // Combine data
      const balances = commissionBalances.map((balance: any) => {
        const withdrawn = withdrawalAmounts.find((w: any) => w._id === balance._id);
        return {
          currency: balance._id,
          totalEarned: balance.totalEarned,
          availableForWithdrawal: Math.max(0, balance.totalEarned - (withdrawn?.totalWithdrawn || 0)),
          totalWithdrawn: withdrawn?.totalWithdrawn || 0,
          pendingAmount: balance.pendingAmount,
          lastCommissionDate: balance.lastCommissionDate
        };
      });

      res.status(200).json({
        success: true,
        data: balances
      });
    } catch (error) {
      logger.error('Error fetching commission balance:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch commission balance'
      });
    }
  }

  /**
   * Get commission balance for a specific currency
   */
  async getCommissionBalanceByCurrency(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      const currency = req.params.currency?.toUpperCase();

      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      if (!currency) {
        res.status(400).json({ success: false, message: 'Currency parameter is required' });
        return;
      }

      // Get commission balance for specific currency
      const [commissionData, withdrawalData] = await Promise.all([
        ReferralCommission.aggregate([
          {
            $match: {
              referrerId: new mongoose.Types.ObjectId(userId),
              currency: currency
            }
          },
          {
            $group: {
              _id: null,
              totalEarned: { $sum: { $cond: [{ $eq: ['$status', 'approved'] }, '$amount', 0] } },
              pendingAmount: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, '$amount', 0] } },
              lastCommissionDate: { $max: '$createdAt' }
            }
          }
        ]),
        Withdrawal.aggregate([
          {
            $match: {
              userId: new mongoose.Types.ObjectId(userId),
              currency: currency,
              withdrawalType: 'commission',
              status: { $in: ['completed', 'approved'] }
            }
          },
          {
            $group: {
              _id: null,
              totalWithdrawn: { $sum: '$amount' }
            }
          }
        ])
      ]);

      const commission = commissionData[0];
      const withdrawal = withdrawalData[0];

      if (!commission) {
        res.status(200).json({
          success: true,
          data: null
        });
        return;
      }

      const balance = {
        currency,
        totalEarned: commission.totalEarned,
        availableForWithdrawal: Math.max(0, commission.totalEarned - (withdrawal?.totalWithdrawn || 0)),
        totalWithdrawn: withdrawal?.totalWithdrawn || 0,
        pendingAmount: commission.pendingAmount,
        lastCommissionDate: commission.lastCommissionDate
      };

      res.status(200).json({
        success: true,
        data: balance
      });
    } catch (error) {
      logger.error('Error fetching commission balance by currency:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch commission balance'
      });
    }
  }

  /**
   * Request commission withdrawal
   */
  async requestCommissionWithdrawal(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      const { amount, currency, targetAddress, network, memo } = req.body;

      // Validate input
      if (!amount || !currency || !targetAddress) {
        res.status(400).json({
          success: false,
          message: 'Amount, currency, and target address are required'
        });
        return;
      }

      // Check minimum withdrawal amount (50 USDT equivalent)
      const minimumWithdrawal = 50;
      if (amount < minimumWithdrawal) {
        res.status(400).json({
          success: false,
          message: `Minimum withdrawal amount is ${minimumWithdrawal} USDT equivalent`
        });
        return;
      }

      // Check available balance
      const commissionData = await ReferralCommission.aggregate([
        {
          $match: {
            referrerId: new mongoose.Types.ObjectId(userId),
            currency: currency.toUpperCase(),
            status: 'approved'
          }
        },
        {
          $group: {
            _id: null,
            totalEarned: { $sum: '$amount' }
          }
        }
      ]);

      const withdrawalData = await Withdrawal.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(userId),
            currency: currency.toUpperCase(),
            withdrawalType: 'commission',
            status: { $in: ['completed', 'approved'] }
          }
        },
        {
          $group: {
            _id: null,
            totalWithdrawn: { $sum: '$amount' }
          }
        }
      ]);

      const totalEarned = commissionData[0]?.totalEarned || 0;
      const totalWithdrawn = withdrawalData[0]?.totalWithdrawn || 0;
      const availableBalance = totalEarned - totalWithdrawn;

      if (amount > availableBalance) {
        res.status(400).json({
          success: false,
          message: 'Insufficient commission balance',
          data: {
            requestedAmount: amount,
            availableBalance,
            totalEarned,
            totalWithdrawn
          }
        });
        return;
      }

      // Create withdrawal transaction
      const withdrawalTransaction = new Withdrawal({
        userId: new mongoose.Types.ObjectId(userId),
        currency: currency.toUpperCase(),
        amount,
        targetAddress,
        withdrawalType: 'commission',
        status: 'pending',
        blockchainNetwork: network || 'ethereum',
        availableBalance,
        minimumWithdrawal
      });

      await withdrawalTransaction.save();

      res.status(201).json({
        success: true,
        transactionId: withdrawalTransaction._id,
        estimatedProcessingTime: '24-48 hours',
        message: 'Commission withdrawal request submitted successfully'
      });
    } catch (error) {
      logger.error('Error requesting commission withdrawal:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process withdrawal request'
      });
    }
  }

  /**
   * Validate commission withdrawal
   */
  async validateCommissionWithdrawal(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      const { amount, currency, targetAddress } = req.body;
      const errors: string[] = [];
      const warnings: string[] = [];

      // Validate input
      if (!amount || amount <= 0) {
        errors.push('Amount must be greater than 0');
      }

      if (!currency) {
        errors.push('Currency is required');
      }

      if (!targetAddress) {
        errors.push('Target address is required');
      }

      // Check minimum withdrawal
      const minimumRequired = 50;
      if (amount < minimumRequired) {
        errors.push(`Minimum withdrawal amount is ${minimumRequired} USDT equivalent`);
      }

      // Get available balance
      let availableBalance = 0;
      if (currency) {
        const [commissionData, withdrawalData] = await Promise.all([
          ReferralCommission.aggregate([
            {
              $match: {
                referrerId: new mongoose.Types.ObjectId(userId),
                currency: currency.toUpperCase(),
                status: 'approved'
              }
            },
            {
              $group: {
                _id: null,
                totalEarned: { $sum: '$amount' }
              }
            }
          ]),
          Withdrawal.aggregate([
            {
              $match: {
                userId: new mongoose.Types.ObjectId(userId),
                currency: currency.toUpperCase(),
                withdrawalType: 'commission',
                status: { $in: ['completed', 'approved'] }
              }
            },
            {
              $group: {
                _id: null,
                totalWithdrawn: { $sum: '$amount' }
              }
            }
          ])
        ]);

        const totalEarned = commissionData[0]?.totalEarned || 0;
        const totalWithdrawn = withdrawalData[0]?.totalWithdrawn || 0;
        availableBalance = totalEarned - totalWithdrawn;

        if (amount > availableBalance) {
          errors.push('Insufficient commission balance');
        }
      }

      // Add warnings
      if (amount > availableBalance * 0.8) {
        warnings.push('You are withdrawing a large portion of your commission balance');
      }

      res.status(200).json({
        success: true,
        data: {
          isValid: errors.length === 0,
          errors,
          warnings,
          availableBalance,
          minimumRequired
        }
      });
    } catch (error) {
      logger.error('Error validating commission withdrawal:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to validate withdrawal'
      });
    }
  }

  /**
   * Get commission withdrawal history
   */
  async getCommissionWithdrawalHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const currency = req.query.currency as string;
      const status = req.query.status as string;

      // Build query
      const query: any = {
        userId: new mongoose.Types.ObjectId(userId),
        withdrawalType: 'commission'
      };

      if (currency) {
        query.currency = currency.toUpperCase();
      }

      if (status) {
        query.status = status;
      }

      // Execute query with pagination
      const [withdrawals, total] = await Promise.all([
        Withdrawal.find(query)
          .sort({ createdAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit)
          .lean(),
        Withdrawal.countDocuments(query)
      ]);

      const pagination = {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      };

      res.status(200).json({
        success: true,
        data: {
          withdrawals,
          pagination
        }
      });
    } catch (error) {
      logger.error('Error fetching commission withdrawal history:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch withdrawal history'
      });
    }
  }

  /**
   * Get referral analytics
   */
  async getReferralAnalytics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      const timeframe = req.query.timeframe as string || 'month';

      // Get monthly referral data
      const monthlyData = await ReferralCommission.aggregate([
        { $match: { referrerId: new mongoose.Types.ObjectId(userId) } },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            count: { $sum: 1 },
            commissions: { $sum: '$amount' }
          }
        },
        { $sort: { '_id.year': -1, '_id.month': -1 } },
        { $limit: 12 }
      ]);

      // Calculate conversion rate and average commission
      const totalReferrals = await User.countDocuments({ referrerId: new mongoose.Types.ObjectId(userId) });
      const activeReferrals = await ReferralCommission.distinct('referredId', {
        referrerId: new mongoose.Types.ObjectId(userId)
      });

      const totalCommissions = await ReferralCommission.aggregate([
        { $match: { referrerId: new mongoose.Types.ObjectId(userId) } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]);

      const conversionRate = totalReferrals > 0 ? (activeReferrals.length / totalReferrals) * 100 : 0;
      const averageCommission = activeReferrals.length > 0 ?
        (totalCommissions[0]?.total || 0) / activeReferrals.length : 0;

      const analytics = {
        monthlyReferrals: monthlyData.map((item: any) => ({
          month: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`,
          count: item.count,
          commissions: item.commissions
        })),
        conversionRate,
        averageCommissionPerReferral: averageCommission,
        topPerformingMonths: monthlyData.slice(0, 3).map((item: any) => ({
          month: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`,
          performance: item.commissions
        }))
      };

      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error('Error fetching referral analytics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch analytics'
      });
    }
  }

  /**
   * Get commission withdrawal history
   */
  async getCommissionWithdrawalHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const currency = req.query.currency as string;
      const status = req.query.status as string;

      // Build query
      const query: any = {
        userId: new mongoose.Types.ObjectId(userId),
        withdrawalType: 'referral'
      };

      if (currency) {
        query.currency = currency.toUpperCase();
      }

      if (status) {
        query.status = status;
      }

      // Execute query with pagination
      const [withdrawals, total] = await Promise.all([
        Withdrawal.find(query)
          .sort({ createdAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit)
          .lean(),
        Withdrawal.countDocuments(query)
      ]);

      const pagination = {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      };

      res.status(200).json({
        success: true,
        data: {
          withdrawals,
          pagination
        }
      });
    } catch (error) {
      logger.error('Error fetching commission withdrawal history:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch withdrawal history'
      });
    }
  }

  /**
   * Get referral analytics
   */
  async getReferralAnalytics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user?._id;
      if (!userId) {
        res.status(401).json({ success: false, message: 'User not authenticated' });
        return;
      }

      const timeframe = req.query.timeframe as string || 'month';

      // Get monthly referral data
      const monthlyData = await ReferralCommission.aggregate([
        { $match: { referrerId: new mongoose.Types.ObjectId(userId) } },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            count: { $sum: 1 },
            commissions: { $sum: '$amount' }
          }
        },
        { $sort: { '_id.year': -1, '_id.month': -1 } },
        { $limit: 12 }
      ]);

      // Calculate conversion rate and average commission
      const totalReferrals = await User.countDocuments({ referrerId: new mongoose.Types.ObjectId(userId) });
      const activeReferrals = await ReferralCommission.distinct('referredId', {
        referrerId: new mongoose.Types.ObjectId(userId)
      });

      const totalCommissions = await ReferralCommission.aggregate([
        { $match: { referrerId: new mongoose.Types.ObjectId(userId) } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]);

      const conversionRate = totalReferrals > 0 ? (activeReferrals.length / totalReferrals) * 100 : 0;
      const averageCommission = activeReferrals.length > 0 ?
        (totalCommissions[0]?.total || 0) / activeReferrals.length : 0;

      const analytics = {
        monthlyReferrals: monthlyData.map((item: any) => ({
          month: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`,
          count: item.count,
          commissions: item.commissions
        })),
        conversionRate,
        averageCommissionPerReferral: averageCommission,
        topPerformingMonths: monthlyData.slice(0, 3).map((item: any) => ({
          month: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`,
          performance: item.commissions
        }))
      };

      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error('Error fetching referral analytics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch analytics'
      });
    }
  }
}

export default new UserReferralController();
