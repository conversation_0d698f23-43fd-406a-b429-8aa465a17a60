import { logger } from './logger';

interface ErrorContext {
  userId?: string;
  component?: string;
  action?: string;
  additionalData?: Record<string, any>;
}

interface ErrorReport {
  error: Error;
  context: ErrorContext;
  timestamp: string;
  userAgent: string;
  url: string;
}

class ErrorTrackingService {
  private static instance: ErrorTrackingService;
  // Removed error endpoint as we no longer send errors to the backend

  private constructor() {
    this.initializeErrorHandlers();
  }

  public static getInstance(): ErrorTrackingService {
    if (!ErrorTrackingService.instance) {
      ErrorTrackingService.instance = new ErrorTrackingService();
    }
    return ErrorTrackingService.instance;
  }

  private initializeErrorHandlers() {
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError(event.reason, {
        action: 'unhandledRejection',
        additionalData: {
          type: 'promise',
          message: event.reason?.message
        }
      });
    });

    window.addEventListener('error', (event) => {
      this.captureError(event.error, {
        action: 'uncaughtError',
        additionalData: {
          type: 'runtime',
          filename: event.filename,
          lineNo: event.lineno,
          colNo: event.colno
        }
      });
    });
  }

  public async captureError(error: Error, context: ErrorContext = {}): Promise<void> {
    try {
      const errorReport: ErrorReport = {
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        } as Error,
        context: {
          ...context,
          userId: localStorage.getItem('userId') || undefined,
        },
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };

      // Log locally only - removed server reporting
      logger.error('Application error:', {
        error: errorReport.error,
        context: errorReport.context
      });

    } catch (reportError) {
      // Fallback to console if logging fails
      console.error('Error logging failed:', reportError);
      console.error('Original error:', error);
    }
  }

  // Removed sendErrorReport method as we no longer send errors to the backend

  // Removed storeFailedReport method as we no longer store failed reports

  // Removed retryFailedReports method as we no longer retry failed reports

  public getErrorContext(): ErrorContext {
    return {
      userId: localStorage.getItem('userId') || undefined,
      component: this.getCurrentComponent(),
      action: this.getCurrentAction()
    };
  }

  private getCurrentComponent(): string {
    // Extract component name from current route
    const pathname = window.location.pathname;
    const routes = pathname.split('/').filter(Boolean);
    return routes[routes.length - 1] || 'root';
  }

  private getCurrentAction(): string {
    // This could be set by the application when actions are performed
    return (window as any).__currentAction || 'unknown';
  }

  public setCurrentAction(action: string): void {
    (window as any).__currentAction = action;
  }
}

export const errorTracking = ErrorTrackingService.getInstance();