/**
 * Database Race Condition Test Script
 * Tests concurrent database operations to identify potential race conditions
 * 
 * This script directly tests database operations without going through HTTP APIs
 * to isolate and identify race conditions at the database level.
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') });

// Import models and services
import User from '../src/models/User';
import Wallet from '../src/models/walletModel';
import InvestmentPackage from '../src/models/investmentPackageModel';
import Transaction from '../src/models/transactionModel';
import Withdrawal from '../src/models/withdrawalModel';
import withdrawalService from '../src/services/withdrawalService';

// Test configuration
interface TestConfig {
  testUserId: string;
  concurrentOperations: number;
  withdrawalAmount: number;
  cryptocurrency: string;
  walletAddress: string;
  network: string;
  withdrawalType: 'balance' | 'interest' | 'commission';
}

const testConfig: TestConfig = {
  testUserId: '',
  concurrentOperations: 10,
  withdrawalAmount: 0.01,
  cryptocurrency: 'BTC',
  walletAddress: '**********************************',
  network: 'bitcoin',
  withdrawalType: 'interest'
};

// Connect to MongoDB
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGO_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield';
    console.log('🔗 Connecting to MongoDB for database race condition testing...');
    
    await mongoose.connect(mongoUri);
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Setup test data
const setupTestData = async () => {
  try {
    console.log('🔧 Setting up test data for database race condition testing...');

    // Find or create test user
    let testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (!testUser) {
      testUser = new User({
        firstName: 'Test',
        lastName: 'DBRace',
        email: '<EMAIL>',
        password: 'hashedpassword123',
        isEmailVerified: true,
        role: 'user'
      });
      await testUser.save();
      console.log('✅ Test user created');
    }

    testConfig.testUserId = testUser._id.toString();

    // Setup wallet with sufficient balance
    let wallet = await Wallet.findOne({ userId: testUser._id });
    
    if (!wallet) {
      wallet = new Wallet({
        userId: testUser._id,
        assets: []
      });
    }

    const requiredBalance = testConfig.withdrawalAmount * testConfig.concurrentOperations * 3;
    const assetIndex = wallet.assets.findIndex(asset => asset.symbol === testConfig.cryptocurrency);

    if (assetIndex === -1) {
      wallet.assets.push({
        symbol: testConfig.cryptocurrency,
        balance: requiredBalance,
        interestBalance: requiredBalance,
        commissionBalance: requiredBalance,
        lockedBalance: 0
      });
    } else {
      wallet.assets[assetIndex].balance = requiredBalance;
      wallet.assets[assetIndex].interestBalance = requiredBalance;
      wallet.assets[assetIndex].commissionBalance = requiredBalance;
    }

    await wallet.save();
    console.log(`✅ Wallet setup with ${requiredBalance} ${testConfig.cryptocurrency} balance`);

    return {
      userId: testUser._id.toString(),
      initialBalance: requiredBalance
    };

  } catch (error) {
    console.error('❌ Error setting up test data:', error);
    throw error;
  }
};

// Get wallet balance
const getWalletBalance = async (userId: string, cryptocurrency: string) => {
  try {
    const wallet = await Wallet.findOne({ userId });
    if (!wallet) return null;

    const asset = wallet.assets.find(a => a.symbol === cryptocurrency);
    if (!asset) return null;

    return {
      balance: asset.balance || 0,
      interestBalance: asset.interestBalance || 0,
      commissionBalance: asset.commissionBalance || 0,
      lockedBalance: asset.lockedBalance || 0
    };
  } catch (error) {
    console.error('Error getting wallet balance:', error);
    return null;
  }
};

// Test concurrent withdrawal operations
const testConcurrentWithdrawals = async (operationId: number): Promise<{
  success: boolean;
  operationId: number;
  error?: string;
  timestamp: number;
  withdrawalId?: string;
}> => {
  const startTime = Date.now();
  
  try {
    console.log(`🚀 Operation ${operationId}: Starting withdrawal...`);

    const withdrawal = await withdrawalService.submitWithdrawal({
      userId: testConfig.testUserId,
      cryptocurrency: testConfig.cryptocurrency,
      withdrawalType: testConfig.withdrawalType,
      amount: testConfig.withdrawalAmount,
      walletAddress: testConfig.walletAddress,
      network: testConfig.network
    });

    console.log(`✅ Operation ${operationId}: Withdrawal successful in ${Date.now() - startTime}ms`);

    return {
      success: true,
      operationId,
      timestamp: Date.now() - startTime,
      withdrawalId: withdrawal._id.toString()
    };

  } catch (error: any) {
    console.log(`❌ Operation ${operationId}: Withdrawal failed in ${Date.now() - startTime}ms - ${error.message}`);
    
    return {
      success: false,
      operationId,
      error: error.message || 'Unknown error',
      timestamp: Date.now() - startTime
    };
  }
};

// Test concurrent balance updates
const testConcurrentBalanceUpdates = async (operationId: number): Promise<{
  success: boolean;
  operationId: number;
  error?: string;
  timestamp: number;
}> => {
  const startTime = Date.now();
  
  try {
    console.log(`🔄 Operation ${operationId}: Starting balance update...`);

    // Simulate concurrent balance updates
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      const wallet = await Wallet.findOne({ userId: testConfig.testUserId }).session(session);
      if (!wallet) throw new Error('Wallet not found');

      const assetIndex = wallet.assets.findIndex(a => a.symbol === testConfig.cryptocurrency);
      if (assetIndex === -1) throw new Error('Asset not found');

      // Simulate some processing time
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100));

      // Update balance
      wallet.assets[assetIndex].interestBalance = (wallet.assets[assetIndex].interestBalance || 0) + 0.001;
      await wallet.save({ session });

      await session.commitTransaction();
      session.endSession();

      console.log(`✅ Operation ${operationId}: Balance update successful in ${Date.now() - startTime}ms`);

      return {
        success: true,
        operationId,
        timestamp: Date.now() - startTime
      };

    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw error;
    }

  } catch (error: any) {
    console.log(`❌ Operation ${operationId}: Balance update failed in ${Date.now() - startTime}ms - ${error.message}`);
    
    return {
      success: false,
      operationId,
      error: error.message || 'Unknown error',
      timestamp: Date.now() - startTime
    };
  }
};

// Run database race condition tests
const runDatabaseRaceConditionTests = async () => {
  try {
    console.log('\n🧪 Starting Database Race Condition Tests...');
    console.log(`📊 Configuration:`);
    console.log(`   - Concurrent operations: ${testConfig.concurrentOperations}`);
    console.log(`   - Withdrawal amount: ${testConfig.withdrawalAmount} ${testConfig.cryptocurrency}`);
    console.log(`   - Withdrawal type: ${testConfig.withdrawalType}`);
    console.log('');

    // Get initial balance
    const initialBalance = await getWalletBalance(testConfig.testUserId, testConfig.cryptocurrency);
    console.log('💰 Initial wallet balance:', initialBalance);
    console.log('');

    // Test 1: Concurrent Withdrawals
    console.log('🧪 Test 1: Concurrent Withdrawal Operations');
    console.log('-'.repeat(50));

    const withdrawalOperations = Array.from({ length: testConfig.concurrentOperations }, (_, i) => 
      testConcurrentWithdrawals(i + 1)
    );

    const withdrawalStartTime = Date.now();
    const withdrawalResults = await Promise.allSettled(withdrawalOperations);
    const withdrawalTotalTime = Date.now() - withdrawalStartTime;

    let withdrawalSuccessCount = 0;
    let withdrawalFailureCount = 0;
    const withdrawalErrors: string[] = [];

    withdrawalResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const res = result.value;
        if (res.success) {
          withdrawalSuccessCount++;
        } else {
          withdrawalFailureCount++;
          withdrawalErrors.push(res.error || 'Unknown error');
        }
      } else {
        withdrawalFailureCount++;
        withdrawalErrors.push(result.reason);
      }
    });

    console.log(`✅ Withdrawal test completed in ${withdrawalTotalTime}ms`);
    console.log(`   - Successful: ${withdrawalSuccessCount}`);
    console.log(`   - Failed: ${withdrawalFailureCount}`);

    // Test 2: Concurrent Balance Updates
    console.log('\n🧪 Test 2: Concurrent Balance Update Operations');
    console.log('-'.repeat(50));

    const balanceOperations = Array.from({ length: testConfig.concurrentOperations }, (_, i) => 
      testConcurrentBalanceUpdates(i + 1)
    );

    const balanceStartTime = Date.now();
    const balanceResults = await Promise.allSettled(balanceOperations);
    const balanceTotalTime = Date.now() - balanceStartTime;

    let balanceSuccessCount = 0;
    let balanceFailureCount = 0;
    const balanceErrors: string[] = [];

    balanceResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const res = result.value;
        if (res.success) {
          balanceSuccessCount++;
        } else {
          balanceFailureCount++;
          balanceErrors.push(res.error || 'Unknown error');
        }
      } else {
        balanceFailureCount++;
        balanceErrors.push(result.reason);
      }
    });

    console.log(`✅ Balance update test completed in ${balanceTotalTime}ms`);
    console.log(`   - Successful: ${balanceSuccessCount}`);
    console.log(`   - Failed: ${balanceFailureCount}`);

    // Get final balance and check integrity
    const finalBalance = await getWalletBalance(testConfig.testUserId, testConfig.cryptocurrency);
    console.log('\n💰 Final wallet balance:', finalBalance);

    // Calculate expected vs actual balance changes
    const expectedWithdrawalDeduction = withdrawalSuccessCount * testConfig.withdrawalAmount;
    const expectedBalanceIncrease = balanceSuccessCount * 0.001;
    
    const actualInterestBalanceChange = (finalBalance?.interestBalance || 0) - (initialBalance?.interestBalance || 0);
    const expectedInterestBalanceChange = -expectedWithdrawalDeduction + expectedBalanceIncrease;

    console.log('\n📈 Balance Integrity Analysis:');
    console.log(`   - Expected withdrawal deduction: ${expectedWithdrawalDeduction} ${testConfig.cryptocurrency}`);
    console.log(`   - Expected balance increase: ${expectedBalanceIncrease} ${testConfig.cryptocurrency}`);
    console.log(`   - Expected net change: ${expectedInterestBalanceChange} ${testConfig.cryptocurrency}`);
    console.log(`   - Actual net change: ${actualInterestBalanceChange} ${testConfig.cryptocurrency}`);
    console.log(`   - Difference: ${Math.abs(expectedInterestBalanceChange - actualInterestBalanceChange)} ${testConfig.cryptocurrency}`);

    // Check for data integrity issues
    const hasDataIntegrityIssue = Math.abs(expectedInterestBalanceChange - actualInterestBalanceChange) > 0.000001;

    console.log('\n🎯 Test Summary:');
    console.log(`   - Withdrawal operations: ${withdrawalSuccessCount}/${testConfig.concurrentOperations} successful`);
    console.log(`   - Balance operations: ${balanceSuccessCount}/${testConfig.concurrentOperations} successful`);
    console.log(`   - Data integrity: ${hasDataIntegrityIssue ? '❌ ISSUE DETECTED' : '✅ OK'}`);

    if (withdrawalErrors.length > 0 || balanceErrors.length > 0) {
      console.log('\n❌ Error Summary:');
      if (withdrawalErrors.length > 0) {
        console.log('   Withdrawal errors:');
        withdrawalErrors.forEach(error => console.log(`     - ${error}`));
      }
      if (balanceErrors.length > 0) {
        console.log('   Balance update errors:');
        balanceErrors.forEach(error => console.log(`     - ${error}`));
      }
    }

    return {
      withdrawalSuccessCount,
      withdrawalFailureCount,
      balanceSuccessCount,
      balanceFailureCount,
      hasDataIntegrityIssue,
      withdrawalErrors,
      balanceErrors
    };

  } catch (error) {
    console.error('❌ Error running database race condition tests:', error);
    throw error;
  }
};

// Cleanup test data
const cleanupTestData = async () => {
  try {
    console.log('\n🧹 Cleaning up test data...');
    
    await Withdrawal.deleteMany({ userId: testConfig.testUserId });
    await Transaction.deleteMany({ userId: testConfig.testUserId });
    
    console.log('✅ Test data cleaned up');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
  }
};

// Main test function
const main = async () => {
  console.log('🧪 Database Race Condition Test');
  console.log('='.repeat(50));

  try {
    await connectDB();
    await setupTestData();
    
    const results = await runDatabaseRaceConditionTests();
    
    await cleanupTestData();
    
    console.log('\n🎉 Database race condition test completed!');
    
    if (results.hasDataIntegrityIssue) {
      console.log('⚠️ WARNING: Data integrity issues detected!');
      process.exit(1);
    } else {
      console.log('✅ No data integrity issues detected');
      process.exit(0);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
  }
};

// Handle errors
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught Exception:', err);
  process.exit(1);
});

// Run the test
main();
