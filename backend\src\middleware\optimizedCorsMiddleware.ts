import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * Optimized CORS middleware for maximum compatibility with Chrome, Safari, and other browsers
 * This middleware handles all CORS requirements in one place to avoid conflicts
 */
export const optimizedCorsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const origin = req.get('Origin');
  const userAgent = req.get('User-Agent') || '';
  const isSafari = userAgent.includes('Safari') && !userAgent.includes('Chrome');
  const isChrome = userAgent.includes('Chrome');
  const isFirefox = userAgent.includes('Firefox');
  
  // Define comprehensive allowed origins
  const allowedOrigins = [
    // Production domains
    process.env.FRONTEND_URL,
    'https://shpnfinance.com',
    'https://www.shpnfinance.com',
    'https://cryptoyield.com',
    'https://www.cryptoyield.com',
    
    // Development localhost ports (common Vite/React ports)
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3003',
    'http://localhost:3004',
    'http://localhost:3005',
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:8080',
    
    // Backend ports (for direct API testing)
    'http://localhost:5000',
    'http://localhost:5001',
    'http://localhost:5002',
    
    // Docker/container origins
    'http://frontend',
    'http://frontend:80',
    'http://frontend:3000'
  ].filter(Boolean); // Remove undefined values
  
  // Determine if origin is allowed
  let isOriginAllowed = false;
  
  if (!origin) {
    // No origin header - allow for mobile apps, Postman, curl, etc.
    isOriginAllowed = true;
  } else if (process.env.NODE_ENV === 'development') {
    // In development, be more permissive
    isOriginAllowed = origin.includes('localhost') || 
                     origin.includes('127.0.0.1') || 
                     allowedOrigins.includes(origin);
  } else {
    // In production, strictly check against allowed origins
    isOriginAllowed = allowedOrigins.includes(origin);
  }
  
  // Set Access-Control-Allow-Origin
  if (isOriginAllowed) {
    if (origin) {
      res.header('Access-Control-Allow-Origin', origin);
    } else {
      res.header('Access-Control-Allow-Origin', '*');
    }
  } else {
    logger.warn(`CORS blocked origin: ${origin}`);
    // Don't set Access-Control-Allow-Origin for blocked origins
    return res.status(403).json({ 
      error: 'CORS policy violation',
      message: 'Origin not allowed'
    });
  }
  
  // Essential CORS headers
  res.header('Access-Control-Allow-Credentials', 'true');
  
  // Comprehensive HTTP methods
  const allowedMethods = [
    'GET',
    'POST', 
    'PUT',
    'DELETE',
    'OPTIONS',
    'PATCH',
    'HEAD'
  ];
  res.header('Access-Control-Allow-Methods', allowedMethods.join(', '));
  
  // Comprehensive headers for maximum compatibility
  const allowedHeaders = [
    // Standard headers
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    
    // Cache control
    'Cache-Control',
    'Pragma',
    'Expires',
    'If-Modified-Since',
    'If-None-Match',
    
    // Range requests (for file downloads)
    'Range',
    'Content-Range',
    
    // Custom headers
    'X-API-Key',
    'X-CSRF-Token',
    'X-Client-Version',
    
    // Browser security headers
    'DNT',
    'Keep-Alive',
    'User-Agent',
    
    // Content headers
    'Content-Disposition',
    'Content-Length',
    
    // Safari-specific headers
    'Sec-Fetch-Site',
    'Sec-Fetch-Mode',
    'Sec-Fetch-Dest'
  ];
  res.header('Access-Control-Allow-Headers', allowedHeaders.join(', '));
  
  // Headers to expose to the client
  const exposedHeaders = [
    'Content-Length',
    'Content-Type',
    'Content-Disposition',
    'Content-Range',
    'Accept-Ranges',
    'X-Total-Count',
    'X-Page-Count',
    'X-Rate-Limit-Remaining',
    'X-Rate-Limit-Reset'
  ];
  res.header('Access-Control-Expose-Headers', exposedHeaders.join(', '));
  
  // Cache preflight requests for 24 hours
  res.header('Access-Control-Max-Age', '86400');
  
  // Browser-specific optimizations
  if (isSafari) {
    // Safari requires more specific Vary header
    res.header('Vary', 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers');
    res.header('Cross-Origin-Resource-Policy', 'cross-origin');
    res.header('Cross-Origin-Embedder-Policy', 'unsafe-none');
    
    // Safari-specific timing headers
    res.header('Timing-Allow-Origin', origin || '*');
  } else if (isChrome) {
    // Chrome optimizations
    res.header('Vary', 'Origin');
    res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  } else if (isFirefox) {
    // Firefox optimizations
    res.header('Vary', 'Origin');
    res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  } else {
    // Default for other browsers
    res.header('Vary', 'Origin');
    res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  }
  
  // Handle OPTIONS preflight requests
  if (req.method === 'OPTIONS') {
    const requestedMethod = req.get('Access-Control-Request-Method');
    const requestedHeaders = req.get('Access-Control-Request-Headers');
    
    logger.debug(`CORS preflight request`, {
      browser: isSafari ? 'Safari' : isChrome ? 'Chrome' : isFirefox ? 'Firefox' : 'Other',
      origin,
      requestedMethod,
      requestedHeaders,
      userAgent: userAgent.substring(0, 100)
    });
    
    // Return 204 No Content for preflight requests
    return res.status(204).end();
  }
  
  // Log CORS requests in development
  if (process.env.NODE_ENV === 'development' && origin) {
    logger.debug(`CORS request from ${origin} to ${req.method} ${req.path}`);
  }
  
  next();
};

/**
 * Simple CORS middleware for static file serving
 */
export const staticFilesCorsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Allow all origins for static files
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Range, Content-Type');
  res.header('Access-Control-Expose-Headers', 'Content-Length, Content-Range, Accept-Ranges');
  res.header('Cross-Origin-Resource-Policy', 'cross-origin');
  
  if (req.method === 'OPTIONS') {
    return res.status(204).end();
  }
  
  next();
};
