import React, { useState, useEffect } from 'react';
import {
  <PERSON>dal,
  <PERSON>dalOverlay,
  ModalContent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Alert,
  AlertIcon,
  useToast,
  FormErrorMessage,
  Box,
  Icon,
  PinInput,
  PinInputField,
  Center,
  Divider
} from '@chakra-ui/react';
import { FaShieldAlt, FaEnvelope, FaSync } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { walletManagementService, WithdrawalAddress } from '../../services/walletManagementService';
import { getCryptoColor } from '../../utils/cryptoUtils';
import { getCryptoIcon } from '../../utils/cryptoIconMap';

interface VerifyWalletModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  address: WithdrawalAddress | null;
}

const VerifyWalletModal: React.FC<VerifyWalletModalProps> = ({ 
  isOpen, 
  onClose, 
  onSuccess, 
  address 
}) => {
  const { t } = useTranslation();
  const toast = useToast();

  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [error, setError] = useState('');

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setVerificationCode('');
      setError('');
    }
  }, [isOpen]);

  // Handle verification
  const handleVerify = async () => {
    if (!address || verificationCode.length !== 6) {
      setError(t('Please enter a valid 6-digit verification code'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      await walletManagementService.verifyAddress(address._id, {
        verificationCode: verificationCode.toUpperCase()
      });

      toast({
        title: t('Success'),
        description: t('Wallet address verified successfully'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      onSuccess();
    } catch (error: any) {
      const errorMessage = error.message || t('Verification failed');
      setError(errorMessage);
      
      toast({
        title: t('Verification Failed'),
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle resend verification code
  const handleResendCode = async () => {
    if (!address) return;

    setResendLoading(true);
    try {
      await walletManagementService.resendVerificationCode(address._id);
      
      toast({
        title: t('Code Sent'),
        description: t('A new verification code has been sent to your email'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: t('Error'),
        description: error.message || t('Failed to resend verification code'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setResendLoading(false);
    }
  };

  // Handle PIN input change
  const handlePinChange = (value: string) => {
    setVerificationCode(value);
    if (error) {
      setError('');
    }
  };

  if (!address) {
    return null;
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={3}>
            <Icon as={FaShieldAlt} color="green.500" />
            <Text>{t('Verify Wallet Address')}</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <VStack spacing={6} align="stretch">
            {/* Address Info */}
            <Box>
              <VStack spacing={3} align="center">
                <Icon
                  as={getCryptoIcon(address.currency)}
                  color={getCryptoColor(address.currency)}
                  boxSize={12}
                />
                <VStack spacing={1}>
                  <Text fontSize="lg" fontWeight="bold">
                    {address.currency} - {address.label}
                  </Text>
                  <Text
                    fontSize="sm"
                    color="gray.600"
                    fontFamily="mono"
                    textAlign="center"
                    wordBreak="break-all"
                  >
                    {address.formattedAddress}
                  </Text>
                </VStack>
              </VStack>
            </Box>

            <Divider />

            {/* Verification Instructions */}
            <Alert status="info" borderRadius="md">
              <AlertIcon />
              <VStack align="start" spacing={2} flex={1}>
                <Text fontWeight="semibold">
                  {t('Verification Required')}
                </Text>
                <Text fontSize="sm">
                  {t('A 6-digit verification code has been sent to your registered email address. Please enter the code below to verify this wallet address.')}
                </Text>
              </VStack>
            </Alert>

            {/* PIN Input */}
            <FormControl isInvalid={!!error}>
              <FormLabel textAlign="center">
                {t('Enter Verification Code')}
              </FormLabel>
              <Center>
                <HStack spacing={2}>
                  <PinInput
                    value={verificationCode}
                    onChange={handlePinChange}
                    size="lg"
                    placeholder=""
                    type="alphanumeric"
                  >
                    <PinInputField />
                    <PinInputField />
                    <PinInputField />
                    <PinInputField />
                    <PinInputField />
                    <PinInputField />
                  </PinInput>
                </HStack>
              </Center>
              {error && (
                <FormErrorMessage textAlign="center" mt={2}>
                  {error}
                </FormErrorMessage>
              )}
            </FormControl>

            {/* Resend Code */}
            <Center>
              <VStack spacing={2}>
                <Text fontSize="sm" color="gray.600">
                  {t("Didn't receive the code?")}
                </Text>
                <Button
                  variant="link"
                  size="sm"
                  leftIcon={<FaSync />}
                  onClick={handleResendCode}
                  isLoading={resendLoading}
                  loadingText={t('Sending...')}
                  colorScheme="blue"
                >
                  {t('Resend Code')}
                </Button>
              </VStack>
            </Center>

            {/* Security Notice */}
            <Alert status="warning" borderRadius="md">
              <AlertIcon />
              <VStack align="start" spacing={1} flex={1}>
                <Text fontWeight="semibold">{t('Security Notice')}</Text>
                <Text fontSize="sm">
                  {t('For your security, verification codes expire after 10 minutes. Only verified addresses can be used for withdrawals.')}
                </Text>
              </VStack>
            </Alert>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="ghost" onClick={onClose}>
              {t('Cancel')}
            </Button>
            <Button
              colorScheme="green"
              onClick={handleVerify}
              isLoading={loading}
              loadingText={t('Verifying...')}
              disabled={verificationCode.length !== 6}
              leftIcon={<FaShieldAlt />}
            >
              {t('Verify Address')}
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default VerifyWalletModal;
