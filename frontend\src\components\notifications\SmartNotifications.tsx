import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Icon,
  Badge,
  Button,
  useColorModeValue,
  Card,
  CardBody,
  Flex,
  Avatar,
  useToast,
  Slide,
  CloseButton,
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaCoins,
  FaGift,
  FaFire,
  FaBell,
  FaRocket,
  FaHeart,
  FaTrophy,
  FaUsers,
  FaChartLine,
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const MotionBox = motion(Box);

interface SmartNotification {
  id: string;
  type: 'earning' | 'achievement' | 'referral' | 'promotion' | 'milestone';
  title: string;
  message: string;
  icon: any;
  color: string;
  emoji: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
}

const SmartNotifications: React.FC = () => {
  const { t } = useTranslation();
  const [notifications, setNotifications] = useState<SmartNotification[]>([]);
  const [showFloating, setShowFloating] = useState(false);
  const toast = useToast();

  // Theme colors
  const bgColor = useColorModeValue('#FFFFFF', '#1E2026');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');

  // Sample notifications
  const sampleNotifications: SmartNotification[] = [
    {
      id: '1',
      type: 'earning',
      title: '💰 Günlük Kazanç!',
      message: 'Bugün $12.50 kazandın! Toplam kazancın: $245.80',
      icon: FaCoins,
      color: '#02C076',
      emoji: '💰',
      action: {
        label: 'Detayları Gör',
        onClick: () => console.log('View earnings')
      },
      timestamp: new Date(),
      read: false,
      priority: 'high'
    },
    {
      id: '2',
      type: 'achievement',
      title: '🏆 Yeni Başarım!',
      message: '7 günlük streak tamamladın! +100 XP kazandın',
      icon: FaTrophy,
      color: '#FCD535',
      emoji: '🏆',
      action: {
        label: 'Ödülü Al',
        onClick: () => console.log('Claim reward')
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 30),
      read: false,
      priority: 'medium'
    },
    {
      id: '3',
      type: 'referral',
      title: '👥 Yeni Referans!',
      message: 'Ahmet senin linkini kullanarak katıldı! +$5 bonus kazandın',
      icon: FaUsers,
      color: '#F84960',
      emoji: '👥',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
      read: false,
      priority: 'medium'
    },
    {
      id: '4',
      type: 'promotion',
      title: '🎁 Özel Fırsat!',
      message: 'Sadece bugün: %20 bonus faiz oranı!',
      icon: FaGift,
      color: '#9945FF',
      emoji: '🎁',
      action: {
        label: 'Fırsatı Yakala',
        onClick: () => console.log('Special offer')
      },
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),
      read: true,
      priority: 'high'
    }
  ];

  useEffect(() => {
    setNotifications(sampleNotifications);
    
    // Show floating notification for new earnings
    const timer = setTimeout(() => {
      setShowFloating(true);
      setTimeout(() => setShowFloating(false), 5000);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'earning': return FaCoins;
      case 'achievement': return FaTrophy;
      case 'referral': return FaUsers;
      case 'promotion': return FaGift;
      case 'milestone': return FaRocket;
      default: return FaBell;
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (minutes < 60) return `${minutes}dk önce`;
    if (hours < 24) return `${hours}sa önce`;
    return date.toLocaleDateString('tr-TR');
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <>
      {/* Floating Notification */}
      <AnimatePresence>
        {showFloating && (
          <MotionBox
            position="fixed"
            top={4}
            right={4}
            zIndex={9999}
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ duration: 0.3 }}
          >
            <Card
              bg="linear-gradient(135deg, #02C076, #00D4AA)"
              color="white"
              maxW="300px"
              boxShadow="0 8px 25px rgba(2, 192, 118, 0.3)"
            >
              <CardBody p={4}>
                <HStack justify="space-between" align="start">
                  <HStack spacing={3}>
                    <Box
                      bg="rgba(255,255,255,0.2)"
                      p={2}
                      borderRadius="lg"
                    >
                      <Icon as={FaCoins} boxSize={5} />
                    </Box>
                    <VStack align="start" spacing={1}>
                      <Text fontWeight="bold" fontSize="sm">
                        💰 Yeni Kazanç!
                      </Text>
                      <Text fontSize="xs" opacity={0.9}>
                        +$2.50 faiz geldi!
                      </Text>
                    </VStack>
                  </HStack>
                  <CloseButton
                    size="sm"
                    onClick={() => setShowFloating(false)}
                  />
                </HStack>
              </CardBody>
            </Card>
          </MotionBox>
        )}
      </AnimatePresence>

      {/* Notifications Panel */}
      <Box p={4}>
        <VStack spacing={4} align="stretch">
          {/* Header */}
          <HStack justify="space-between">
            <HStack>
              <Icon as={FaBell} color="#FCD535" boxSize={6} />
              <Text fontSize="xl" fontWeight="bold" color={textColor}>
                🔔 Bildirimler
              </Text>
              {unreadCount > 0 && (
                <Badge
                  bg="#F84960"
                  color="white"
                  borderRadius="full"
                  px={2}
                >
                  {unreadCount}
                </Badge>
              )}
            </HStack>
            
            <Button
              size="sm"
              variant="ghost"
              colorScheme="gray"
              onClick={() => setNotifications(prev =>
                prev.map(n => ({ ...n, read: true }))
              )}
            >
              Tümünü Okundu İşaretle
            </Button>
          </HStack>

          {/* Notifications List */}
          <VStack spacing={3} align="stretch">
            {notifications.map((notification) => (
              <MotionBox
                key={notification.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card
                  bg={notification.read ? bgColor : `${notification.color}10`}
                  borderWidth="1px"
                  borderColor={notification.read ? '#2D3748' : notification.color}
                  cursor="pointer"
                  onClick={() => markAsRead(notification.id)}
                  _hover={{
                    transform: 'translateY(-2px)',
                    boxShadow: `0 4px 12px ${notification.color}30`
                  }}
                  transition="all 0.2s"
                >
                  <CardBody p={4}>
                    <HStack spacing={3} align="start">
                      {/* Icon */}
                      <Box
                        bg={`${notification.color}20`}
                        p={2}
                        borderRadius="lg"
                        flexShrink={0}
                      >
                        <Icon
                          as={getNotificationIcon(notification.type)}
                          color={notification.color}
                          boxSize={5}
                        />
                      </Box>

                      {/* Content */}
                      <VStack align="start" spacing={1} flex={1}>
                        <HStack justify="space-between" width="100%">
                          <Text
                            fontWeight="bold"
                            color={textColor}
                            fontSize="sm"
                          >
                            {notification.title}
                          </Text>
                          <Text
                            fontSize="xs"
                            color="#848E9C"
                          >
                            {formatTime(notification.timestamp)}
                          </Text>
                        </HStack>
                        
                        <Text
                          color="#848E9C"
                          fontSize="sm"
                          lineHeight="1.4"
                        >
                          {notification.message}
                        </Text>

                        {/* Action Button */}
                        {notification.action && (
                          <Button
                            size="xs"
                            bg={notification.color}
                            color="white"
                            _hover={{ opacity: 0.8 }}
                            onClick={(e) => {
                              e.stopPropagation();
                              notification.action!.onClick();
                            }}
                            mt={2}
                          >
                            {notification.action.label}
                          </Button>
                        )}
                      </VStack>

                      {/* Unread Indicator */}
                      {!notification.read && (
                        <Box
                          w="8px"
                          h="8px"
                          bg={notification.color}
                          borderRadius="full"
                          flexShrink={0}
                        />
                      )}
                    </HStack>
                  </CardBody>
                </Card>
              </MotionBox>
            ))}
          </VStack>

          {/* Empty State */}
          {notifications.length === 0 && (
            <Box textAlign="center" py={8}>
              <Text fontSize="4xl" mb={2}>🔔</Text>
              <Text color="#848E9C" fontSize="lg">
                Henüz bildirim yok
              </Text>
              <Text color="#848E9C" fontSize="sm">
                Yeni kazançlar ve güncellemeler burada görünecek
              </Text>
            </Box>
          )}
        </VStack>
      </Box>
    </>
  );
};

export default SmartNotifications;
