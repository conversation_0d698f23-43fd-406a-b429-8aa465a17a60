import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import DOMPurify from 'isomorphic-dompurify';

// Supported currencies
const SUPPORTED_CURRENCIES = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI'];

// Sanitization helper function
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return input;

  // Remove HTML tags and potentially dangerous characters
  const sanitized = DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });

  // Additional sanitization for SQL injection prevention
  return sanitized
    .replace(/[<>'"]/g, '') // Remove dangerous characters
    .trim(); // Remove leading/trailing whitespace
};

/**
 * Validation middleware for investment package creation
 */
export const validateInvestmentPackage = [
  body('amount')
    .isNumeric()
    .withMessage('Amount must be a number')
    .isFloat({ min: 0.000001 })
    .withMessage('Amount must be greater than 0')
    .custom((value) => {
      if (value > 1000000) {
        throw new Error('Amount cannot exceed 1,000,000');
      }
      return true;
    }),

  body('currency')
    .optional()
    .isString()
    .withMessage('Currency must be a string')
    .isLength({ min: 2, max: 10 })
    .withMessage('Currency must be between 2-10 characters')
    .custom((value) => {
      if (value && !SUPPORTED_CURRENCIES.includes(value.toUpperCase())) {
        throw new Error(`Currency must be one of: ${SUPPORTED_CURRENCIES.join(', ')}`);
      }
      return true;
    }),

  body('compoundEnabled')
    .optional()
    .isBoolean()
    .withMessage('Compound enabled must be a boolean'),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array().map(error => ({
          field: (error as any).param || (error as any).path,
          message: error.msg,
          value: (error as any).value
        }))
      });
      return;
    }
    next();
  }
];

/**
 * Validation middleware for withdrawal requests
 */
export const validateWithdrawal = [
  body('amount')
    .isNumeric()
    .withMessage('Amount must be a number')
    .isFloat({ min: 0.000001 })
    .withMessage('Amount must be greater than 0')
    .custom((value) => {
      if (value > 100000) {
        throw new Error('Single withdrawal cannot exceed 100,000');
      }
      return true;
    }),

  body('currency')
    .optional()
    .isString()
    .withMessage('Currency must be a string')
    .isLength({ min: 2, max: 10 })
    .withMessage('Currency must be between 2-10 characters')
    .custom((value) => {
      if (value && !SUPPORTED_CURRENCIES.includes(value.toUpperCase())) {
        throw new Error(`Currency must be one of: ${SUPPORTED_CURRENCIES.join(', ')}`);
      }
      return true;
    }),

  body('emergency')
    .optional()
    .isBoolean()
    .withMessage('Emergency flag must be a boolean'),

  body('walletAddress')
    .optional()
    .isString()
    .withMessage('Wallet address must be a string')
    .isLength({ min: 10, max: 100 })
    .withMessage('Wallet address must be between 10-100 characters'),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array().map(error => ({
          field: (error as any).param || (error as any).path,
          message: error.msg,
          value: (error as any).value
        }))
      });
      return;
    }
    next();
  }
];

/**
 * Validation middleware for admin operations
 */
export const validateAdminPackageUpdate = [
  body('status')
    .optional()
    .isIn(['pending', 'active', 'completed', 'withdrawn'])
    .withMessage('Status must be one of: pending, active, completed, withdrawn'),

  body('interestRate')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Interest rate must be between 0 and 1'),

  body('totalEarned')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Total earned must be non-negative'),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array().map(error => ({
          field: (error as any).param || (error as any).path,
          message: error.msg,
          value: (error as any).value
        }))
      });
      return;
    }
    next();
  }
];

/**
 * Validation middleware for time service operations
 */
export const validateTimeOperation = [
  body('durationHours')
    .optional()
    .isInt({ min: 1, max: 168 }) // Max 1 week
    .withMessage('Duration must be between 1 and 168 hours'),

  body('durationMinutes')
    .optional()
    .isInt({ min: 1, max: 1440 }) // Max 24 hours
    .withMessage('Duration must be between 1 and 1440 minutes'),

  body('reason')
    .optional()
    .isString()
    .withMessage('Reason must be a string')
    .isLength({ min: 5, max: 200 })
    .withMessage('Reason must be between 5-200 characters'),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array().map(error => ({
          field: (error as any).param || (error as any).path,
          message: error.msg,
          value: (error as any).value
        }))
      });
      return;
    }
    next();
  }
];

/**
 * Validation middleware for crypto API operations
 */
export const validateCurrencyConversion = [
  body('amount')
    .isNumeric()
    .withMessage('Amount must be a number')
    .isFloat({ min: 0.000001 })
    .withMessage('Amount must be greater than 0'),

  body('fromCurrency')
    .isString()
    .withMessage('From currency must be a string')
    .custom((value) => {
      if (!SUPPORTED_CURRENCIES.includes(value.toUpperCase())) {
        throw new Error(`From currency must be one of: ${SUPPORTED_CURRENCIES.join(', ')}`);
      }
      return true;
    }),

  body('toCurrency')
    .isString()
    .withMessage('To currency must be a string')
    .custom((value) => {
      if (!SUPPORTED_CURRENCIES.includes(value.toUpperCase())) {
        throw new Error(`To currency must be one of: ${SUPPORTED_CURRENCIES.join(', ')}`);
      }
      return true;
    }),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array().map(error => ({
          field: (error as any).param || (error as any).path,
          message: error.msg,
          value: (error as any).value
        }))
      });
      return;
    }
    next();
  }
];

/**
 * General purpose validation error handler
 */
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      status: 'error',
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: (error as any).param || (error as any).path,
        message: error.msg,
        value: (error as any).value
      }))
    });
    return;
  }
  next();
};

/**
 * Custom validation for package ID parameter
 */
export const validatePackageId = (req: Request, res: Response, next: NextFunction) => {
  const { packageId } = req.params;

  if (!packageId || !packageId.match(/^[0-9a-fA-F]{24}$/)) {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid package ID format'
    });
  }

  next();
};

/**
 * Rate limiting validation for sensitive operations
 */
export const validateRateLimit = (maxRequests: number = 5, windowMs: number = 60000) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction) => {
    const clientId = req.ip + (req.user?._id || 'anonymous');
    const now = Date.now();

    const clientData = requests.get(clientId);

    if (!clientData || now > clientData.resetTime) {
      requests.set(clientId, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (clientData.count >= maxRequests) {
      return res.status(429).json({
        status: 'error',
        message: 'Too many requests, please try again later',
        retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
      });
    }

    clientData.count++;
    next();
  };
};

/**
 * User registration validation with enhanced security
 */
export const validateUserRegistration = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
    .customSanitizer(sanitizeInput),

  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),

  body('firstName')
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters')
    .matches(/^[a-zA-ZÀ-ÿğüşıöçĞÜŞİÖÇ\s'-]+$/)
    .withMessage('First name can only contain letters, spaces, hyphens, and apostrophes')
    .customSanitizer(sanitizeInput),

  body('lastName')
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters')
    .matches(/^[a-zA-ZÀ-ÿğüşıöçĞÜŞİÖÇ\s'-]+$/)
    .withMessage('Last name can only contain letters, spaces, hyphens, and apostrophes')
    .customSanitizer(sanitizeInput),

  body('username')
    .optional()
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores')
    .customSanitizer(sanitizeInput),

  body('birthDate')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid birth date')
    .custom((value) => {
      if (value) {
        const birthDate = new Date(value);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        if (age < 18) {
          throw new Error('You must be at least 18 years old');
        }
      }
      return true;
    }),

  body('phoneNumber')
    .optional()
    .isLength({ min: 10, max: 20 })
    .withMessage('Phone number must be between 10 and 20 characters')
    .matches(/^[\+]?[0-9\s\-\(\)]+$/)
    .withMessage('Please provide a valid phone number')
    .customSanitizer(sanitizeInput),

  body('country')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Country name cannot exceed 100 characters')
    .customSanitizer(sanitizeInput),

  body('city')
    .optional()
    .isLength({ max: 100 })
    .withMessage('City name cannot exceed 100 characters')
    .customSanitizer(sanitizeInput),

  body('referralCode')
    .optional()
    .isLength({ min: 3, max: 20 })
    .withMessage('Referral code must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9]+$/)
    .withMessage('Referral code can only contain letters and numbers')
    .customSanitizer(sanitizeInput),

  body('marketingConsent')
    .optional()
    .isBoolean()
    .withMessage('Marketing consent must be a boolean value'),

  handleValidationErrors
];

/**
 * User login validation with enhanced security
 */
export const validateUserLogin = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
    .customSanitizer(sanitizeInput),

  body('password')
    .isLength({ min: 1, max: 128 })
    .withMessage('Password is required')
    .customSanitizer(sanitizeInput),

  handleValidationErrors
];

/**
 * Profile update validation with enhanced security
 */
export const validateProfileUpdate = [
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
    .customSanitizer(sanitizeInput),

  body('firstName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters')
    .matches(/^[a-zA-ZÀ-ÿ\s'-]+$/)
    .withMessage('First name can only contain letters, spaces, hyphens, and apostrophes')
    .customSanitizer(sanitizeInput),

  body('lastName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters')
    .matches(/^[a-zA-ZÀ-ÿ\s'-]+$/)
    .withMessage('Last name can only contain letters, spaces, hyphens, and apostrophes')
    .customSanitizer(sanitizeInput),

  body('phoneNumber')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number')
    .customSanitizer(sanitizeInput),

  body('country')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Country name cannot exceed 100 characters')
    .customSanitizer(sanitizeInput),

  body('city')
    .optional()
    .isLength({ max: 100 })
    .withMessage('City name cannot exceed 100 characters')
    .customSanitizer(sanitizeInput),

  body('walletAddress')
    .optional()
    .matches(/^0x[a-fA-F0-9]{40}$/)
    .withMessage('Please provide a valid Ethereum wallet address')
    .customSanitizer(sanitizeInput),

  body('password')
    .optional()
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),

  handleValidationErrors
];

/**
 * MongoDB ObjectId validation
 */
export const validateObjectId = [
  param('id')
    .isMongoId()
    .withMessage('Invalid ID format'),

  handleValidationErrors
];

/**
 * Validation middleware for withdrawal address creation
 */
export const validateWithdrawalAddress = [
  body('currency')
    .isString()
    .withMessage('Currency must be a string')
    .isIn(SUPPORTED_CURRENCIES)
    .withMessage(`Currency must be one of: ${SUPPORTED_CURRENCIES.join(', ')}`),

  body('address')
    .isString()
    .withMessage('Address must be a string')
    .isLength({ min: 10, max: 100 })
    .withMessage('Address must be between 10 and 100 characters')
    .custom((value, { req }) => {
      const currency = req.body.currency;
      if (!validateAddressFormat(value, currency)) {
        throw new Error('Invalid address format for the selected currency');
      }
      return true;
    })
    .customSanitizer(sanitizeInput),

  body('label')
    .isString()
    .withMessage('Label must be a string')
    .isLength({ min: 1, max: 50 })
    .withMessage('Label must be between 1 and 50 characters')
    .customSanitizer(sanitizeInput),

  body('network')
    .optional()
    .isString()
    .withMessage('Network must be a string')
    .isLength({ min: 1, max: 20 })
    .withMessage('Network must be between 1 and 20 characters')
    .customSanitizer(sanitizeInput),

  handleValidationErrors
];

/**
 * Validation middleware for withdrawal address update
 */
export const validateAddressUpdate = [
  body('label')
    .optional()
    .isString()
    .withMessage('Label must be a string')
    .isLength({ min: 1, max: 50 })
    .withMessage('Label must be between 1 and 50 characters')
    .customSanitizer(sanitizeInput),

  body('isDefault')
    .optional()
    .isBoolean()
    .withMessage('isDefault must be a boolean'),

  handleValidationErrors
];

/**
 * Validation middleware for verification code
 */
export const validateVerificationCode = [
  body('verificationCode')
    .isString()
    .withMessage('Verification code must be a string')
    .isLength({ min: 6, max: 6 })
    .withMessage('Verification code must be exactly 6 characters')
    .matches(/^[A-F0-9]{6}$/)
    .withMessage('Verification code must contain only uppercase letters and numbers')
    .customSanitizer(sanitizeInput),

  handleValidationErrors
];

/**
 * Helper function to validate address format based on currency
 */
function validateAddressFormat(address: string, currency: string): boolean {
  switch (currency.toUpperCase()) {
    case 'BTC':
      // Bitcoin address validation (Legacy, SegWit)
      return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/.test(address);
    case 'ETH':
    case 'USDT':
    case 'BNB':
    case 'LINK':
    case 'UNI':
      // Ethereum-based address validation
      return /^0x[a-fA-F0-9]{40}$/.test(address);
    case 'ADA':
      // Cardano address validation (simplified)
      return /^addr1[a-z0-9]{58}$/.test(address);
    case 'DOT':
      // Polkadot address validation (simplified)
      return /^1[a-zA-Z0-9]{47}$/.test(address);
    default:
      return false;
  }
}
