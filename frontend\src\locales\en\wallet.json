{"title": "Wallet", "subtitle": "Manage your crypto assets", "overview": {"title": "Wallet Overview", "totalBalance": "Total Balance", "availableBalance": "Available Balance", "lockedBalance": "Locked Balance", "estimatedValue": "Estimated Value", "portfolioValue": "Portfolio Value", "totalAssets": "Total Assets", "activeAssets": "Active Assets", "profitLoss": "Profit/Loss", "dailyChange": "Daily Change", "weeklyChange": "Weekly Change", "monthlyChange": "Monthly Change"}, "assets": {"title": "My Assets", "noAssets": "No assets yet", "loading": "Loading assets...", "error": "Error loading assets", "refresh": "Refresh", "addAsset": "Add <PERSON>set", "hideSmallBalances": "Hide small balances", "showAll": "Show All", "sortBy": "Sort By", "filterBy": "Filter <PERSON>", "searchAssets": "Search assets...", "assetName": "Asset Name", "balance": "Balance", "value": "Value", "change24h": "24h Change", "price": "Price", "actions": "Actions"}, "transactions": {"title": "Transaction History", "noTransactions": "No transactions yet", "loading": "Loading transactions...", "error": "Error loading transactions", "viewAll": "View All", "filter": "Filter", "export": "Export", "transactionId": "Transaction ID", "type": "Type", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "date": "Date", "fee": "Fee", "confirmations": "Confirmations", "txHash": "Transaction Hash", "blockHeight": "Block Height", "types": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>", "transfer": "Transfer", "exchange": "Exchange", "earning": "Earning", "fee": "Fee", "bonus": "Bonus", "referral": "Referral", "staking": "Staking", "unstaking": "Unstaking"}}, "deposit": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Deposit crypto to your wallet", "selectCurrency": "Select Currency", "depositAddress": "Deposit Address", "qrCode": "QR Code", "copyAddress": "Copy Address", "generateNewAddress": "Generate New Address", "minimumDeposit": "Minimum Deposit", "networkFee": "Network Fee", "confirmations": "Required Confirmations", "processingTime": "Processing Time", "instructions": "Deposit Instructions", "warning": "Warning", "warningText": "Only send {currency}. Sending other coins may result in loss.", "steps": {"step1": "Select currency", "step2": "Copy address", "step3": "Make transfer", "step4": "Wait for confirmation"}, "recentDeposits": "Recent Deposits", "depositHistory": "Deposit History"}, "withdrawal": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Withdraw crypto from your wallet", "selectCurrency": "Select Currency", "withdrawalAddress": "<PERSON><PERSON><PERSON> Address", "amount": "Amount", "availableBalance": "Available Balance", "minimumWithdrawal": "Minimum Withdrawal", "maximumWithdrawal": "Maximum <PERSON>wal", "networkFee": "Network Fee", "processingFee": "Processing Fee", "totalFee": "Total Fee", "youWillReceive": "You Will Receive", "processingTime": "Processing Time", "addressBook": "Address Book", "addToAddressBook": "Add to Address Book", "savedAddresses": "Saved Addresses", "verifyAddress": "Verify Address", "confirmWithdrawal": "Confirm <PERSON>", "withdrawButton": "Withdraw", "cancelButton": "Cancel", "steps": {"step1": "Select currency", "step2": "Enter address and amount", "step3": "Confirm fees", "step4": "Confirm withdrawal"}, "recentWithdrawals": "Recent Withdrawals", "withdrawalHistory": "Withdrawal History", "validation": {"addressRequired": "Withdrawal address is required", "invalidAddress": "Invalid address", "amountRequired": "Amount is required", "insufficientBalance": "Insufficient balance", "belowMinimum": "Below minimum withdrawal amount", "aboveMaximum": "Above maximum withdrawal amount", "invalidAmount": "Invalid amount"}}, "transfer": {"title": "Transfer", "subtitle": "Transfer to another user", "recipientEmail": "Recipient Email", "recipientUsername": "Recipient Username", "selectCurrency": "Select Currency", "amount": "Amount", "transferFee": "Transfer Fee", "description": "Description (Optional)", "confirmTransfer": "Confirm Transfer", "transferButton": "Transfer", "cancelButton": "Cancel", "recentTransfers": "Recent Transfers", "transferHistory": "Transfer History", "validation": {"recipientRequired": "Recipient is required", "invalidRecipient": "Invalid recipient", "cannotTransferToSelf": "Cannot transfer to yourself", "amountRequired": "Amount is required", "insufficientBalance": "Insufficient balance", "invalidAmount": "Invalid amount"}}, "exchange": {"title": "Exchange", "subtitle": "Exchange your cryptocurrencies", "fromCurrency": "From Currency", "toCurrency": "To <PERSON><PERSON><PERSON><PERSON>", "fromAmount": "From Amount", "toAmount": "To Amount", "exchangeRate": "Exchange Rate", "exchangeFee": "Exchange Fee", "estimatedReceive": "Estimated Receive", "minimumExchange": "Minimum Exchange", "maximumExchange": "Maximum Exchange", "exchangeButton": "Exchange", "cancelButton": "Cancel", "recentExchanges": "Recent Exchanges", "exchangeHistory": "Exchange History", "validation": {"fromCurrencyRequired": "From currency is required", "toCurrencyRequired": "To currency is required", "sameCurrency": "Cannot select the same currency", "amountRequired": "Amount is required", "insufficientBalance": "Insufficient balance", "belowMinimum": "Below minimum exchange amount", "aboveMaximum": "Above maximum exchange amount"}}, "security": {"title": "Security", "twoFactorAuth": "Two-Factor Authentication", "withdrawalPassword": "<PERSON><PERSON><PERSON> Password", "addressWhitelist": "Address Whitelist", "sessionManagement": "Session Management", "loginHistory": "Login History", "deviceManagement": "Device Management", "securityAlerts": "Security Alerts", "enable": "Enable", "disable": "Disable", "configure": "Configure", "view": "View"}, "settings": {"title": "Wallet Settings", "displaySettings": "Display Settings", "defaultCurrency": "<PERSON><PERSON><PERSON>", "hideSmallBalances": "Hide small balances", "showTestnet": "Show testnet", "notifications": "Notifications", "depositNotifications": "Deposit notifications", "withdrawalNotifications": "Withdrawal notifications", "transferNotifications": "Transfer notifications", "priceAlerts": "Price alerts", "backup": "Backup", "exportWallet": "Export wallet", "importWallet": "Import wallet", "backupPhrase": "Backup phrase", "privateKeys": "Private keys"}, "errors": {"networkError": "Network error", "insufficientFunds": "Insufficient funds", "invalidAddress": "Invalid address", "transactionFailed": "Transaction failed", "addressGenerationFailed": "Address generation failed", "balanceUpdateFailed": "Balance update failed", "withdrawalLimitExceeded": "Withdrawal limit exceeded", "dailyLimitExceeded": "Daily limit exceeded", "maintenanceMode": "Maintenance mode", "serviceUnavailable": "Service unavailable"}, "success": {"depositInitiated": "<PERSON><PERSON><PERSON><PERSON> initiated", "withdrawalInitiated": "<PERSON><PERSON><PERSON> initiated", "transferCompleted": "Transfer completed", "exchangeCompleted": "Exchange completed", "addressCopied": "Address copied", "settingsSaved": "Setting<PERSON> saved", "addressGenerated": "New address generated", "backupCompleted": "Backup completed"}}