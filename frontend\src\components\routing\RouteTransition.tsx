import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Box } from '@chakra-ui/react';
import { routes, RouteType } from '../../routes/RouteController';

interface RouteTransitionProps {
  children: React.ReactNode;
  transitionType?: 'fade' | 'slide' | 'scale' | 'none';
  duration?: number;
}

/**
 * Route Transition component
 * 
 * Features:
 * - Smooth transitions between routes
 * - Different transition types based on route type
 * - Customizable duration
 * - Route-specific transition settings
 */
const RouteTransition: React.FC<RouteTransitionProps> = ({
  children,
  transitionType = 'fade',
  duration = 0.3
}) => {
  const location = useLocation();
  const [isInitialRender, setIsInitialRender] = useState(true);
  const [currentTransitionType, setCurrentTransitionType] = useState(transitionType);
  const [currentDuration, setCurrentDuration] = useState(duration);

  // Determine transition type based on route
  useEffect(() => {
    // Skip transition on initial render
    if (isInitialRender) {
      setIsInitialRender(false);
      return;
    }

    // Find current route
    const currentRoute = routes.find(route => {
      if (route.exact) {
        return route.path === location.pathname;
      }
      return location.pathname.startsWith(route.path);
    });

    // Set transition type based on route type
    if (currentRoute) {
      switch (currentRoute.type) {
        case RouteType.PUBLIC:
          setCurrentTransitionType('fade');
          setCurrentDuration(0.3);
          break;
        case RouteType.PROTECTED:
          setCurrentTransitionType('slide');
          setCurrentDuration(0.4);
          break;
        case RouteType.ADMIN:
          setCurrentTransitionType('scale');
          setCurrentDuration(0.5);
          break;
        default:
          setCurrentTransitionType(transitionType);
          setCurrentDuration(duration);
      }
    }
  }, [location.pathname, isInitialRender, transitionType, duration]);

  // Define transition variants
  const getVariants = () => {
    switch (currentTransitionType) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 }
        };
      case 'slide':
        return {
          initial: { x: 20, opacity: 0 },
          animate: { x: 0, opacity: 1 },
          exit: { x: -20, opacity: 0 }
        };
      case 'scale':
        return {
          initial: { scale: 0.95, opacity: 0 },
          animate: { scale: 1, opacity: 1 },
          exit: { scale: 0.95, opacity: 0 }
        };
      case 'none':
      default:
        return {
          initial: { opacity: 1 },
          animate: { opacity: 1 },
          exit: { opacity: 1 }
        };
    }
  };

  return (
    <Box width="100%" overflow="hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={location.pathname}
          initial="initial"
          animate="animate"
          exit="exit"
          variants={getVariants()}
          transition={{ duration: currentDuration, ease: 'easeInOut' }}
          style={{ width: '100%' }}
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </Box>
  );
};

export default RouteTransition;
