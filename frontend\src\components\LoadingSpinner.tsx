import { Center, Spinner, Box, Text, keyframes, useColorModeValue, VStack, Fade } from '@chakra-ui/react';
import { memo, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

// Animasyon tanımları
const pulse = keyframes`
  0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(240, 185, 11, 0.7); }
  70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(240, 185, 11, 0); }
  100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(240, 185, 11, 0); }
`;

const rotate = keyframes`
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
`;

// Yükleme mesajları
const loadingMessages = [
  'loading.message1', // "Veriler yükleniyor..."
  'loading.message2', // "Hesabınız hazırlanıyor..."
  'loading.message3', // "Bağlantı kuruluyor..."
  'loading.message4', // "Neredeyse hazır..."
  'loading.message5', // "Bilgiler alınıyor..."
];

// Gelişmiş yükleme spinner bileşeni
const LoadingSpinner = () => {
  const { t } = useTranslation();
  const [messageIndex, setMessageIndex] = useState(0);
  const [showTip, setShowTip] = useState(false);

  // Yükleme mesajını belirli aralıklarla değiştir
  useEffect(() => {
    const messageTimer = setInterval(() => {
      setMessageIndex((prevIndex) => (prevIndex + 1) % loadingMessages.length);
    }, 3000);

    // 5 saniye sonra ipucu göster
    const tipTimer = setTimeout(() => {
      setShowTip(true);
    }, 5000);

    return () => {
      clearInterval(messageTimer);
      clearTimeout(tipTimer);
    };
  }, [loadingMessages.length]);

  // Animasyon stilleri
  const pulseAnimation = `${pulse} 2s infinite`;
  const rotateAnimation = `${rotate} 10s linear infinite`;

  return (
    <Center h="100vh" flexDirection="column" bg="gray.900" position="fixed" top="0" left="0" right="0" bottom="0" zIndex="9999">
      <VStack spacing={6}>
        <Box position="relative" mb={4} animation={rotateAnimation}>
          {/* Dış halka */}
          <Box
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            width="80px"
            height="80px"
            borderRadius="full"
            border="2px solid"
            borderColor="rgba(240, 185, 11, 0.2)"
          />

          {/* Orta halka */}
          <Spinner
            thickness="4px"
            speed="0.65s"
            emptyColor="gray.800"
            color="#F0B90B"
            size="xl"
          />

          {/* İç logo */}
          <Box
            position="absolute"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            width="30px"
            height="30px"
            borderRadius="md"
            bg="#F0B90B"
            display="flex"
            alignItems="center"
            justifyContent="center"
            animation={pulseAnimation}
            boxShadow="0 0 10px rgba(240, 185, 11, 0.5)"
          >
            <Box
              width="20px"
              height="20px"
              borderRadius="sm"
              bg="#0B0E11"
            />
          </Box>
        </Box>

        {/* Yükleme mesajı */}
        <Text
          color="#F0B90B"
          fontWeight="bold"
          fontSize="lg"
          textAlign="center"
        >
          {t(loadingMessages[messageIndex], 'Yükleniyor...')}
        </Text>

        {/* İpucu mesajı - 5 saniye sonra göster */}
        <Fade in={showTip} transition={{ enter: { duration: 0.5 } }}>
          <Text
            color="gray.400"
            fontSize="sm"
            maxW="300px"
            textAlign="center"
            display={showTip ? 'block' : 'none'}
          >
            {t('loading.tip', 'Bu biraz zaman alabilir. Lütfen bekleyin...')}
          </Text>
        </Fade>
      </VStack>
    </Center>
  );
};

// Memo kullanarak gereksiz yeniden render'ları önlüyoruz
export default memo(LoadingSpinner);