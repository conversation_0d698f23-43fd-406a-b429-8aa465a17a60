const moment = require('moment-timezone');

/**
 * Enhanced time service for crypto interest calculations
 * Handles 03:00 UTC+3 timing for interest calculations and withdrawals
 */
class CryptoTimeService {
  private readonly TURKEY_TIMEZONE = 'Europe/Istanbul';
  private readonly INTEREST_HOUR = 3; // 03:00
  private readonly INTEREST_MINUTE = 0;

  /**
   * Get current Turkey time (UTC+3)
   */
  getTurkeyTime(): Date {
    return moment.tz(this.TURKEY_TIMEZONE).toDate();
  }

  /**
   * Get next 03:00 UTC+3 time
   */
  getNextInterestTime(): Date {
    const now = moment.tz(this.TURKEY_TIMEZONE);
    let next = now.clone().hour(this.INTEREST_HOUR).minute(this.INTEREST_MINUTE).second(0).millisecond(0);
    
    // If current time is after 03:00, move to next day
    if (now.hour() >= this.INTEREST_HOUR) {
      next = next.add(1, 'day');
    }
    
    return next.toDate();
  }

  /**
   * Get time until next interest calculation
   */
  getTimeUntilNextInterest(): {
    hours: number;
    minutes: number;
    seconds: number;
    totalMilliseconds: number;
  } {
    const now = moment.tz(this.TURKEY_TIMEZONE);
    const next = moment(this.getNextInterestTime());
    const diff = next.diff(now);
    
    const duration = moment.duration(diff);
    
    return {
      hours: Math.floor(duration.asHours()),
      minutes: duration.minutes(),
      seconds: duration.seconds(),
      totalMilliseconds: diff
    };
  }

  /**
   * Check if current time allows withdrawals (after 03:00 UTC+3)
   */
  isWithdrawalTimeAllowed(): boolean {
    const now = moment.tz(this.TURKEY_TIMEZONE);
    const today3AM = now.clone().hour(this.INTEREST_HOUR).minute(this.INTEREST_MINUTE).second(0).millisecond(0);
    
    return now.isAfter(today3AM);
  }

  /**
   * Get next withdrawal allowed time
   */
  getNextWithdrawalTime(): Date {
    const now = moment.tz(this.TURKEY_TIMEZONE);
    let next = now.clone().hour(this.INTEREST_HOUR).minute(this.INTEREST_MINUTE).second(0).millisecond(0);
    
    // If current time is before 03:00, withdrawal is allowed today at 03:00
    // If current time is after 03:00, withdrawal is already allowed
    if (now.hour() < this.INTEREST_HOUR) {
      // Withdrawal will be allowed today at 03:00
      return next.toDate();
    }
    
    // Withdrawal is already allowed, return current time
    return now.toDate();
  }

  /**
   * Calculate time until withdrawal is allowed
   */
  getTimeUntilWithdrawalAllowed(): {
    hours: number;
    minutes: number;
    seconds: number;
    totalMilliseconds: number;
    isAllowed: boolean;
  } {
    const now = moment.tz(this.TURKEY_TIMEZONE);
    const isAllowed = this.isWithdrawalTimeAllowed();
    
    if (isAllowed) {
      return {
        hours: 0,
        minutes: 0,
        seconds: 0,
        totalMilliseconds: 0,
        isAllowed: true
      };
    }
    
    const nextWithdrawal = moment(this.getNextWithdrawalTime());
    const diff = nextWithdrawal.diff(now);
    const duration = moment.duration(diff);
    
    return {
      hours: Math.floor(duration.asHours()),
      minutes: duration.minutes(),
      seconds: duration.seconds(),
      totalMilliseconds: diff,
      isAllowed: false
    };
  }

  /**
   * Check if it's time for interest calculation (exactly 03:00 UTC+3)
   */
  isInterestCalculationTime(): boolean {
    const now = moment.tz(this.TURKEY_TIMEZONE);
    return now.hour() === this.INTEREST_HOUR && now.minute() === this.INTEREST_MINUTE;
  }

  /**
   * Get formatted time string for display
   */
  formatTimeUntil(timeData: { hours: number; minutes: number; seconds: number }): string {
    const { hours, minutes, seconds } = timeData;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Get interest calculation schedule for a package
   */
  getInterestSchedule(activatedAt: Date, days: number = 30): Date[] {
    const schedule: Date[] = [];
    const start = moment(activatedAt).tz(this.TURKEY_TIMEZONE);
    
    for (let i = 0; i < days; i++) {
      const interestTime = start.clone().add(i, 'days');
      schedule.push(interestTime.toDate());
    }
    
    return schedule;
  }

  /**
   * Calculate days since activation
   */
  getDaysSinceActivation(activatedAt: Date): number {
    const now = moment.tz(this.TURKEY_TIMEZONE);
    const activated = moment(activatedAt).tz(this.TURKEY_TIMEZONE);
    
    return Math.floor(now.diff(activated, 'days', true));
  }

  /**
   * Get status for time lock display
   */
  getTimeLockStatus(): {
    isWithdrawalAllowed: boolean;
    nextInterestTime: Date;
    timeUntilNextInterest: string;
    timeUntilWithdrawal: string;
    currentTurkeyTime: Date;
  } {
    const isWithdrawalAllowed = this.isWithdrawalTimeAllowed();
    const nextInterestTime = this.getNextInterestTime();
    const timeUntilInterest = this.getTimeUntilNextInterest();
    const timeUntilWithdrawal = this.getTimeUntilWithdrawalAllowed();
    
    return {
      isWithdrawalAllowed,
      nextInterestTime,
      timeUntilNextInterest: this.formatTimeUntil(timeUntilInterest),
      timeUntilWithdrawal: this.formatTimeUntil(timeUntilWithdrawal),
      currentTurkeyTime: this.getTurkeyTime()
    };
  }

  /**
   * Validate if a package can earn interest today
   */
  canEarnInterestToday(lastCalculatedAt: Date): boolean {
    const now = moment.tz(this.TURKEY_TIMEZONE);
    const lastCalculated = moment(lastCalculatedAt).tz(this.TURKEY_TIMEZONE);
    const today3AM = now.clone().hour(this.INTEREST_HOUR).minute(this.INTEREST_MINUTE).second(0).millisecond(0);
    
    // If we haven't passed 03:00 today, check yesterday's 03:00
    if (now.hour() < this.INTEREST_HOUR) {
      today3AM.subtract(1, 'day');
    }
    
    // Can earn interest if last calculation was before today's 03:00
    return lastCalculated.isBefore(today3AM);
  }

  /**
   * Get next interest calculation time for a specific package
   */
  getNextInterestTimeForPackage(lastCalculatedAt: Date): Date {
    const lastCalculated = moment(lastCalculatedAt).tz(this.TURKEY_TIMEZONE);
    const nextInterest = lastCalculated.clone().add(1, 'day').hour(this.INTEREST_HOUR).minute(this.INTEREST_MINUTE).second(0).millisecond(0);
    
    return nextInterest.toDate();
  }
}

export default new CryptoTimeService();
