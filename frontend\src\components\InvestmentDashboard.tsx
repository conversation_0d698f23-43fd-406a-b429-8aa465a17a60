import React, { useEffect, useState } from 'react';
import {
  Box,
  Flex,
  Text,
  Badge,
  Icon,
  VStack,
  HStack,
  Grid,
  GridItem,
  Progress,
  Heading,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useColorModeValue,
} from '@chakra-ui/react';
import { FaMoneyBillWave, FaCalendarAlt, FaPercentage, FaCoins } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { Transaction as BaseTransaction } from './TransactionHistory';
import { cryptoIcons, cryptoNames, cryptoColors, getCryptoIcon, getCryptoName, getCryptoColor } from '../utils/cryptoIcons';
import { formatCurrency as formatCurrencyWithSymbol } from '../utils/formatters';

// Extended Transaction interface to include 'processing' status
interface Transaction extends BaseTransaction {
  status: 'pending' | 'approved' | 'rejected' | 'processing';
}

interface InvestmentDashboardProps {
  className?: string;
}

const InvestmentDashboard: React.FC<InvestmentDashboardProps> = ({ className }) => {
  const { t } = useTranslation();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [latestInvestment, setLatestInvestment] = useState<Transaction | null>(null);
  const [totalInvested, setTotalInvested] = useState(0);
  const [totalEarned, setTotalEarned] = useState(0);
  const [nextPayment, setNextPayment] = useState<Date | null>(null);
  const [hoursToNextPayment, setHoursToNextPayment] = useState(0);

  // Colors
  const bgColor = useColorModeValue("#1E2329", "#1E2329");
  const cardBgColor = useColorModeValue("#0B0E11", "#0B0E11");
  const borderColor = useColorModeValue("#2B3139", "#2B3139");
  const textColor = useColorModeValue("#EAECEF", "#EAECEF");
  const secondaryTextColor = useColorModeValue("#848E9C", "#848E9C");
  const primaryColor = useColorModeValue("#F0B90B", "#F0B90B");

  useEffect(() => {
    // Load transactions from localStorage
    const loadTransactions = () => {
      try {
        const storedTransactions = localStorage.getItem('transactions');
        console.log('Loading transactions in InvestmentDashboard:', storedTransactions);

        if (storedTransactions) {
          const parsedTransactions = JSON.parse(storedTransactions) as Transaction[];

          // Filter only deposit transactions
          const depositTransactions = parsedTransactions.filter(tx => tx.type === 'deposit');
          console.log('Deposit transactions:', depositTransactions);
          setTransactions(depositTransactions);

          // Always show the most recent transaction, regardless of status
          if (depositTransactions.length > 0) {
            // Sort by date descending (newest first)
            depositTransactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
            console.log('Latest investment:', depositTransactions[0]);
            setLatestInvestment(depositTransactions[0]);

            // Get approved deposits for calculations
            const approvedDeposits = depositTransactions.filter(tx => tx.status === 'approved');

            // Calculate total invested (only approved investments)
            const totalInvestedAmount = approvedDeposits.reduce((sum, tx) => sum + tx.amount, 0);
            setTotalInvested(totalInvestedAmount);

            // Calculate total earned (1% daily of total invested)
            if (approvedDeposits.length > 0) {
              let totalEarned = 0;

              approvedDeposits.forEach(deposit => {
                const depositDate = new Date(deposit.date);
                const currentDate = new Date();
                const diffTime = Math.abs(currentDate.getTime() - depositDate.getTime());
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                // 1% daily interest
                const earned = deposit.amount * 0.01 * diffDays;
                totalEarned += earned;
              });

              setTotalEarned(totalEarned);
            }
          } else {
            console.log('No deposit transactions found');
            setLatestInvestment(null);
          }

          // Set next payment date (24 hours from now)
          const nextPaymentDate = new Date();
          nextPaymentDate.setDate(nextPaymentDate.getDate() + 1);
          nextPaymentDate.setHours(0, 0, 0, 0);
          setNextPayment(nextPaymentDate);

          // Calculate hours to next payment
          const now = new Date();
          const diffMs = nextPaymentDate.getTime() - now.getTime();
          const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
          setHoursToNextPayment(diffHours);
        } else {
          console.log('No transactions found in localStorage');
          setTransactions([]);
          setLatestInvestment(null);
        }
      } catch (error) {
        console.error('Error loading transactions:', error);
        setTransactions([]);
        setLatestInvestment(null);
      }
    };

    // Handle storage changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'transactions' || e.key === 'lastTransactionUpdate') {
        console.log('Storage change detected in InvestmentDashboard');
        loadTransactions();
      }
    };

    // Handle custom transaction updated event
    const handleTransactionUpdated = (e: CustomEvent) => {
      console.log('Transaction updated event detected in InvestmentDashboard', e.detail);
      // Force immediate reload of transactions
      setTimeout(loadTransactions, 0);
    };

    // Add event listeners
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('transactionUpdated', handleTransactionUpdated as EventListener);

    // Initial load
    loadTransactions();

    // Set up polling to check for new transactions every 3 seconds
    // This is a fallback in case the event listeners don't work
    const intervalId = setInterval(() => {
      console.log('InvestmentDashboard: Polling for new transactions');
      loadTransactions();
    }, 3000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('transactionUpdated', handleTransactionUpdated as EventListener);
      clearInterval(intervalId);
    };
  }, []);

  // If no investment found
  if (!latestInvestment) {
    return (
      <Box
        bg={bgColor}
        p={6}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
        className={className}
        textAlign="center"
      >
        <Flex justify="space-between" align="center" mb={4}>
          <Heading size="md" color={textColor}>{t('investment.dashboard', 'Investment Dashboard')}</Heading>
        </Flex>
        <Box py={8}>
          <Icon as={FaCoins} boxSize={12} color={primaryColor} mb={4} />
          <Text color={secondaryTextColor} fontSize="lg" fontWeight="medium" mb={2}>
            {t('investment.noInvestments', 'You have no investment')}
          </Text>
          <Text color={secondaryTextColor} mb={4}>
            {t('investment.startInvesting', 'Start investing to see your dashboard and earn daily returns!')}
          </Text>
        </Box>
      </Box>
    );
  }

  // Check if there are any approved investments
  const hasApprovedInvestments = latestInvestment.status === 'approved';

  // If no approved investments, show pending message
  if (!hasApprovedInvestments) {
    return (
      <Box
        bg={bgColor}
        p={6}
        borderRadius="lg"
        borderWidth="1px"
        borderColor={borderColor}
        className={className}
        textAlign="center"
      >
        <Flex justify="space-between" align="center" mb={4}>
          <Heading size="md" color={textColor}>{t('investment.dashboard', 'Investment Dashboard')}</Heading>
          <Badge
            colorScheme="yellow"
            px={2}
            py={1}
            fontSize="sm"
            fontWeight="bold"
          >
            {t('investment.pending', 'Pending')}
          </Badge>
        </Flex>
        <Box py={8}>
          <Icon as={FaCoins} boxSize={12} color={primaryColor} mb={4} />
          <Text color={secondaryTextColor} fontSize="lg" fontWeight="medium" mb={2}>
            {t('investment.pendingApproval', 'Your investment is pending approval')}
          </Text>
          <Text color={secondaryTextColor} mb={4}>
            {t('investment.dashboardAvailableSoon', 'Your investment dashboard will be available once your investment is approved.')}
          </Text>
        </Box>
      </Box>
    );
  }

  // Calculate current value (investment + earnings)
  const currentValue = latestInvestment.amount + totalEarned;

  // Format currency for display with crypto symbol
  const formatCurrency = (amount: number) => {
    return formatCurrencyWithSymbol(amount, latestInvestment.currency);
  };

  return (
    <Box
      bg={bgColor}
      p={6}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      className={className}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md" color={textColor}>Your Investment Dashboard</Heading>
        <Badge
          colorScheme="green"
          px={2}
          py={1}
          fontSize="sm"
          fontWeight="bold"
        >
          Active
        </Badge>
      </Flex>

      {/* Main investment info */}
      <Flex
        direction={{ base: "column", md: "row" }}
        justify="space-between"
        align="center"
        mb={6}
        borderLeftWidth="4px"
        borderLeftColor={primaryColor}
        pl={4}
      >
        {/* Left side - Investment details */}
        <HStack spacing={6} mb={{ base: 4, md: 0 }}>
          <Flex
            bg={`${getCryptoColor(latestInvestment.currency, primaryColor)}33`}
            p={4}
            borderRadius="full"
            align="center"
            justify="center"
            boxSize="80px"
          >
            <Icon
              as={getCryptoIcon(latestInvestment.currency)}
              color={getCryptoColor(latestInvestment.currency, primaryColor)}
              boxSize={10}
            />
          </Flex>
          <Box>
            <Text color={secondaryTextColor} fontSize="sm" mb={1}>Latest Investment</Text>
            <Text color={textColor} fontSize="3xl" fontWeight="bold">
              {latestInvestment.amount} {latestInvestment.currency}
            </Text>
            <HStack spacing={2} mt={1}>
              <Badge bg={`${getCryptoColor(latestInvestment.currency, primaryColor)}33`} color={getCryptoColor(latestInvestment.currency, primaryColor)} px={2} py={1} borderRadius="md">
                {latestInvestment.currency}
              </Badge>
              <Badge bg="#0ECB8133" color="#0ECB81" px={2} py={1} borderRadius="md">1% Daily</Badge>
            </HStack>
          </Box>
        </HStack>

        {/* Right side - Current value */}
        <Box textAlign={{ base: "left", md: "right" }}>
          <Text color={secondaryTextColor} fontSize="sm" mb={1}>Current Value</Text>
          <Text color={textColor} fontSize="3xl" fontWeight="bold">
            {formatCurrency(currentValue)}
          </Text>
          <Text color="#0ECB81" fontSize="sm" mt={1}>
            <StatArrow type="increase" />
            +{formatCurrency(totalEarned)} today
          </Text>
        </Box>
      </Flex>

      {/* Stats Grid */}
      <Grid templateColumns={{ base: "1fr", md: "repeat(3, 1fr)" }} gap={6} mb={6}>
        {/* Investment Date & Time */}
        <GridItem>
          <Box
            bg={cardBgColor}
            p={4}
            borderRadius="md"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <Flex align="center" mb={2}>
              <Icon as={FaCalendarAlt} color={primaryColor} mr={2} />
              <Text color={secondaryTextColor} fontSize="sm">Investment Date & Time</Text>
            </Flex>
            <Text color={textColor} fontWeight="bold">
              {new Date(latestInvestment.date).toLocaleDateString()}
            </Text>
            <Text color={textColor} fontSize="sm">
              {new Date(latestInvestment.date).toLocaleTimeString()} UTC
            </Text>
            <Text color={secondaryTextColor} fontSize="xs" mt={2}>
              {Math.floor((new Date().getTime() - new Date(latestInvestment.date).getTime()) / (1000 * 60 * 60 * 24))} days ago
            </Text>
          </Box>
        </GridItem>

        {/* Daily Interest Rate */}
        <GridItem>
          <Box
            bg={cardBgColor}
            p={4}
            borderRadius="md"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <Flex align="center" mb={2}>
              <Icon as={FaPercentage} color="#0ECB81" mr={2} />
              <Text color={secondaryTextColor} fontSize="sm">Daily Interest Rate</Text>
            </Flex>
            <Text color={textColor} fontWeight="bold">1.0% Daily</Text>
            <Text color={textColor} fontSize="sm">
              {latestInvestment.currency}: {formatCurrency(latestInvestment.amount * 0.01)} per day
            </Text>
            <Text color={secondaryTextColor} fontSize="xs" mt={2}>
              365% Annual Yield on all assets
            </Text>
          </Box>
        </GridItem>

        {/* Total Interest Earned */}
        <GridItem>
          <Box
            bg={cardBgColor}
            p={4}
            borderRadius="md"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <Flex align="center" mb={2}>
              <Icon as={FaCoins} color="#F0B90B" mr={2} />
              <Text color={secondaryTextColor} fontSize="sm">Total Interest Earned</Text>
            </Flex>
            <>
              <Text color={textColor} fontWeight="bold">
                {(totalEarned / latestInvestment.amount).toFixed(2)} {latestInvestment.currency}
              </Text>
              <Text color={textColor} fontSize="sm">
                {formatCurrency(totalEarned)}
              </Text>
              <Text color="#0ECB81" fontSize="xs" mt={2}>
                +0.1 {latestInvestment.currency}, +{formatCurrency(latestInvestment.amount * 0.01)} today
              </Text>
            </>
          </Box>
        </GridItem>
      </Grid>

      {/* Next Interest Payment */}
      {nextPayment && (
        <Box
          bg={cardBgColor}
          p={4}
          borderRadius="md"
          borderWidth="1px"
          borderColor={borderColor}
          borderLeftWidth="4px"
          borderLeftColor="#0ECB81"
        >
          <Flex justify="space-between" align="center">
            <HStack>
              <Icon as={FaMoneyBillWave} color="#0ECB81" boxSize={5} />
              <Box>
                <Text color={secondaryTextColor} fontSize="sm">Next Interest Payment</Text>
                <Text color={textColor} fontWeight="bold">
                  {nextPayment.toLocaleDateString()}
                </Text>
                <Text color={textColor} fontSize="sm">
                  00:00:00 UTC
                </Text>
              </Box>
            </HStack>

            <Box textAlign="right">
              <Text color={secondaryTextColor} fontSize="sm" mb={1}>In {hoursToNextPayment} hours</Text>
              <Text color={textColor} fontWeight="bold">
                Expected: {(latestInvestment.amount * 0.01).toFixed(2)} {latestInvestment.currency}
              </Text>
              <Text color={textColor} fontSize="sm">
                ({formatCurrency(latestInvestment.amount * 0.01)})
              </Text>
            </Box>
          </Flex>
        </Box>
      )}
    </Box>
  );
};

export default InvestmentDashboard;
