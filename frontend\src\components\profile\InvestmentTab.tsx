import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Badge,
  Button,
  Icon,
  SimpleGrid,
  Flex,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Progress,
  Select,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Divider,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Center
} from '@chakra-ui/react';
import {
  FaChartLine,
  FaClock,
  FaCoins,
  FaShieldAlt,
  FaCheckCircle,
  FaPercentage
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import useAuth from '../../hooks/useAuth';
import { investmentBalanceService, InvestmentBalance } from '../../services/investmentBalanceService';
import { investmentPackageService } from '../../services/api';
import DepositModal from '../modals/DepositModal';
import WithdrawModal from '../modals/WithdrawModal';

interface InvestmentPackage {
  _id: string;
  packageId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  accumulatedInterest: number;
  totalEarned: number;
  activeDays: number;
  totalDays: number;
  roi: number;
  createdAt: string;
  activatedAt?: string;
  interestRate: number;
  transactionId?: string;
}

interface InterestDistribution {
  _id: string;
  distributionId: string;
  cryptocurrency: string;
  amount: number;
  usdValue: number;
  distributionDate: string;
  type: 'daily' | 'bonus' | 'completion';
  status: 'completed' | 'pending' | 'failed';
  transactionHash: string;
  packageId: {
    packageId: string;
    currency: string;
    amount: number;
  };
}

interface WithdrawalEligibility {
  eligible: boolean;
  currentBalance: number;
  minimumRequired: number;
  availableForWithdrawal: number;
  withdrawalFee: number;
  usdEquivalent: number;
  status: 'eligible' | 'insufficient_balance' | 'cooldown';
}

interface InvestmentTabProps {
  onDepositOpen: () => void;
}

const InvestmentTab: React.FC<InvestmentTabProps> = ({ onDepositOpen }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();
  const { isOpen: isDistributionOpen, onOpen: onDistributionOpen, onClose: onDistributionClose } = useDisclosure();
  const { isOpen: isWithdrawalOpen, onOpen: onWithdrawalOpen, onClose: onWithdrawalClose } = useDisclosure();

  // Real investment packages from unified transaction system
  const [investmentPackages, setInvestmentPackages] = useState<InvestmentPackage[]>([]);
  const [distributions, setDistributions] = useState<InterestDistribution[]>([]);
  const [withdrawalEligibility, setWithdrawalEligibility] = useState<WithdrawalEligibility | null>(null);
  const [loading, setLoading] = useState(true);
  const [distributionsLoading, setDistributionsLoading] = useState(false);
  const [sortBy, setSortBy] = useState('date');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedPackage, setSelectedPackage] = useState<InvestmentPackage | null>(null);

  // Real investment balances from unified transaction system
  const [investmentBalances, setInvestmentBalances] = useState<InvestmentBalance[]>([]);
  const [balancesLoading, setBalancesLoading] = useState(true);

  // Modal states for deposit and withdraw functionality
  const { isOpen: isDepositModalOpen, onOpen: onDepositModalOpen, onClose: onDepositModalClose } = useDisclosure();
  const { isOpen: isWithdrawModalOpen, onOpen: onWithdrawModalOpen, onClose: onWithdrawModalClose } = useDisclosure();
  const [selectedCrypto, setSelectedCrypto] = useState<string>('USDT');
  const [selectedInterestAmount, setSelectedInterestAmount] = useState<number>(0);

  // Fetch comprehensive investment data with enhanced error handling
  const fetchInvestmentData = async () => {
    try {
      setLoading(true);
      console.log('🔄 InvestmentTab: Fetching investment data');

      // Connect to investment system for real investment data
      // This will be implemented when the investment system is ready
      console.log('⚠️ InvestmentTab: Investment system not yet connected');

      // Set empty arrays until real implementation
      setInvestmentPackages([]);
      setDistributions([]);
      setWithdrawalEligibility(null);

      console.log('✅ InvestmentTab: Investment data fetch completed (empty state)');
    } catch (error: any) {
      console.error('❌ InvestmentTab: Error fetching investment data:', error);

      // Set empty arrays to prevent UI errors
      setInvestmentPackages([]);
      setDistributions([]);
      setWithdrawalEligibility(null);

      toast({
        title: t('common.error', 'Error'),
        description: t('investments.fetchError', 'Failed to load investment data'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch investment balances from unified transaction system
  const fetchInvestmentBalances = async () => {
    try {
      setBalancesLoading(true);
      console.log('🔄 InvestmentTab: Fetching investment balances from unified transaction system');

      // Connect to unified transaction system for real balance data
      // This will be implemented when the investment system is ready
      console.log('⚠️ InvestmentTab: Investment balance system not yet connected to unified transaction system');

      // Set empty array until real implementation
      setInvestmentBalances([]);

      console.log('✅ InvestmentTab: Investment balances fetch completed (empty state)');
    } catch (error: any) {
      console.error('❌ InvestmentTab: Error fetching investment balances:', error);

      // Set empty array to prevent UI errors
      setInvestmentBalances([]);

      toast({
        title: t('common.error', 'Error'),
        description: t('investments.balancesFetchError', 'Failed to load investment balances'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setBalancesLoading(false);
    }
  };

  // Fetch interest distributions
  const fetchDistributions = async (packageId?: string) => {
    try {
      setDistributionsLoading(true);
      const url = packageId
        ? `/api/investment-packages/distributions?packageId=${packageId}`
        : '/api/investment-packages/distributions';

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDistributions(data.data.distributions || []);
      }
    } catch (error) {
      console.error('Error fetching distributions:', error);
    } finally {
      setDistributionsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchInvestmentData();
      fetchInvestmentBalances();
    }
  }, [user]);

  // Enhanced WebSocket integration for real-time updates
  useEffect(() => {
    if (!user) return;

    // Subscribe to investment updates
    const handleInvestmentUpdate = (data: any) => {
      console.log('Investment update received:', data);
      fetchInvestmentData();
      fetchInvestmentBalances();
    };

    // Subscribe to balance updates
    const handleBalanceUpdate = (data: any) => {
      console.log('Balance update received:', data);
      fetchInvestmentBalances();
    };

    // Subscribe to distribution updates
    const handleDistributionUpdate = (data: any) => {
      console.log('Distribution update received:', data);
      fetchDistributions();
    };

    // Subscribe to deposit updates (for investment package creation)
    const handleDepositUpdate = (data: any) => {
      console.log('Deposit update received:', data);
      if (data.type === 'deposit_approved' || data.type === 'investment_created') {
        fetchInvestmentData();
        fetchInvestmentBalances();
      }
    };

    // Subscribe to withdrawal updates
    const handleWithdrawalUpdate = (data: any) => {
      console.log('Withdrawal update received:', data);
      fetchInvestmentBalances();
    };

    // Import WebSocket hook dynamically to avoid circular dependencies
    import('../../hooks/useWebSocket').then((module: any) => {
      const useWebSocket = module.default || module.useWebSocket;
      if (!useWebSocket) {
        console.warn('WebSocket hook not available');
        return;
      }
      const { subscribe, unsubscribe } = useWebSocket();

      // Subscribe to all relevant events
      subscribe('investment_update', handleInvestmentUpdate);
      subscribe('investment_package_created', handleInvestmentUpdate);
      subscribe('investment_package_updated', handleInvestmentUpdate);
      subscribe('balance_update', handleBalanceUpdate);
      subscribe('wallet_balance_updated', handleBalanceUpdate);
      subscribe('distribution_update', handleDistributionUpdate);
      subscribe('interest_distributed', handleDistributionUpdate);
      subscribe('deposit_update', handleDepositUpdate);
      subscribe('deposit_approved', handleDepositUpdate);
      subscribe('withdrawal_update', handleWithdrawalUpdate);
      subscribe('withdrawal_processed', handleWithdrawalUpdate);

      return () => {
        unsubscribe('investment_update', handleInvestmentUpdate);
        unsubscribe('investment_package_created', handleInvestmentUpdate);
        unsubscribe('investment_package_updated', handleInvestmentUpdate);
        unsubscribe('balance_update', handleBalanceUpdate);
        unsubscribe('wallet_balance_updated', handleBalanceUpdate);
        unsubscribe('distribution_update', handleDistributionUpdate);
        unsubscribe('interest_distributed', handleDistributionUpdate);
        unsubscribe('deposit_update', handleDepositUpdate);
        unsubscribe('deposit_approved', handleDepositUpdate);
        unsubscribe('withdrawal_update', handleWithdrawalUpdate);
        unsubscribe('withdrawal_processed', handleWithdrawalUpdate);
      };
    }).catch(error => {
      console.error('Error setting up WebSocket subscriptions:', error);
    });
  }, [user]);

  // Calculate summary statistics grouped by currency
  const summaryByCurrency = investmentPackages.reduce((acc, pkg) => {
    if (!acc[pkg.currency]) {
      acc[pkg.currency] = {
        totalInvested: 0,
        totalEarned: 0,
        activePackages: 0,
        totalPackages: 0,
        totalROI: 0
      };
    }

    acc[pkg.currency].totalInvested += pkg.amount;
    acc[pkg.currency].totalEarned += pkg.totalEarned;
    acc[pkg.currency].totalPackages += 1;
    acc[pkg.currency].totalROI += pkg.roi;

    if (pkg.status === 'active') {
      acc[pkg.currency].activePackages += 1;
    }

    return acc;
  }, {} as Record<string, {
    totalInvested: number;
    totalEarned: number;
    activePackages: number;
    totalPackages: number;
    totalROI: number;
  }>);

  // Legacy calculations for backward compatibility
  const totalInvested = investmentPackages.reduce((sum, pkg) => sum + pkg.amount, 0);
  const totalEarned = investmentPackages.reduce((sum, pkg) => sum + pkg.totalEarned, 0);
  const activePackages = investmentPackages.filter(pkg => pkg.status === 'active').length;
  const averageROI = investmentPackages.length > 0
    ? investmentPackages.reduce((sum, pkg) => sum + pkg.roi, 0) / investmentPackages.length
    : 0;

  const formatCryptocurrencyAmount = (amount: number, currency: string) => {
    return `${amount.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    })} ${currency}`;
  };

  const formatUSDValue = (amount: number) => {
    return `$${amount.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  const getPackageProgress = (pkg: InvestmentPackage) => {
    return (pkg.activeDays / pkg.totalDays) * 100;
  };

  const getPackageStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#02C076';
      case 'completed': return '#F0B90B';
      case 'withdrawn': return '#F84960';
      case 'pending': return '#848E9C';
      default: return '#848E9C';
    }
  };

  const calculateDailyInterest = (principal: number, rate: number) => {
    return principal * (rate / 100);
  };

  // Enhanced deposit button click handler for investment packages
  const handleDepositClick = (packageCurrency: string, packageData?: any) => {
    console.log(`Opening deposit modal for ${packageCurrency}:`, {
      currency: packageCurrency,
      packageData: packageData
    });
    setSelectedCrypto(packageCurrency);
    onDepositModalOpen();
  };

  // Enhanced withdraw button click handler for investment packages with Total Earned data
  const handleWithdrawClick = (packageCurrency: string, packageData?: any) => {
    const totalEarned = packageData?.accumulatedInterest || 0;
    console.log(`Opening withdraw modal for ${packageCurrency}:`, {
      currency: packageCurrency,
      totalEarned: totalEarned,
      accumulatedInterest: packageData?.accumulatedInterest,
      packageId: packageData?._id
    });
    setSelectedCrypto(packageCurrency);
    setSelectedInterestAmount(totalEarned);
    onWithdrawModalOpen();
  };

  // Enhanced modal handlers for button integration
  const handleDepositModalOpen = () => {
    onDepositModalOpen();
  };

  const handleWithdrawModalOpen = () => {
    onWithdrawModalOpen();
  };

  if (loading) {
    return (
      <Center py={10}>
        <VStack spacing={4}>
          <Spinner color="#F0B90B" size="xl" thickness="4px" />
          <Text color="#848E9C">
            {t('investments.loading', 'Loading investment data...')}
          </Text>
        </VStack>
      </Center>
    );
  }

  return (
    <Box
      bg="linear-gradient(135deg, rgba(30, 32, 38, 0.95) 0%, rgba(30, 32, 38, 0.85) 100%)"
      backdropFilter="blur(20px)"
      p={{ base: 8, md: 12 }}
      borderRadius="2xl"
      borderWidth="1px"
      borderColor="rgba(240, 185, 11, 0.3)"
      boxShadow="0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(240, 185, 11, 0.1)"
      position="relative"
      overflow="hidden"
      _before={{
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        height: "2px",
        background: "linear-gradient(90deg, transparent, rgba(240, 185, 11, 0.6), transparent)",
      }}
    >
      <VStack spacing={8} textAlign="center" maxW="700px" mx="auto">
        <Box
          bg="rgba(240, 185, 11, 0.1)"
          p={6}
          borderRadius="full"
          borderWidth="2px"
          borderColor="rgba(240, 185, 11, 0.3)"
        >
          <Icon as={FaCoins} color="#FCD535" boxSize={16} />
        </Box>

        <VStack spacing={4}>
          <Text color="#FCD535" fontSize="3xl" fontWeight="700" letterSpacing="0.5px">
            Secure Cryptocurrency Investment
          </Text>
          <Text color="#EAECEF" fontSize="xl" fontWeight="500" lineHeight="1.5">
            Grow your digital assets with our professional investment platform
          </Text>
          <Box
            bg="linear-gradient(135deg, rgba(2, 192, 118, 0.2) 0%, rgba(2, 192, 118, 0.1) 100%)"
            px={6}
            py={3}
            borderRadius="full"
            borderWidth="2px"
            borderColor="rgba(2, 192, 118, 0.4)"
            display="inline-block"
          >
            <HStack spacing={2}>
              <Icon as={FaPercentage} color="#02C076" boxSize={5} />
              <Text color="#02C076" fontSize="xl" fontWeight="700">
                1% Daily Interest Rate
              </Text>
            </HStack>
          </Box>
        </VStack>

        <VStack spacing={6} w="100%">
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6} w="100%">
            <VStack spacing={3} p={6} bg="rgba(11, 14, 17, 0.6)" borderRadius="xl" borderWidth="1px" borderColor="rgba(43, 49, 57, 0.8)">
              <Icon as={FaShieldAlt} color="#02C076" boxSize={8} />
              <Text color="#EAECEF" fontWeight="600" fontSize="lg">Secure & Safe</Text>
              <Text color="#848E9C" fontSize="sm" textAlign="center">
                Bank-level security with multi-layer protection for your investments
              </Text>
            </VStack>

            <VStack spacing={3} p={6} bg="rgba(11, 14, 17, 0.6)" borderRadius="xl" borderWidth="1px" borderColor="rgba(43, 49, 57, 0.8)">
              <Icon as={FaChartLine} color="#FCD535" boxSize={8} />
              <Text color="#EAECEF" fontWeight="600" fontSize="lg">1% Daily Returns</Text>
              <Text color="#848E9C" fontSize="sm" textAlign="center">
                Earn 1% daily interest on your cryptocurrency investments with guaranteed returns
              </Text>
            </VStack>

            <VStack spacing={3} p={6} bg="rgba(11, 14, 17, 0.6)" borderRadius="xl" borderWidth="1px" borderColor="rgba(43, 49, 57, 0.8)">
              <Icon as={FaClock} color="#02C076" boxSize={8} />
              <Text color="#EAECEF" fontWeight="600" fontSize="lg">24/7 Support</Text>
              <Text color="#848E9C" fontSize="sm" textAlign="center">
                Round-the-clock customer support for all your investment needs
              </Text>
            </VStack>
          </SimpleGrid>

          <Box
            bg="rgba(2, 192, 118, 0.1)"
            p={6}
            borderRadius="xl"
            borderWidth="1px"
            borderColor="rgba(2, 192, 118, 0.3)"
            w="100%"
          >
            <HStack justify="center" spacing={4} mb={4}>
              <Icon as={FaCheckCircle} color="#02C076" boxSize={6} />
              <Text color="#02C076" fontSize="lg" fontWeight="600">
                Trusted by thousands of investors worldwide
              </Text>
            </HStack>
            <Text color="#EAECEF" textAlign="center" fontSize="md">
              Join our growing community of successful cryptocurrency investors earning 1% daily returns.
              Start building your digital wealth today with our proven investment strategies and guaranteed daily profits.
            </Text>
          </Box>

          <Button
            leftIcon={<Icon as={FaCoins} />}
            bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
            color="#0B0E11"
            size="xl"
            minH="60px"
            fontSize="lg"
            fontWeight="700"
            borderRadius="xl"
            px={12}
            onClick={handleDepositModalOpen}
            boxShadow="0 4px 20px rgba(240, 185, 11, 0.4)"
            _hover={{
              bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
              transform: "translateY(-3px)",
              boxShadow: "0 8px 30px rgba(240, 185, 11, 0.5)"
            }}
            _active={{
              transform: "translateY(-1px)"
            }}
          >
            Start Investing Now
          </Button>
        </VStack>
      </VStack>
    </Box>
  );
};

export default InvestmentTab;
