import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Badge,
  Select,
  Input,
  InputGroup,
  InputLeftElement,
  useToast,
  Spinner,
  Flex,
  Icon,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  SimpleGrid
} from '@chakra-ui/react';
import {
  FaSearch,
  FaEye,
  FaCheck,
  FaTimes,
  FaEllipsisV,
  FaSync,
  FaUser,
  FaCoins,
  FaWallet,
  FaClock,
  FaCog
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { getCryptoIcon, getCryptoColor } from '../../utils/cryptoIcons';
import adminWithdrawalService, { AdminWithdrawal } from '../../services/adminWithdrawalService';
import {
  getStatusColorScheme,
  getStatusLabel,
  getStatusBadgeProps,
  canTransitionStatus,
  getAllowedStatusTransitions
} from '../../utils/transactionStatusUtils';
import TransactionStatusBadge from '../../components/TransactionStatusBadge';



interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

const WithdrawalManagement: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();

  // State management
  const [withdrawals, setWithdrawals] = useState<AdminWithdrawal[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });

  // Filters
  const [filters, setFilters] = useState({
    status: '',
    cryptocurrency: '',
    withdrawalType: '',
    search: ''
  });

  // Modal and action states
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<AdminWithdrawal | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Amount editing states
  const [isEditingAmount, setIsEditingAmount] = useState(false);
  const [editedAmount, setEditedAmount] = useState<number>(0);
  const [amountEditNotes, setAmountEditNotes] = useState('');
  const [isAmountSubmitting, setIsAmountSubmitting] = useState(false);

  // Load withdrawals on component mount and filter changes
  useEffect(() => {
    loadWithdrawals();
  }, [pagination.page, filters]);

  // WebSocket for real-time cross-section updates
  useEffect(() => {
    const handleWithdrawalAmountUpdate = (data: any) => {
      console.log('Real-time withdrawal amount update received:', data);

      // Update withdrawals list if the updated withdrawal is in current view
      setWithdrawals(prev => prev.map(withdrawal =>
        withdrawal.id === data.withdrawalId
          ? {
              ...withdrawal,
              amount: data.newAmount,
              netAmount: data.netAmount,
              adminNotes: data.adminNotes || withdrawal.adminNotes
            }
          : withdrawal
      ));

      // Update selected withdrawal if it's the one being modified
      if (selectedWithdrawal && selectedWithdrawal.id === data.withdrawalId) {
        setSelectedWithdrawal(prev => prev ? {
          ...prev,
          amount: data.newAmount,
          netAmount: data.netAmount,
          adminNotes: data.adminNotes || prev.adminNotes
        } : null);
      }
    };

    const handleDepositAmountUpdate = (data: any) => {
      console.log('Cross-section deposit amount update received:', data);

      // Show notification about related deposit amount change
      if (data.userId && selectedWithdrawal && selectedWithdrawal.user?.id === data.userId) {
        toast({
          title: t('admin.withdrawals.crossSectionUpdate', 'Related Deposit Updated'),
          description: t('admin.withdrawals.crossSectionDesc', `Deposit amount changed to $${data.newAmount}`),
          status: 'info',
          duration: 8000,
          isClosable: true,
          position: 'top-right',
        });
      }

      // Refresh withdrawals list to show any related changes
      if (data.userId) {
        loadWithdrawals();
      }
    };

    // Set up WebSocket listeners
    if (typeof window !== 'undefined' && window.io) {
      const socket = window.io();
      socket.on('admin_withdrawal_amount_updated', handleWithdrawalAmountUpdate);
      socket.on('admin_deposit_amount_updated', handleDepositAmountUpdate);

      return () => {
        socket.off('admin_withdrawal_amount_updated', handleWithdrawalAmountUpdate);
        socket.off('admin_deposit_amount_updated', handleDepositAmountUpdate);
      };
    }
  }, [selectedWithdrawal, toast, t]);

  // Load withdrawal data on component mount
  useEffect(() => {
    loadWithdrawals();
  }, []);

  const loadWithdrawals = async () => {
    try {
      setLoading(true);
      const response = await adminWithdrawalService.getWithdrawals({
        page: pagination.page,
        limit: pagination.limit,
        status: filters.status || undefined,
        cryptocurrency: filters.cryptocurrency || undefined,
        withdrawalType: filters.withdrawalType || undefined,
        search: filters.search || undefined
      });

      setWithdrawals(response.data.withdrawals);
      setPagination(response.data.pagination);
    } catch (error: any) {
      console.error('Error loading withdrawals:', error);
      setWithdrawals([]);
      setPagination({
        page: 1,
        limit: 20,
        total: 0,
        pages: 0
      });
    } finally {
      setLoading(false);
    }
  };



  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };

  const handleViewDetails = (withdrawal: AdminWithdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setIsDetailsModalOpen(true);

    // Initialize amount editing state
    setEditedAmount(withdrawal.amount);
    setAmountEditNotes('');
    setIsEditingAmount(false);
  };

  const handleStatusUpdate = async (withdrawalId: string, status: string) => {
    try {
      setActionLoading(withdrawalId);
      const response = await adminWithdrawalService.updateWithdrawalStatus(withdrawalId, {
        status,
        adminNotes: `Status updated to ${status} by admin`
      });

      toast({
        title: t('admin.withdrawals.success.statusUpdated', 'Success'),
        description: response.message || t('admin.withdrawals.success.statusUpdatedDesc', 'Withdrawal status updated successfully'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      loadWithdrawals(); // Refresh the list
    } catch (error: any) {
      toast({
        title: t('admin.withdrawals.error.updateFailed', 'Error'),
        description: error.message || t('admin.withdrawals.error.updateFailedDesc', 'Failed to update withdrawal status'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleAmountUpdate = async () => {
    if (!selectedWithdrawal || editedAmount <= 0) {
      toast({
        title: t('admin.withdrawals.error', 'Error'),
        description: t('admin.withdrawals.invalidAmount', 'Please enter a valid amount'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsAmountSubmitting(true);
    try {
      const response = await adminWithdrawalService.updateWithdrawalAmount(selectedWithdrawal.id, {
        amount: editedAmount,
        adminNotes: amountEditNotes || undefined
      });

      toast({
        title: t('admin.withdrawals.amountUpdateSuccess', 'Amount Updated Successfully'),
        description: t('admin.withdrawals.amountUpdateDesc', `Withdrawal amount changed from ${selectedWithdrawal.amount} to ${editedAmount} ${selectedWithdrawal.cryptocurrency}. Changes synchronized across all admin sections and wallet cards.`),
        status: 'success',
        duration: 8000,
        isClosable: true,
        position: 'top-right',
      });

      // Update local state
      setSelectedWithdrawal(prev => prev ? {
        ...prev,
        amount: editedAmount,
        netAmount: editedAmount - prev.networkFee,
        adminNotes: amountEditNotes || prev.adminNotes
      } : null);

      // Emit wallet sync event for real-time updates to wallet cards
      try {
        const walletSyncEvent = new CustomEvent('walletSync', {
          detail: {
            type: 'withdrawal_amount_updated',
            userId: selectedWithdrawal.user.id,
            cryptocurrency: selectedWithdrawal.cryptocurrency,
            oldAmount: selectedWithdrawal.amount,
            newAmount: editedAmount,
            withdrawalId: selectedWithdrawal.id,
            timestamp: new Date().toISOString()
          }
        });
        window.dispatchEvent(walletSyncEvent);
        console.log('Wallet sync event dispatched for withdrawal amount update');
      } catch (syncError) {
        console.error('Error dispatching wallet sync event:', syncError);
      }

      // Refresh withdrawals list
      await loadWithdrawals();

      // Reset editing state
      setIsEditingAmount(false);
      setAmountEditNotes('');

    } catch (error: any) {
      console.error('Error updating withdrawal amount:', error);
      toast({
        title: t('admin.withdrawals.error', 'Error'),
        description: error.response?.data?.message || t('admin.withdrawals.amountUpdateError', 'Failed to update withdrawal amount'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsAmountSubmitting(false);
    }
  };

  return (
    <Box bg="#0B0E11" minH="100vh" p={6}>
      <Container maxW="7xl">
        <VStack spacing={6} align="stretch">
          {/* Header */}
          <Box>
            <Heading color="white" size="lg" mb={2}>
              {t('admin.withdrawals.title', 'Withdrawal Management')}
            </Heading>
            <Text color="gray.400">
              {t('admin.withdrawals.subtitle', 'Manage and process user withdrawal requests with complete crypto address verification')}
            </Text>
          </Box>

          {/* Filters */}
          <Box bg="gray.800" p={{ base: 3, md: 4 }} borderRadius="md" borderWidth="1px" borderColor="gray.600">
            <VStack spacing={4} align="stretch">
              <InputGroup maxW={{ base: "100%", md: "300px" }}>
                <InputLeftElement>
                  <Icon as={FaSearch} color="gray.400" />
                </InputLeftElement>
                <Input
                  placeholder={t('admin.withdrawals.filters.searchPlaceholder', 'Search by user, email, or wallet address...')}
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  bg="gray.700"
                  borderColor="gray.600"
                  color="white"
                  minH={{ base: "44px", md: "auto" }}
                  fontSize={{ base: "16px", md: "14px" }}
                />
              </InputGroup>

              <HStack spacing={4} w="full" flexWrap="wrap">

                <Select
                  placeholder={t('admin.withdrawals.filters.allStatuses', 'All Statuses')}
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  maxW={{ base: "100%", md: "150px" }}
                  bg="gray.700"
                  borderColor="gray.600"
                  color="white"
                  minH={{ base: "44px", md: "auto" }}
                  fontSize={{ base: "16px", md: "14px" }}
                >
                  <option value="pending">{t('admin.withdrawals.status.pending', 'Pending')}</option>
                  <option value="approved">{t('admin.withdrawals.status.approved', 'Approved')}</option>
                  <option value="rejected">{t('admin.withdrawals.status.rejected', 'Rejected')}</option>
                  <option value="completed">{t('admin.withdrawals.status.completed', 'Completed')}</option>
                  <option value="failed">{t('admin.withdrawals.status.failed', 'Failed')}</option>
                </Select>

                <Select
                  placeholder={t('admin.withdrawals.filters.allCryptos', 'All Cryptocurrencies')}
                  value={filters.cryptocurrency}
                  onChange={(e) => handleFilterChange('cryptocurrency', e.target.value)}
                  maxW={{ base: "100%", md: "150px" }}
                  bg="gray.700"
                  borderColor="gray.600"
                  color="white"
                  minH={{ base: "44px", md: "auto" }}
                  fontSize={{ base: "16px", md: "14px" }}
                >
                  <option value="BTC">Bitcoin (BTC)</option>
                  <option value="ETH">Ethereum (ETH)</option>
                  <option value="USDT">Tether (USDT)</option>
                  <option value="BNB">Binance Coin (BNB)</option>
                  <option value="SOL">Solana (SOL)</option>
                  <option value="DOGE">Dogecoin (DOGE)</option>
                  <option value="TRX">Tron (TRX)</option>
                </Select>

                <Button
                  leftIcon={<FaSync />}
                  onClick={loadWithdrawals}
                  colorScheme="blue"
                  variant="outline"
                  w={{ base: "100%", md: "auto" }}
                  minH={{ base: "44px", md: "auto" }}
                  fontSize={{ base: "16px", md: "14px" }}
                >
                  {t('admin.withdrawals.actions.refresh', 'Refresh')}
                </Button>
              </HStack>
            </VStack>
          </Box>

          {/* Withdrawals Table */}
          <Box bg="gray.800" borderRadius="md" borderWidth="1px" borderColor="gray.600" overflow="hidden">
            <Box overflowX="auto">
            {loading ? (
              <Flex justify="center" align="center" h="200px">
                <VStack spacing={4}>
                  <Spinner size="lg" color="blue.500" />
                  <Text color="gray.400">{t('admin.withdrawals.loading', 'Loading withdrawal requests...')}</Text>
                </VStack>
              </Flex>
            ) : withdrawals.length === 0 ? (
              <Flex justify="center" align="center" h="200px">
                <VStack spacing={4}>
                  <Icon as={FaWallet} boxSize={12} color="gray.500" />
                  <Text color="gray.400" textAlign="center">
                    {t('admin.withdrawals.noData', 'No withdrawal requests found')}
                  </Text>
                  <Text color="gray.500" fontSize="sm" textAlign="center">
                    {t('admin.withdrawals.noDataDesc', 'Withdrawal requests will appear here when users submit them')}
                  </Text>
                </VStack>
              </Flex>
            ) : (
              <Table variant="simple" minW="800px">
                <Thead bg="gray.700">
                  <Tr>
                    <Th color="gray.300">{t('admin.withdrawals.table.user', 'User')}</Th>
                    <Th color="gray.300">{t('admin.withdrawals.table.crypto', 'Crypto')}</Th>
                    <Th color="gray.300">{t('admin.withdrawals.table.type', 'Type')}</Th>
                    <Th color="gray.300">{t('admin.withdrawals.table.amount', 'Amount')}</Th>
                    <Th color="gray.300">{t('admin.withdrawals.table.address', 'Wallet Address')}</Th>
                    <Th color="gray.300">{t('admin.withdrawals.table.status', 'Status')}</Th>
                    <Th color="gray.300">{t('admin.withdrawals.table.date', 'Date')}</Th>
                    <Th color="gray.300">{t('admin.withdrawals.table.actions', 'Actions')}</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {withdrawals.map((withdrawal) => (
                    <Tr key={withdrawal.id} _hover={{ bg: "gray.700" }}>
                      <Td>
                        <VStack align="start" spacing={0}>
                          <Text color="white" fontWeight="medium">
                            {withdrawal.user?.name || 'Unknown User'}
                          </Text>
                          <Text color="gray.400" fontSize="sm">
                            {withdrawal.user?.email || 'No email'}
                          </Text>
                          {withdrawal.user?.phoneNumber && (
                            <Text color="gray.500" fontSize="xs">
                              {withdrawal.user.phoneNumber}
                            </Text>
                          )}
                          {withdrawal.user?.country && (
                            <Text color="gray.500" fontSize="xs">
                              {withdrawal.user.country}
                            </Text>
                          )}
                        </VStack>
                      </Td>
                      <Td>
                        <HStack>
                          <Icon
                            as={getCryptoIcon(withdrawal.cryptocurrency)}
                            color={getCryptoColor(withdrawal.cryptocurrency)}
                            boxSize={5}
                          />
                          <Text color="white">{withdrawal.cryptocurrency}</Text>
                        </HStack>
                      </Td>
                      <Td>
                        <Text color="white">
                          {adminWithdrawalService.formatWithdrawalType(withdrawal.withdrawalType)}
                        </Text>
                      </Td>
                      <Td>
                        <VStack align="start" spacing={0}>
                          <Text color="white" fontWeight="medium">
                            {adminWithdrawalService.formatCurrency(withdrawal.amount, withdrawal.cryptocurrency)}
                          </Text>
                          <Text color="gray.400" fontSize="sm">
                            {t('admin.withdrawals.table.net', 'Net')}: {adminWithdrawalService.formatCurrency(withdrawal.netAmount, withdrawal.cryptocurrency)}
                          </Text>
                          <Text color="gray.500" fontSize="xs">
                            {t('admin.withdrawals.table.usdValue', 'USD')}: {adminWithdrawalService.formatUSD(withdrawal.usdValue)}
                          </Text>
                        </VStack>
                      </Td>
                      <Td maxW="200px">
                        <VStack align="start" spacing={1}>
                          <Text
                            color="white"
                            fontSize="sm"
                            fontFamily="mono"
                            wordBreak="break-all"
                            title={withdrawal.walletAddress}
                          >
                            {withdrawal.walletAddress ?
                              `${withdrawal.walletAddress.substring(0, 8)}...${withdrawal.walletAddress.substring(withdrawal.walletAddress.length - 6)}`
                              : t('admin.withdrawals.table.noAddress', 'No address')
                            }
                          </Text>
                          <Text color="gray.400" fontSize="xs">
                            {withdrawal.network || t('admin.withdrawals.table.unknownNetwork', 'Unknown network')}
                          </Text>
                          {withdrawal.txHash && (
                            <Text color="blue.400" fontSize="xs" fontFamily="mono">
                              {t('admin.withdrawals.table.txHash', 'TX')}: {withdrawal.txHash.substring(0, 8)}...
                            </Text>
                          )}
                        </VStack>
                      </Td>
                      <Td>
                        <TransactionStatusBadge
                          status={withdrawal.status}
                          transactionType="withdrawal"
                          size="sm"
                          showIcon={true}
                          showTooltip={true}
                        />
                      </Td>
                      <Td>
                        <Text color="white" fontSize="sm">
                          {new Date(withdrawal.createdAt).toLocaleDateString()}
                        </Text>
                      </Td>
                      <Td>
                        <Menu>
                          <MenuButton
                            as={IconButton}
                            icon={<FaEllipsisV />}
                            variant="ghost"
                            color="gray.400"
                            size="sm"
                          />
                          <MenuList bg="gray.700" borderColor="gray.600">
                            <MenuItem
                              icon={<FaEye />}
                              onClick={() => handleViewDetails(withdrawal)}
                              bg="gray.700"
                              color="white"
                              _hover={{ bg: "gray.600" }}
                            >
                              {t('admin.withdrawals.actions.viewDetails', 'View Details')}
                            </MenuItem>
                            {/* Dynamic status transition menu items */}
                            {getAllowedStatusTransitions(withdrawal.status).map((allowedStatus) => (
                              <MenuItem
                                key={allowedStatus}
                                icon={
                                  allowedStatus === 'approved' ? <FaCheck /> :
                                  allowedStatus === 'completed' ? <FaCheck /> :
                                  allowedStatus === 'rejected' ? <FaTimes /> :
                                  allowedStatus === 'failed' ? <FaTimes /> : <FaCheck />
                                }
                                onClick={() => handleStatusUpdate(withdrawal.id, allowedStatus)}
                                bg="gray.700"
                                color={
                                  allowedStatus === 'approved' ? 'green.400' :
                                  allowedStatus === 'completed' ? 'blue.400' :
                                  allowedStatus === 'rejected' ? 'red.400' :
                                  allowedStatus === 'failed' ? 'red.400' : 'gray.400'
                                }
                                _hover={{ bg: "gray.600" }}
                                isDisabled={actionLoading === withdrawal.id}
                              >
                                {t(`admin.withdrawals.actions.${allowedStatus}`,
                                  allowedStatus === 'approved' ? 'Approve' :
                                  allowedStatus === 'completed' ? 'Mark Completed' :
                                  allowedStatus === 'rejected' ? 'Reject' :
                                  allowedStatus === 'failed' ? 'Mark Failed' :
                                  allowedStatus === 'pending' ? 'Reset to Pending' :
                                  getStatusLabel(allowedStatus)
                                )}
                              </MenuItem>
                            ))}
                          </MenuList>
                        </Menu>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}
            </Box>

            {/* Pagination */}
            {!loading && withdrawals.length > 0 && pagination.pages > 1 && (
              <Box p={{ base: 3, md: 4 }} bg="gray.700" borderTopWidth="1px" borderColor="gray.600">
                <VStack spacing={3}>
                  <Text color="gray.400" fontSize="sm" textAlign="center">
                    {t('admin.withdrawals.pagination.showing', 'Showing {{start}} to {{end}} of {{total}} withdrawals', {
                      start: (pagination.page - 1) * pagination.limit + 1,
                      end: Math.min(pagination.page * pagination.limit, pagination.total),
                      total: pagination.total
                    })}
                  </Text>
                  <HStack spacing={2} justify="center">
                    <Button
                      size={{ base: "md", md: "sm" }}
                      variant="outline"
                      colorScheme="gray"
                      isDisabled={pagination.page === 1}
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                      minH={{ base: "44px", md: "auto" }}
                      fontSize={{ base: "16px", md: "14px" }}
                    >
                      {t('admin.withdrawals.pagination.previous', 'Previous')}
                    </Button>
                    <Text color="white" fontSize="sm" px={3} textAlign="center">
                      {t('admin.withdrawals.pagination.pageOf', 'Page {{current}} of {{total}}', {
                        current: pagination.page,
                        total: pagination.pages
                      })}
                    </Text>
                    <Button
                      size={{ base: "md", md: "sm" }}
                      variant="outline"
                      colorScheme="gray"
                      isDisabled={pagination.page === pagination.pages}
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                      minH={{ base: "44px", md: "auto" }}
                      fontSize={{ base: "16px", md: "14px" }}
                    >
                      {t('admin.withdrawals.pagination.next', 'Next')}
                    </Button>
                  </HStack>
                </VStack>
              </Box>
            )}
          </Box>
        </VStack>
      </Container>

      {/* Withdrawal Details Modal */}
      <Modal
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        size={{ base: "full", md: "4xl" }}
        scrollBehavior="inside"
      >
        <ModalOverlay bg="blackAlpha.800" />
        <ModalContent
          bg="#1A202C"
          borderColor="gray.600"
          maxH={{ base: "100vh", md: "90vh" }}
          m={{ base: 0, md: 4 }}
          borderRadius={{ base: 0, md: "md" }}
        >
          <ModalHeader color="white" borderBottomWidth="1px" borderColor="gray.600">
            <HStack>
              <Icon as={FaEye} color="blue.400" />
              <Text>{t('admin.withdrawals.modal.title', 'Withdrawal Request Details')}</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton color="gray.400" />

          {selectedWithdrawal && (
            <ModalBody py={{ base: 4, md: 6 }} px={{ base: 4, md: 6 }}>
              <VStack spacing={{ base: 6, md: 8 }} align="stretch">
                {/* User Information */}
                <Box>
                  <Heading size="md" color="white" mb={4} display="flex" alignItems="center">
                    <Icon as={FaUser} mr={2} color="blue.400" />
                    {t('admin.withdrawals.modal.userInfo', 'User Information')}
                  </Heading>
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.userName', 'Full Name')}
                      </Text>
                      <Text color="white" fontWeight="medium">
                        {selectedWithdrawal.user?.name || t('admin.withdrawals.modal.unknownUser', 'Unknown User')}
                      </Text>
                    </Box>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.userEmail', 'Email Address')}
                      </Text>
                      <Text color="white" fontWeight="medium">
                        {selectedWithdrawal.user?.email || t('admin.withdrawals.modal.noEmail', 'No email provided')}
                      </Text>
                    </Box>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.userPhone', 'Phone Number')}
                      </Text>
                      <Text color="white" fontWeight="medium">
                        {selectedWithdrawal.user?.phoneNumber || t('admin.withdrawals.modal.noPhone', 'Not provided')}
                      </Text>
                    </Box>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.userCountry', 'Country')}
                      </Text>
                      <Text color="white" fontWeight="medium">
                        {selectedWithdrawal.user?.country || t('admin.withdrawals.modal.noCountry', 'Not specified')}
                      </Text>
                    </Box>
                  </SimpleGrid>
                </Box>

                {/* Withdrawal Details */}
                <Box>
                  <Heading size="md" color="white" mb={4} display="flex" alignItems="center">
                    <Icon as={FaCoins} mr={2} color="green.400" />
                    {t('admin.withdrawals.modal.withdrawalDetails', 'Withdrawal Details')}
                  </Heading>
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.cryptocurrency', 'Cryptocurrency')}
                      </Text>
                      <HStack>
                        <Icon
                          as={getCryptoIcon(selectedWithdrawal.cryptocurrency)}
                          color={getCryptoColor(selectedWithdrawal.cryptocurrency)}
                          boxSize={5}
                        />
                        <Text color="white" fontWeight="medium" fontSize="lg">
                          {selectedWithdrawal.cryptocurrency}
                        </Text>
                      </HStack>
                    </Box>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.withdrawalType', 'Withdrawal Type')}
                      </Text>
                      <Text color="white" fontWeight="medium">
                        {adminWithdrawalService.formatWithdrawalType(selectedWithdrawal.withdrawalType)}
                      </Text>
                    </Box>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.requestedAmount', 'Requested Amount')}
                      </Text>
                      {isEditingAmount ? (
                        <VStack spacing={3} align="stretch">
                          <Input
                            type="number"
                            value={editedAmount}
                            onChange={(e) => setEditedAmount(parseFloat(e.target.value) || 0)}
                            placeholder="Enter new amount"
                            bg="gray.800"
                            borderColor="blue.400"
                            color="white"
                            fontSize={{ base: "16px", md: "lg" }}
                            fontWeight="bold"
                            minH={{ base: "44px", md: "auto" }}
                          />
                          <Input
                            placeholder="Reason for amount change (optional)"
                            value={amountEditNotes}
                            onChange={(e) => setAmountEditNotes(e.target.value)}
                            bg="gray.800"
                            borderColor="gray.600"
                            color="white"
                            fontSize={{ base: "16px", md: "sm" }}
                            minH={{ base: "44px", md: "auto" }}
                          />
                          <VStack spacing={2} w="full">
                            <Button
                              size={{ base: "md", md: "sm" }}
                              colorScheme="green"
                              onClick={handleAmountUpdate}
                              isLoading={isAmountSubmitting}
                              loadingText="Updating & Syncing to Wallets..."
                              w="full"
                              minH={{ base: "44px", md: "auto" }}
                              fontSize={{ base: "16px", md: "14px" }}
                              isDisabled={editedAmount <= 0}
                            >
                              {t('admin.withdrawals.modal.saveAmount', 'Save & Sync Amount')}
                            </Button>
                            <Button
                              size={{ base: "md", md: "sm" }}
                              variant="outline"
                              onClick={() => {
                                setIsEditingAmount(false);
                                setEditedAmount(selectedWithdrawal.amount);
                                setAmountEditNotes('');
                              }}
                              w="full"
                              minH={{ base: "44px", md: "auto" }}
                              fontSize={{ base: "16px", md: "14px" }}
                            >
                              {t('admin.withdrawals.modal.cancelEdit', 'Cancel')}
                            </Button>
                          </VStack>
                        </VStack>
                      ) : (
                        <VStack spacing={2} align="stretch">
                          <HStack justify="space-between" align="center">
                            <Text color="white" fontWeight="bold" fontSize="lg">
                              {adminWithdrawalService.formatCurrency(selectedWithdrawal.amount, selectedWithdrawal.cryptocurrency)}
                            </Text>
                            <Button
                              size={{ base: "sm", md: "sm" }}
                              colorScheme="yellow"
                              variant="outline"
                              onClick={() => setIsEditingAmount(true)}
                              minH={{ base: "44px", md: "auto" }}
                              fontSize={{ base: "16px", md: "14px" }}
                              leftIcon={<Icon as={FaCog} />}
                            >
                              {t('admin.withdrawals.modal.editAmount', 'Edit Amount')}
                            </Button>
                          </HStack>
                          <Text fontSize="xs" color="green.400">
                            {t('admin.withdrawals.editAlwaysEnabled', `✅ Unlimited editing enabled - Current status: ${selectedWithdrawal.status}`)}
                          </Text>
                        </VStack>
                      )}
                    </Box>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.netAmount', 'Net Amount (After Fees)')}
                      </Text>
                      <Text color="green.400" fontWeight="bold" fontSize="lg">
                        {adminWithdrawalService.formatCurrency(selectedWithdrawal.netAmount, selectedWithdrawal.cryptocurrency)}
                      </Text>
                    </Box>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.usdValue', 'USD Value')}
                      </Text>
                      <Text color="white" fontWeight="medium">
                        {adminWithdrawalService.formatUSD(selectedWithdrawal.usdValue)}
                      </Text>
                    </Box>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.status', 'Current Status')}
                      </Text>
                      <TransactionStatusBadge
                        status={selectedWithdrawal.status}
                        transactionType="withdrawal"
                        size="lg"
                        showIcon={true}
                        showTooltip={false}
                      />
                    </Box>
                  </SimpleGrid>
                </Box>

                {/* Crypto Address Information */}
                <Box>
                  <Heading size="md" color="white" mb={4} display="flex" alignItems="center">
                    <Icon as={FaWallet} mr={2} color="orange.400" />
                    {t('admin.withdrawals.modal.addressInfo', 'Destination Wallet Information')}
                  </Heading>
                  <Box bg="gray.700" p={6} borderRadius="md" border="2px solid" borderColor="orange.400">
                    <VStack spacing={4} align="stretch">
                      <Box>
                        <Text color="gray.400" fontSize="sm" mb={2}>
                          {t('admin.withdrawals.modal.walletAddress', 'Destination Wallet Address')}
                        </Text>
                        <Box bg="gray.800" p={4} borderRadius="md" border="1px solid" borderColor="gray.600">
                          <Text
                            color="white"
                            fontFamily="mono"
                            fontSize="md"
                            wordBreak="break-all"
                            lineHeight="1.5"
                          >
                            {selectedWithdrawal.walletAddress || t('admin.withdrawals.modal.noAddressProvided', 'No wallet address provided')}
                          </Text>
                        </Box>
                        {selectedWithdrawal.walletAddress && (
                          <VStack mt={2} spacing={2} align="stretch">
                            <Button
                              size={{ base: "md", md: "sm" }}
                              colorScheme="blue"
                              variant="outline"
                              onClick={() => navigator.clipboard.writeText(selectedWithdrawal.walletAddress)}
                              w="100%"
                              minH={{ base: "44px", md: "auto" }}
                              fontSize={{ base: "16px", md: "14px" }}
                            >
                              {t('admin.withdrawals.modal.copyAddress', 'Copy Address')}
                            </Button>
                            <Button
                              size={{ base: "md", md: "sm" }}
                              colorScheme="green"
                              variant="outline"
                              as="a"
                              href={`https://blockchair.com/${selectedWithdrawal.cryptocurrency.toLowerCase()}/address/${selectedWithdrawal.walletAddress}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              w="100%"
                              minH={{ base: "44px", md: "auto" }}
                              fontSize={{ base: "16px", md: "14px" }}
                            >
                              {t('admin.withdrawals.modal.viewOnExplorer', 'View on Explorer')}
                            </Button>
                          </VStack>
                        )}
                      </Box>

                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                        <Box>
                          <Text color="gray.400" fontSize="sm" mb={1}>
                            {t('admin.withdrawals.modal.network', 'Network')}
                          </Text>
                          <Text color="white" fontWeight="medium">
                            {selectedWithdrawal.network || t('admin.withdrawals.modal.unknownNetwork', 'Unknown network')}
                          </Text>
                        </Box>
                        {selectedWithdrawal.txHash && (
                          <Box>
                            <Text color="gray.400" fontSize="sm" mb={1}>
                              {t('admin.withdrawals.modal.transactionHash', 'Transaction Hash')}
                            </Text>
                            <Text color="blue.400" fontFamily="mono" fontSize="sm" wordBreak="break-all">
                              {selectedWithdrawal.txHash}
                            </Text>
                          </Box>
                        )}
                      </SimpleGrid>
                    </VStack>
                  </Box>
                </Box>

                {/* Timeline and Notes */}
                <Box>
                  <Heading size="md" color="white" mb={4} display="flex" alignItems="center">
                    <Icon as={FaClock} mr={2} color="purple.400" />
                    {t('admin.withdrawals.modal.timeline', 'Request Timeline')}
                  </Heading>
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.requestDate', 'Request Date')}
                      </Text>
                      <Text color="white" fontWeight="medium">
                        {new Date(selectedWithdrawal.createdAt).toLocaleString()}
                      </Text>
                    </Box>
                    <Box bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={1}>
                        {t('admin.withdrawals.modal.lastUpdate', 'Last Updated')}
                      </Text>
                      <Text color="white" fontWeight="medium">
                        {new Date(selectedWithdrawal.updatedAt).toLocaleString()}
                      </Text>
                    </Box>
                  </SimpleGrid>

                  {selectedWithdrawal.adminNotes && (
                    <Box mt={4} bg="gray.700" p={4} borderRadius="md">
                      <Text color="gray.400" fontSize="sm" mb={2}>
                        {t('admin.withdrawals.modal.adminNotes', 'Admin Notes')}
                      </Text>
                      <Text color="white">
                        {selectedWithdrawal.adminNotes}
                      </Text>
                    </Box>
                  )}
                </Box>

                {/* Quick Actions - Dynamic based on allowed transitions */}
                {getAllowedStatusTransitions(selectedWithdrawal.status).length > 0 && (
                  <Box>
                    <Heading size="md" color="white" mb={4} display="flex" alignItems="center">
                      <Icon as={FaCog} mr={2} color="yellow.400" />
                      {t('admin.withdrawals.modal.quickActions', 'Quick Actions')}
                    </Heading>
                    <VStack spacing={4} align="stretch">
                      {getAllowedStatusTransitions(selectedWithdrawal.status).map((allowedStatus) => (
                        <Button
                          key={allowedStatus}
                          colorScheme={
                            allowedStatus === 'approved' ? 'green' :
                            allowedStatus === 'completed' ? 'blue' :
                            allowedStatus === 'rejected' ? 'red' :
                            allowedStatus === 'failed' ? 'red' : 'gray'
                          }
                          leftIcon={
                            allowedStatus === 'approved' ? <FaCheck /> :
                            allowedStatus === 'completed' ? <FaCheck /> :
                            allowedStatus === 'rejected' ? <FaTimes /> :
                            allowedStatus === 'failed' ? <FaTimes /> : <FaCheck />
                          }
                          onClick={() => {
                            handleStatusUpdate(selectedWithdrawal.id, allowedStatus);
                            setIsDetailsModalOpen(false);
                          }}
                          isLoading={actionLoading === selectedWithdrawal.id}
                          loadingText={t(`admin.withdrawals.modal.${allowedStatus}ing`, `${getStatusLabel(allowedStatus)}...`)}
                          w="100%"
                          minH={{ base: "44px", md: "auto" }}
                          fontSize={{ base: "16px", md: "14px" }}
                        >
                          {t(`admin.withdrawals.modal.${allowedStatus}Withdrawal`,
                            allowedStatus === 'approved' ? 'Approve Withdrawal' :
                            allowedStatus === 'completed' ? 'Mark as Completed' :
                            allowedStatus === 'rejected' ? 'Reject Withdrawal' :
                            allowedStatus === 'failed' ? 'Mark as Failed' :
                            allowedStatus === 'pending' ? 'Reset to Pending' :
                            `${getStatusLabel(allowedStatus)} Withdrawal`
                          )}
                        </Button>
                      ))}
                    </VStack>
                  </Box>
                )}
              </VStack>
            </ModalBody>
          )}

          <ModalFooter borderTopWidth="1px" borderColor="gray.600" px={{ base: 4, md: 6 }}>
            <Button
              colorScheme="gray"
              onClick={() => setIsDetailsModalOpen(false)}
              w={{ base: "100%", md: "auto" }}
              minH={{ base: "44px", md: "auto" }}
              fontSize={{ base: "16px", md: "14px" }}
            >
              {t('admin.withdrawals.modal.close', 'Close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default WithdrawalManagement;
