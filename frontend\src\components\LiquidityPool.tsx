import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  Flex,
  Progress,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Button,
  Tooltip,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  SimpleGrid,
  Divider,
  Link,
  useColorModeValue,
  Skeleton
} from '@chakra-ui/react';
import { ExternalLinkIcon, InfoOutlineIcon } from '@chakra-ui/icons';
import { 
  FaWater, 
  FaShieldAlt, 
  FaChartPie, 
  FaExchangeAlt, 
  FaLock,
  FaRegClock,
  FaServer,
  FaCheckCircle,
  FaFileContract,
  FaRegFileAlt
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

// Mock data for liquidity pools
const liquidityPoolsData = [
  {
    id: 'pool1',
    name: 'Main Investment Pool',
    totalValue: 12500000,
    utilization: 78,
    assets: [
      { symbol: 'USDT', percentage: 45, amount: 5625000 },
      { symbol: 'BTC', percentage: 25, amount: 3125000 },
      { symbol: 'ETH', percentage: 20, amount: 2500000 },
      { symbol: 'Other', percentage: 10, amount: 1250000 }
    ],
    dailyVolume: 750000,
    weeklyChange: 5.2,
    audited: true,
    lastAudit: '2023-04-15',
    contractAddress: '******************************************'
  },
  {
    id: 'pool2',
    name: 'Insurance Fund',
    totalValue: 5000000,
    utilization: 15,
    assets: [
      { symbol: 'USDT', percentage: 60, amount: 3000000 },
      { symbol: 'BTC', percentage: 20, amount: 1000000 },
      { symbol: 'ETH', percentage: 15, amount: 750000 },
      { symbol: 'Other', percentage: 5, amount: 250000 }
    ],
    dailyVolume: 50000,
    weeklyChange: 1.8,
    audited: true,
    lastAudit: '2023-04-15',
    contractAddress: '******************************************'
  },
  {
    id: 'pool3',
    name: 'Referral Rewards Pool',
    totalValue: 2500000,
    utilization: 42,
    assets: [
      { symbol: 'USDT', percentage: 70, amount: 1750000 },
      { symbol: 'BTC', percentage: 15, amount: 375000 },
      { symbol: 'ETH', percentage: 10, amount: 250000 },
      { symbol: 'Other', percentage: 5, amount: 125000 }
    ],
    dailyVolume: 125000,
    weeklyChange: 3.5,
    audited: true,
    lastAudit: '2023-04-15',
    contractAddress: '******************************************'
  }
];

// Mock data for recent transactions
const recentTransactionsData = [
  { id: 'tx1', type: 'Deposit', amount: 50000, asset: 'USDT', timestamp: '2023-05-01T12:34:56Z', pool: 'Main Investment Pool', status: 'completed' },
  { id: 'tx2', type: 'Withdrawal', amount: 10000, asset: 'USDT', timestamp: '2023-05-01T10:23:45Z', pool: 'Main Investment Pool', status: 'completed' },
  { id: 'tx3', type: 'Interest', amount: 5000, asset: 'USDT', timestamp: '2023-05-01T09:12:34Z', pool: 'Main Investment Pool', status: 'completed' },
  { id: 'tx4', type: 'Deposit', amount: 1.5, asset: 'BTC', timestamp: '2023-04-30T18:45:12Z', pool: 'Insurance Fund', status: 'completed' },
  { id: 'tx5', type: 'Referral', amount: 2500, asset: 'USDT', timestamp: '2023-04-30T15:30:22Z', pool: 'Referral Rewards Pool', status: 'completed' }
];

// Mock data for audit reports
const auditReportsData = [
  { id: 'audit1', name: 'CertiK Security Audit', date: '2023-04-15', auditor: 'CertiK', score: '95/100', status: 'Passed', url: 'https://certik.com/reports/shipping-Finance' },
  { id: 'audit2', name: 'Hacken Pool Security Audit', date: '2023-02-20', auditor: 'Hacken', score: '92/100', status: 'Passed', url: 'https://hacken.io/audits/shipping-Finance' },
  { id: 'audit3', name: 'PeckShield Smart Contract Audit', date: '2023-01-10', auditor: 'PeckShield', score: '90/100', status: 'Passed', url: 'https://peckshield.com/audits/shipping-Finance' }
];

interface LiquidityPoolProps {
  showTransactions?: boolean;
  showAudits?: boolean;
}

const LiquidityPool: React.FC<LiquidityPoolProps> = ({
  showTransactions = true,
  showAudits = true
}) => {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPool, setSelectedPool] = useState(liquidityPoolsData[0]);
  const [activeTab, setActiveTab] = useState(0);
  
  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";
  
  // Asset colors
  const assetColors = {
    USDT: "#26A17B",
    BTC: "#F7931A",
    ETH: "#627EEA",
    Other: "#B7BDC6"
  };
  
  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };
  
  // Format large numbers
  const formatNumber = (num: number) => {
    return num.toLocaleString(undefined, { maximumFractionDigits: 2 });
  };
  
  return (
    <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
      <Flex align="center" mb={6}>
        <Icon as={FaWater} color={primaryColor} boxSize={5} mr={2} />
        <Heading size="md" color={textColor}>{t('liquidity.title', 'Liquidity Pools')}</Heading>
      </Flex>
      
      <VStack spacing={6} align="stretch">
        {/* Pool Selection Tabs */}
        <Tabs 
          variant="soft-rounded" 
          colorScheme="yellow" 
          onChange={(index) => {
            setActiveTab(index);
            setSelectedPool(liquidityPoolsData[index]);
          }}
        >
          <TabList mb={4} overflowX="auto" css={{ scrollbarWidth: 'none' }} whiteSpace="nowrap">
            {liquidityPoolsData.map((pool, index) => (
              <Tab 
                key={pool.id}
                color={activeTab === index ? primaryColor : secondaryTextColor}
                _selected={{ bg: 'rgba(240, 185, 11, 0.1)' }}
                mr={2}
              >
                {pool.name}
              </Tab>
            ))}
          </TabList>
          
          <TabPanels>
            {liquidityPoolsData.map((pool) => (
              <TabPanel key={pool.id} p={0}>
                {isLoading ? (
                  <VStack spacing={4} align="stretch">
                    <Skeleton height="100px" borderRadius="md" />
                    <Skeleton height="200px" borderRadius="md" />
                    <Skeleton height="150px" borderRadius="md" />
                  </VStack>
                ) : (
                  <>
                    {/* Pool Overview */}
                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} mb={6}>
                      <Stat bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                        <StatLabel color={secondaryTextColor}>{t('liquidity.totalValue', 'Total Value Locked')}</StatLabel>
                        <StatNumber color={textColor}>${formatNumber(pool.totalValue)}</StatNumber>
                        <StatHelpText color={pool.weeklyChange >= 0 ? "green.400" : "red.400"}>
                          <StatArrow type={pool.weeklyChange >= 0 ? "increase" : "decrease"} />
                          {pool.weeklyChange}% {t('liquidity.weeklyChange', 'this week')}
                        </StatHelpText>
                      </Stat>
                      
                      <Stat bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                        <StatLabel color={secondaryTextColor}>{t('liquidity.utilization', 'Utilization Rate')}</StatLabel>
                        <StatNumber color={textColor}>{pool.utilization}%</StatNumber>
                        <Box mt={2}>
                          <Progress 
                            value={pool.utilization} 
                            size="sm" 
                            colorScheme={pool.utilization > 90 ? "red" : pool.utilization > 75 ? "yellow" : "green"} 
                            borderRadius="full" 
                            bg="#2B3139"
                          />
                        </Box>
                      </Stat>
                      
                      <Stat bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                        <StatLabel color={secondaryTextColor}>{t('liquidity.dailyVolume', 'Daily Volume')}</StatLabel>
                        <StatNumber color={textColor}>${formatNumber(pool.dailyVolume)}</StatNumber>
                        <HStack mt={2}>
                          <Badge colorScheme="green" variant="subtle">
                            {t('liquidity.active', 'Active')}
                          </Badge>
                          {pool.audited && (
                            <Badge colorScheme="blue" variant="subtle">
                              {t('liquidity.audited', 'Audited')}
                            </Badge>
                          )}
                        </HStack>
                      </Stat>
                    </SimpleGrid>
                    
                    {/* Asset Allocation */}
                    <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor} mb={6}>
                      <Flex align="center" mb={4}>
                        <Icon as={FaChartPie} color={primaryColor} mr={2} />
                        <Text color={textColor} fontWeight="medium">{t('liquidity.assetAllocation', 'Asset Allocation')}</Text>
                      </Flex>
                      
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                        {/* Pie Chart Placeholder */}
                        <Box 
                          h="200px" 
                          display="flex" 
                          alignItems="center" 
                          justifyContent="center"
                          bg={bgColor}
                          borderRadius="md"
                          borderWidth="1px"
                          borderColor={borderColor}
                        >
                          <Text color={secondaryTextColor}>
                            {t('liquidity.chartPlaceholder', 'Asset allocation chart')}
                          </Text>
                        </Box>
                        
                        {/* Asset List */}
                        <VStack align="stretch" spacing={3}>
                          {pool.assets.map((asset) => (
                            <Box key={asset.symbol}>
                              <Flex justify="space-between" mb={1}>
                                <HStack>
                                  <Box w="12px" h="12px" borderRadius="sm" bg={assetColors[asset.symbol] || "#B7BDC6"} />
                                  <Text color={textColor}>{asset.symbol}</Text>
                                </HStack>
                                <Text color={textColor} fontWeight="medium">${formatNumber(asset.amount)}</Text>
                              </Flex>
                              <Flex justify="space-between">
                                <Progress 
                                  value={asset.percentage} 
                                  size="sm" 
                                  colorScheme="yellow"
                                  borderRadius="full" 
                                  bg="#2B3139"
                                  flex="1"
                                  mr={2}
                                />
                                <Text color={secondaryTextColor} fontSize="sm">{asset.percentage}%</Text>
                              </Flex>
                            </Box>
                          ))}
                        </VStack>
                      </SimpleGrid>
                    </Box>
                    
                    {/* Pool Details */}
                    <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor} mb={6}>
                      <Flex align="center" mb={4}>
                        <Icon as={FaFileContract} color={primaryColor} mr={2} />
                        <Text color={textColor} fontWeight="medium">{t('liquidity.poolDetails', 'Pool Details')}</Text>
                      </Flex>
                      
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                        <VStack align="start" spacing={3}>
                          <HStack>
                            <Text color={secondaryTextColor}>{t('liquidity.contractAddress', 'Contract Address')}:</Text>
                            <Text color={textColor} fontFamily="monospace">{pool.contractAddress.substring(0, 8)}...{pool.contractAddress.substring(pool.contractAddress.length - 6)}</Text>
                            <Link href={`https://etherscan.io/address/${pool.contractAddress}`} isExternal color={primaryColor}>
                              <ExternalLinkIcon mx="2px" />
                            </Link>
                          </HStack>
                          
                          <HStack>
                            <Text color={secondaryTextColor}>{t('liquidity.lastAudit', 'Last Audit')}:</Text>
                            <Text color={textColor}>{pool.lastAudit}</Text>
                          </HStack>
                          
                          <HStack>
                            <Text color={secondaryTextColor}>{t('liquidity.securityStatus', 'Security Status')}:</Text>
                            <Badge colorScheme="green">
                              <HStack spacing={1}>
                                <Icon as={FaCheckCircle} boxSize={3} />
                                <Text fontSize="xs">{t('liquidity.secure', 'Secure')}</Text>
                              </HStack>
                            </Badge>
                          </HStack>
                        </VStack>
                        
                        <VStack align="start" spacing={3}>
                          <HStack>
                            <Icon as={FaShieldAlt} color="green.400" boxSize={4} />
                            <Text color={textColor}>{t('liquidity.multiSig', 'Multi-signature security')}</Text>
                          </HStack>
                          
                          <HStack>
                            <Icon as={FaLock} color="green.400" boxSize={4} />
                            <Text color={textColor}>{t('liquidity.timelock', '24-hour timelock for major changes')}</Text>
                          </HStack>
                          
                          <HStack>
                            <Icon as={FaRegClock} color="green.400" boxSize={4} />
                            <Text color={textColor}>{t('liquidity.realtime', 'Real-time monitoring')}</Text>
                          </HStack>
                        </VStack>
                      </SimpleGrid>
                    </Box>
                    
                    {/* Recent Transactions */}
                    {showTransactions && (
                      <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor} mb={showAudits ? 6 : 0}>
                        <Flex align="center" mb={4}>
                          <Icon as={FaExchangeAlt} color={primaryColor} mr={2} />
                          <Text color={textColor} fontWeight="medium">{t('liquidity.recentTransactions', 'Recent Transactions')}</Text>
                        </Flex>
                        
                        <Box overflowX="auto">
                          <Table variant="simple" size="sm">
                            <Thead>
                              <Tr>
                                <Th color={secondaryTextColor} borderColor={borderColor}>{t('liquidity.type', 'Type')}</Th>
                                <Th color={secondaryTextColor} borderColor={borderColor}>{t('liquidity.amount', 'Amount')}</Th>
                                <Th color={secondaryTextColor} borderColor={borderColor}>{t('liquidity.asset', 'Asset')}</Th>
                                <Th color={secondaryTextColor} borderColor={borderColor}>{t('liquidity.pool', 'Pool')}</Th>
                                <Th color={secondaryTextColor} borderColor={borderColor}>{t('liquidity.time', 'Time')}</Th>
                                <Th color={secondaryTextColor} borderColor={borderColor}>{t('liquidity.status', 'Status')}</Th>
                              </Tr>
                            </Thead>
                            <Tbody>
                              {recentTransactionsData
                                .filter(tx => tx.pool === pool.name)
                                .map((tx) => (
                                <Tr key={tx.id}>
                                  <Td color={textColor} borderColor={borderColor}>{tx.type}</Td>
                                  <Td color={textColor} borderColor={borderColor}>
                                    {tx.type === 'Withdrawal' ? '-' : '+'}{tx.amount} {tx.asset}
                                  </Td>
                                  <Td color={textColor} borderColor={borderColor}>{tx.asset}</Td>
                                  <Td color={textColor} borderColor={borderColor}>{tx.pool}</Td>
                                  <Td color={textColor} borderColor={borderColor} fontSize="sm">
                                    {formatTimestamp(tx.timestamp)}
                                  </Td>
                                  <Td borderColor={borderColor}>
                                    <Badge 
                                      colorScheme={tx.status === 'completed' ? 'green' : tx.status === 'pending' ? 'yellow' : 'red'} 
                                      variant="subtle"
                                    >
                                      {tx.status}
                                    </Badge>
                                  </Td>
                                </Tr>
                              ))}
                            </Tbody>
                          </Table>
                        </Box>
                      </Box>
                    )}
                    
                    {/* Audit Reports */}
                    {showAudits && (
                      <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                        <Flex align="center" mb={4}>
                          <Icon as={FaRegFileAlt} color={primaryColor} mr={2} />
                          <Text color={textColor} fontWeight="medium">{t('liquidity.auditReports', 'Audit Reports')}</Text>
                        </Flex>
                        
                        <VStack spacing={3} align="stretch">
                          {auditReportsData.map((audit) => (
                            <Flex 
                              key={audit.id}
                              justify="space-between" 
                              align="center"
                              p={3}
                              bg={bgColor}
                              borderRadius="md"
                              borderWidth="1px"
                              borderColor={borderColor}
                            >
                              <HStack>
                                <Icon as={FaRegFileAlt} color="green.400" boxSize={5} />
                                <Box>
                                  <Text color={textColor} fontWeight="medium">{audit.name}</Text>
                                  <Text color={secondaryTextColor} fontSize="sm">
                                    {audit.date} • {audit.auditor}
                                  </Text>
                                </Box>
                              </HStack>
                              
                              <HStack>
                                <Badge colorScheme="green" variant="subtle">
                                  {audit.score}
                                </Badge>
                                <Link href={audit.url} isExternal>
                                  <Button size="sm" variant="outline" colorScheme="yellow" rightIcon={<ExternalLinkIcon />}>
                                    {t('liquidity.view', 'View')}
                                  </Button>
                                </Link>
                              </HStack>
                            </Flex>
                          ))}
                        </VStack>
                      </Box>
                    )}
                  </>
                )}
              </TabPanel>
            ))}
          </TabPanels>
        </Tabs>
      </VStack>
    </Box>
  );
};

export default LiquidityPool;
