import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/userModel';
import { logger } from '../utils/logger';
import { catchAsync } from '../utils/errorHandler';
import { AppError } from '../utils/AppError';
import { cacheService } from '../services/cacheService';
import { emailService } from '../services/emailService';

// Generate JWT with improved security
const generateToken = (id: string) => {
  const secret = process.env.JWT_SECRET || 'fallback_secret';
  const expiresIn = process.env.JWT_EXPIRES_IN || '30d';

  return jwt.sign({ id }, secret, {
    expiresIn,
    algorithm: 'HS512' // Daha gü<PERSON>l<PERSON> algoritma
  } as jwt.SignOptions);
};

// Validate email format
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate password strength
const isStrongPassword = (password: string): boolean => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  return password.length >= minLength &&
         hasUpperCase &&
         hasLowerCase &&
         hasNumbers &&
         hasSpecialChar;
};

// @desc    Register a new user
// @route   POST /api/users/register
// @access  Public
export const register = catchAsync(async (req: Request, res: Response) => {
  const { email, password, firstName, lastName, username, birthDate, phoneNumber, country, city, referralCode, marketingConsent } = req.body;

  console.log('🚀 BACKEND REGISTRATION: Received registration request');
  console.log('📧 Registration data:', {
    email,
    firstName,
    lastName,
    username,
    country,
    city,
    hasPassword: !!password,
    passwordLength: password?.length,
    hasReferralCode: !!referralCode,
    marketingConsent
  });

  // Basic validation (middleware handles detailed validation)
  const validationErrors: any = {};

  if (!email?.trim()) {
    validationErrors.email = 'Email is required';
  }
  if (!password?.trim()) {
    validationErrors.password = 'Password is required';
  }
  if (!firstName?.trim()) {
    validationErrors.firstName = 'First name is required';
  }
  if (!lastName?.trim()) {
    validationErrors.lastName = 'Last name is required';
  }

  if (Object.keys(validationErrors).length > 0) {
    console.log('❌ BACKEND REGISTRATION: Basic validation failed:', validationErrors);
    res.status(400).json({
      status: 'fail',
      message: 'Validation failed',
      errors: validationErrors
    });
    return;
  }

  // Check if user exists
  const userExists = await User.findOne({ email: email.toLowerCase() });
  if (userExists) {
    throw new AppError('Bu email adresi zaten kayıtlı', 400);
  }

  // Validate referral code if provided
  let referredBy = null;
  let referrerId = null;
  if (referralCode) {
    const referrer = await User.findOne({ referralCode });
    if (!referrer) {
      throw new AppError('Geçersiz referans kodu', 400);
    }
    referredBy = referralCode;
    referrerId = referrer._id;
  }

  let user: any;
  try {
    // Create user
    const userData: any = {
      email: email.toLowerCase(),
      password,
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      phoneNumber: phoneNumber?.trim(),
      country: country?.trim(),
      city: city?.trim(),
      referredBy,
      referrerId,
      level: 1,
      totalCommission: 0,
      marketingConsent: marketingConsent || false
    };

    // Add optional fields if provided
    if (username?.trim()) {
      userData.username = username.trim();
    }
    if (birthDate) {
      userData.birthDate = new Date(birthDate);
    }

    console.log('📤 BACKEND REGISTRATION: Creating user with data:', {
      ...userData,
      password: '[HIDDEN]'
    });

    user = await User.create(userData);
    console.log('✅ BACKEND REGISTRATION: User created successfully:', user._id);
  } catch (error: any) {
    logger.error('User creation error:', error);
    throw new AppError(
      error.code === 11000
        ? 'Bu email adresi zaten kayıtlı'
        : 'Kayıt işlemi başarısız oldu. Lütfen tekrar deneyin.',
      400
    );
  }

  // Update referrer stats
  if (referredBy) {
    try {
      await User.findOneAndUpdate(
        { referralCode: referredBy },
        { $inc: { referralCount: 1, referralEarnings: 0 } }
      );
      logger.info(`User ${user._id} registered with referral code ${referredBy}`);
    } catch (error) {
      logger.error('Error updating referrer stats:', error);
      // Continue with registration even if referrer update fails
    }
  }

  // Generate email verification token and send verification email
  try {
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // Send verification email (don't block registration if email fails)
    emailService.sendVerificationEmail(user, verificationToken).catch(error => {
      logger.error('Failed to send verification email during registration', {
        userId: user._id,
        email: user.email,
        error: error.message
      });
    });

    logger.info('Email verification token generated and email sent', {
      userId: user._id,
      email: user.email
    });
  } catch (error) {
    logger.error('Error generating email verification token', {
      userId: user._id,
      email: user.email,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    // Continue with registration even if email verification setup fails
  }

  // Generate JWT token
  const token = generateToken(user._id?.toString() || '');

  // Set token in HTTP-only cookie
  res.cookie('token', token, {
    httpOnly: true, // Prevents JavaScript access
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production for cross-site requests
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    path: '/' // Available across the entire site
  });

  console.log('📥 BACKEND REGISTRATION: Sending success response');

  res.status(201).json({
    status: 'success',
    message: 'Registration successful. Please check your email to verify your account.',
    data: {
      _id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      username: user.username,
      birthDate: user.birthDate,
      phoneNumber: user.phoneNumber,
      country: user.country,
      city: user.city,
      referralCode: user.referralCode,
      marketingConsent: user.marketingConsent,
      emailVerified: user.emailVerified
      // Token is now sent in cookie, not in response body
    }
  });
});

// @desc    Auth user & get token
// @route   POST /api/users/login
// @access  Public
export const login = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // Input validation
    if (!email || !password) {
      return res.status(400).json({
        status: 'fail',
        message: 'Please fill in all fields',
        errors: {
          email: !email ? 'Email field is required' : undefined,
          password: !password ? 'Password field is required' : undefined
        }
      });
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return res.status(400).json({
        status: 'fail',
        message: 'Invalid email format',
        errors: {
          email: 'Please enter a valid email address'
        }
      });
    }

    // Check for user email
    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      // Log failed login attempt - user not found
      logger.warn(`Failed login attempt - user not found for email: ${email}`);

      return res.status(401).json({
        status: 'fail',
        message: 'Invalid email or password',
        error: 'INVALID_CREDENTIALS',
        details: 'User not found'
      });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      // Log failed login attempt - wrong password
      logger.warn(`Failed login attempt - wrong password for email: ${email}`);

      return res.status(401).json({
        status: 'fail',
        message: 'Invalid email or password',
        error: 'INVALID_CREDENTIALS',
        details: 'Password incorrect'
      });
    }

    // Additional security checks can be added here in the future
    // For example: account locking, email verification, etc.

    // Log successful login
    logger.info(`User ${user._id} logged in successfully`);

    // Update last login timestamp
    try {
      user.lastLogin = new Date();
      await user.save({ validateBeforeSave: false });
    } catch (saveError) {
      logger.error('Error updating last login timestamp:', saveError);
      // Continue with login even if timestamp update fails
    }

    // Generate token
    const token = generateToken(user._id?.toString() || '');

    // Set token in HTTP-only cookie
    res.cookie('token', token, {
      httpOnly: true, // Prevents JavaScript access
      secure: process.env.NODE_ENV === 'production', // HTTPS only in production
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production for cross-site requests
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      path: '/' // Available across the entire site
    });

    // Set admin cookie if user is admin
    if (user.isAdmin) {
      res.cookie('adminToken', 'true', {
        httpOnly: false, // Allow JavaScript access for UI state
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production for cross-site requests
        maxAge: 30 * 24 * 60 * 60 * 1000,
        path: '/'
      });
      logger.info(`Admin cookie set for user ${user._id}`);
    }

    return res.status(200).json({
      status: 'success',
      message: 'Login successful',
      data: {
        _id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phoneNumber: user.phoneNumber,
        country: user.country,
        city: user.city,
        walletAddress: user.walletAddress,
        kycVerified: user.kycVerified,
        twoFactorEnabled: user.twoFactorEnabled,
        referralCode: user.referralCode,
        referralCount: user.referralCount,
        referralEarnings: user.referralEarnings,
        marketingConsent: user.marketingConsent,
        isAdmin: user.isAdmin,
        lastLogin: user.lastLogin,
        emailVerified: user.emailVerified
        // Token is now sent in cookie, not in response body
      }
    });

  } catch (error: any) {
    // Log the error for debugging
    logger.error('Login error:', {
      message: error.message,
      stack: error.stack,
      email: req.body?.email
    });

    // Handle different types of errors
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        status: 'fail',
        message: 'Validation error',
        error: 'VALIDATION_ERROR',
        details: error.message
      });
    }

    if (error.name === 'MongoError' || error.name === 'MongooseError') {
      return res.status(500).json({
        status: 'error',
        message: 'Database error',
        error: 'DATABASE_ERROR',
        details: 'Please try again later'
      });
    }

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return res.status(503).json({
        status: 'error',
        message: 'Service unavailable',
        error: 'SERVICE_UNAVAILABLE',
        details: 'Please try again later'
      });
    }

    // Generic server error
    return res.status(500).json({
      status: 'error',
      message: 'Internal server error',
      error: 'INTERNAL_SERVER_ERROR',
      details: 'An unexpected error occurred. Please try again later.'
    });
  }
};

// @desc    Logout user
// @route   POST /api/users/logout
// @access  Public
export const logout = catchAsync(async (_req: Request, res: Response) => {
  // Clear the token cookie
  res.clearCookie('token', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    path: '/'
  });

  // Clear the admin token cookie if it exists
  res.clearCookie('adminToken', {
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    path: '/'
  });

  // Clear the ws_auth cookie if it exists
  res.clearCookie('ws_auth', {
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    path: '/'
  });

  res.status(200).json({
    status: 'success',
    message: 'Başarıyla çıkış yapıldı'
  });
});

// @desc    Refresh JWT token
// @route   POST /api/users/refresh-token
// @access  Public (but requires valid token in cookie)
export const refreshToken = catchAsync(async (req: Request, res: Response) => {
  // Get token from cookies
  const token = req.cookies?.token;

  if (!token) {
    return res.status(401).json({
      status: 'fail',
      message: 'No token provided for refresh',
      error: 'NO_TOKEN'
    });
  }

  try {
    // Verify the existing token
    const decoded: any = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret');

    // Get user from the token
    const user = await User.findById(decoded.id).select('-password');

    if (!user) {
      // Clear invalid cookie
      res.clearCookie('token');
      return res.status(401).json({
        status: 'fail',
        message: 'User not found for token refresh',
        error: 'USER_NOT_FOUND'
      });
    }

    // Generate new token
    const newToken = generateToken(user._id?.toString() || '');

    // Set new token in HTTP-only cookie
    res.cookie('token', newToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      path: '/'
    });

    // Update admin cookie if user is admin
    if (user.isAdmin) {
      res.cookie('adminToken', 'true', {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production for cross-site requests
        maxAge: 30 * 24 * 60 * 60 * 1000,
        path: '/'
      });
    }

    logger.info(`Token refreshed for user ${user._id}`);

    res.status(200).json({
      status: 'success',
      message: 'Token refreshed successfully',
      data: {
        _id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        isAdmin: user.isAdmin,
        kycVerified: user.kycVerified,
        twoFactorEnabled: user.twoFactorEnabled,
        referralCode: user.referralCode,
        emailVerified: user.emailVerified
        // New token is sent in cookie, not in response body
      }
    });

  } catch (error: any) {
    logger.error('Token refresh error:', error);

    // Clear invalid cookies
    res.clearCookie('token');
    res.clearCookie('adminToken');

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        status: 'fail',
        message: 'Token has expired',
        error: 'TOKEN_EXPIRED'
      });
    }

    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        status: 'fail',
        message: 'Invalid token',
        error: 'INVALID_TOKEN'
      });
    }

    return res.status(500).json({
      status: 'error',
      message: 'Token refresh failed',
      error: 'REFRESH_FAILED'
    });
  }
});

// @desc    Get user profile
// @route   GET /api/users/profile
// @access  Private
export const getProfile = catchAsync(async (req: Request, res: Response) => {
  const cacheKey = `user:profile:${req.user._id}`;

  // Try to get from cache first
  const cachedProfile = cacheService.get(cacheKey);
  if (cachedProfile) {
    res.json(cachedProfile);
    return;
  }

  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  const userProfile = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    walletAddress: user.walletAddress,
    kycVerified: user.kycVerified,
    twoFactorEnabled: user.twoFactorEnabled,
    referralCode: user.referralCode,
    referralCount: user.referralCount,
    referralEarnings: user.referralEarnings,
    emailVerified: user.emailVerified,
  };

  // Cache the profile for 5 minutes
  cacheService.set(cacheKey, userProfile, 300);

  res.json(userProfile);
});

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
export const updateProfile = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // Validate email if it's being updated
  if (req.body.email && req.body.email !== user.email) {
    if (!isValidEmail(req.body.email)) {
      throw new AppError('Geçerli bir email adresi giriniz', 400);
    }

    // Check if email is already in use
    const existingUser = await User.findOne({ email: req.body.email.toLowerCase() });
    if (existingUser && existingUser._id?.toString() !== user._id?.toString()) {
      throw new AppError('Bu email adresi zaten kullanımda', 400);
    }
  }

  // Validate password if it's being updated
  if (req.body.password && !isStrongPassword(req.body.password)) {
    throw new AppError('Şifre en az 8 karakter uzunluğunda olmalı ve büyük harf, küçük harf, rakam ve özel karakter içermelidir', 400);
  }

  // Update user fields
  user.firstName = req.body.firstName || user.firstName;
  user.lastName = req.body.lastName || user.lastName;
  user.email = req.body.email ? req.body.email.toLowerCase() : user.email;
  user.walletAddress = req.body.walletAddress || user.walletAddress;

  if (req.body.password) {
    user.password = req.body.password;
  }

  const updatedUser = await user.save();

  // Clear user profile cache
  cacheService.delete(`user:profile:${user._id}`);

  // Generate new token
  const token = generateToken(updatedUser._id?.toString() || '');

  // Update token in HTTP-only cookie
  res.cookie('token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    maxAge: 30 * 24 * 60 * 60 * 1000,
    path: '/'
  });

  res.json({
    _id: updatedUser._id,
    firstName: updatedUser.firstName,
    lastName: updatedUser.lastName,
    email: updatedUser.email,
    walletAddress: updatedUser.walletAddress,
    kycVerified: updatedUser.kycVerified,
    twoFactorEnabled: updatedUser.twoFactorEnabled,
    referralCode: updatedUser.referralCode,
    referralCount: updatedUser.referralCount,
    referralEarnings: updatedUser.referralEarnings
    // Token is now sent in cookie, not in response body
  });
});

// @desc    Enable two-factor authentication
// @route   POST /api/users/enable-2fa
// @access  Private
export const enableTwoFactor = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // In a real implementation, this would generate a 2FA secret and QR code
  // For demo purposes, we'll just toggle the 2FA status
  user.twoFactorEnabled = !user.twoFactorEnabled;
  await user.save();

  // Clear user profile cache
  cacheService.delete(`user:profile:${user._id}`);

  res.json({
    message: user.twoFactorEnabled
      ? 'İki faktörlü doğrulama etkinleştirildi'
      : 'İki faktörlü doğrulama devre dışı bırakıldı',
    twoFactorEnabled: user.twoFactorEnabled
  });
});

// @desc    Verify KYC documents
// @route   POST /api/users/verify-kyc
// @access  Private
export const verifyKyc = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // In a real implementation, this would process KYC documents
  // For demo purposes, we'll just toggle the KYC status
  user.kycVerified = !user.kycVerified;
  await user.save();

  // Clear user profile cache
  cacheService.delete(`user:profile:${user._id}`);

  res.json({
    message: user.kycVerified
      ? 'KYC doğrulaması başarılı'
      : 'KYC doğrulaması iptal edildi',
    kycVerified: user.kycVerified
  });
});

// @desc    Get referral statistics
// @route   GET /api/users/referrals
// @access  Private
export const getReferrals = catchAsync(async (req: Request, res: Response) => {
  const cacheKey = `user:referrals:${req.user._id}`;

  // Try to get from cache first
  const cachedReferrals = cacheService.get(cacheKey);
  if (cachedReferrals) {
    res.json(cachedReferrals);
    return;
  }

  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // Find users referred by this user
  const referredUsers = await User.find({ referredBy: user.referralCode })
    .select('firstName lastName email createdAt')
    .sort({ createdAt: -1 });

  const referralStats = {
    referralCode: user.referralCode,
    referralLink: `${process.env.FRONTEND_URL || 'https://shippingFinance.com'}/register?ref=${user.referralCode}`,
    referralCount: user.referralCount,
    referralEarnings: user.referralEarnings,
    referralRate: 3, // 3% referral rate
    referredUsers: referredUsers.map((u: any) => ({
      name: `${u.firstName} ${u.lastName}`,
      email: u.email,
      joinedAt: u.createdAt
    }))
  };

  // Cache the referrals for 5 minutes
  cacheService.set(cacheKey, referralStats, 300);

  res.json(referralStats);
});