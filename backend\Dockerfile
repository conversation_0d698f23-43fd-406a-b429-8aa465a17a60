FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build TypeScript code
RUN npm run build

# Production stage
FROM node:20-alpine AS production

WORKDIR /app

# Copy package files and install production dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy built files from builder stage
COPY --from=builder /app/dist ./dist

# Copy other necessary files
COPY .env.example .env

# Set environment variables
ENV NODE_ENV=production
ENV FRONTEND_URL=https://shpnfinance.com
ENV PORT=5000
ENV EMAIL_HOST=mail.shpnfinance.com
ENV EMAIL_PORT=587
ENV EMAIL_SECURE=false
ENV EMAIL_USER=<EMAIL>
ENV EMAIL_PASS=ThisIsPass@123
ENV EMAIL_FROM=<EMAIL>
# Expose port
EXPOSE 5000

# Start the application
CMD ["node", "dist/index.js"]