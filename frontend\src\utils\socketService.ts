import { io, Socket } from 'socket.io-client';
import { logger } from './logger';
import { errorTracking } from './errorTracking';

interface SocketConfig {
  url: string;
  reconnectAttempts: number;
  reconnectInterval: number;
  heartbeatInterval: number;
}

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho tin nhắn Socket.IO
interface SocketMessage {
  type: string;
  payload?: Record<string, unknown>;
}

// Định nghĩa kiểu dữ liệu cho các handler
type MessageHandler = (data: Record<string, unknown>) => void;
type StatusHandler = (status: boolean) => void;

class SocketService {
  private static instance: SocketService;
  private socket: Socket | null = null;
  private reconnectCount = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private messageHandlers: Map<string, Set<MessageHandler>> = new Map();
  private statusHandlers: Set<StatusHandler> = new Set();
  private messageQueue: SocketMessage[] = [];
  private isConnected = false;
  private readonly config: SocketConfig;
  private tokenRefreshTimer: NodeJS.Timeout | null = null;
  private readonly apiUrl: string;
  private sessionId: string | null = null;
  private userId: string | null = null;

  private constructor() {
    // Lấy URL Socket.IO từ biến môi trường
    const wsHost = import.meta.env.VITE_WEBSOCKET_URL || '/ws';

    // Nếu URL bắt đầu bằng /, đó là đường dẫn tương đối
    // Nếu URL bắt đầu bằng 'ws:' hoặc 'wss:', sử dụng nó như là
    const wsUrl = wsHost.startsWith('/')
      ? `${window.location.protocol}//${window.location.host}${wsHost}` // Đường dẫn tương đối
      : wsHost.startsWith('ws:') || wsHost.startsWith('wss:')
        ? wsHost // URL WebSocket tuyệt đối
        : `${window.location.protocol}//${wsHost}`; // Chỉ hostname

    // Lấy API URL từ biến môi trường
    this.apiUrl = import.meta.env.VITE_API_URL || '';

    // Nếu API URL không được cung cấp, sử dụng host hiện tại
    if (!this.apiUrl) {
      this.apiUrl = `${window.location.protocol}//${window.location.host}/api`;
    }

    this.config = {
      url: wsUrl,
      reconnectAttempts: 5,
      reconnectInterval: 3000,
      heartbeatInterval: 30000
    };

    logger.info(`Socket.IO service initialized with URL: ${wsUrl}`);
    logger.info(`API URL for Socket.IO authentication: ${this.apiUrl}`);

    // Log biến môi trường để debug
    logger.debug('Socket.IO environment variables:', {
      VITE_WEBSOCKET_URL: import.meta.env.VITE_WEBSOCKET_URL,
      VITE_API_URL: import.meta.env.VITE_API_URL,
      protocol: window.location.protocol,
      host: window.location.host,
      wsHost: wsHost,
      wsUrl: wsUrl,
      isRelative: wsHost.startsWith('/'),
      isAbsolute: wsHost.startsWith('ws:') || wsHost.startsWith('wss:')
    });

    // Log ra console để dễ debug
    console.log('Socket.IO configuration:', {
      url: wsUrl,
      apiUrl: this.apiUrl,
      env: import.meta.env.VITE_WEBSOCKET_URL,
      mode: import.meta.env.MODE
    });
  }

  public static getInstance(): SocketService {
    if (!SocketService.instance) {
      SocketService.instance = new SocketService();
    }
    return SocketService.instance;
  }

  /**
   * Xác thực Socket.IO với server
   * Phương thức này sẽ gọi API để thiết lập cookie xác thực cho Socket.IO
   * @returns Promise với kết quả xác thực (true nếu thành công, false nếu thất bại)
   */
  private async authenticateSocketIO(): Promise<boolean> {
    try {
      logger.info('Authenticating Socket.IO connection');
      console.log(`Requesting Socket.IO authentication from: ${this.apiUrl}/users/ws-token`);

      // Kiểm tra nếu cookies có sẵn
      const cookies = document.cookie;
      console.log('Available cookies:', cookies ? 'Yes' : 'No');

      // Gửi yêu cầu đến server để xác thực Socket.IO sử dụng fetch
      // Đảm bảo không thêm /api vào đường dẫn nếu API_URL đã có /api
      const wsTokenUrl = this.apiUrl.endsWith('/api')
        ? `${this.apiUrl}/users/ws-token`
        : `${this.apiUrl}/api/users/ws-token`;

      console.log('WS Token URL:', wsTokenUrl);

      const response = await fetch(wsTokenUrl, {
        method: 'GET',
        credentials: 'include', // Tương đương với withCredentials: true trong axios
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Accept': 'application/json'
        }
      });

      // Kiểm tra nếu response không thành công
      if (!response.ok) {
        try {
          // Thử đọc nội dung lỗi
          const errorText = await response.text();
          logger.error('Socket.IO authentication failed with status:', {
            status: response.status,
            error: errorText
          });
          console.error('Socket.IO authentication failed with status:', response.status, errorText);
        } catch (readError) {
          logger.error('Socket.IO authentication failed with status:', {
            status: response.status,
            readError: readError instanceof Error ? readError.message : 'Unknown error'
          });
          console.error('Socket.IO authentication failed with status:', response.status);
        }
        return false;
      }

      // Parse JSON response
      const data = await response.json();

      console.log('Socket.IO authentication response:', {
        status: response.status,
        hasData: !!data,
        success: data?.status === 'success',
        userId: data?.userId
      });

      if (data && data.status === 'success') {
        logger.info('Socket.IO authentication successful');

        // Lưu sessionId từ response
        if (data.sessionId) {
          this.sessionId = data.sessionId;
          console.log('Socket.IO session ID received:', data.sessionId);
        }

        // Kiểm tra xem cookie ws_auth đã được thiết lập chưa
        const hasCookie = document.cookie.split(';').some(item => item.trim().startsWith('ws_auth='));

        if (hasCookie) {
          logger.info('Socket.IO authentication cookie found');
          console.log('Socket.IO authentication cookie found');

          // Lưu thông tin người dùng nếu có
          if (data.userId) {
            console.log('Socket.IO authenticated for user:', data.userId);
            this.userId = data.userId;
          }

          return true;
        } else {
          logger.warn('Socket.IO authentication cookie not found despite successful response');
          console.warn('Socket.IO authentication cookie not found despite successful response');
          return false;
        }
      } else {
        logger.error('Socket.IO authentication failed', data);
        console.error('Socket.IO authentication failed:', data);
        return false;
      }
    } catch (error) {
      // Xử lý lỗi với fetch
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      logger.error('Failed to authenticate Socket.IO:', {
        error: errorMessage,
        status: 'unknown'
      });

      console.error('Failed to authenticate Socket.IO:', {
        error: errorMessage,
        status: 'unknown',
        data: null
      });

      return false;
    }
  }

  /**
   * Check if user is authenticated by looking for auth cookies
   */
  private checkUserAuthentication(): boolean {
    try {
      // Check for authentication cookies
      const cookies = document.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = value;
        return acc;
      }, {} as Record<string, string>);

      // Check for token or ws_auth cookie
      const hasToken = cookies['token'] && cookies['token'] !== 'undefined';
      const hasWsAuth = cookies['ws_auth'] && cookies['ws_auth'] !== 'undefined';

      return hasToken || hasWsAuth;
    } catch (error) {
      console.warn('Error checking user authentication:', error);
      return false;
    }
  }

  /**
   * Kết nối đến Socket.IO server sử dụng cookie cho xác thực
   * @param mode Chế độ xác thực, mặc định là 'cookie'
   */
  public async connect(mode: string = 'cookie'): Promise<void> {
    try {
      // Check if user is authenticated before attempting WebSocket connection
      const isAuthenticated = this.checkUserAuthentication();
      if (!isAuthenticated) {
        logger.info('User not authenticated, skipping Socket.IO connection');
        console.log('User not authenticated, skipping Socket.IO connection');
        return;
      }

      // Kiểm tra xem đã có kết nối hoạt động chưa
      if (this.socket && this.socket.connected && this.isConnected) {
        logger.info('Socket.IO already connected, skipping reconnection');
        console.log('Socket.IO already connected, skipping reconnection', {
          socketId: this.socket.id,
          sessionId: this.sessionId
        });
        return;
      }

      // Xác thực Socket.IO trước khi kết nối
      if (mode === 'cookie') {
        logger.info('Using cookie-based authentication for Socket.IO');
        console.log('Attempting to authenticate Socket.IO with cookies');

        // Thử tối đa 3 lần để xác thực
        let retryCount = 0;
        const maxRetries = 3;
        let authenticated = false;

        while (retryCount < maxRetries && !authenticated) {
          authenticated = await this.authenticateSocketIO();

          if (authenticated) {
            logger.info('Socket.IO authentication successful');
            console.log('Successfully authenticated Socket.IO on attempt', retryCount + 1);
            break;
          }

          retryCount++;
          if (retryCount < maxRetries) {
            logger.warn(`Failed to authenticate Socket.IO, retrying (${retryCount}/${maxRetries})...`);
            console.warn(`Failed to authenticate Socket.IO, retrying (${retryCount}/${maxRetries})...`);
            // Đợi một chút trước khi thử lại
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        if (!authenticated) {
          logger.error(`Failed to authenticate Socket.IO after ${maxRetries} attempts, aborting connection`);
          console.error(`Failed to authenticate Socket.IO after ${maxRetries} attempts, aborting connection`);
          return;
        }
      } else if (mode === 'dev') {
        // Chế độ phát triển, không cần xác thực
        logger.info('Using development mode for Socket.IO connection');
        console.log('Development mode: skipping authentication');
      } else {
        logger.warn(`Unknown Socket.IO authentication mode: ${mode}`);
        return;
      }

      // Dọn dẹp kết nối hiện tại
      if (this.socket) {
        logger.debug('Cleaning up existing Socket.IO connection before reconnecting');
        this.cleanup();
      }

      // Tạo URL Socket.IO
      const socketUrl = this.config.url;
      logger.debug(`Connecting to Socket.IO URL: ${socketUrl}`);

      // Log ra console để dễ debug
      console.log(`Attempting Socket.IO connection to: ${socketUrl}`);

      try {
        // Tạo kết nối Socket.IO với các tùy chọn được tối ưu hóa
        this.socket = io(socketUrl, {
          path: '/ws', // Đường dẫn Socket.IO endpoint
          transports: ['polling', 'websocket'], // Bắt đầu với polling, sau đó upgrade lên websocket
          upgrade: true, // Cho phép upgrade từ polling lên websocket
          rememberUpgrade: true, // Nhớ upgrade cho các kết nối tiếp theo
          reconnection: true, // Cho phép Socket.IO tự động kết nối lại
          reconnectionAttempts: 5, // Giảm số lần thử kết nối lại
          reconnectionDelay: 2000, // Tăng thời gian chờ giữa các lần kết nối lại
          reconnectionDelayMax: 10000, // Tăng thời gian chờ tối đa
          timeout: 45000, // Tăng thời gian timeout kết nối
          autoConnect: true,
          withCredentials: true, // Quan trọng: cho phép gửi cookies
          auth: {
            sessionId: this.sessionId || `session_${Date.now()}`,
            userId: this.userId
          }, // Thêm thông tin xác thực
          forceNew: false, // Không tạo kết nối mới nếu đã có
          multiplex: true, // Cho phép sử dụng lại kết nối
          extraHeaders: {
            'X-Client-Version': import.meta.env.VITE_APP_VERSION || '1.0.0',
            'X-Client-Platform': 'web'
          },
          // Thêm các tùy chọn để tránh lỗi 426
          closeOnBeforeunload: false, // Không đóng kết nối khi trang sắp tải lại
          timestampRequests: true, // Thêm timestamp vào requests
          timestampParam: 't', // Tham số timestamp
        });
        console.log('Socket.IO instance created successfully', {
          id: this.socket.id,
          sessionId: this.sessionId
        });
      } catch (socketError) {
        console.error('Error creating Socket.IO instance:', socketError);
        throw socketError;
      }

      this.setupEventHandlers();

      // Thêm timeout kết nối
      const connectionTimeout = setTimeout(() => {
        if (this.socket && !this.socket.connected) {
          logger.error('Socket.IO connection timeout');
          this.handleConnectionError(new Error('Connection timeout'));
        }
      }, 10000); // 10 giây timeout

      // Xóa timeout khi kết nối được thiết lập
      this.socket.on('connect', () => {
        clearTimeout(connectionTimeout);
        this.handleOpen();
      });

      logger.info('Socket.IO connection initiated');
    } catch (error) {
      logger.error('Socket.IO connection error:', { error: error instanceof Error ? error.message : 'Unknown error' });
      this.handleConnectionError(error);
    }
  }

  private handleOpen(): void {
    this.isConnected = true;
    this.reconnectCount = 0;
    logger.info('Socket.IO connected successfully');
    console.log('Socket.IO connection established successfully', {
      url: this.config.url,
      connected: this.socket?.connected,
      id: this.socket?.id,
      sessionId: this.sessionId,
      userId: this.userId
    });

    // Gửi thông tin sessionId để server có thể xác định kết nối
    if (this.socket && this.sessionId) {
      this.socket.emit('register_session', {
        sessionId: this.sessionId,
        userId: this.userId
      });
    }

    this.notifyStatusHandlers(true);
    this.startHeartbeat();
    this.processMessageQueue();
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Xử lý sự kiện ngắt kết nối
    this.socket.on('disconnect', (reason) => {
      this.isConnected = false;
      logger.warn(`Socket.IO closed: Reason: ${reason || 'No reason provided'}`);
      console.warn('Socket.IO connection closed', {
        reason: reason || 'No reason provided',
        url: this.config.url,
        socketId: this.socket?.id,
        sessionId: this.sessionId
      });

      // Phân loại lý do ngắt kết nối
      let disconnectType = 'normal';
      let shouldShowNotification = true;

      // Phân tích lý do ngắt kết nối để xác định loại và hành động
      if (reason === 'io server disconnect') {
        // Ngắt kết nối từ server - không cần kết nối lại tự động
        disconnectType = 'server';
        shouldShowNotification = true;
      } else if (reason === 'io client disconnect') {
        // Ngắt kết nối từ client - không cần kết nối lại
        disconnectType = 'client';
        shouldShowNotification = false;
      } else if (reason === 'transport close' || reason === 'transport error' || reason === 'ping timeout') {
        // Lỗi mạng - cần kết nối lại
        disconnectType = 'network';
        shouldShowNotification = true;
      }

      console.log('Socket.IO disconnect analysis:', {
        reason,
        disconnectType,
        shouldShowNotification
      });

      // Thông báo cho các status handler
      this.notifyStatusHandlers(false);

      // Xử lý kết nối lại nếu cần
      if (disconnectType === 'network' || disconnectType === 'normal') {
        this.handleReconnect();
      }
    });

    // Xử lý sự kiện lỗi kết nối
    this.socket.on('connect_error', (error) => {
      logger.error('Socket.IO error:', error);
      console.error('Socket.IO connection error', {
        error: error.message,
        url: this.config.url,
        connected: this.socket?.connected,
        socketId: this.socket?.id,
        sessionId: this.sessionId
      });
      this.handleConnectionError(error);
    });

    // Xử lý sự kiện kết nối lại
    this.socket.on('reconnect', (attemptNumber) => {
      logger.info(`Socket.IO reconnected after ${attemptNumber} attempts`);
      console.log(`Socket.IO reconnected after ${attemptNumber} attempts`, {
        socketId: this.socket?.id,
        sessionId: this.sessionId
      });
    });

    // Xử lý sự kiện đang cố gắng kết nối lại
    this.socket.on('reconnect_attempt', (attemptNumber) => {
      logger.info(`Socket.IO reconnect attempt ${attemptNumber}`);
      console.log(`Socket.IO reconnect attempt ${attemptNumber}`, {
        sessionId: this.sessionId
      });
    });

    // Xử lý sự kiện lỗi kết nối lại
    this.socket.on('reconnect_error', (error) => {
      logger.error('Socket.IO reconnect error:', error);
      console.error('Socket.IO reconnect error', {
        error: error.message,
        sessionId: this.sessionId
      });
    });

    // Xử lý sự kiện kết nối lại thất bại
    this.socket.on('reconnect_failed', () => {
      logger.error('Socket.IO reconnect failed after all attempts');
      console.error('Socket.IO reconnect failed after all attempts', {
        sessionId: this.sessionId
      });
    });

    // Xử lý tin nhắn từ server
    this.socket.on('message', (data: SocketMessage) => {
      try {
        // Log tin nhắn thô để debug
        logger.debug(`Raw Socket.IO message received: ${JSON.stringify(data).substring(0, 200)}${JSON.stringify(data).length > 200 ? '...' : ''}`);

        if (data.type === 'heartbeat') {
          this.handleHeartbeat();
          return;
        }

        // Log loại tin nhắn đã phân tích
        logger.debug(`Parsed Socket.IO message type: ${data.type}`);

        this.handleMessage(data);
      } catch (error) {
        logger.error('Socket.IO message parsing error:', {
          error: error instanceof Error ? error.message : 'Unknown error',
          dataType: typeof data
        });
      }
    });
  }

  public handleMessage(data: SocketMessage): void {
    try {
      const { type, payload } = data;

      // Log loại tin nhắn nhận được để debug
      logger.info(`Received Socket.IO message: ${type}`, {
        messageType: type,
        payloadId: payload?.id || payload?._id,
        status: payload?.status
      });

      // Xử lý đặc biệt cho các loại tin nhắn cụ thể
      switch (type) {
        case 'error':
          logger.error('Socket.IO error message:', payload || {});
          console.error('Socket.IO server error:', payload || {});

          // Kiểm tra lỗi xác thực
          if (payload && typeof payload === 'object' && 'message' in payload) {
            const errorMessage = String(payload.message);
            if (
              errorMessage.includes('Invalid or expired authorization') ||
              errorMessage.includes('Authentication required') ||
              errorMessage.includes('Not authenticated')
            ) {
              console.warn('Socket.IO authentication error, attempting to refresh authentication...');

              // Thử làm mới xác thực
              this.refreshAuthentication().catch((refreshError: Error) => {
                console.error('Failed to refresh authentication after error:', refreshError);
              });
            }
          }
          break;
        case 'connection_established':
          logger.info('Socket.IO connection established:', payload || {});
          break;
        case 'profile_data':
          logger.info('Received user profile data');
          break;
        case 'transactions_data':
          if (payload && typeof payload === 'object' && 'transactions' in payload) {
            const transactions = Array.isArray(payload.transactions) ? payload.transactions : [];
            logger.info(`Received ${transactions.length} transactions`);
          } else {
            logger.info('Received transactions data (empty or invalid format)');
          }
          break;
        case 'investments_data':
          if (payload && typeof payload === 'object' && 'investments' in payload) {
            const investments = Array.isArray(payload.investments) ? payload.investments : [];
            logger.info(`Received ${investments.length} investments`);
          } else {
            logger.info('Received investments data (empty or invalid format)');
          }
          break;
        case 'wallet_balance':
          logger.info('Received wallet balance data');
          break;
        case 'deposit_initiated':
        case 'withdrawal_initiated':
          logger.info(`${type} successfully`);
          break;
        case 'deposit_status_updated':
        case 'withdrawal_status_updated':
          if (payload && typeof payload === 'object' && 'transaction' in payload) {
            const transaction = payload.transaction as Record<string, unknown>;
            logger.info(`Transaction status updated: ${transaction.status || 'unknown'}`);
          } else {
            logger.info(`${type} received (no status details)`);
          }
          break;
        case 'new_deposit':
          if (payload && typeof payload === 'object') {
            logger.info('Received new deposit notification:', {
              id: 'id' in payload ? payload.id : ('_id' in payload ? payload._id : 'unknown'),
              user: 'user' in payload ? payload.user : 'unknown',
              amount: 'amount' in payload ? payload.amount : 0,
              currency: 'currency' in payload ? payload.currency : ('asset' in payload ? payload.asset : 'unknown'),
              status: 'status' in payload ? payload.status : 'unknown'
            });
          } else {
            logger.info('Received new deposit notification (no details)');
          }
          break;
        case 'deposit_updated':
          if (payload && typeof payload === 'object') {
            logger.info('Received deposit update notification:', {
              id: 'id' in payload ? payload.id : ('_id' in payload ? payload._id : 'unknown'),
              user: 'user' in payload ? payload.user : 'unknown',
              amount: 'amount' in payload ? payload.amount : 0,
              currency: 'currency' in payload ? payload.currency : ('asset' in payload ? payload.asset : 'unknown'),
              status: 'status' in payload ? payload.status : 'unknown',
              previousStatus: 'previousStatus' in payload ? payload.previousStatus : 'unknown'
            });
          } else {
            logger.info('Received deposit update notification (no details)');
          }
          break;
        case 'admin_subscription_success':
          logger.info('Successfully subscribed to admin updates');
          break;
        default:
          logger.debug(`Unhandled message type: ${type}`);
          break;
      }

      // Thông báo cho tất cả các handler đã đăng ký cho loại tin nhắn này
      const handlers = this.messageHandlers.get(type);
      if (handlers && handlers.size > 0) {
        logger.info(`Found ${handlers.size} handlers for message type: ${type}`);
        handlers.forEach(handler => {
          try {
            logger.debug(`Executing handler for message type: ${type}`);
            // Đảm bảo payload luôn là một object, ngay cả khi undefined
            handler(payload || {});
          } catch (error) {
            const payloadId = payload && typeof payload === 'object' ?
              ('id' in payload ? payload.id : ('_id' in payload ? payload._id : 'unknown')) :
              'unknown';

            logger.error('Message handler error:', {
              error: error instanceof Error ? error.message : 'Unknown error',
              messageType: type,
              payloadId
            });
            errorTracking.captureError(error as Error, {
              action: 'socketMessageHandler',
              additionalData: { messageType: type }
            });
          }
        });
      } else {
        logger.warn(`No handlers registered for message type: ${type}`);
      }
    } catch (error) {
      logger.error('Error in handleMessage:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        data: JSON.stringify(data).substring(0, 200)
      });
    }
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatTimer = setInterval(() => {
      this.sendHeartbeat();
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private sendHeartbeat(): void {
    this.send({ type: 'heartbeat' });
  }

  private handleHeartbeat(): void {
    // Reset connection monitoring
    this.isConnected = true;
  }

  /**
   * Làm mới xác thực Socket.IO
   * Sẽ xác thực lại và kết nối lại nếu cần thiết
   */
  private async refreshAuthentication(): Promise<void> {
    console.log('Refreshing Socket.IO authentication...');

    try {
      // Xác thực lại
      const authenticated = await this.authenticateSocketIO();

      if (!authenticated) {
        console.error('Failed to refresh Socket.IO authentication');
        return;
      }

      console.log('Socket.IO authentication refreshed successfully');

      // Nếu hiện đang kết nối, kết nối lại
      if (this.isConnected) {
        console.log('Reconnecting with refreshed authentication...');

        // Dọn dẹp kết nối hiện tại
        this.cleanup();

        // Kết nối lại
        await this.connect('cookie');
      }
    } catch (error) {
      console.error('Error refreshing Socket.IO authentication:', error);
    }
  }

  private handleConnectionError(error: unknown): void {
    errorTracking.captureError(error as Error, {
      action: 'socketConnection'
    });
    this.handleReconnect();
  }

  private handleReconnect(): void {
    // Xóa timer kết nối lại cũ nếu có
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // Tăng số lần thử kết nối lại
    this.reconnectCount++;

    // Kiểm tra số lần thử kết nối lại
    if (this.reconnectCount > this.config.reconnectAttempts) {
      logger.error(`Socket.IO reconnect failed after ${this.config.reconnectAttempts} attempts`);
      console.error(`Socket.IO reconnect failed after ${this.config.reconnectAttempts} attempts`, {
        sessionId: this.sessionId,
        userId: this.userId
      });

      // Thông báo cho các status handler về việc kết nối thất bại
      this.notifyStatusHandlers(false);

      // Reset bộ đếm kết nối lại sau một khoảng thời gian dài
      setTimeout(() => {
        this.reconnectCount = 0;
        this.handleReconnect(); // Thử lại sau khoảng thời gian dài
      }, 60000); // 1 phút

      return;
    }

    // Tính toán thời gian chờ kết nối lại (tăng dần với jitter)
    const baseDelay = Math.min(
      this.config.reconnectInterval * Math.pow(1.5, this.reconnectCount - 1),
      30000 // Tối đa 30 giây
    );

    // Thêm jitter (±20%) để tránh "thundering herd problem"
    const jitter = baseDelay * 0.2 * (Math.random() - 0.5);
    const delay = Math.max(1000, baseDelay + jitter);

    logger.info(`Socket.IO reconnecting in ${Math.round(delay)}ms (attempt ${this.reconnectCount}/${this.config.reconnectAttempts})`);
    console.log(`Socket.IO reconnecting in ${Math.round(delay)}ms (attempt ${this.reconnectCount}/${this.config.reconnectAttempts})`, {
      sessionId: this.sessionId,
      userId: this.userId
    });

    // Đặt timer kết nối lại
    this.reconnectTimer = setTimeout(() => {
      logger.info(`Socket.IO reconnect attempt ${this.reconnectCount}`);

      // Kiểm tra kết nối internet trước khi thử kết nối lại
      if (!navigator.onLine) {
        logger.warn('Browser reports no internet connection, delaying reconnect');
        console.warn('Browser reports no internet connection, delaying reconnect');

        // Thử lại sau khi có kết nối internet
        setTimeout(() => this.handleReconnect(), 5000);
        return;
      }

      // Thử kết nối lại
      this.connect('cookie').catch(error => {
        logger.error('Socket.IO reconnect error:', error);
        console.error('Socket.IO reconnect error:', error);
        this.handleReconnect(); // Thử lại nếu thất bại
      });
    }, delay);
  }

  public subscribe(type: string, handler: MessageHandler): () => void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, new Set());
    }
    this.messageHandlers.get(type)?.add(handler);

    return () => {
      this.unsubscribe(type, handler);
    };
  }

  public unsubscribe(type: string, handler: MessageHandler): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.messageHandlers.delete(type);
      }
      logger.debug(`Unsubscribed handler from ${type} events`);
    }
  }

  // User profile methods
  public getProfile(): void {
    this.send({ type: 'get_profile' });
  }

  public updateProfile(profileData: Record<string, unknown>): void {
    this.send({
      type: 'update_profile',
      payload: profileData
    });
  }

  // Transaction methods
  public getTransactions(page: number = 1, limit: number = 10): void {
    this.send({
      type: 'get_transactions',
      payload: { page, limit }
    });
  }

  public getTransaction(id: string): void {
    this.send({
      type: 'get_transaction',
      payload: { id }
    });
  }

  // Real-time subscription methods
  public subscribeToTransactionUpdates(filters?: Record<string, unknown>): void {
    this.send({
      type: 'subscribe_transaction_updates',
      payload: { filters }
    });
  }

  public subscribeToDepositUpdates(filters?: Record<string, unknown>): void {
    this.send({
      type: 'subscribe_deposit_updates',
      payload: { filters }
    });
  }

  public subscribeToWithdrawalUpdates(filters?: Record<string, unknown>): void {
    this.send({
      type: 'subscribe_withdrawal_updates',
      payload: { filters }
    });
  }

  public subscribeToInvestmentUpdates(filters?: Record<string, unknown>): void {
    this.send({
      type: 'subscribe_investment_updates',
      payload: { filters }
    });
  }

  // Investment methods
  public getInvestments(page: number = 1, limit: number = 10): void {
    this.send({
      type: 'get_investments',
      payload: { page, limit }
    });
  }

  public getInvestment(id: string): void {
    this.send({
      type: 'get_investment',
      payload: { id }
    });
  }

  public createInvestment(investmentData: Record<string, unknown>): void {
    this.send({
      type: 'create_investment',
      payload: investmentData
    });
  }

  // Wallet methods
  public getWalletBalance(): void {
    this.send({ type: 'get_wallet_balance' });
  }

  public depositAsset(depositData: Record<string, unknown>): void {
    this.send({
      type: 'deposit_asset',
      payload: depositData
    });
  }

  public withdrawAsset(withdrawalData: Record<string, unknown>): void {
    this.send({
      type: 'withdraw_asset',
      payload: withdrawalData
    });
  }

  // Admin methods
  public adminGetUsers(page: number = 1, limit: number = 10): void {
    this.send({
      type: 'admin_get_users',
      payload: { page, limit }
    });
  }

  public adminGetDeposits(page: number = 1, limit: number = 10): void {
    this.send({
      type: 'admin_get_deposits',
      payload: { page, limit }
    });
  }

  public adminGetWithdrawals(page: number = 1, limit: number = 10): void {
    this.send({
      type: 'admin_get_withdrawals',
      payload: { page, limit }
    });
  }

  public adminUpdateDepositStatus(id: string, status: string): void {
    this.send({
      type: 'admin_update_deposit_status',
      payload: { id, status }
    });
  }

  public subscribeToStatus(handler: StatusHandler): () => void {
    this.statusHandlers.add(handler);
    return () => {
      this.statusHandlers.delete(handler);
    };
  }

  private notifyStatusHandlers(status: boolean): void {
    this.statusHandlers.forEach(handler => {
      try {
        handler(status);
      } catch (error) {
        logger.error('Status handler error:', { error: error instanceof Error ? error.message : 'Unknown error' });
      }
    });
  }

  public send(data: SocketMessage): void {
    if (!this.isConnected) {
      this.messageQueue.push(data);
      return;
    }

    try {
      this.socket?.emit('message', data);
    } catch (error) {
      logger.error('Socket.IO send error:', { error: error instanceof Error ? error.message : 'Unknown error' });
      this.messageQueue.push(data);
      this.handleConnectionError(error);
    }
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const data = this.messageQueue.shift();
      if (data) {
        this.send(data);
      }
    }
  }

  private cleanup(): void {
    this.stopHeartbeat();

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // Xóa timer làm mới token nếu có
    if (this.tokenRefreshTimer) {
      clearTimeout(this.tokenRefreshTimer);
      this.tokenRefreshTimer = null;
    }

    if (this.socket) {
      // Thông báo server rằng chúng ta đang đóng kết nối
      if (this.isConnected && this.sessionId) {
        try {
          // Gửi thông báo ngắt kết nối với sessionId
          this.socket.emit('unregister_session', {
            sessionId: this.sessionId,
            userId: this.userId
          });
          logger.info('Sent unregister_session message to server', {
            socketId: this.socket.id,
            sessionId: this.sessionId,
            userId: this.userId
          });
        } catch (error) {
          logger.error('Error sending unregister_session message:', {
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Xóa tất cả các event listener để tránh rò rỉ bộ nhớ
      this.socket.removeAllListeners();
      this.socket.off();

      // Đóng kết nối
      try {
        this.socket.disconnect();
        logger.info('Socket disconnected successfully', {
          socketId: this.socket.id
        });
      } catch (error) {
        logger.error('Error disconnecting socket:', {
          error: error instanceof Error ? error.message : 'Unknown error',
          socketId: this.socket.id
        });
      }

      this.socket = null;
    }

    this.isConnected = false;

    // Xóa hàng đợi tin nhắn
    this.messageQueue = [];

    // Thông báo cho các status handler
    this.notifyStatusHandlers(false);

    logger.info('Socket.IO connection cleaned up');
  }

  public disconnect(): void {
    // Log cố gắng ngắt kết nối
    logger.info('Disconnecting Socket.IO service');
    console.log('Disconnecting Socket.IO service', {
      socketId: this.socket?.id,
      sessionId: this.sessionId,
      isConnected: this.isConnected
    });

    // Dọn dẹp kết nối Socket.IO
    this.cleanup();

    // Xóa hàng đợi tin nhắn
    this.messageQueue = [];

    // Xóa tất cả các handler để tránh rò rỉ bộ nhớ
    this.messageHandlers.forEach((handlers) => {
      handlers.clear();
    });
    this.messageHandlers.clear();

    // Xóa status handlers
    this.statusHandlers.clear();

    // Reset bộ đếm kết nối lại
    this.reconnectCount = 0;

    // Xóa timer làm mới token nếu có
    if (this.tokenRefreshTimer) {
      clearTimeout(this.tokenRefreshTimer);
      this.tokenRefreshTimer = null;
    }

    // Xóa thông tin phiên
    this.sessionId = null;
    this.userId = null;

    // Đảm bảo rằng tất cả các tài nguyên đã được giải phóng
    try {
      // Truy cập io manager thông qua window
      const ioObj = (window as any).io;
      if (ioObj && ioObj.managers) {
        // Xóa tất cả các manager để đảm bảo không có kết nối nào còn lại
        Object.keys(ioObj.managers).forEach(key => {
          const manager = ioObj.managers[key];
          console.log(`Closing manager for ${key}`);
          manager.disconnect();
          // Xóa manager khỏi danh sách
          delete ioObj.managers[key];
        });
        console.log('All Socket.IO managers closed');
      }
    } catch (error) {
      console.error('Error closing Socket.IO managers:', error);
    }

    logger.info('Socket.IO service fully disconnected');
    console.log('Socket.IO service fully disconnected');
  }

  public getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * Phương thức thử nghiệm để mô phỏng nhận tin nhắn Socket.IO
   * Được sử dụng để kiểm tra xử lý sự kiện Socket.IO
   * @param data Dữ liệu tin nhắn để mô phỏng
   */
  public simulateMessage(data: SocketMessage): void {
    logger.info('Simulating Socket.IO message:', data);
    try {
      // Xử lý tin nhắn như thể nó được nhận từ server
      this.handleMessage(data);
    } catch (error) {
      logger.error('Error simulating Socket.IO message:', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}

export { SocketService };