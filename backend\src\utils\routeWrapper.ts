import { Request, Response, NextFunction, RequestHandler } from 'express';

// Define a type for controller functions
type ControllerFunction = (req: Request, res: Response, next: NextFunction) => Promise<any> | any;

/**
 * Wraps a controller function to make it compatible with Express's RequestHandler type
 * This is a more compatible version that works with Express's type system
 */
export const wrapController = (fn: ControllerFunction): RequestHandler => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      await fn(req, res, next);
    } catch (err) {
      next(err);
    }
  };
};
