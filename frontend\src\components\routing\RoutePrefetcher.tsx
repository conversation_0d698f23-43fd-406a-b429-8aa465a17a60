import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { routes, RouteType } from '../../routes/RouteController';
import useAuth from '../../hooks/useAuth';

/**
 * Route Prefetcher component
 *
 * Features:
 * - Intelligently prefetches related routes based on current location
 * - Prefetches only routes the user has access to
 * - Uses requestIdleCallback for non-blocking prefetching
 * - Customizable prefetching strategies
 */
const RoutePrefetcher: React.FC = () => {
  const location = useLocation();
  const { user } = useAuth();

  useEffect(() => {
    // Define prefetch relationships
    const getPrefetchRoutes = (currentPath: string): string[] => {
      // Common prefetch paths for all users
      const commonPrefetch: Record<string, string[]> = {
        '/': ['/login', '/register', '/about', '/contact', '/faq'],
        '/login': ['/register', '/profile', '/'],
        '/register': ['/login', '/profile', '/'],
        '/about': ['/contact', '/faq', '/'],
        '/contact': ['/about', '/faq', '/'],
        '/faq': ['/about', '/contact', '/']
      };

      // Authenticated user prefetch paths
      const authPrefetch: Record<string, string[]> = {
        '/profile': ['/wallet', '/investments'],
        '/wallet': ['/profile', '/investments'],
        '/investments': ['/profile', '/wallet']
      };

      // Admin user prefetch paths
      const adminPrefetch: Record<string, string[]> = {
        '/admin': ['/admin/users', '/admin/transactions', '/admin/home-management', '/admin/profile-management'],
        '/admin/users': ['/admin/transactions', '/admin/profile-management'],
        '/admin/transactions': ['/admin/deposits', '/admin/withdrawals'],
        '/admin/deposits': ['/admin/withdrawals', '/admin/transactions'],
        '/admin/withdrawals': ['/admin/deposits', '/admin/transactions'],
        '/admin/home-management': ['/admin/content', '/admin/site-management'],
        '/admin/profile-management': ['/admin/users', '/admin/settings'],
        '/admin/content': ['/admin/home-management', '/admin/site-management'],
        '/admin/settings': ['/admin/system-management', '/admin/commission'],
        '/admin/commission': ['/admin/settings', '/admin/system-management'],
        '/admin/site-management': ['/admin/content', '/admin/home-management'],
        '/admin/system-management': ['/admin/settings', '/admin/commission']
      };

      // Combine prefetch paths based on user role
      let prefetchPaths: string[] = commonPrefetch[currentPath] || [];

      if (user) {
        // Add authenticated paths
        if (authPrefetch[currentPath]) {
          prefetchPaths = [...prefetchPaths, ...authPrefetch[currentPath]];
        }

        // Add admin paths if user is admin
        if (user.isAdmin && adminPrefetch[currentPath]) {
          prefetchPaths = [...prefetchPaths, ...adminPrefetch[currentPath]];
        }
      }

      return prefetchPaths;
    };

    // Prefetch routes
    const prefetchRoutes = () => {
      const pathsToPrefetch = getPrefetchRoutes(location.pathname);

      // Filter out routes the user doesn't have access to
      const accessiblePaths = pathsToPrefetch.filter(path => {
        const route = routes.find(r => r.path === path);
        if (!route) return false;

        if (route.type === RouteType.PUBLIC) return true;
        if (route.type === RouteType.PROTECTED) return !!user;
        if (route.type === RouteType.ADMIN) return !!user && !!user.isAdmin;

        return false;
      });

      // Prefetch each route
      accessiblePaths.forEach(path => {
        const route = routes.find(r => r.path === path);
        if (route) {
          // Trigger lazy loading of the component
          route.component.preload?.();
        }
      });
    };

    // Use requestIdleCallback for non-blocking prefetching
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(prefetchRoutes, { timeout: 2000 });
    } else {
      // Fallback for browsers that don't support requestIdleCallback
      setTimeout(prefetchRoutes, 1000);
    }
  }, [location.pathname, user]);

  // This component doesn't render anything
  return null;
};

export default RoutePrefetcher;
