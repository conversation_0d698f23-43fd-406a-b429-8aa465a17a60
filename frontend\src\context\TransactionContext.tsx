import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Transaction } from '../components/TransactionHistory';
import { useToast } from '@chakra-ui/react';

interface TransactionContextType {
  transactions: Transaction[];
  loading: boolean;
  error: string | null;
  lastUpdate: Date | null;
  newTransactionIds: Set<string>;
  updatedTransactionIds: Set<string>;
  addTransaction: (transaction: Transaction) => void;
  updateTransaction: (transaction: Transaction) => void;
  refreshTransactions: () => void;
  setTransactionFilter: (filter: 'all' | 'deposit' | 'withdrawal' | 'interest' | 'commission') => void;
  transactionFilter: 'all' | 'deposit' | 'withdrawal' | 'interest' | 'commission';
}

const TransactionContext = createContext<TransactionContextType | undefined>(undefined);

export const useTransactions = () => {
  const context = useContext(TransactionContext);
  if (!context) {
    throw new Error('useTransactions must be used within a TransactionProvider');
  }
  return context;
};

interface TransactionProviderProps {
  children: ReactNode;
}

export const TransactionProvider: React.FC<TransactionProviderProps> = ({ children }) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [newTransactionIds, setNewTransactionIds] = useState<Set<string>>(new Set());
  const [updatedTransactionIds, setUpdatedTransactionIds] = useState<Set<string>>(new Set());
  const [transactionFilter, setFilter] = useState<'all' | 'deposit' | 'withdrawal'>('all');
  
  const toast = useToast();

  // Load transactions from localStorage
  const loadTransactions = () => {
    console.log('TransactionContext: Loading transactions');
    setLoading(true);
    setError(null);
    
    try {
      // Try to get transactions from localStorage
      const storedTransactions = localStorage.getItem('transactions');
      
      if (storedTransactions) {
        try {
          const parsedTransactions = JSON.parse(storedTransactions) as Transaction[];
          console.log(`TransactionContext: Loaded ${parsedTransactions.length} transactions from localStorage`);
          
          // Filter transactions based on the filter
          let filteredTransactions = parsedTransactions;
          if (transactionFilter === 'deposit') {
            filteredTransactions = parsedTransactions.filter(tx => tx.type === 'deposit');
          } else if (transactionFilter === 'withdrawal') {
            filteredTransactions = parsedTransactions.filter(tx => tx.type === 'withdrawal');
          }
          
          // Sort transactions by date (newest first)
          filteredTransactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
          
          setTransactions(filteredTransactions);
          setLastUpdate(new Date());
        } catch (parseError) {
          console.error('TransactionContext: Error parsing transactions JSON:', parseError);
          
          // Try to recover from backup
          const backupTransactions = localStorage.getItem('transactions_backup');
          if (backupTransactions) {
            try {
              const parsedBackupTransactions = JSON.parse(backupTransactions) as Transaction[];
              console.log(`TransactionContext: Recovered ${parsedBackupTransactions.length} transactions from backup`);
              setTransactions(parsedBackupTransactions);
              setLastUpdate(new Date());
            } catch (backupError) {
              console.error('TransactionContext: Error parsing backup transactions:', backupError);
              setError('Failed to load transactions. Please try again.');
            }
          } else {
            setError('Failed to load transactions. Please try again.');
          }
        }
      } else {
        console.log('TransactionContext: No transactions found in localStorage');
        setTransactions([]);
      }
    } catch (error) {
      console.error('TransactionContext: Error loading transactions:', error);
      setError('Failed to load transactions. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Add a new transaction
  const addTransaction = (transaction: Transaction) => {
    console.log('TransactionContext: Adding new transaction', transaction);
    
    try {
      // Get existing transactions
      const existingTransactions = localStorage.getItem('transactions');
      let transactions = existingTransactions ? JSON.parse(existingTransactions) : [];
      
      // Add new transaction at the beginning of the array
      transactions.unshift(transaction);
      
      // Save transactions to localStorage
      localStorage.setItem('transactions', JSON.stringify(transactions));
      console.log('TransactionContext: Transaction saved to localStorage');
      
      // Create a backup
      localStorage.setItem('transactions_backup', JSON.stringify(transactions));
      
      // Force a refresh by setting a timestamp
      const updateTimestamp = Date.now().toString();
      localStorage.setItem('lastTransactionUpdate', updateTimestamp);
      
      // Mark this transaction as new
      setNewTransactionIds(prev => {
        const newSet = new Set(prev);
        newSet.add(transaction.id);
        return newSet;
      });
      
      // Refresh transactions
      loadTransactions();
      
      // Show toast notification
      toast({
        title: `New ${transaction.type} transaction`,
        description: `${transaction.amount} ${transaction.currency} - ${transaction.status}`,
        status: 'info',
        duration: 5000,
        isClosable: true,
        position: 'top-right'
      });
    } catch (error) {
      console.error('TransactionContext: Error adding transaction:', error);
      setError('Failed to add transaction. Please try again.');
    }
  };

  // Update an existing transaction
  const updateTransaction = (transaction: Transaction) => {
    console.log('TransactionContext: Updating transaction', transaction);
    
    try {
      // Get existing transactions
      const existingTransactions = localStorage.getItem('transactions');
      if (!existingTransactions) {
        console.error('TransactionContext: No transactions found in localStorage');
        return;
      }
      
      let transactions = JSON.parse(existingTransactions);
      
      // Find the transaction to update
      const index = transactions.findIndex((tx: Transaction) => tx.id === transaction.id);
      if (index === -1) {
        console.error('TransactionContext: Transaction not found', transaction.id);
        return;
      }
      
      // Update the transaction
      transactions[index] = transaction;
      
      // Save transactions to localStorage
      localStorage.setItem('transactions', JSON.stringify(transactions));
      console.log('TransactionContext: Transaction updated in localStorage');
      
      // Create a backup
      localStorage.setItem('transactions_backup', JSON.stringify(transactions));
      
      // Force a refresh by setting a timestamp
      const updateTimestamp = Date.now().toString();
      localStorage.setItem('lastTransactionUpdate', updateTimestamp);
      
      // Mark this transaction as updated
      setUpdatedTransactionIds(prev => {
        const newSet = new Set(prev);
        newSet.add(transaction.id);
        return newSet;
      });
      
      // Refresh transactions
      loadTransactions();
      
      // Show toast notification
      toast({
        title: `Transaction ${transaction.status}`,
        description: `Your ${transaction.type} of ${transaction.amount} ${transaction.currency} has been ${transaction.status}`,
        status: transaction.status === 'approved' ? 'success' : transaction.status === 'rejected' ? 'error' : 'info',
        duration: 5000,
        isClosable: true,
        position: 'top-right'
      });
    } catch (error) {
      console.error('TransactionContext: Error updating transaction:', error);
      setError('Failed to update transaction. Please try again.');
    }
  };

  // Set transaction filter
  const setTransactionFilter = (filter: 'all' | 'deposit' | 'withdrawal' | 'interest' | 'commission') => {
    console.log('TransactionContext: Setting transaction filter to', filter);
    setFilter(filter);
    loadTransactions();
  };

  // Refresh transactions
  const refreshTransactions = () => {
    console.log('TransactionContext: Refreshing transactions');
    loadTransactions();
  };

  // Load transactions on mount and when filter changes
  useEffect(() => {
    console.log('TransactionContext: Initial load');
    loadTransactions();
    
    // Set up event listeners for real-time updates
    const handleTransactionUpdated = (e: Event) => {
      console.log('TransactionContext: Transaction updated event received', (e as CustomEvent).detail);
      loadTransactions();
    };
    
    const handleDataRefresh = (e: Event) => {
      console.log('TransactionContext: Data refresh event received', (e as CustomEvent).detail);
      loadTransactions();
    };
    
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'transactions' || e.key === 'lastTransactionUpdate' || e.key?.startsWith('transaction_')) {
        console.log('TransactionContext: Storage change detected', e.key);
        loadTransactions();
      }
    };
    
    // Add event listeners
    window.addEventListener('transactionUpdated', handleTransactionUpdated);
    window.addEventListener('dataRefresh', handleDataRefresh);
    window.addEventListener('storage', handleStorageChange);
    
    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('transactionUpdated', handleTransactionUpdated);
      window.removeEventListener('dataRefresh', handleDataRefresh);
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [transactionFilter]);

  return (
    <TransactionContext.Provider
      value={{
        transactions,
        loading,
        error,
        lastUpdate,
        newTransactionIds,
        updatedTransactionIds,
        addTransaction,
        updateTransaction,
        refreshTransactions,
        setTransactionFilter,
        transactionFilter
      }}
    >
      {children}
    </TransactionContext.Provider>
  );
};
