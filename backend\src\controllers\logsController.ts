import { Request, Response } from 'express';
import { logger } from '../utils/logger';

/**
 * Handle frontend log submissions
 * @route POST /api/logs
 * @desc Receive and process logs from frontend
 * @access Public (for development) / Protected (for production)
 */
export const submitLogs = async (req: Request, res: Response) => {
  try {
    const { logs, level = 'info', source = 'frontend' } = req.body;

    // Validate request body
    if (!logs) {
      return res.status(400).json({
        success: false,
        message: 'Logs data is required'
      });
    }

    // Process logs based on type
    if (Array.isArray(logs)) {
      // Handle multiple logs
      logs.forEach((logEntry: any) => {
        processLogEntry(logEntry, source);
      });
    } else {
      // Handle single log entry
      processLogEntry(logs, source);
    }

    res.status(200).json({
      success: true,
      message: 'Logs received successfully'
    });

  } catch (error: any) {
    logger.error('Error processing frontend logs:', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    res.status(500).json({
      success: false,
      message: 'Failed to process logs'
    });
  }
};

/**
 * Process individual log entry
 */
function processLogEntry(logEntry: any, source: string) {
  try {
    const {
      level = 'info',
      message,
      timestamp,
      url,
      userAgent,
      userId,
      error,
      ...metadata
    } = logEntry;

    const logData = {
      source,
      message: message || 'Frontend log entry',
      timestamp: timestamp || new Date().toISOString(),
      url,
      userAgent,
      userId,
      ...metadata
    };

    // Log based on level
    switch (level.toLowerCase()) {
      case 'error':
        logger.error('FRONTEND_ERROR', {
          ...logData,
          error: error || logEntry
        });
        break;
      case 'warn':
      case 'warning':
        logger.warn('FRONTEND_WARNING', logData);
        break;
      case 'debug':
        logger.debug('FRONTEND_DEBUG', logData);
        break;
      case 'info':
      default:
        logger.info('FRONTEND_INFO', logData);
        break;
    }
  } catch (error) {
    logger.error('Error processing individual log entry:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      logEntry,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Get system logs (admin only)
 * @route GET /api/logs
 * @desc Retrieve system logs with filtering
 * @access Admin
 */
export const getSystemLogs = async (req: Request, res: Response) => {
  try {
    const {
      level,
      source,
      startDate,
      endDate,
      page = 1,
      limit = 50
    } = req.query;

    // This is a placeholder implementation
    // In a real system, you would read from log files or a logging database
    res.status(200).json({
      success: true,
      message: 'Log retrieval not implemented yet',
      data: {
        logs: [],
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total: 0,
          totalPages: 0
        }
      }
    });

  } catch (error: any) {
    logger.error('Error retrieving system logs:', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve logs'
    });
  }
};

/**
 * Clear old logs (admin only)
 * @route DELETE /api/logs
 * @desc Clear old log entries
 * @access Admin
 */
export const clearLogs = async (req: Request, res: Response) => {
  try {
    const { olderThan } = req.body;

    // This is a placeholder implementation
    res.status(200).json({
      success: true,
      message: 'Log clearing not implemented yet'
    });

  } catch (error: any) {
    logger.error('Error clearing logs:', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    res.status(500).json({
      success: false,
      message: 'Failed to clear logs'
    });
  }
};
