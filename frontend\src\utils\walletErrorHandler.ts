/**
 * Wallet Error Handler Utility
 * Provides user-friendly error messages for wallet-related errors
 */

export interface WalletError {
  code: string;
  message: string;
  userMessage: string;
  action?: string;
}

export const handleWalletError = (error: any): WalletError => {
  console.error('Wallet Error:', error);

  // Default error
  let walletError: WalletError = {
    code: 'UNKNOWN_ERROR',
    message: error.message || 'Unknown error occurred',
    userMessage: 'An unexpected error occurred. Please try again.',
    action: 'retry'
  };

  // Handle different error types
  if (error.response) {
    const status = error.response.status;
    const data = error.response.data;

    switch (status) {
      case 400:
        walletError = {
          code: 'BAD_REQUEST',
          message: data?.message || 'Bad request',
          userMessage: 'Invalid request. Please check your input and try again.',
          action: 'retry'
        };
        break;

      case 401:
        walletError = {
          code: 'UNAUTHORIZED',
          message: data?.message || 'Unauthorized',
          userMessage: 'Your session has expired. Please login again.',
          action: 'login'
        };
        break;

      case 403:
        walletError = {
          code: 'FORBIDDEN',
          message: data?.message || 'Forbidden',
          userMessage: 'You do not have permission to access this resource.',
          action: 'contact_support'
        };
        break;

      case 404:
        walletError = {
          code: 'NOT_FOUND',
          message: data?.message || 'Not found',
          userMessage: 'Wallet service not found. Please contact support.',
          action: 'contact_support'
        };
        break;

      case 500:
        walletError = {
          code: 'SERVER_ERROR',
          message: data?.message || 'Internal server error',
          userMessage: 'Server temporarily unavailable. Please try again in a moment.',
          action: 'retry_later'
        };
        break;

      case 502:
      case 503:
      case 504:
        walletError = {
          code: 'SERVICE_UNAVAILABLE',
          message: data?.message || 'Service unavailable',
          userMessage: 'Service temporarily unavailable. Please try again later.',
          action: 'retry_later'
        };
        break;

      default:
        walletError = {
          code: `HTTP_${status}`,
          message: data?.message || `HTTP ${status} error`,
          userMessage: 'An error occurred while processing your request.',
          action: 'retry'
        };
    }
  } else if (error.code === 'ECONNABORTED') {
    walletError = {
      code: 'TIMEOUT',
      message: 'Request timeout',
      userMessage: 'Request timed out. Please check your connection and try again.',
      action: 'retry'
    };
  } else if (error.message && error.message.includes('Network Error')) {
    walletError = {
      code: 'NETWORK_ERROR',
      message: 'Network error',
      userMessage: 'Network connection error. Please check your internet connection.',
      action: 'check_connection'
    };
  }

  return walletError;
};

export const getActionMessage = (action: string): string => {
  switch (action) {
    case 'retry':
      return 'Please try again.';
    case 'login':
      return 'Please login again.';
    case 'retry_later':
      return 'Please try again in a few minutes.';
    case 'contact_support':
      return 'Please contact support if the problem persists.';
    case 'check_connection':
      return 'Please check your internet connection.';
    default:
      return 'Please try again or contact support.';
  }
};

export default {
  handleWalletError,
  getActionMessage
};
