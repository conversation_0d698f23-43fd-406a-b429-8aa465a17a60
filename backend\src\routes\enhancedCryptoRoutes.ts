import express from 'express';
import {
  getCryptoBalances,
  getEnhancedInvestmentPackages,
  validateWithdrawal,
  processDeposit,
  getTimeLockStatus,
  getInvestmentSummary,
  generateDepositAddress,
  calculateInterestPreview
} from '../controllers/enhancedCryptoController';
import { protect } from '../middleware/authMiddleware';
import { handleValidationErrors } from '../middleware/validationMiddleware';
import { body, param, query } from 'express-validator';

const router = express.Router();

/**
 * @route   GET /api/crypto/balances
 * @desc    Get user's crypto balances and investment overview
 * @access  Private
 */
router.get('/balances', protect, getCryptoBalances);

/**
 * @route   GET /api/crypto/packages
 * @desc    Get enhanced investment packages with real-time data
 * @access  Private
 */
router.get('/packages', protect, getEnhancedInvestmentPackages);

/**
 * @route   GET /api/crypto/packages/:packageId/validate-withdrawal
 * @desc    Validate withdrawal eligibility for a specific package
 * @access  Private
 */
router.get(
  '/packages/:packageId/validate-withdrawal',
  protect,
  [
    param('packageId')
      .isMongoId()
      .withMessage('Invalid package ID format')
  ],
  handleValidationErrors,
  validateWithdrawal
);

/**
 * @route   POST /api/crypto/deposit
 * @desc    Process crypto deposit and create investment package
 * @access  Private
 */
router.post(
  '/deposit',
  protect,
  [
    body('currency')
      .isIn(['BTC', 'ETH', 'USDT', 'BNB', 'SOL'])
      .withMessage('Unsupported currency'),
    body('amount')
      .isFloat({ min: 0.000001 })
      .withMessage('Amount must be a positive number'),
    body('transactionHash')
      .isLength({ min: 10, max: 100 })
      .withMessage('Invalid transaction hash'),
    body('walletAddress')
      .isLength({ min: 10, max: 100 })
      .withMessage('Invalid wallet address')
  ],
  handleValidationErrors,
  processDeposit
);

/**
 * @route   GET /api/crypto/time-lock-status
 * @desc    Get time lock status and next interest calculation info
 * @access  Public
 */
router.get('/time-lock-status', getTimeLockStatus);

/**
 * @route   GET /api/crypto/investment-summary
 * @desc    Get comprehensive investment summary and statistics
 * @access  Private
 */
router.get('/investment-summary', protect, getInvestmentSummary);

/**
 * @route   GET /api/crypto/deposit-address/:currency
 * @desc    Generate or get deposit address for specific currency
 * @access  Private
 */
router.get(
  '/deposit-address/:currency',
  protect,
  [
    param('currency')
      .isIn(['BTC', 'ETH', 'USDT', 'BNB', 'SOL'])
      .withMessage('Unsupported currency')
  ],
  handleValidationErrors,
  generateDepositAddress
);

/**
 * @route   GET /api/crypto/interest-preview
 * @desc    Calculate interest preview for deposit amount
 * @access  Public
 */
router.get(
  '/interest-preview',
  [
    query('currency')
      .isIn(['BTC', 'ETH', 'USDT', 'BNB', 'SOL'])
      .withMessage('Unsupported currency'),
    query('amount')
      .isFloat({ min: 0.000001 })
      .withMessage('Amount must be a positive number')
  ],
  handleValidationErrors,
  calculateInterestPreview
);

export default router;
