@echo off
setlocal enabledelayedexpansion

REM Development Hot Reload Setup Script for Windows
REM This script rebuilds and restarts the backend container with optimized hot reload

echo 🔥 Setting up hot reload for CryptoYield Backend...

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker first.
    exit /b 1
)

echo [INFO] Stopping existing backend container...
docker-compose -f docker-compose.dev.yml stop backend 2>nul

echo [INFO] Removing existing backend container...
docker-compose -f docker-compose.dev.yml rm -f backend 2>nul

echo [INFO] Rebuilding backend image with hot reload optimizations...
docker-compose -f docker-compose.dev.yml build --no-cache backend
if errorlevel 1 (
    echo [ERROR] Failed to build backend image
    exit /b 1
)

echo [INFO] Starting backend with hot reload...
docker-compose -f docker-compose.dev.yml up -d backend
if errorlevel 1 (
    echo [ERROR] Failed to start backend container
    exit /b 1
)

echo [INFO] Waiting for backend to be healthy...
set /a timeout=60
set /a counter=0

:wait_loop
if !counter! geq !timeout! (
    echo [ERROR] Backend failed to start within !timeout! seconds
    echo [INFO] Checking backend logs...
    docker-compose -f docker-compose.dev.yml logs --tail 20 backend
    exit /b 1
)

docker-compose -f docker-compose.dev.yml ps backend | findstr "healthy" >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] Backend is healthy and ready!
    goto :success
)

if !counter! equ 30 (
    echo [WARNING] Backend is taking longer than expected to start...
)

timeout /t 2 /nobreak >nul
set /a counter+=2
goto :wait_loop

:success
echo [SUCCESS] 🚀 Hot reload setup complete!
echo [INFO] Backend is running at: http://localhost:5000
echo [INFO] Health check: http://localhost:5000/health
echo.
echo [INFO] 📝 Hot reload features:
echo [INFO]   ✅ Source code changes will automatically restart the server
echo [INFO]   ✅ TypeScript compilation on-the-fly
echo [INFO]   ✅ Environment variables from .env.docker
echo [INFO]   ✅ Debug port available at 9229
echo.
echo [INFO] 📊 Monitoring commands:
echo [INFO]   View logs: docker-compose -f docker-compose.dev.yml logs -f backend
echo [INFO]   Restart:   docker-compose -f docker-compose.dev.yml restart backend
echo [INFO]   Stop:      docker-compose -f docker-compose.dev.yml stop backend
echo.
echo [INFO] 🔧 Testing hot reload:
echo [INFO]   1. Edit any file in backend/src/
echo [INFO]   2. Save the file
echo [INFO]   3. Watch the container logs for restart message
echo [INFO]   4. Test API endpoint to verify changes

REM Show current logs
echo [INFO] Current backend logs (last 10 lines):
docker-compose -f docker-compose.dev.yml logs --tail 10 backend

pause
