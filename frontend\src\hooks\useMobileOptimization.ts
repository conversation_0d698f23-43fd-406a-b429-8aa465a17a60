import { useState, useEffect, useCallback, useRef } from 'react';
import { useBreakpointValue, useToast } from '@chakra-ui/react';
import { PWAService } from '../services/PWAService';

/**
 * Mobile Optimization Hook
 * Provides comprehensive mobile optimization features
 */

export interface MobileOptimizationState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  orientation: 'portrait' | 'landscape';
  touchSupport: boolean;
  installPromptAvailable: boolean;
  isInstalled: boolean;
  isOnline: boolean;
  batteryLevel?: number;
  connectionType?: string;
  devicePixelRatio: number;
  viewportHeight: number;
  viewportWidth: number;
  safeAreaInsets: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

export interface MobileOptimizationActions {
  installPWA: () => Promise<boolean>;
  enableFullscreen: () => Promise<void>;
  exitFullscreen: () => Promise<void>;
  vibrate: (pattern: number | number[]) => boolean;
  shareContent: (data: ShareData) => Promise<boolean>;
  requestWakeLock: () => Promise<WakeLockSentinel | null>;
  releaseWakeLock: () => Promise<void>;
  optimizeForMobile: () => void;
  handleTouchGestures: (element: HTMLElement) => void;
}

export const useMobileOptimization = () => {
  const toast = useToast();
  const pwaService = PWAService.getInstance();
  const wakeLockRef = useRef<WakeLockSentinel | null>(null);

  // Responsive breakpoints
  const isMobile = useBreakpointValue({ base: true, md: false }) ?? false;
  const isTablet = useBreakpointValue({ base: false, md: true, lg: false }) ?? false;
  const isDesktop = useBreakpointValue({ base: false, lg: true }) ?? false;

  const [state, setState] = useState<MobileOptimizationState>({
    isMobile,
    isTablet,
    isDesktop,
    orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
    touchSupport: 'ontouchstart' in window,
    installPromptAvailable: false,
    isInstalled: false,
    isOnline: navigator.onLine,
    devicePixelRatio: window.devicePixelRatio || 1,
    viewportHeight: window.innerHeight,
    viewportWidth: window.innerWidth,
    safeAreaInsets: {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0
    }
  });

  /**
   * Update mobile state
   */
  const updateMobileState = useCallback(() => {
    const capabilities = pwaService.getPWACapabilities();
    
    setState(prev => ({
      ...prev,
      isMobile,
      isTablet,
      isDesktop,
      orientation: window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',
      installPromptAvailable: capabilities.isInstallable,
      isInstalled: capabilities.isInstalled,
      isOnline: capabilities.isOnline,
      viewportHeight: window.innerHeight,
      viewportWidth: window.innerWidth,
      safeAreaInsets: getSafeAreaInsets()
    }));
  }, [isMobile, isTablet, isDesktop, pwaService]);

  /**
   * Get safe area insets for notched devices
   */
  const getSafeAreaInsets = useCallback(() => {
    const style = getComputedStyle(document.documentElement);
    return {
      top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
      bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
      left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0'),
      right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0')
    };
  }, []);

  /**
   * Install PWA
   */
  const installPWA = useCallback(async (): Promise<boolean> => {
    try {
      const success = await pwaService.installPWA();
      if (success) {
        toast({
          title: 'App Installed',
          description: 'CryptoYield has been installed successfully!',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      }
      return success;
    } catch (error) {
      toast({
        title: 'Installation Failed',
        description: 'Failed to install the app. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return false;
    }
  }, [pwaService, toast]);

  /**
   * Enable fullscreen mode
   */
  const enableFullscreen = useCallback(async (): Promise<void> => {
    if (!document.fullscreenEnabled) return;

    try {
      await document.documentElement.requestFullscreen();
    } catch (error) {
      console.error('Failed to enable fullscreen:', error);
    }
  }, []);

  /**
   * Exit fullscreen mode
   */
  const exitFullscreen = useCallback(async (): Promise<void> => {
    if (!document.fullscreenElement) return;

    try {
      await document.exitFullscreen();
    } catch (error) {
      console.error('Failed to exit fullscreen:', error);
    }
  }, []);

  /**
   * Vibrate device
   */
  const vibrate = useCallback((pattern: number | number[]): boolean => {
    if (!('vibrate' in navigator)) return false;

    try {
      return navigator.vibrate(pattern);
    } catch (error) {
      console.error('Vibration failed:', error);
      return false;
    }
  }, []);

  /**
   * Share content using Web Share API
   */
  const shareContent = useCallback(async (data: ShareData): Promise<boolean> => {
    if (!('share' in navigator)) {
      // Fallback to clipboard
      try {
        await navigator.clipboard.writeText(data.url || data.text || '');
        toast({
          title: 'Copied to Clipboard',
          description: 'Content has been copied to clipboard',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        return true;
      } catch (error) {
        return false;
      }
    }

    try {
      await navigator.share(data);
      return true;
    } catch (error) {
      console.error('Share failed:', error);
      return false;
    }
  }, [toast]);

  /**
   * Request wake lock to prevent screen from sleeping
   */
  const requestWakeLock = useCallback(async (): Promise<WakeLockSentinel | null> => {
    if (!('wakeLock' in navigator)) return null;

    try {
      const wakeLock = await navigator.wakeLock.request('screen');
      wakeLockRef.current = wakeLock;
      
      wakeLock.addEventListener('release', () => {
        console.log('Wake lock released');
        wakeLockRef.current = null;
      });

      return wakeLock;
    } catch (error) {
      console.error('Wake lock request failed:', error);
      return null;
    }
  }, []);

  /**
   * Release wake lock
   */
  const releaseWakeLock = useCallback(async (): Promise<void> => {
    if (wakeLockRef.current) {
      try {
        await wakeLockRef.current.release();
        wakeLockRef.current = null;
      } catch (error) {
        console.error('Wake lock release failed:', error);
      }
    }
  }, []);

  /**
   * Optimize interface for mobile
   */
  const optimizeForMobile = useCallback(() => {
    if (!isMobile) return;

    // Disable zoom on input focus
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
      );
    }

    // Add mobile-specific CSS classes
    document.body.classList.add('mobile-optimized');

    // Optimize touch targets
    const style = document.createElement('style');
    style.textContent = `
      .mobile-optimized button,
      .mobile-optimized input,
      .mobile-optimized select,
      .mobile-optimized textarea {
        min-height: 44px;
        min-width: 44px;
      }
      
      .mobile-optimized {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
      }
      
      .mobile-optimized input,
      .mobile-optimized textarea {
        -webkit-user-select: text;
        user-select: text;
      }
    `;
    document.head.appendChild(style);
  }, [isMobile]);

  /**
   * Handle touch gestures
   */
  const handleTouchGestures = useCallback((element: HTMLElement) => {
    if (!state.touchSupport) return;

    let startX = 0;
    let startY = 0;
    let startTime = 0;

    const handleTouchStart = (e: TouchEvent) => {
      const touch = e.touches[0];
      startX = touch.clientX;
      startY = touch.clientY;
      startTime = Date.now();
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const touch = e.changedTouches[0];
      const endX = touch.clientX;
      const endY = touch.clientY;
      const endTime = Date.now();

      const deltaX = endX - startX;
      const deltaY = endY - startY;
      const deltaTime = endTime - startTime;

      // Detect swipe gestures
      if (deltaTime < 500 && Math.abs(deltaX) > 50) {
        if (deltaX > 0) {
          element.dispatchEvent(new CustomEvent('swiperight'));
        } else {
          element.dispatchEvent(new CustomEvent('swipeleft'));
        }
      }

      if (deltaTime < 500 && Math.abs(deltaY) > 50) {
        if (deltaY > 0) {
          element.dispatchEvent(new CustomEvent('swipedown'));
        } else {
          element.dispatchEvent(new CustomEvent('swipeup'));
        }
      }

      // Detect long press
      if (deltaTime > 500 && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
        element.dispatchEvent(new CustomEvent('longpress'));
      }
    };

    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [state.touchSupport]);

  /**
   * Monitor battery status
   */
  const monitorBattery = useCallback(async () => {
    if (!('getBattery' in navigator)) return;

    try {
      const battery = await (navigator as any).getBattery();
      
      setState(prev => ({
        ...prev,
        batteryLevel: battery.level
      }));

      battery.addEventListener('levelchange', () => {
        setState(prev => ({
          ...prev,
          batteryLevel: battery.level
        }));
      });
    } catch (error) {
      console.error('Battery monitoring failed:', error);
    }
  }, []);

  /**
   * Monitor network connection
   */
  const monitorConnection = useCallback(() => {
    if (!('connection' in navigator)) return;

    const connection = (navigator as any).connection;
    
    setState(prev => ({
      ...prev,
      connectionType: connection.effectiveType
    }));

    connection.addEventListener('change', () => {
      setState(prev => ({
        ...prev,
        connectionType: connection.effectiveType
      }));
    });
  }, []);

  // Initialize mobile optimization
  useEffect(() => {
    updateMobileState();
    optimizeForMobile();
    monitorBattery();
    monitorConnection();

    // Event listeners
    window.addEventListener('resize', updateMobileState);
    window.addEventListener('orientationchange', updateMobileState);
    window.addEventListener('online', updateMobileState);
    window.addEventListener('offline', updateMobileState);

    return () => {
      window.removeEventListener('resize', updateMobileState);
      window.removeEventListener('orientationchange', updateMobileState);
      window.removeEventListener('online', updateMobileState);
      window.removeEventListener('offline', updateMobileState);
      releaseWakeLock();
    };
  }, [updateMobileState, optimizeForMobile, monitorBattery, monitorConnection, releaseWakeLock]);

  const actions: MobileOptimizationActions = {
    installPWA,
    enableFullscreen,
    exitFullscreen,
    vibrate,
    shareContent,
    requestWakeLock,
    releaseWakeLock,
    optimizeForMobile,
    handleTouchGestures
  };

  return {
    ...state,
    actions
  };
};
