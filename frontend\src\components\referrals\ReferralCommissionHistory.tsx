import React, { useState, useEffect } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Badge,
  Flex,
  Spinner,
  useToast,
  useColorModeValue,
  Button,
  HStack,
  VStack,
  Heading,
  Card,
  CardBody,
  Select,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Icon,
  Divider,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription
} from '@chakra-ui/react';
import Pagination from '../common/Pagination';
import { FaCoins, FaExchangeAlt, FaFilter } from 'react-icons/fa';
import { userService } from '../../services/api';
import { formatDate, formatCurrency } from '../../utils/formatters';
import { useTranslation } from 'react-i18next';

interface Commission {
  id: string;
  amount: number;
  asset: string;
  type: string;
  status: string;
  description: string;
  metadata?: any;
  user?: {
    firstName: string;
    lastName: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface CommissionResponse {
  success: boolean;
  data: Commission[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  message: string;
}

const ReferralCommissionHistory: React.FC = () => {
  const [commissions, setCommissions] = useState<Commission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [currencyFilter, setCurrencyFilter] = useState<string>('');
  const toast = useToast();
  const { t } = useTranslation();

  // Colors
  const bgColor = useColorModeValue('white', '#1E2329');
  const cardBgColor = useColorModeValue('gray.50', '#0B0E11');
  const borderColor = useColorModeValue('gray.200', '#2B3139');
  const textColor = useColorModeValue('gray.800', '#EAECEF');
  const secondaryTextColor = useColorModeValue('gray.600', '#848E9C');
  const accentColor = '#F0B90B';

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchCommissions(1);
  }, [statusFilter, currencyFilter]);

  const fetchCommissions = async (page: number) => {
    setLoading(true);
    setError(null);

    try {
      const params: any = { page, limit: 10 };
      if (statusFilter) params.status = statusFilter;
      if (currencyFilter) params.type = currencyFilter; // Use 'type' instead of 'currency'

      const response = await userService.getReferralCommissions(params);
      const data: CommissionResponse = response.data;

      if (data.success) {
        setCommissions(data.data);
        // Update pagination
        setTotalPages(data.pagination.pages);
        setCurrentPage(data.pagination.page);
      } else {
        throw new Error(data.message || 'Failed to fetch commissions');
      }
    } catch (err: any) {
      console.error('Error fetching commission history:', err);
      const errorMessage = err.response?.data?.message || err.message || 'Failed to load commission history. Please try again.';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (nextPage: number) => {
    fetchCommissions(nextPage);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
      case 'completed':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'rejected':
      case 'failed':
        return 'red';
      default:
        return 'gray';
    }
  };

  return (
    <Box>
      <Heading size="md" mb={4} color={textColor}>
        {t('referrals.commissionHistory', 'Commission History')}
      </Heading>

      {/* Commission Summary */}
      {commissions.length > 0 && (
        <Card bg={cardBgColor} boxShadow="sm" borderRadius="md" mb={6}>
          <CardBody>
            <HStack spacing={4}>
              <Icon as={FaCoins} boxSize={8} color={accentColor} />
              <Stat>
                <StatLabel>{t('referrals.commissionHistory', 'Commission History')}</StatLabel>
                <StatNumber fontSize="xl">{commissions.length}</StatNumber>
                <StatHelpText>
                  {t('referrals.totalCommissions', 'Total commission records')}
                </StatHelpText>
              </Stat>
            </HStack>
          </CardBody>
        </Card>
      )}

      {/* Filters */}
      <Flex mb={4} gap={4} direction={{ base: 'column', md: 'row' }}>
        <Box flex="1">
          <Select
            id="status-filter"
            name="status-filter"
            placeholder={t('common.filterByStatus', 'Filter by status')}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            bg={bgColor}
            borderColor={borderColor}
            color={textColor}
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="completed">Completed</option>
            <option value="rejected">Rejected</option>
            <option value="failed">Failed</option>
          </Select>
        </Box>
        <Box flex="1">
          <Select
            id="type-filter"
            name="type-filter"
            placeholder={t('common.filterByType', 'Filter by type')}
            value={currencyFilter}
            onChange={(e) => setCurrencyFilter(e.target.value)}
            bg={bgColor}
            borderColor={borderColor}
            color={textColor}
          >
            <option value="">All Types</option>
            <option value="commission">Commission</option>
            <option value="referral_commission">Referral Commission</option>
            <option value="platform_commission">Platform Commission</option>
          </Select>
        </Box>
      </Flex>

      {/* Commission Table */}
      {loading ? (
        <Flex justify="center" align="center" minH="200px">
          <Spinner size="xl" thickness="4px" speed="0.65s" color={accentColor} />
        </Flex>
      ) : error ? (
        <Alert status="error" variant="subtle" borderRadius="md">
          <AlertIcon />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : commissions.length > 0 ? (
        <Box>
          <Box overflowX="auto">
            <Table variant="simple" size="md">
              <Thead>
                <Tr>
                  <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.date', 'Date')}</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.type', 'Type')}</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.amount', 'Amount')}</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.status', 'Status')}</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.description', 'Description')}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {commissions.map((commission) => (
                  <Tr key={commission.id}>
                    <Td color={textColor} borderColor={borderColor}>{formatDate(commission.createdAt)}</Td>
                    <Td color={textColor} borderColor={borderColor}>
                      <Badge colorScheme="blue" variant="subtle">
                        {commission.type}
                      </Badge>
                    </Td>
                    <Td color={textColor} borderColor={borderColor}>
                      {formatCurrency(commission.amount, commission.asset)}
                    </Td>
                    <Td borderColor={borderColor}>
                      <Badge colorScheme={getStatusColor(commission.status)}>
                        {commission.status}
                      </Badge>
                    </Td>
                    <Td color={textColor} borderColor={borderColor} maxW="200px">
                      <Text isTruncated title={commission.description}>
                        {commission.description || t('common.noDescription', 'No description')}
                      </Text>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </Box>
      ) : (
        <Card bg={cardBgColor} p={6} borderRadius="md" textAlign="center">
          <VStack spacing={4}>
            <Icon as={FaExchangeAlt} boxSize={12} color={secondaryTextColor} />
            <Text color={textColor} fontWeight="medium">
              {t('referrals.noCommissionsYet', 'You have no commission history yet')}
            </Text>
            <Text color={secondaryTextColor}>
              {t('referrals.startEarning', 'Start earning by sharing your referral link with friends')}
            </Text>
          </VStack>
        </Card>
      )}
    </Box>
  );
};

export default ReferralCommissionHistory;
