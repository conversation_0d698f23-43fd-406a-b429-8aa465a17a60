import express from 'express';
import {
  getUser<PERSON><PERSON><PERSON>wal<PERSON>dd<PERSON>,
  add<PERSON><PERSON><PERSON><PERSON><PERSON>ddress,
  verify<PERSON><PERSON>drawalAddress,
  update<PERSON><PERSON><PERSON>walAddress,
  deleteWithdrawalAddress,
  setDefaultWithdrawalAddress,
  getWithdrawalAddress,
  resendVerificationCode,
  getSupportedCurrencies
} from '../controllers/walletManagementController';
import { protect } from '../middleware/authMiddleware';
import { cacheMiddleware, clearCache } from '../middleware/cacheMiddleware';
import { wrapController } from '../utils/routeWrapper';
import { sensitiveOperationRateLimit } from '../middleware/rateLimitMiddleware';
import {
  validateWithdrawalAddress,
  validateAddressUpdate,
  validateVerificationCode
} from '../middleware/validationMiddleware';

const router = express.Router();

// All routes are protected and require authentication
router.use(protect);

// @desc    Get supported currencies and networks
// @route   GET /api/wallet-management/supported-currencies
// @access  Private
router.get('/supported-currencies', 
  cacheMiddleware({ 
    keyPrefix: 'api:wallet-management:currencies:',
    ttl: 3600 // Cache for 1 hour
  }), 
  wrapController(getSupportedCurrencies)
);

// @desc    Get all withdrawal addresses for user
// @route   GET /api/wallet-management/addresses
// @access  Private
router.get('/addresses', 
  cacheMiddleware({ 
    keyPrefix: 'api:wallet-management:addresses:',
    keyGenerator: (req) => `${req.user._id}:${req.query.currency || 'all'}`
  }), 
  wrapController(getUserWithdrawalAddresses)
);

// @desc    Add new withdrawal address
// @route   POST /api/wallet-management/addresses
// @access  Private
router.post('/addresses', 
  sensitiveOperationRateLimit,
  validateWithdrawalAddress,
  clearCache('wallet-management:addresses:'),
  wrapController(addWithdrawalAddress)
);

// @desc    Get withdrawal address by ID
// @route   GET /api/wallet-management/addresses/:id
// @access  Private
router.get('/addresses/:id', 
  cacheMiddleware({ 
    keyPrefix: 'api:wallet-management:address:',
    keyGenerator: (req) => `${req.user._id}:${req.params.id}`
  }), 
  wrapController(getWithdrawalAddress)
);

// @desc    Update withdrawal address
// @route   PUT /api/wallet-management/addresses/:id
// @access  Private
router.put('/addresses/:id', 
  sensitiveOperationRateLimit,
  validateAddressUpdate,
  clearCache('wallet-management:addresses:'),
  wrapController(updateWithdrawalAddress)
);

// @desc    Delete withdrawal address
// @route   DELETE /api/wallet-management/addresses/:id
// @access  Private
router.delete('/addresses/:id', 
  sensitiveOperationRateLimit,
  clearCache('wallet-management:addresses:'),
  wrapController(deleteWithdrawalAddress)
);

// @desc    Verify withdrawal address
// @route   POST /api/wallet-management/addresses/:id/verify
// @access  Private
router.post('/addresses/:id/verify', 
  sensitiveOperationRateLimit,
  validateVerificationCode,
  clearCache('wallet-management:addresses:'),
  wrapController(verifyWithdrawalAddress)
);

// @desc    Set default withdrawal address
// @route   POST /api/wallet-management/addresses/:id/set-default
// @access  Private
router.post('/addresses/:id/set-default', 
  sensitiveOperationRateLimit,
  clearCache('wallet-management:addresses:'),
  wrapController(setDefaultWithdrawalAddress)
);

// @desc    Resend verification code
// @route   POST /api/wallet-management/addresses/:id/resend-verification
// @access  Private
router.post('/addresses/:id/resend-verification', 
  sensitiveOperationRateLimit,
  wrapController(resendVerificationCode)
);

export default router;
