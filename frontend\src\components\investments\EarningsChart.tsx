import React, { useState, useEffect } from 'react';
import {
  Box,
  HStack,
  VStack,
  Text,
  Select,
  Button,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Grid,
  useColorModeValue
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';

const MotionBox = motion(Box);

interface EarningsData {
  date: string;
  earnings: number;
  cumulative: number;
  packages: number;
  dailyRate: number;
}

interface ChartData {
  name: string;
  value: number;
  color: string;
}

const EarningsChart: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar'>('area');
  const [earningsData, setEarningsData] = useState<EarningsData[]>([]);
  const [loading, setLoading] = useState(true);

  // Generate mock data for demonstration
  const generateMockData = (days: number): EarningsData[] => {
    const data: EarningsData[] = [];
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    let cumulativeEarnings = 0;
    
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      
      // Simulate daily earnings with some randomness
      const baseEarnings = 45 + Math.random() * 10; // 45-55 USDT daily
      const dailyEarnings = Math.round(baseEarnings * 100) / 100;
      cumulativeEarnings += dailyEarnings;
      
      data.push({
        date: date.toISOString().split('T')[0],
        earnings: dailyEarnings,
        cumulative: Math.round(cumulativeEarnings * 100) / 100,
        packages: Math.floor(Math.random() * 3) + 2, // 2-4 packages
        dailyRate: 1.0 + (Math.random() * 0.4) // 1.0-1.4% rate
      });
    }
    
    return data;
  };

  const fetchEarningsData = async () => {
    setLoading(true);
    
    try {
      // In production, this would fetch from API
      // const response = await fetch(`/api/investment-packages/earnings-history?range=${timeRange}`);
      
      // For now, use mock data
      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
      const mockData = generateMockData(days);
      setEarningsData(mockData);
    } catch (error) {
      console.error('Error fetching earnings data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchEarningsData();
  }, [timeRange]);

  // Calculate statistics
  const totalEarnings = earningsData.reduce((sum, item) => sum + item.earnings, 0);
  const averageDailyEarnings = totalEarnings / earningsData.length || 0;
  const lastWeekEarnings = earningsData.slice(-7).reduce((sum, item) => sum + item.earnings, 0);
  const previousWeekEarnings = earningsData.slice(-14, -7).reduce((sum, item) => sum + item.earnings, 0);
  const weeklyGrowth = previousWeekEarnings > 0 ? ((lastWeekEarnings - previousWeekEarnings) / previousWeekEarnings) * 100 : 0;

  // Prepare chart data
  const chartData = earningsData.map(item => ({
    date: new Date(item.date).toLocaleDateString('tr-TR', { 
      month: 'short', 
      day: 'numeric' 
    }),
    'Günlük Kazanç': item.earnings,
    'Kümülatif Kazanç': item.cumulative,
    'Aktif Paket': item.packages,
    'Faiz Oranı': item.dailyRate
  }));

  // Currency distribution data (mock)
  const currencyData: ChartData[] = [
    { name: 'USDT', value: 65, color: '#26D0CE' },
    { name: 'BTC', value: 20, color: '#F7931A' },
    { name: 'ETH', value: 10, color: '#627EEA' },
    { name: 'BNB', value: 5, color: '#F3BA2F' }
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Box
          bg="gray.800"
          p={3}
          borderRadius="md"
          borderColor="gold.400"
          borderWidth="1px"
          boxShadow="lg"
        >
          <Text color="white" fontWeight="bold" mb={2}>
            {label}
          </Text>
          {payload.map((entry: any, index: number) => (
            <Text key={index} color={entry.color} fontSize="sm">
              {entry.dataKey}: {entry.value.toFixed(2)}
              {entry.dataKey.includes('Kazanç') ? ' USDT' : 
               entry.dataKey === 'Faiz Oranı' ? '%' : ''}
            </Text>
          ))}
        </Box>
      );
    }
    return null;
  };

  const renderChart = () => {
    const commonProps = {
      data: chartData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 }
    };

    switch (chartType) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#4A5568" />
            <XAxis dataKey="date" stroke="#A0AEC0" fontSize={12} />
            <YAxis stroke="#A0AEC0" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Line 
              type="monotone" 
              dataKey="Günlük Kazanç" 
              stroke="#FCD535" 
              strokeWidth={3}
              dot={{ fill: '#FCD535', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#FCD535', strokeWidth: 2 }}
            />
            <Line 
              type="monotone" 
              dataKey="Kümülatif Kazanç" 
              stroke="#48BB78" 
              strokeWidth={2}
              strokeDasharray="5 5"
            />
          </LineChart>
        );
      
      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#4A5568" />
            <XAxis dataKey="date" stroke="#A0AEC0" fontSize={12} />
            <YAxis stroke="#A0AEC0" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="Günlük Kazanç"
              stroke="#FCD535"
              fill="url(#colorEarnings)"
              strokeWidth={2}
            />
            <defs>
              <linearGradient id="colorEarnings" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#FCD535" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#FCD535" stopOpacity={0.1} />
              </linearGradient>
            </defs>
          </AreaChart>
        );
      
      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="#4A5568" />
            <XAxis dataKey="date" stroke="#A0AEC0" fontSize={12} />
            <YAxis stroke="#A0AEC0" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="Günlük Kazanç" fill="#FCD535" radius={[4, 4, 0, 0]} />
          </BarChart>
        );
      
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <Box h="400px" display="flex" alignItems="center" justifyContent="center">
        <Text color="gray.400">Grafik yükleniyor...</Text>
      </Box>
    );
  }

  return (
    <VStack spacing={6} align="stretch">
      {/* Controls */}
      <HStack justify="space-between" wrap="wrap" spacing={4}>
        <HStack spacing={4}>
          <Select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            size="sm"
            maxW="120px"
            bg="gray.700"
            borderColor="gray.600"
            color="white"
            _focus={{ borderColor: 'gold.400' }}
          >
            <option value="7d" style={{ backgroundColor: '#2D3748' }}>7 Gün</option>
            <option value="30d" style={{ backgroundColor: '#2D3748' }}>30 Gün</option>
            <option value="90d" style={{ backgroundColor: '#2D3748' }}>90 Gün</option>
            <option value="1y" style={{ backgroundColor: '#2D3748' }}>1 Yıl</option>
          </Select>

          <Select
            value={chartType}
            onChange={(e) => setChartType(e.target.value as any)}
            size="sm"
            maxW="120px"
            bg="gray.700"
            borderColor="gray.600"
            color="white"
            _focus={{ borderColor: 'gold.400' }}
          >
            <option value="area" style={{ backgroundColor: '#2D3748' }}>Alan</option>
            <option value="line" style={{ backgroundColor: '#2D3748' }}>Çizgi</option>
            <option value="bar" style={{ backgroundColor: '#2D3748' }}>Çubuk</option>
          </Select>
        </HStack>

        <Button
          size="sm"
          colorScheme="blue"
          variant="outline"
          onClick={fetchEarningsData}
        >
          Yenile
        </Button>
      </HStack>

      {/* Statistics */}
      <Grid templateColumns={{ base: '1fr', md: 'repeat(3, 1fr)' }} gap={4}>
        <Stat bg="gray.700" p={4} borderRadius="md" borderColor="gray.600" borderWidth="1px">
          <StatLabel color="gray.400">Toplam Kazanç</StatLabel>
          <StatNumber color="green.400">${totalEarnings.toFixed(2)}</StatNumber>
          <StatHelpText color="gray.500">
            Son {timeRange === '7d' ? '7' : timeRange === '30d' ? '30' : timeRange === '90d' ? '90' : '365'} gün
          </StatHelpText>
        </Stat>

        <Stat bg="gray.700" p={4} borderRadius="md" borderColor="gray.600" borderWidth="1px">
          <StatLabel color="gray.400">Ortalama Günlük</StatLabel>
          <StatNumber color="blue.400">${averageDailyEarnings.toFixed(2)}</StatNumber>
          <StatHelpText color="gray.500">
            Günlük ortalama kazanç
          </StatHelpText>
        </Stat>

        <Stat bg="gray.700" p={4} borderRadius="md" borderColor="gray.600" borderWidth="1px">
          <StatLabel color="gray.400">Haftalık Büyüme</StatLabel>
          <StatNumber color={weeklyGrowth >= 0 ? "green.400" : "red.400"}>
            <StatArrow type={weeklyGrowth >= 0 ? "increase" : "decrease"} />
            %{Math.abs(weeklyGrowth).toFixed(2)}
          </StatNumber>
          <StatHelpText color="gray.500">
            Önceki haftaya göre
          </StatHelpText>
        </Stat>
      </Grid>

      {/* Main Chart */}
      <Box h="400px" bg="gray.700" p={4} borderRadius="md" borderColor="gray.600" borderWidth="1px">
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </Box>

      {/* Currency Distribution */}
      <Grid templateColumns={{ base: '1fr', lg: '2fr 1fr' }} gap={6}>
        <Box bg="gray.700" p={4} borderRadius="md" borderColor="gray.600" borderWidth="1px">
          <Text fontSize="md" fontWeight="bold" color="white" mb={4}>
            📊 Para Birimi Dağılımı
          </Text>
          <Box h="200px">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={currencyData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: %${value}`}
                >
                  {currencyData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value) => [`%${value}`, 'Oran']}
                  contentStyle={{
                    backgroundColor: '#2D3748',
                    border: '1px solid #FCD535',
                    borderRadius: '8px',
                    color: 'white'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </Box>
        </Box>

        <VStack spacing={4} align="stretch">
          {currencyData.map((currency, index) => (
            <MotionBox
              key={currency.name}
              bg="gray.700"
              p={3}
              borderRadius="md"
              borderColor="gray.600"
              borderWidth="1px"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <HStack justify="space-between">
                <HStack spacing={3}>
                  <Box w={3} h={3} bg={currency.color} borderRadius="full" />
                  <Text color="white" fontWeight="bold">{currency.name}</Text>
                </HStack>
                <Text color="gray.400">%{currency.value}</Text>
              </HStack>
            </MotionBox>
          ))}
        </VStack>
      </Grid>
    </VStack>
  );
};

export default EarningsChart;
