import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { AppError } from '../utils/AppError';

/**
 * Schema for deposit transaction
 */
export const depositSchema = Joi.object({
  token: Joi.string().required().messages({
    'any.required': 'Token is required'
  }),
  amount: Joi.number().positive().required().messages({
    'number.base': 'Amount must be a number',
    'number.positive': 'Amount must be positive',
    'any.required': 'Amount is required'
  }),
  mode: Joi.string().valid('commission', 'interest').required().messages({
    'any.only': 'Mode must be either "commission" or "interest"',
    'any.required': 'Mode is required'
  })
});

/**
 * Schema for withdrawal transaction
 */
export const withdrawalSchema = Joi.object({
  token: Joi.string().required().messages({
    'any.required': 'Token is required'
  }),
  amount: Joi.number().positive().required().messages({
    'number.base': 'Amount must be a number',
    'number.positive': 'Amount must be positive',
    'any.required': 'Amount is required'
  })
});

/**
 * Schema for toggling yield mode
 */
export const toggleModeSchema = Joi.object({
  token: Joi.string().required().messages({
    'any.required': 'Token is required'
  })
});

/**
 * Middleware to validate deposit
 */
export const validateDeposit = (req: Request, res: Response, next: NextFunction) => {
  const { error } = depositSchema.validate(req.body, { abortEarly: false });
  
  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join(', ');
    return next(new AppError(errorMessage, 400));
  }
  
  next();
};

/**
 * Middleware to validate withdrawal
 */
export const validateWithdrawal = (req: Request, res: Response, next: NextFunction) => {
  const { error } = withdrawalSchema.validate(req.body, { abortEarly: false });
  
  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join(', ');
    return next(new AppError(errorMessage, 400));
  }
  
  next();
};

/**
 * Middleware to validate toggle mode
 */
export const validateToggleMode = (req: Request, res: Response, next: NextFunction) => {
  const { error } = toggleModeSchema.validate(req.body, { abortEarly: false });
  
  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join(', ');
    return next(new AppError(errorMessage, 400));
  }
  
  next();
};
