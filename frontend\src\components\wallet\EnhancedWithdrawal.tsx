import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  Icon,
  useDisclosure,
  useToast,
  Card,
  CardBody,
  CardHeader,
  FormControl,
  FormLabel,
  Select,
  Input,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Alert,
  AlertIcon,
  Badge,
  Divider,
  Spinner,
  Center,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText
} from '@chakra-ui/react';
import {
  FaArrowUp,
  FaWallet,
  FaCalculator,
  FaExclamationTriangle,
  FaInfoCircle
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { 
  walletManagementService, 
  enhancedWithdrawalService,
  WithdrawalAddress,
  FeeEstimation,
  WithdrawalLimits
} from '../../services/walletManagementService';
import useWallet from '../../hooks/useWallet';

const EnhancedWithdrawal: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const { wallet, fetchWallet } = useWallet();

  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [selectedAddressId, setSelectedAddressId] = useState('');
  const [amount, setAmount] = useState<number>(0);
  const [twoFactorCode, setTwoFactorCode] = useState('');
  
  const [addresses, setAddresses] = useState<WithdrawalAddress[]>([]);
  const [feeEstimation, setFeeEstimation] = useState<FeeEstimation | null>(null);
  const [limits, setLimits] = useState<WithdrawalLimits | null>(null);
  
  const [loading, setLoading] = useState(false);
  const [loadingAddresses, setLoadingAddresses] = useState(false);
  const [loadingEstimation, setLoadingEstimation] = useState(false);

  // Available currencies from wallet
  const availableCurrencies = wallet?.assets?.filter(asset => asset.balance > 0) || [];

  // Fetch addresses when currency changes
  useEffect(() => {
    if (selectedCurrency) {
      fetchAddresses();
      fetchLimits();
    } else {
      setAddresses([]);
      setSelectedAddressId('');
    }
  }, [selectedCurrency]);

  // Estimate fee when amount or currency changes
  useEffect(() => {
    if (selectedCurrency && amount > 0) {
      estimateFee();
    } else {
      setFeeEstimation(null);
    }
  }, [selectedCurrency, amount]);

  // Fetch withdrawal addresses
  const fetchAddresses = async () => {
    if (!selectedCurrency) return;

    setLoadingAddresses(true);
    try {
      const response = await walletManagementService.getUserAddresses(selectedCurrency);
      const verifiedAddresses = response.data.filter(addr => addr.isVerified);
      setAddresses(verifiedAddresses);
      
      // Auto-select default address
      const defaultAddress = verifiedAddresses.find(addr => addr.isDefault);
      if (defaultAddress) {
        setSelectedAddressId(defaultAddress._id);
      } else if (verifiedAddresses.length > 0) {
        setSelectedAddressId(verifiedAddresses[0]._id);
      }
    } catch (error: any) {
      toast({
        title: t('Error'),
        description: error.message || t('Failed to fetch withdrawal addresses'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoadingAddresses(false);
    }
  };

  // Fetch withdrawal limits
  const fetchLimits = async () => {
    if (!selectedCurrency) return;

    try {
      const response = await enhancedWithdrawalService.getWithdrawalLimits(selectedCurrency);
      setLimits(response.data as WithdrawalLimits);
    } catch (error) {
      console.error('Failed to fetch limits:', error);
    }
  };

  // Estimate withdrawal fee
  const estimateFee = async () => {
    if (!selectedCurrency || amount <= 0) return;

    setLoadingEstimation(true);
    try {
      const response = await enhancedWithdrawalService.estimateWithdrawalFee(amount, selectedCurrency);
      setFeeEstimation(response.data);
    } catch (error: any) {
      console.error('Failed to estimate fee:', error);
    } finally {
      setLoadingEstimation(false);
    }
  };

  // Handle withdrawal submission
  const handleWithdrawal = async () => {
    if (!selectedCurrency || !selectedAddressId || amount <= 0) {
      toast({
        title: t('Error'),
        description: t('Please fill in all required fields'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    setLoading(true);
    try {
      const response = await enhancedWithdrawalService.createWithdrawal({
        addressId: selectedAddressId,
        amount,
        currency: selectedCurrency,
        twoFactorCode: twoFactorCode || undefined
      });

      toast({
        title: t('Success'),
        description: t('Withdrawal request submitted successfully'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Reset form
      setAmount(0);
      setTwoFactorCode('');
      setFeeEstimation(null);
      
      // Refresh wallet data
      fetchWallet();

    } catch (error: any) {
      toast({
        title: t('Withdrawal Failed'),
        description: error.message || t('Failed to submit withdrawal request'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Get selected asset balance
  const selectedAsset = availableCurrencies.find(asset => asset.symbol === selectedCurrency);
  const availableBalance = selectedAsset?.balance || 0;

  // Get selected address
  const selectedAddress = addresses.find(addr => addr._id === selectedAddressId);

  return (
    <Container maxW="4xl" py={8}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <VStack spacing={2} align="center">
          <Icon as={FaArrowUp} boxSize={12} color="blue.500" />
          <Heading size="lg" textAlign="center">
            {t('Withdraw Cryptocurrency')}
          </Heading>
          <Text color="gray.600" textAlign="center">
            {t('Withdraw your funds to your verified wallet addresses')}
          </Text>
        </VStack>

        <Card>
          <CardHeader>
            <Heading size="md">{t('Withdrawal Details')}</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={6} align="stretch">
              {/* Currency Selection */}
              <FormControl>
                <FormLabel>{t('Select Currency')}</FormLabel>
                <Select
                  placeholder={t('Choose currency to withdraw')}
                  value={selectedCurrency}
                  onChange={(e) => setSelectedCurrency(e.target.value)}
                >
                  {availableCurrencies.map((asset) => (
                    <option key={asset.symbol} value={asset.symbol}>
                      {asset.symbol} - {t('Available')}: {asset.balance.toFixed(6)}
                    </option>
                  ))}
                </Select>
              </FormControl>

              {/* Address Selection */}
              {selectedCurrency && (
                <FormControl>
                  <FormLabel>{t('Withdrawal Address')}</FormLabel>
                  {loadingAddresses ? (
                    <Center py={4}>
                      <Spinner size="sm" />
                    </Center>
                  ) : addresses.length === 0 ? (
                    <Alert status="warning">
                      <AlertIcon />
                      <Text>
                        {t('No verified addresses found for')} {selectedCurrency}. 
                        {t('Please add and verify an address first.')}
                      </Text>
                    </Alert>
                  ) : (
                    <Select
                      value={selectedAddressId}
                      onChange={(e) => setSelectedAddressId(e.target.value)}
                    >
                      {addresses.map((address) => (
                        <option key={address._id} value={address._id}>
                          {address.label} - {address.formattedAddress}
                          {address.isDefault ? ` (${t('Default')})` : ''}
                        </option>
                      ))}
                    </Select>
                  )}
                </FormControl>
              )}

              {/* Amount Input */}
              {selectedCurrency && (
                <FormControl>
                  <FormLabel>
                    {t('Amount')} ({selectedCurrency})
                  </FormLabel>
                  <NumberInput
                    value={amount}
                    onChange={(_, value) => setAmount(value || 0)}
                    min={0}
                    max={availableBalance}
                    precision={6}
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                  <HStack justify="space-between" mt={2}>
                    <Text fontSize="sm" color="gray.600">
                      {t('Available')}: {availableBalance.toFixed(6)} {selectedCurrency}
                    </Text>
                    <Button
                      size="xs"
                      variant="link"
                      onClick={() => setAmount(availableBalance)}
                    >
                      {t('Max')}
                    </Button>
                  </HStack>
                </FormControl>
              )}

              {/* 2FA Code */}
              <FormControl>
                <FormLabel>{t('Two-Factor Authentication Code')} ({t('Optional')})</FormLabel>
                <Input
                  placeholder={t('Enter 2FA code if enabled')}
                  value={twoFactorCode}
                  onChange={(e) => setTwoFactorCode(e.target.value)}
                  maxLength={6}
                />
              </FormControl>
            </VStack>
          </CardBody>
        </Card>

        {/* Fee Estimation */}
        {feeEstimation && (
          <Card>
            <CardHeader>
              <HStack spacing={2}>
                <Icon as={FaCalculator} color="green.500" />
                <Heading size="md">{t('Fee Estimation')}</Heading>
              </HStack>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <HStack justify="space-between">
                  <Text>{t('Withdrawal Amount')}:</Text>
                  <Text fontWeight="bold">
                    {feeEstimation.originalAmount} {selectedCurrency}
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text>{t('Network Fee')} ({feeEstimation.feePercentage}%):</Text>
                  <Text color="red.500">
                    -{feeEstimation.withdrawalFee} {selectedCurrency}
                  </Text>
                </HStack>
                <Divider />
                <HStack justify="space-between">
                  <Text fontWeight="bold">{t('You will receive')}:</Text>
                  <Text fontWeight="bold" fontSize="lg" color="green.500">
                    {feeEstimation.netAmount} {selectedCurrency}
                  </Text>
                </HStack>
              </VStack>
            </CardBody>
          </Card>
        )}

        {/* Limits Information */}
        {limits && (
          <Card>
            <CardHeader>
              <HStack spacing={2}>
                <Icon as={FaInfoCircle} color="blue.500" />
                <Heading size="md">{t('Withdrawal Limits')}</Heading>
              </HStack>
            </CardHeader>
            <CardBody>
              <HStack spacing={6} wrap="wrap">
                <Stat>
                  <StatLabel>{t('Minimum')}</StatLabel>
                  <StatNumber fontSize="md">
                    {limits.minimum} {selectedCurrency}
                  </StatNumber>
                </Stat>
                <Stat>
                  <StatLabel>{t('Maximum')}</StatLabel>
                  <StatNumber fontSize="md">
                    {limits.maximum} {selectedCurrency}
                  </StatNumber>
                </Stat>
                <Stat>
                  <StatLabel>{t('Daily Limit')}</StatLabel>
                  <StatNumber fontSize="md">
                    {limits.dailyLimit} {selectedCurrency}
                  </StatNumber>
                </Stat>
                <Stat>
                  <StatLabel>{t('Processing Time')}</StatLabel>
                  <StatNumber fontSize="md">{limits.estimatedTime}</StatNumber>
                </Stat>
              </HStack>
            </CardBody>
          </Card>
        )}

        {/* Selected Address Info */}
        {selectedAddress && (
          <Card>
            <CardHeader>
              <HStack spacing={2}>
                <Icon as={FaWallet} color="purple.500" />
                <Heading size="md">{t('Destination Address')}</Heading>
              </HStack>
            </CardHeader>
            <CardBody>
              <VStack spacing={3} align="stretch">
                <HStack justify="space-between">
                  <Text>{t('Label')}:</Text>
                  <Text fontWeight="bold">{selectedAddress.label}</Text>
                </HStack>
                <HStack justify="space-between">
                  <Text>{t('Address')}:</Text>
                  <Text fontFamily="mono" fontSize="sm">
                    {selectedAddress.formattedAddress}
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text>{t('Network')}:</Text>
                  <Badge variant="outline">{selectedAddress.network}</Badge>
                </HStack>
                {selectedAddress.isDefault && (
                  <HStack justify="space-between">
                    <Text>{t('Status')}:</Text>
                    <Badge colorScheme="blue">{t('Default Address')}</Badge>
                  </HStack>
                )}
              </VStack>
            </CardBody>
          </Card>
        )}

        {/* Submit Button */}
        <Button
          colorScheme="blue"
          size="lg"
          leftIcon={<FaArrowUp />}
          onClick={handleWithdrawal}
          isLoading={loading}
          loadingText={t('Processing...')}
          disabled={
            !selectedCurrency || 
            !selectedAddressId || 
            amount <= 0 || 
            !feeEstimation ||
            (limits && amount < limits.minimum) ||
            (limits && amount > limits.maximum)
          }
        >
          {t('Submit Withdrawal Request')}
        </Button>

        {/* Important Notice */}
        <Alert status="warning" borderRadius="md">
          <AlertIcon />
          <VStack align="start" spacing={1} flex={1}>
            <Text fontWeight="semibold">{t('Important Notice')}</Text>
            <Text fontSize="sm">
              {t('Please double-check all details before submitting. Withdrawal requests cannot be cancelled once processed. Processing time may vary depending on network conditions.')}
            </Text>
          </VStack>
        </Alert>
      </VStack>
    </Container>
  );
};

export default EnhancedWithdrawal;
