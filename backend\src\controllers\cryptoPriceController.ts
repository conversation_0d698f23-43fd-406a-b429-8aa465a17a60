import { Request, Response } from 'express';
import { cryptoApiService } from '../services/cryptoApiService';
import { cryptoDataCollectionService } from '../services/cryptoDataCollectionService';
import logger from '../utils/logger';

/**
 * @desc    Get current price for a specific cryptocurrency
 * @route   GET /api/crypto/price/:symbol
 * @access  Public
 */
export const getCryptoPrice = async (req: Request, res: Response): Promise<void> => {
  try {
    const { symbol } = req.params;
    
    if (!symbol) {
      res.status(400).json({
        status: 'error',
        message: 'Cryptocurrency symbol is required'
      });
      return;
    }

    const normalizedSymbol = symbol.toUpperCase();
    
    // Get price from crypto API service
    const exchangeRate = await cryptoApiService.getExchangeRate(normalizedSymbol, 'USDT');
    
    res.json({
      status: 'success',
      data: {
        symbol: normalizedSymbol,
        price: exchangeRate.rate,
        currency: 'USD',
        source: exchangeRate.source,
        timestamp: exchangeRate.timestamp
      }
    });

  } catch (error: any) {
    logger.error(`Error fetching price for ${req.params.symbol}:`, error);
    
    // Return fallback prices for development/testing
    const fallbackPrices: {[key: string]: number} = {
      'BTC': 106238,
      'ETH': 2615,
      'USDT': 1,
      'BNB': 667.21,
      'SOL': 153.01,
      'TRX': 0.27,
      'DOGE': 0.2
    };
    
    const symbol = req.params.symbol?.toUpperCase();
    const fallbackPrice = fallbackPrices[symbol] || 1;
    
    logger.warn(`Using fallback price for ${symbol}: $${fallbackPrice}`);
    
    res.json({
      status: 'success',
      data: {
        symbol: symbol,
        price: fallbackPrice,
        currency: 'USD',
        source: 'fallback',
        timestamp: new Date(),
        note: 'Fallback price used due to API error'
      }
    });
  }
};

/**
 * @desc    Get current prices for all supported cryptocurrencies
 * @route   GET /api/crypto/prices
 * @access  Public
 */
export const getAllCryptoPrices = async (req: Request, res: Response): Promise<void> => {
  try {
    const prices = await cryptoApiService.getAllPrices();
    
    res.json({
      status: 'success',
      data: prices,
      timestamp: new Date()
    });

  } catch (error: any) {
    logger.error('Error fetching all crypto prices:', error);
    
    // Return fallback prices
    const fallbackPrices = {
      'BTC': 106238,
      'ETH': 2615,
      'USDT': 1,
      'BNB': 667.21,
      'SOL': 153.01,
      'TRX': 0.27,
      'DOGE': 0.2
    };
    
    res.json({
      status: 'success',
      data: fallbackPrices,
      timestamp: new Date(),
      note: 'Fallback prices used due to API error'
    });
  }
};

/**
 * @desc    Convert amount from one cryptocurrency to USD
 * @route   GET /api/crypto/convert/:symbol/:amount
 * @access  Public
 */
export const convertToUSD = async (req: Request, res: Response): Promise<void> => {
  try {
    const { symbol, amount } = req.params;
    
    if (!symbol || !amount) {
      res.status(400).json({
        status: 'error',
        message: 'Symbol and amount are required'
      });
      return;
    }

    const normalizedSymbol = symbol.toUpperCase();
    const numericAmount = parseFloat(amount);
    
    if (isNaN(numericAmount) || numericAmount <= 0) {
      res.status(400).json({
        status: 'error',
        message: 'Amount must be a valid positive number'
      });
      return;
    }

    // Get exchange rate
    const exchangeRate = await cryptoApiService.getExchangeRate(normalizedSymbol, 'USDT');
    const usdValue = numericAmount * exchangeRate.rate;
    
    res.json({
      status: 'success',
      data: {
        fromSymbol: normalizedSymbol,
        fromAmount: numericAmount,
        toSymbol: 'USD',
        toAmount: usdValue,
        exchangeRate: exchangeRate.rate,
        source: exchangeRate.source,
        timestamp: exchangeRate.timestamp
      }
    });

  } catch (error: any) {
    logger.error(`Error converting ${req.params.symbol} to USD:`, error);
    
    res.status(500).json({
      status: 'error',
      message: 'Failed to convert cryptocurrency to USD',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Check if amount meets minimum USD requirement
 * @route   GET /api/crypto/check-minimum/:symbol/:amount
 * @access  Public
 */
export const checkMinimumUSD = async (req: Request, res: Response): Promise<void> => {
  try {
    const { symbol, amount } = req.params;
    const minimumUSD = parseFloat(req.query.minimum as string) || 50;
    
    if (!symbol || !amount) {
      res.status(400).json({
        status: 'error',
        message: 'Symbol and amount are required'
      });
      return;
    }

    const normalizedSymbol = symbol.toUpperCase();
    const numericAmount = parseFloat(amount);
    
    if (isNaN(numericAmount) || numericAmount <= 0) {
      res.status(400).json({
        status: 'error',
        message: 'Amount must be a valid positive number'
      });
      return;
    }

    // Use the crypto API service's checkMinimumWithdrawal method
    const result = await cryptoApiService.checkMinimumWithdrawal(numericAmount, normalizedSymbol);
    
    res.json({
      status: 'success',
      data: {
        symbol: normalizedSymbol,
        amount: numericAmount,
        usdValue: result.usdtValue,
        minimumRequired: result.minimumRequired,
        meetsMinimum: result.meetsMinimum,
        timestamp: new Date()
      }
    });

  } catch (error: any) {
    logger.error(`Error checking minimum USD for ${req.params.symbol}:`, error);
    
    res.status(500).json({
      status: 'error',
      message: 'Failed to check minimum USD requirement',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get price history for a specific cryptocurrency
 * @route   GET /api/crypto/history/:symbol
 * @access  Public
 */
export const getCryptoPriceHistory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { symbol } = req.params;
    const { startDate, endDate, limit } = req.query;

    if (!symbol) {
      res.status(400).json({
        status: 'error',
        message: 'Cryptocurrency symbol is required'
      });
      return;
    }

    const normalizedSymbol = symbol.toUpperCase();

    // Parse query parameters
    const start = startDate ? new Date(startDate as string) : undefined;
    const end = endDate ? new Date(endDate as string) : undefined;
    const limitNum = limit ? parseInt(limit as string, 10) : 100;

    // Validate dates
    if (start && isNaN(start.getTime())) {
      res.status(400).json({
        status: 'error',
        message: 'Invalid start date format'
      });
      return;
    }

    if (end && isNaN(end.getTime())) {
      res.status(400).json({
        status: 'error',
        message: 'Invalid end date format'
      });
      return;
    }

    // Get price history from database
    const history = await cryptoDataCollectionService.getPriceHistory(
      normalizedSymbol,
      start,
      end,
      limitNum
    );

    res.json({
      status: 'success',
      data: {
        symbol: normalizedSymbol,
        history,
        count: history.length,
        startDate: start,
        endDate: end,
        limit: limitNum
      },
      timestamp: new Date()
    });

  } catch (error: any) {
    logger.error(`Error fetching price history for ${req.params.symbol}:`, error);

    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch price history',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get latest prices from database
 * @route   GET /api/crypto/latest-prices
 * @access  Public
 */
export const getLatestPricesFromDB = async (req: Request, res: Response): Promise<void> => {
  try {
    const latestPrices = await cryptoDataCollectionService.getAllLatestPricesFromDB();

    res.json({
      status: 'success',
      data: latestPrices,
      count: latestPrices.length,
      timestamp: new Date()
    });

  } catch (error: any) {
    logger.error('Error fetching latest prices from database:', error);

    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch latest prices from database',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get latest price for a specific cryptocurrency from database
 * @route   GET /api/crypto/latest/:symbol
 * @access  Public
 */
export const getLatestPriceFromDB = async (req: Request, res: Response): Promise<void> => {
  try {
    const { symbol } = req.params;

    if (!symbol) {
      res.status(400).json({
        status: 'error',
        message: 'Cryptocurrency symbol is required'
      });
      return;
    }

    const normalizedSymbol = symbol.toUpperCase();
    const latestPrice = await cryptoDataCollectionService.getLatestPriceFromDB(normalizedSymbol);

    if (!latestPrice) {
      res.status(404).json({
        status: 'error',
        message: `No price data found for ${normalizedSymbol}`
      });
      return;
    }

    res.json({
      status: 'success',
      data: latestPrice,
      timestamp: new Date()
    });

  } catch (error: any) {
    logger.error(`Error fetching latest price from database for ${req.params.symbol}:`, error);

    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch latest price from database',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Manually trigger price data collection
 * @route   POST /api/crypto/collect-prices
 * @access  Public (should be protected in production)
 */
export const triggerPriceCollection = async (req: Request, res: Response): Promise<void> => {
  try {
    logger.info('Manual price collection triggered via API');

    const results = await cryptoDataCollectionService.collectAllPriceData();
    const stats = cryptoDataCollectionService.getCollectionStats();

    res.json({
      status: 'success',
      message: 'Price collection completed',
      data: {
        collectedPrices: results,
        stats
      },
      timestamp: new Date()
    });

  } catch (error: any) {
    logger.error('Error during manual price collection:', error);

    res.status(500).json({
      status: 'error',
      message: 'Failed to collect price data',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
