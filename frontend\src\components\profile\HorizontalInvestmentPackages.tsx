import React from 'react';
import {
  Box,
  Text,
  VStack,
  Icon,
  Center
} from '@chakra-ui/react';
import { FaCoins } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

interface HorizontalInvestmentPackagesProps {
  onViewDetails?: (packageId: string) => void;
  onCreateNew?: () => void;
}

const HorizontalInvestmentPackages: React.FC<HorizontalInvestmentPackagesProps> = ({
  onViewDetails,
  onCreateNew
}) => {
  const { t } = useTranslation();

  return (
    <Center py={8}>
      <VStack spacing={4}>
        <Icon as={FaCoins} color="#848E9C" boxSize={16} />
        <Text color="#EAECEF" fontSize="lg" fontWeight="600">
          {t('investments.title', 'Investment Packages')}
        </Text>
        <Text color="#848E9C" fontSize="sm" textAlign="center" maxW="500px">
          Investment packages will be displayed here when connected to the unified transaction system.
          This component will show horizontal scrolling investment cards with earnings and progress tracking.
        </Text>
      </VStack>
    </Center>
  );
};

export default HorizontalInvestmentPackages;
