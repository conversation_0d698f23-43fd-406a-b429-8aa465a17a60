import mongoose, { Document, Schema } from 'mongoose';

export interface IDepositTransaction extends Document {
  userId: mongoose.Types.ObjectId;
  currency: string;
  amount: number;
  walletAddress: string;
  transactionHash: string;
  confirmations: number;
  requiredConfirmations: number;
  status: 'pending' | 'confirmed' | 'failed' | 'expired';
  blockHeight?: number;
  networkFee?: number;
  usdtValue?: number;
  conversionRate?: number;
  createdAt: Date;
  confirmedAt?: Date;
  investmentPackageId?: mongoose.Types.ObjectId;
  autoInvestmentEnabled: boolean;
  notes?: string;

  // Instance methods
  canCreateInvestment(): boolean;
  markAsConfirmed(): Promise<IDepositTransaction>;
  calculateUSDTValue(): Promise<number>;
}

const depositTransactionSchema = new Schema<IDepositTransaction>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    currency: {
      type: String,
      required: [true, 'Currency is required'],
      uppercase: true,
      enum: ['BTC', 'ETH', 'USDT', 'BNB', 'SOL', 'DOGE', 'TRX'],
      index: true
    },
    amount: {
      type: Number,
      required: [true, 'Amount is required'],
      min: [0.000001, 'Amount must be positive'],
      validate: {
        validator: function(value: number) {
          return value > 0;
        },
        message: 'Amount must be positive'
      }
    },
    walletAddress: {
      type: String,
      required: [true, 'Wallet address is required'],
      trim: true,
      index: true
    },
    transactionHash: {
      type: String,
      required: [true, 'Transaction hash is required'],
      trim: true,
      unique: true,
      index: true
    },
    confirmations: {
      type: Number,
      default: 0,
      min: 0
    },
    requiredConfirmations: {
      type: Number,
      default: 3,
      min: 1
    },
    status: {
      type: String,
      enum: ['pending', 'confirmed', 'failed', 'expired'],
      default: 'pending',
      index: true
    },
    blockHeight: {
      type: Number,
      min: 0
    },
    networkFee: {
      type: Number,
      min: 0
    },
    usdtValue: {
      type: Number,
      min: 0
    },
    conversionRate: {
      type: Number,
      min: 0
    },
    confirmedAt: {
      type: Date,
      index: true
    },
    investmentPackageId: {
      type: Schema.Types.ObjectId,
      ref: 'InvestmentPackage',
      index: true
    },
    autoInvestmentEnabled: {
      type: Boolean,
      default: true
    },
    notes: {
      type: String,
      trim: true,
      maxlength: 500
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for performance
depositTransactionSchema.index({ userId: 1, status: 1 });
depositTransactionSchema.index({ currency: 1, status: 1 });
depositTransactionSchema.index({ createdAt: -1 });
depositTransactionSchema.index({ confirmedAt: -1 });

// Virtual for time since creation
depositTransactionSchema.virtual('timeSinceCreation').get(function() {
  return Date.now() - this.createdAt.getTime();
});

// Virtual for confirmation progress
depositTransactionSchema.virtual('confirmationProgress').get(function() {
  return Math.min(100, (this.confirmations / this.requiredConfirmations) * 100);
});

// Instance method to check if can create investment
depositTransactionSchema.methods.canCreateInvestment = function(): boolean {
  return this.status === 'confirmed' &&
         this.autoInvestmentEnabled &&
         !this.investmentPackageId &&
         this.usdtValue &&
         this.usdtValue >= 0.000001; // Minimum amount to prevent abuse
};

// Instance method to mark as confirmed
depositTransactionSchema.methods.markAsConfirmed = async function(): Promise<IDepositTransaction> {
  this.status = 'confirmed';
  this.confirmedAt = new Date();
  return await this.save();
};

// Instance method to calculate USDT value
depositTransactionSchema.methods.calculateUSDTValue = async function(): Promise<number> {
  if (this.currency === 'USDT') {
    return this.amount;
  }

  if (this.usdtValue && this.conversionRate) {
    return this.usdtValue;
  }

  // This would use the crypto API service to get current rate
  // For now, return a calculated value based on stored rate
  return this.amount * (this.conversionRate || 1);
};

// Static methods interface
interface IDepositTransactionModel extends mongoose.Model<IDepositTransaction> {
  getPendingDeposits(): Promise<IDepositTransaction[]>;
  getUserDeposits(userId: string): Promise<IDepositTransaction[]>;
  getConfirmedDepositsWithoutInvestment(): Promise<IDepositTransaction[]>;
  getTotalDepositsByUser(userId: string): Promise<any[]>;
}

// Static method to get pending deposits
depositTransactionSchema.statics.getPendingDeposits = function() {
  return this.find({
    status: 'pending'
  }).populate('userId', 'email firstName lastName');
};

// Static method to get user deposits
depositTransactionSchema.statics.getUserDeposits = function(userId: string) {
  return this.find({ userId })
    .sort({ createdAt: -1 })
    .populate('investmentPackageId');
};

// Static method to get confirmed deposits without investment packages
depositTransactionSchema.statics.getConfirmedDepositsWithoutInvestment = function() {
  return this.find({
    status: 'confirmed',
    autoInvestmentEnabled: true,
    investmentPackageId: { $exists: false }
  }).populate('userId', 'email firstName lastName');
};

// Static method to get total deposits by user
depositTransactionSchema.statics.getTotalDepositsByUser = function(userId: string) {
  return this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId), status: 'confirmed' } },
    {
      $group: {
        _id: '$currency',
        totalAmount: { $sum: '$amount' },
        totalUSDTValue: { $sum: '$usdtValue' },
        count: { $sum: 1 }
      }
    }
  ]);
};

// Pre-save middleware
depositTransactionSchema.pre('save', function(next) {
  // Auto-expire old pending transactions (24 hours)
  const timeSinceCreation = Date.now() - this.createdAt.getTime();
  if (this.status === 'pending' && timeSinceCreation > 24 * 60 * 60 * 1000) {
    this.status = 'expired';
  }
  next();
});

const DepositTransaction = mongoose.model<IDepositTransaction, IDepositTransactionModel>(
  'DepositTransaction',
  depositTransactionSchema
);

export default DepositTransaction;
