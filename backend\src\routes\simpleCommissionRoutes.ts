import express from 'express';
import {
  getCommissionRatesController,
  getUserCommissionEarningsController,
  calculateCommissionPreview,
  getUserCommissionHistory,
  getCommissionStats,
  updateCommissionRates,
  getAllCommissions
} from '../controllers/simpleCommissionController';
import { protect, admin } from '../middleware/authMiddleware';
import { sensitiveOperationRateLimit } from '../middleware/rateLimitMiddleware';

const router = express.Router();

// General routes (auth required)
router.get('/',
  protect,
  sensitiveOperationRateLimit, // Rate limiting
  getAllCommissions
);

// Public routes (no auth required)
router.get('/rates',
  sensitiveOperationRateLimit, // Rate limiting
  getCommissionRatesController
);

// User routes (auth required)
router.get('/my-earnings',
  protect,
  sensitiveOperationRateLimit, // Rate limiting
  getUserCommissionEarningsController
);

router.get('/my-history',
  protect,
  sensitiveOperationRateLimit, // Rate limiting
  getUserCommissionHistory
);

router.post('/calculate',
  protect,
  sensitiveOperationRateLimit, // Rate limiting
  calculateCommissionPreview
);

// Admin routes (admin auth required)
router.get('/admin/stats',
  protect,
  admin,
  sensitiveOperationRateLimit, // Rate limiting
  getCommissionStats
);

router.put('/admin/rates',
  protect,
  admin,
  sensitiveOperationRateLimit, // Rate limiting
  updateCommissionRates
);

export default router;
