import { metricsMiddleware, metrics } from '../middleware/metricsMiddleware';
import { metricsAggregator } from '../services/metricsAggregator';
import { Application } from 'express';
import { logger } from '../utils/logger';

export function initializeMetrics(app: Application) {
  // Apply metrics middleware to all routes
  app.use(metricsMiddleware);

  // Add metrics endpoint
  app.get('/metrics', async (req, res) => {
    res.set('Content-Type', metrics.contentType);
    try {
      // Force update metrics before serving
      await metricsAggregator.forceUpdate();
      const metricsData = await metrics.metrics();
      res.end(metricsData);
    } catch (error) {
      logger.error('Error serving metrics:', error);
      res.status(500).end();
    }
  });

  // Add health check endpoint
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version
    });
  });

  // Initialize metrics aggregator
  metricsAggregator;

  logger.info('Metrics initialization completed');
}