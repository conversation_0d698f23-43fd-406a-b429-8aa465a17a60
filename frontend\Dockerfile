FROM node:18-alpine AS builder

WORKDIR /app

# Accept build arguments
ARG API_URL
ARG CONTRACT_ADDRESS
ARG INFURA_ID
ARG STORAGE_KEY
ARG SOCKET_URL

# Set environment variables for build
ENV VITE_API_URL=$API_URL
ENV VITE_CONTRACT_ADDRESS=$CONTRACT_ADDRESS
ENV VITE_INFURA_ID=$INFURA_ID
ENV VITE_STORAGE_KEY=$STORAGE_KEY
ENV VITE_SOCKET_URL=$SOCKET_URL

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage with Nginx
FROM nginx:alpine

# Copy built files from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy Nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]