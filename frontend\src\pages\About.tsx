import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  Grid,
  GridItem,
  Image,
  Flex,
  Icon,
  Divider,
  List,
  ListItem,
  ListIcon,
  Button,
  HStack,
} from '@chakra-ui/react';
import { FaShip, FaGlobe, FaChartLine, FaCheckCircle, FaUsers, FaHandshake, FaArrowRight, FaMoneyBillWave } from 'react-icons/fa';
import { Link as RouterLink } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const About = () => {
  const { t } = useTranslation('about');

  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  return (
    <Box bg={bgColor} minH="100vh">
      {/* Hero Section */}
      <Box
        bg={`linear-gradient(rgba(11, 14, 17, 0.8), rgba(11, 14, 17, 0.9)), url('/images/global-trade.jpg')`}
        bgSize="cover"
        bgPosition="center"
        py={20}
      >
        <Container maxW="container.xl">
          <VStack spacing={6} align="center" textAlign="center" maxW="800px" mx="auto">
            <Heading
              as="h1"
              size="2xl"
              color={primaryColor}
              lineHeight="1.2"
            >
              {t('hero.title', 'About Us')}
            </Heading>

            <Text fontSize="xl" color={textColor}>
              {t('hero.description', 'Shipping Finance is an innovative financial platform that offers investors stable and sustainable returns by leveraging opportunities in international trade.')}
            </Text>
          </VStack>
        </Container>
      </Box>

      {/* Company Info Section */}
      <Box py={16}>
        <Container maxW="container.xl">
          <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={10}>
            <GridItem>
              <VStack align="flex-start" spacing={6}>
                <Heading color={textColor}>{t('vision.title', 'Our Vision')}</Heading>
                <Text fontSize="lg" color={secondaryTextColor}>
                  {t('vision.description', 'Our vision at Shipping Finance is to provide our investors with a reliable, transparent, and high-yield investment platform by combining the opportunities offered by international trade with innovations in technology and finance.')}
                </Text>

                <Heading color={textColor} size="md" pt={4}>{t('mission.title', 'Our Mission')}</Heading>
                <Text fontSize="lg" color={secondaryTextColor}>
                  {t('mission.description', 'Our mission is to be a reliable partner in the financial freedom journey of cryptocurrency investors by offering an alternative and sustainable earnings model through our global trade network and professional team.')}
                </Text>

                <Heading color={textColor} size="md" pt={4}>{t('values.title', 'Our Values')}</Heading>
                <List spacing={3}>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    {t('values.transparency', 'Transparency and trust-focused business model')}
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    {t('values.investorSatisfaction', 'Investor satisfaction and sustainable earnings')}
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    {t('values.innovation', 'Innovative and technology-focused approach')}
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    {t('values.internationalStandards', 'Operation management at international standards')}
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    {t('values.socialResponsibility', 'Social and environmental responsibility')}
                  </ListItem>
                </List>
              </VStack>
            </GridItem>

            <GridItem>
              <Box
                bg={cardBgColor}
                p={8}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
                height="100%"
              >
                <VStack spacing={6} align="flex-start">
                  <Heading size="md" color={primaryColor}>{t('businessModel.title', 'Our Business Model')}</Heading>

                  <Text color={textColor}>
                    {t('businessModel.description', 'Shipping Finance operates with a business model based on the principle of sourcing products from low-cost countries (such as China, Egypt) and selling them in markets that offer high demand and price advantages (especially in Europe and other developed regions).')}
                  </Text>

                  <Text color={textColor}>
                    {t('businessModel.returns', 'Through this trade volume, high profitability is achieved and our investors are provided with an average daily return of 1%.')}
                  </Text>

                  <Divider borderColor={borderColor} />

                  <Heading size="md" color={primaryColor}>{t('howItWorks.title', 'How It Works?')}</Heading>

                  <List spacing={4}>
                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaShip} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('howItWorks.productSourcing.title', 'Product Sourcing')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('howItWorks.productSourcing.description', 'Sourcing quality products from low-cost production regions')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>

                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaGlobe} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('howItWorks.logistics.title', 'Logistics and Distribution')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('howItWorks.logistics.description', 'Delivering products to target markets through our global logistics network')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>

                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaChartLine} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('howItWorks.sales.title', 'Sales and Profit')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('howItWorks.sales.description', 'Sales and profit generation through optimal pricing in high-demand regions')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>

                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaUsers} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('howItWorks.investorReturns.title', 'Investor Returns')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('howItWorks.investorReturns.description', 'Sharing the obtained profit with investors at a daily rate of 1%')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>
                  </List>
                </VStack>
              </Box>
            </GridItem>
          </Grid>
        </Container>
      </Box>



      {/* Summary Section */}
      <Box py={16} bg={bgColor}>
        <Container maxW="container.xl">
          <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={10}>
            <GridItem>
              <Box
                bg={cardBgColor}
                p={8}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
                height="100%"
              >
                <VStack spacing={6} align="flex-start">
                  <Heading size="md" color={primaryColor}>{t('summary.title', 'About Us (Summary)')}</Heading>

                  <Text color={textColor}>
                    {t('summary.description1', 'Shipping Finance is an innovative financial platform that brings the power of international trade to investors.')}
                  </Text>

                  <Text color={textColor}>
                    {t('summary.description2', 'Instead of keeping your cryptocurrencies idle on exchanges, we offer the opportunity to grow them through real commercial activities.')}
                  </Text>

                  <Text color={textColor}>
                    {t('summary.description3', 'With our global logistics network and professional trading operations, we take your investments further every day.')}
                  </Text>




                </VStack>
              </Box>
            </GridItem>

            <GridItem>
              <Box
                bg={`linear-gradient(rgba(30, 35, 41, 0.9), rgba(30, 35, 41, 0.95)), url('/images/bitcoin-ship.jpg')`}
                bgSize="cover"
                bgPosition="center"
                p={8}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
                height="100%"
                position="relative"
                overflow="hidden"
                boxShadow="0 10px 30px -5px rgba(0, 0, 0, 0.3)"
                _before={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  bgGradient: "linear(to-r, #F0B90B10, #F0B90B30)",
                  opacity: 0.2,
                  zIndex: 0
                }}
              >
                <VStack spacing={6} position="relative" zIndex={1}>
                  <Heading size="md" color={primaryColor}>{t('whyChooseUs.title', 'Why Choose Us?')}</Heading>

                  <List spacing={4}>
                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaShip} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('whyChooseUs.realTrading.title', 'Real Trading Activities')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('whyChooseUs.realTrading.description', 'Real earnings from international trade')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>

                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaChartLine} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('whyChooseUs.dailyReturns.title', 'Daily 1% Returns')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('whyChooseUs.dailyReturns.description', 'Regular income from your crypto assets every day')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>

                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaHandshake} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('whyChooseUs.transparency.title', 'Transparency')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('whyChooseUs.transparency.description', 'Full transparency in all operations and transactions')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>
                  </List>


                </VStack>
              </Box>
            </GridItem>
          </Grid>
        </Container>
      </Box>


    </Box>
  );
};

export default About;
