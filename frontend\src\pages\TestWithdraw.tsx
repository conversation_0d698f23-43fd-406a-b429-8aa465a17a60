import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  VStack, 
  HStack, 
  Text, 
  useToast,
  useDisclosure,
  Badge,
  Divider
} from '@chakra-ui/react';
import ModernWalletCard from '../components/ModernWalletCard';
import WithdrawModal from '../components/modals/WithdrawModal';

const TestWithdraw: React.FC = () => {
  const toast = useToast();
  const { isOpen: isWithdrawOpen, onOpen: onWithdrawOpen, onClose: onWithdrawClose } = useDisclosure();
  const [selectedCrypto, setSelectedCrypto] = useState<string>('btc');
  const [testResults, setTestResults] = useState<string[]>([]);

  // Test asset data
  const testAsset = {
    symbol: 'BTC',
    balance: 0.05,
    interestBalance: 0.002,
    commissionBalance: 0.001,
    mode: 'commission' as const,
    activePackages: 1,
    isLocked: false,
    daysUntilUnlock: 0
  };

  const addTestResult = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  const handleWithdraw = (currency: string, walletData: any) => {
    console.log('🎯 TestWithdraw: Withdraw called!', { currency, walletData });
    addTestResult(`✅ onWithdraw called for ${currency}`);
    
    setSelectedCrypto(currency.toLowerCase());
    onWithdrawOpen();
    
    toast({
      title: "Withdraw Handler Called",
      description: `onWithdraw triggered for ${currency}`,
      status: "success",
      duration: 3000,
      isClosable: true,
    });
  };

  const handleDeposit = (currency: string, walletData: any) => {
    console.log('🎯 TestWithdraw: Deposit called!', { currency, walletData });
    addTestResult(`✅ onDeposit called for ${currency}`);
    
    toast({
      title: "Deposit Handler Called",
      description: `onDeposit triggered for ${currency}`,
      status: "info",
      duration: 3000,
      isClosable: true,
    });
  };

  const testDirectAPI = async () => {
    try {
      addTestResult('🔍 Testing direct API call...');
      
      // This will test the API directly
      const response = await fetch('/api/wallets/withdrawable-balance/BTC?type=balance', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      const data = await response.json();
      console.log('🔍 Direct API response:', data);
      addTestResult(`📡 API Response: ${response.status} - ${data.status || 'unknown'}`);
      
    } catch (error) {
      console.error('❌ Direct API test failed:', error);
      addTestResult(`❌ API Error: ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Box bg="#0B0E11" minH="100vh" p="20px">
      <VStack spacing="20px" align="stretch" maxW="800px" mx="auto">
        
        {/* Header */}
        <Box color="white" textAlign="center">
          <Text fontSize="24px" fontWeight="bold" mb="10px">
            🧪 Withdraw Button Test Page
          </Text>
          <Text fontSize="14px" opacity="0.7">
            Test withdraw functionality and debug API responses
          </Text>
        </Box>

        {/* Test Controls */}
        <Box bg="#1A1D21" p="20px" borderRadius="12px" border="1px solid #2D3748">
          <Text color="white" fontSize="16px" fontWeight="600" mb="15px">
            🔧 Test Controls
          </Text>
          
          <HStack spacing="10px" mb="15px">
            <Button size="sm" colorScheme="blue" onClick={testDirectAPI}>
              Test Direct API
            </Button>
            <Button size="sm" colorScheme="gray" onClick={clearResults}>
              Clear Results
            </Button>
          </HStack>
          
          <Text color="gray.400" fontSize="12px">
            Check browser console for detailed logs
          </Text>
        </Box>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Box bg="#1A1D21" p="20px" borderRadius="12px" border="1px solid #2D3748">
            <Text color="white" fontSize="16px" fontWeight="600" mb="15px">
              📊 Test Results
            </Text>
            
            <VStack spacing="5px" align="stretch">
              {testResults.map((result, index) => (
                <Text 
                  key={index} 
                  color="gray.300" 
                  fontSize="12px" 
                  fontFamily="mono"
                  bg="#0B0E11"
                  p="8px"
                  borderRadius="6px"
                >
                  {result}
                </Text>
              ))}
            </VStack>
          </Box>
        )}

        {/* Test Case 1: With onWithdraw prop */}
        <Box bg="#1A1D21" p="20px" borderRadius="12px" border="1px solid #2D3748">
          <HStack justify="space-between" align="center" mb="15px">
            <Text color="white" fontSize="16px" fontWeight="600">
              Test Case 1: With onWithdraw Prop
            </Text>
            <Badge colorScheme="green" variant="solid">
              Should call handler
            </Badge>
          </HStack>
          
          <Text color="gray.400" fontSize="14px" mb="15px">
            This should call the onWithdraw handler and open the modal
          </Text>
          
          <ModernWalletCard
            asset={testAsset}
            onWithdraw={handleWithdraw}
            onDeposit={handleDeposit}
          />
        </Box>

        {/* Test Case 2: Without onWithdraw prop */}
        <Box bg="#1A1D21" p="20px" borderRadius="12px" border="1px solid #2D3748">
          <HStack justify="space-between" align="center" mb="15px">
            <Text color="white" fontSize="16px" fontWeight="600">
              Test Case 2: Without onWithdraw Prop
            </Text>
            <Badge colorScheme="orange" variant="solid">
              Should use fallback
            </Badge>
          </HStack>
          
          <Text color="gray.400" fontSize="14px" mb="15px">
            This should open the fallback modal directly
          </Text>
          
          <ModernWalletCard
            asset={testAsset}
            onDeposit={handleDeposit}
            // onWithdraw prop intentionally omitted
          />
        </Box>

        <Divider borderColor="#2D3748" />

        {/* Instructions */}
        <Box bg="#1A1D21" p="20px" borderRadius="12px" border="1px solid #2D3748">
          <Text color="white" fontSize="16px" fontWeight="600" mb="15px">
            📋 Test Instructions
          </Text>
          
          <VStack spacing="10px" align="stretch">
            <Text color="gray.300" fontSize="14px">
              1. Click "Test Direct API" to check backend connectivity
            </Text>
            <Text color="gray.300" fontSize="14px">
              2. Click withdraw button in Test Case 1 (should call handler)
            </Text>
            <Text color="gray.300" fontSize="14px">
              3. Click withdraw button in Test Case 2 (should use fallback)
            </Text>
            <Text color="gray.300" fontSize="14px">
              4. Check console logs and test results above
            </Text>
            <Text color="gray.300" fontSize="14px">
              5. Verify modal opens in both cases
            </Text>
          </VStack>
        </Box>
      </VStack>

      {/* Withdraw Modal */}
      <WithdrawModal
        isOpen={isWithdrawOpen}
        onClose={onWithdrawClose}
        initialCrypto={selectedCrypto}
        initialWithdrawalType="principal"
        onSuccess={() => {
          onWithdrawClose();
          addTestResult('✅ Withdrawal completed successfully');
          toast({
            title: "Withdrawal Successful",
            description: `Your ${selectedCrypto.toUpperCase()} withdrawal has been processed.`,
            status: "success",
            duration: 5000,
            isClosable: true,
          });
        }}
      />
    </Box>
  );
};

export default TestWithdraw;
