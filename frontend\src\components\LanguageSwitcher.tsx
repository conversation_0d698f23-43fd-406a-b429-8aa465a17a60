import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Button,
  Box,
  Text,
  Flex,
  Image,
  Tooltip,
  useBreakpointValue,
  Divider,
  Spinner
} from '@chakra-ui/react';
import { ChevronDownIcon, CheckIcon } from '@chakra-ui/icons';

// Flag images with SVG for better quality - Supporting exactly 3 languages (English, German, French)
const flags = {
  en: '/flags/en.svg',
  de: '/flags/de.svg',
  fr: '/flags/fr.svg',
  tr: '/flags/tr.svg'
};

// Emoji flags as fallback
const emojiFlags = {
  en: '🇬🇧',
  de: '🇩🇪',
  fr: '🇫🇷',
  tr: '🇹🇷'
};

// Language names in their native language
const languageNames = {
  en: 'English',
  de: 'Deutsch',
  fr: 'Français',
  tr: 'Türkçe'
};

// Language names in English (for screen readers and tooltips)
const languageNamesEnglish = {
  en: 'English',
  de: 'German',
  tr: 'Turkish',
  fr: 'French'
};

// Order of languages to display - Priority: English, German, Turkish, French
const languageOrder = ['en', 'de', 'tr', 'fr'];

const LanguageSwitcher: React.FC = () => {
  const { i18n, t } = useTranslation();
  const [imageLoadError, setImageLoadError] = useState<Record<string, boolean>>({});
  const [isChangingLanguage, setIsChangingLanguage] = useState<boolean>(false);
  const isMobile = useBreakpointValue({ base: true, md: false });

  // Check if SVG flags are available
  useEffect(() => {
    const preloadImages = async () => {
      const errors: Record<string, boolean> = {};

      for (const lang of Object.keys(flags)) {
        try {
          const response = await fetch(flags[lang as keyof typeof flags]);
          if (!response.ok) {
            errors[lang] = true;
          }
        } catch (error) {
          errors[lang] = true;
        }
      }

      setImageLoadError(errors);
    };

    preloadImages();
  }, []);

  const changeLanguage = async (lng: string) => {
    // Don't do anything if the language is already selected
    if (i18n.language?.split('-')[0] === lng) {
      return;
    }

    // Show loading state
    setIsChangingLanguage(true);

    try {
      // Save to localStorage for persistence
      localStorage.setItem('i18nextLng', lng);

      // Set language direction (all supported languages are LTR)
      document.documentElement.dir = 'ltr';
      document.documentElement.lang = lng;

      // Change language using i18n
      await i18n.changeLanguage(lng);

      // Hide loading state after successful change
      setTimeout(() => {
        setIsChangingLanguage(false);
      }, 300);

    } catch (error) {
      console.error('Error changing language:', error);

      // Fallback: force reload if language change fails
      localStorage.setItem('i18nextLng', lng);
      window.location.reload();
    }
  };

  // Set initial direction based on current language
  useEffect(() => {
    const currentLang = i18n.language?.split('-')[0] || 'en';
    // All supported languages are LTR
    document.documentElement.dir = 'ltr';
    document.documentElement.lang = currentLang;
  }, [i18n.language]);

  // Get current language or fallback to English
  const currentLang = i18n.language?.split('-')[0] || 'en';

  // Function to render flag (SVG or emoji fallback)
  const renderFlag = (lang: string) => {
    if (imageLoadError[lang]) {
      return (
        <Text fontSize="lg" lineHeight="1">
          {emojiFlags[lang as keyof typeof emojiFlags]}
        </Text>
      );
    }

    return (
      <Box
        width="24px"
        height="16px"
        borderRadius="2px"
        overflow="hidden"
        boxShadow="0 0 1px rgba(0,0,0,0.3)"
      >
        <Image
          src={flags[lang as keyof typeof flags]}
          alt={`${languageNamesEnglish[lang as keyof typeof languageNamesEnglish]} flag`}
          width="100%"
          height="100%"
          objectFit="cover"
          onError={() => {
            setImageLoadError(prev => ({...prev, [lang]: true}));
          }}
        />
      </Box>
    );
  };

  return (
    <Menu placement="bottom-end" autoSelect={false}>
      <Tooltip
        label={t('common.changeLanguage', 'Change language')}
        placement="bottom"
        openDelay={500}
      >
        <MenuButton
          as={Button}
          rightIcon={isChangingLanguage ? <Spinner size="xs" color="#F0B90B" /> : <ChevronDownIcon />}
          bg="#1E2329"
          color="#EAECEF"
          borderColor="#2B3139"
          _hover={{ bg: "#2B3139" }}
          _active={{ bg: "#2B3139" }}
          size={isMobile ? "sm" : "md"}
          aria-label={t('common.changeLanguage', 'Change language')}
          data-testid="language-switcher"
          isDisabled={isChangingLanguage}
        >
          <Flex align="center">
            {renderFlag(currentLang)}
            {!isMobile && (
              <Text fontSize="sm" ml={2} fontWeight="medium">
                {languageNames[currentLang as keyof typeof languageNames] || languageNames.en}
              </Text>
            )}
          </Flex>
        </MenuButton>
      </Tooltip>

      <MenuList
        bg="#1E2329"
        borderColor="#2B3139"
        minWidth="180px"
        zIndex={1000}
        py={2}
      >
        <Text
          px={4}
          py={2}
          fontSize="xs"
          fontWeight="medium"
          color="#848E9C"
          textTransform="uppercase"
        >
          {t('common.selectLanguage', 'Select Language')}
        </Text>
        <Divider borderColor="#2B3139" my={1} />

        {languageOrder.map((lng) => (
          <MenuItem
            key={lng}
            onClick={() => changeLanguage(lng)}
            bg="#1E2329"
            color="#EAECEF"
            _hover={{ bg: "#2B3139" }}
            px={4}
            py={2}
            role="menuitem"
            isDisabled={isChangingLanguage || currentLang === lng}
          >
            <Flex align="center" justify="space-between" width="100%">
              <Flex align="center">
                {renderFlag(lng)}
                <Text ml={3} fontSize="sm">
                  {languageNames[lng as keyof typeof languageNames]}
                </Text>
              </Flex>

              {currentLang === lng && (
                <CheckIcon color="#F0B90B" boxSize={3} />
              )}
            </Flex>
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};

export default LanguageSwitcher;
