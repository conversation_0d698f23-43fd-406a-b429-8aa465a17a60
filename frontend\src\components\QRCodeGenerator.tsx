import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Image,
  Button,
  Select,
  Input,
  FormControl,
  FormLabel,
  Alert,
  AlertIcon,
  Spinner,
  useToast,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Divider,
  Badge,
  IconButton,
  Tooltip,
  useClipboard,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
} from '@chakra-ui/react';
import { 
  FaDownload, 
  FaCopy, 
  FaQrcode, 
  FaExpand,
  FaPalette,
  FaAdjust
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

interface QRCodeGeneratorProps {
  address?: string;
  currency?: string;
  onGenerate?: (qrCode: string) => void;
  showCustomization?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

interface QRCodeOptions {
  format: 'png' | 'svg';
  size: number;
  darkColor: string;
  lightColor: string;
  margin: number;
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H';
}

const QRCodeGenerator: React.FC<QRCodeGeneratorProps> = ({
  address: initialAddress = '',
  currency: initialCurrency = 'BTC',
  onGenerate,
  showCustomization = true,
  size = 'md'
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const [address, setAddress] = useState(initialAddress);
  const [currency, setCurrency] = useState(initialCurrency);
  const [qrCode, setQrCode] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<QRCodeOptions>({
    format: 'png',
    size: 256,
    darkColor: '#000000',
    lightColor: '#FFFFFF',
    margin: 2,
    errorCorrectionLevel: 'M'
  });

  const { hasCopied, onCopy } = useClipboard(address);

  const supportedCurrencies = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA'];
  const errorLevels = [
    { value: 'L', label: 'Low (7%)' },
    { value: 'M', label: 'Medium (15%)' },
    { value: 'Q', label: 'Quartile (25%)' },
    { value: 'H', label: 'High (30%)' }
  ];

  const sizeMap = {
    sm: { width: '200px', qrSize: 200 },
    md: { width: '256px', qrSize: 256 },
    lg: { width: '320px', qrSize: 320 }
  };

  const generateQRCode = async () => {
    if (!address || !currency) {
      toast({
        title: t('qr.error.missingData', 'Missing Data'),
        description: t('qr.error.addressRequired', 'Address and currency are required'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setLoading(true);
    try {
      const params = new URLSearchParams({
        address,
        currency,
        format: options.format,
        size: options.size.toString(),
        darkColor: options.darkColor,
        lightColor: options.lightColor,
        margin: options.margin.toString(),
        errorCorrectionLevel: options.errorCorrectionLevel
      });

      const response = await fetch(`/api/qrcode/generate?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        if (options.format === 'svg') {
          const svgText = await response.text();
          const svgDataUrl = `data:image/svg+xml;base64,${btoa(svgText)}`;
          setQrCode(svgDataUrl);
        } else {
          const data = await response.json();
          setQrCode(data.data.qrCode);
        }

        if (onGenerate) {
          onGenerate(qrCode);
        }

        toast({
          title: t('qr.success.generated', 'QR Code Generated'),
          description: t('qr.success.ready', 'QR code is ready for use'),
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else {
        throw new Error('Failed to generate QR code');
      }
    } catch (error) {
      console.error('QR code generation error:', error);
      toast({
        title: t('qr.error.generation', 'Generation Failed'),
        description: t('qr.error.tryAgain', 'Failed to generate QR code. Please try again.'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const downloadQRCode = () => {
    if (!qrCode) return;

    const link = document.createElement('a');
    link.href = qrCode;
    link.download = `${currency}-${address.substring(0, 8)}-qr.${options.format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: t('qr.success.downloaded', 'Downloaded'),
      description: t('qr.success.downloadComplete', 'QR code downloaded successfully'),
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  const copyAddress = () => {
    onCopy();
    toast({
      title: t('qr.success.copied', 'Copied'),
      description: t('qr.success.addressCopied', 'Address copied to clipboard'),
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  // Auto-generate QR code when address or currency changes
  useEffect(() => {
    if (address && currency) {
      generateQRCode();
    }
  }, [address, currency]);

  return (
    <Card maxW={sizeMap[size].width} mx="auto">
      <CardHeader>
        <HStack justify="space-between">
          <Heading size="md">
            <HStack>
              <FaQrcode />
              <Text>{t('qr.title', 'QR Code Generator')}</Text>
            </HStack>
          </Heading>
          {showCustomization && (
            <IconButton
              aria-label="Customize"
              icon={<FaPalette />}
              size="sm"
              variant="outline"
              onClick={onOpen}
            />
          )}
        </HStack>
      </CardHeader>

      <CardBody>
        <VStack spacing={4}>
          {/* Address Input */}
          <FormControl>
            <FormLabel>{t('qr.address', 'Wallet Address')}</FormLabel>
            <HStack>
              <Input
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                placeholder={t('qr.addressPlaceholder', 'Enter wallet address')}
                size="sm"
              />
              <Tooltip label={hasCopied ? t('qr.copied', 'Copied!') : t('qr.copy', 'Copy')}>
                <IconButton
                  aria-label="Copy address"
                  icon={<FaCopy />}
                  size="sm"
                  onClick={copyAddress}
                  isDisabled={!address}
                />
              </Tooltip>
            </HStack>
          </FormControl>

          {/* Currency Selection */}
          <FormControl>
            <FormLabel>{t('qr.currency', 'Currency')}</FormLabel>
            <Select
              value={currency}
              onChange={(e) => setCurrency(e.target.value)}
              size="sm"
            >
              {supportedCurrencies.map((curr) => (
                <option key={curr} value={curr}>
                  {curr}
                </option>
              ))}
            </Select>
          </FormControl>

          <Divider />

          {/* QR Code Display */}
          <Box textAlign="center">
            {loading ? (
              <VStack spacing={3}>
                <Spinner size="lg" color="blue.500" />
                <Text fontSize="sm" color="gray.500">
                  {t('qr.generating', 'Generating QR code...')}
                </Text>
              </VStack>
            ) : qrCode ? (
              <VStack spacing={3}>
                <Box
                  border="2px solid"
                  borderColor="gray.200"
                  borderRadius="md"
                  p={2}
                  bg="white"
                >
                  <Image
                    src={qrCode}
                    alt={`QR Code for ${currency} address`}
                    maxW={sizeMap[size].qrSize}
                    maxH={sizeMap[size].qrSize}
                  />
                </Box>
                
                <HStack spacing={2}>
                  <Button
                    leftIcon={<FaDownload />}
                    size="sm"
                    colorScheme="blue"
                    onClick={downloadQRCode}
                  >
                    {t('qr.download', 'Download')}
                  </Button>
                  
                  <IconButton
                    aria-label="Expand"
                    icon={<FaExpand />}
                    size="sm"
                    variant="outline"
                    onClick={onOpen}
                  />
                </HStack>

                <Badge colorScheme="green" fontSize="xs">
                  {options.format.toUpperCase()} • {options.size}px • {options.errorCorrectionLevel}
                </Badge>
              </VStack>
            ) : (
              <Alert status="info" borderRadius="md">
                <AlertIcon />
                <Text fontSize="sm">
                  {t('qr.enterAddress', 'Enter a wallet address to generate QR code')}
                </Text>
              </Alert>
            )}
          </Box>

          {/* Generate Button */}
          <Button
            colorScheme="blue"
            onClick={generateQRCode}
            isLoading={loading}
            loadingText={t('qr.generating', 'Generating...')}
            isDisabled={!address || !currency}
            size="sm"
            width="full"
          >
            {t('qr.generate', 'Generate QR Code')}
          </Button>
        </VStack>
      </CardBody>

      {/* Customization Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <HStack>
              <FaAdjust />
              <Text>{t('qr.customize', 'Customize QR Code')}</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4}>
              {/* Format Selection */}
              <FormControl>
                <FormLabel>{t('qr.format', 'Format')}</FormLabel>
                <Select
                  value={options.format}
                  onChange={(e) => setOptions({...options, format: e.target.value as 'png' | 'svg'})}
                >
                  <option value="png">PNG</option>
                  <option value="svg">SVG</option>
                </Select>
              </FormControl>

              {/* Size */}
              <FormControl>
                <FormLabel>{t('qr.size', 'Size (pixels)')}</FormLabel>
                <Input
                  type="number"
                  min={64}
                  max={1024}
                  value={options.size}
                  onChange={(e) => setOptions({...options, size: parseInt(e.target.value) || 256})}
                />
              </FormControl>

              {/* Colors */}
              <HStack width="full" spacing={4}>
                <FormControl>
                  <FormLabel>{t('qr.darkColor', 'Dark Color')}</FormLabel>
                  <Input
                    type="color"
                    value={options.darkColor}
                    onChange={(e) => setOptions({...options, darkColor: e.target.value})}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>{t('qr.lightColor', 'Light Color')}</FormLabel>
                  <Input
                    type="color"
                    value={options.lightColor}
                    onChange={(e) => setOptions({...options, lightColor: e.target.value})}
                  />
                </FormControl>
              </HStack>

              {/* Error Correction Level */}
              <FormControl>
                <FormLabel>{t('qr.errorCorrection', 'Error Correction')}</FormLabel>
                <Select
                  value={options.errorCorrectionLevel}
                  onChange={(e) => setOptions({...options, errorCorrectionLevel: e.target.value as 'L' | 'M' | 'Q' | 'H'})}
                >
                  {errorLevels.map((level) => (
                    <option key={level.value} value={level.value}>
                      {level.label}
                    </option>
                  ))}
                </Select>
              </FormControl>

              {/* Preview */}
              {qrCode && (
                <Box textAlign="center">
                  <Text fontSize="sm" mb={2}>{t('qr.preview', 'Preview')}</Text>
                  <Image
                    src={qrCode}
                    alt="QR Code Preview"
                    maxW="200px"
                    maxH="200px"
                    border="1px solid"
                    borderColor="gray.200"
                    borderRadius="md"
                  />
                </Box>
              )}

              <Button
                colorScheme="blue"
                onClick={() => {
                  generateQRCode();
                  onClose();
                }}
                width="full"
              >
                {t('qr.applyChanges', 'Apply Changes')}
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Card>
  );
};

export default QRCodeGenerator;
