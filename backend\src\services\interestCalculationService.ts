import mongoose from 'mongoose';
import InvestmentPackage from '../models/investmentPackageModel';
import Wallet from '../models/walletModel';
import Transaction from '../models/transactionModel';
import PaymentHistory from '../models/paymentHistoryModel';
import AuditTrail from '../models/auditTrailModel';
import { executeTransaction } from '../utils/transactionManager';
import { logger } from '../utils/logger';
import timeService from './timeService';

export interface InterestCalculationResult {
  packageId: string;
  userId: string;
  currency: string;
  dailyInterest: number;
  previousBalance: number;
  newBalance: number;
  activeDays: number;
  totalEarned: number;
  transactionId?: string;
  paymentHistoryId?: string;
  success: boolean;
  error?: string;
}

export interface InterestCalculationSummary {
  totalPackages: number;
  successfulCalculations: number;
  failedCalculations: number;
  totalInterestPaid: number;
  errors: Array<{
    packageId: string;
    error: string;
  }>;
  duration: number;
  timestamp: Date;
}

class InterestCalculationService {
  /**
   * Calculate daily interest for a single investment package with full transaction safety
   */
  async calculatePackageInterest(
    pkg: any,
    session: mongoose.ClientSession
  ): Promise<InterestCalculationResult> {
    const result: InterestCalculationResult = {
      packageId: pkg._id.toString(),
      userId: pkg.userId.toString(),
      currency: pkg.currency,
      dailyInterest: 0,
      previousBalance: 0,
      newBalance: 0,
      activeDays: pkg.activeDays || 0,
      totalEarned: pkg.totalEarned || 0,
      success: false
    };

    try {
      // Calculate daily interest
      const dailyInterest = pkg.calculateDailyInterest();
      
      if (dailyInterest <= 0) {
        result.error = 'No interest to calculate';
        return result;
      }

      result.dailyInterest = dailyInterest;
      result.previousBalance = pkg.totalEarned || 0;

      // 1. Update investment package
      pkg.totalEarned += dailyInterest;
      pkg.dailyInterest = dailyInterest;
      pkg.activeDays += 1;
      pkg.lastCalculatedAt = timeService.getTurkeyTime();
      pkg.lastInterestDistribution = timeService.getTurkeyTime();

      await pkg.save({ session });

      result.newBalance = pkg.totalEarned;
      result.activeDays = pkg.activeDays;
      result.totalEarned = pkg.totalEarned;

      // 2. Update user wallet
      const wallet = await this.updateWalletInterestBalance(
        pkg.userId,
        pkg.currency,
        dailyInterest,
        session
      );

      // 3. Create transaction record
      const transaction = await this.createInterestTransaction(
        pkg.userId,
        wallet._id as mongoose.Types.ObjectId,
        pkg.currency,
        dailyInterest,
        pkg._id,
        session
      );

      result.transactionId = transaction._id.toString();

      // 4. Create payment history record
      const paymentHistory = await this.createPaymentHistoryRecord(
        pkg.userId,
        pkg._id,
        dailyInterest,
        pkg.currency,
        transaction._id as mongoose.Types.ObjectId,
        wallet._id as mongoose.Types.ObjectId,
        {
          dailyInterestRate: pkg.interestRate,
          activeDays: pkg.activeDays,
          totalEarned: pkg.totalEarned,
          compoundEnabled: pkg.compoundEnabled,
          calculationMethod: 'daily_1_percent',
          previousBalance: result.previousBalance,
          newBalance: result.newBalance
        },
        session
      );

      result.paymentHistoryId = paymentHistory._id.toString();

      // 5. Create audit trail
      await this.createAuditTrail(
        pkg.userId,
        pkg._id,
        dailyInterest,
        pkg.currency,
        {
          activeDays: pkg.activeDays,
          totalEarned: pkg.totalEarned,
          transactionId: transaction._id,
          paymentHistoryId: paymentHistory._id
        },
        session
      );

      result.success = true;
      return result;

    } catch (error: any) {
      result.error = error.message;
      result.success = false;
      
      logger.error('Interest calculation failed for package', {
        packageId: pkg._id,
        userId: pkg.userId,
        error: error.message,
        stack: error.stack
      });

      return result;
    }
  }

  /**
   * Update wallet interest balance and total interest earned
   */
  private async updateWalletInterestBalance(
    userId: mongoose.Types.ObjectId,
    currency: string,
    interestAmount: number,
    session: mongoose.ClientSession
  ) {
    const wallet = await Wallet.findOne({ userId }).session(session);
    
    if (!wallet) {
      throw new Error(`Wallet not found for user: ${userId}`);
    }

    // Find or create asset in wallet
    let asset = wallet.assets.find(a => a.symbol === currency.toUpperCase());
    
    if (!asset) {
      // Create new asset entry
      wallet.assets.push({
        symbol: currency.toUpperCase(),
        balance: 0,
        commissionBalance: 0,
        interestBalance: interestAmount,
        mode: 'interest'
      });
    } else {
      // Update existing asset
      asset.interestBalance = (asset.interestBalance || 0) + interestAmount;
    }

    // Update total interest earned
    wallet.totalInterestEarned = (wallet.totalInterestEarned || 0) + interestAmount;

    await wallet.save({ session });
    return wallet;
  }

  /**
   * Create interest transaction record
   */
  private async createInterestTransaction(
    userId: mongoose.Types.ObjectId,
    walletId: mongoose.Types.ObjectId,
    currency: string,
    amount: number,
    investmentId: mongoose.Types.ObjectId,
    session: mongoose.ClientSession
  ) {
    const transaction = new Transaction({
      userId,
      walletId,
      type: 'interest',
      asset: currency.toUpperCase(),
      amount,
      status: 'completed',
      investmentId,
      description: 'Daily interest payment',
      metadata: {
        investmentPackageId: investmentId,
        paymentType: 'daily_interest',
        calculatedAt: timeService.getTurkeyTime(),
        interestRate: 0.01 // 1% daily
      }
    });

    await transaction.save({ session });
    return transaction;
  }

  /**
   * Create payment history record
   */
  private async createPaymentHistoryRecord(
    userId: mongoose.Types.ObjectId,
    investmentPackageId: mongoose.Types.ObjectId,
    amount: number,
    currency: string,
    transactionId: mongoose.Types.ObjectId,
    walletId: mongoose.Types.ObjectId,
    metadata: any,
    session: mongoose.ClientSession
  ) {
    const paymentHistory = new PaymentHistory({
      userId,
      investmentPackageId,
      amount,
      currency: currency.toUpperCase(),
      paymentDate: timeService.getTurkeyTime(),
      paymentType: 'interest',
      status: 'completed',
      transactionId,
      walletId,
      metadata
    });

    await paymentHistory.save({ session });
    return paymentHistory;
  }

  /**
   * Create audit trail record
   */
  private async createAuditTrail(
    userId: mongoose.Types.ObjectId,
    packageId: mongoose.Types.ObjectId,
    amount: number,
    currency: string,
    details: any,
    session: mongoose.ClientSession
  ) {
    const crypto = require('crypto');
    const timestamp = new Date();
    const hashData = `${timestamp}-${userId}-INTEREST_CALCULATED-${packageId}-${amount}`;
    const hash = crypto.createHash('sha256').update(hashData).digest('hex');

    const auditLog = new AuditTrail({
      timestamp,
      userId,
      action: 'INTEREST_CALCULATED',
      packageId,
      amount,
      currency,
      hash,
      details,
      status: 'success'
    });

    await auditLog.save({ session });
  }

  /**
   * Process all active investment packages for daily interest calculation
   */
  async processAllActivePackages(): Promise<InterestCalculationSummary> {
    const startTime = Date.now();
    const timestamp = timeService.getTurkeyTime();

    const summary: InterestCalculationSummary = {
      totalPackages: 0,
      successfulCalculations: 0,
      failedCalculations: 0,
      totalInterestPaid: 0,
      errors: [],
      duration: 0,
      timestamp
    };

    try {
      logger.info('🔄 Starting daily interest calculation for all active packages...');

      // Get all active packages
      const activePackages = await InvestmentPackage.getActivePackages();
      summary.totalPackages = activePackages.length;

      if (activePackages.length === 0) {
        logger.info('📊 No active packages found for interest calculation');
        summary.duration = Date.now() - startTime;
        return summary;
      }

      logger.info(`📦 Found ${activePackages.length} active packages for interest calculation`);

      // Process packages in batches to avoid overwhelming the database
      // Optimized batch size for better performance
      const batchSize = 50; // Increased from 10 for better performance
      const batches = [];

      for (let i = 0; i < activePackages.length; i += batchSize) {
        batches.push(activePackages.slice(i, i + batchSize));
      }

      // Process each batch
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        logger.info(`🔄 Processing batch ${batchIndex + 1}/${batches.length} (${batch.length} packages)`);

        // Process batch with transaction safety
        const batchResults = await executeTransaction(async (session) => {
          const results: InterestCalculationResult[] = [];

          for (const pkg of batch) {
            const result = await this.calculatePackageInterest(pkg, session);
            results.push(result);
          }

          return results;
        }, {
          maxRetries: 3,
          retryDelay: 1000
        });

        // Process batch results
        for (const result of batchResults) {
          if (result.success) {
            summary.successfulCalculations++;
            summary.totalInterestPaid += result.dailyInterest;

            logger.info('✅ Interest calculated successfully', {
              packageId: result.packageId,
              userId: result.userId,
              currency: result.currency,
              amount: result.dailyInterest,
              activeDays: result.activeDays,
              totalEarned: result.totalEarned
            });
          } else {
            summary.failedCalculations++;
            summary.errors.push({
              packageId: result.packageId,
              error: result.error || 'Unknown error'
            });

            logger.error('❌ Interest calculation failed', {
              packageId: result.packageId,
              userId: result.userId,
              error: result.error
            });
          }
        }

        // Reduced delay between batches for better performance
        if (batchIndex < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 50)); // Reduced from 100ms
        }
      }

      summary.duration = Date.now() - startTime;

      logger.info('✅ Daily interest calculation completed', {
        totalPackages: summary.totalPackages,
        successful: summary.successfulCalculations,
        failed: summary.failedCalculations,
        totalInterestPaid: summary.totalInterestPaid.toFixed(6),
        duration: `${summary.duration}ms`,
        timestamp: timestamp.toISOString()
      });

      return summary;

    } catch (error: any) {
      summary.duration = Date.now() - startTime;

      logger.error('❌ Daily interest calculation process failed', {
        error: error.message,
        stack: error.stack,
        duration: summary.duration,
        processedSoFar: summary.successfulCalculations
      });

      throw error;
    }
  }

  /**
   * Get interest calculation statistics
   */
  async getCalculationStatistics(startDate?: Date, endDate?: Date) {
    const query: any = { paymentType: 'interest' };

    if (startDate || endDate) {
      query.paymentDate = {};
      if (startDate) query.paymentDate.$gte = startDate;
      if (endDate) query.paymentDate.$lte = endDate;
    }

    const stats = await PaymentHistory.aggregate([
      { $match: query },
      {
        $group: {
          _id: {
            currency: '$currency',
            date: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$paymentDate'
              }
            }
          },
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' }
        }
      },
      {
        $group: {
          _id: '$_id.currency',
          totalAmount: { $sum: '$totalAmount' },
          totalPayments: { $sum: '$count' },
          uniqueUsers: { $sum: { $size: '$uniqueUsers' } },
          dailyBreakdown: {
            $push: {
              date: '$_id.date',
              amount: '$totalAmount',
              count: '$count'
            }
          }
        }
      }
    ]);

    return stats;
  }
}

export default new InterestCalculationService();
