import React, { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Button,
  useToast,
  SimpleGrid,
  Card,
  CardBody,
  Icon
} from '@chakra-ui/react';
import { FaUserShield, FaBell, FaComments, FaCog, FaHeart } from 'react-icons/fa';
import DraggableChatBubble from '../components/DraggableChatBubble';

const ChatBubbleDemo: React.FC = () => {
  const [showBubbles, setShowBubbles] = useState({
    admin: true,
    notification: false,
    chat: false,
    settings: false
  });
  const toast = useToast();

  const handleBubbleClick = (type: string) => {
    toast({
      title: `${type} Bubble Clicked!`,
      description: `You clicked the ${type} chat bubble`,
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  const toggleBubble = (type: keyof typeof showBubbles) => {
    setShowBubbles(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        <Box textAlign="center">
          <Heading size="xl" mb={4}>
            Draggable Chat Bubble Demo
          </Heading>
          <Text fontSize="lg" color="gray.600">
            Test the draggable chat bubble components. Click the buttons below to show/hide different bubbles.
          </Text>
        </Box>

        {/* Controls */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>Controls</Heading>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
              <Button
                colorScheme={showBubbles.admin ? "orange" : "gray"}
                onClick={() => toggleBubble('admin')}
                leftIcon={<FaUserShield />}
              >
                Admin Mode
              </Button>
              <Button
                colorScheme={showBubbles.notification ? "blue" : "gray"}
                onClick={() => toggleBubble('notification')}
                leftIcon={<FaBell />}
              >
                Notifications
              </Button>
              <Button
                colorScheme={showBubbles.chat ? "green" : "gray"}
                onClick={() => toggleBubble('chat')}
                leftIcon={<FaComments />}
              >
                Live Chat
              </Button>
              <Button
                colorScheme={showBubbles.settings ? "purple" : "gray"}
                onClick={() => toggleBubble('settings')}
                leftIcon={<FaCog />}
              >
                Settings
              </Button>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Instructions */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>How to Use</Heading>
            <VStack align="start" spacing={2}>
              <HStack>
                <Icon as={FaHeart} color="red.500" />
                <Text>Click the buttons above to show/hide different chat bubbles</Text>
              </HStack>
              <HStack>
                <Icon as={FaHeart} color="red.500" />
                <Text>Drag the bubbles around the screen using the grip handle (⋮⋮)</Text>
              </HStack>
              <HStack>
                <Icon as={FaHeart} color="red.500" />
                <Text>Click on the bubble content to trigger actions</Text>
              </HStack>
              <HStack>
                <Icon as={FaHeart} color="red.500" />
                <Text>Bubbles have hover and drag animations</Text>
              </HStack>
            </VStack>
          </CardBody>
        </Card>

        {/* Demo Content */}
        <Card>
          <CardBody>
            <Heading size="md" mb={4}>Demo Content</Heading>
            <Text>
              This is the main content area. The chat bubbles will appear as floating elements
              that you can drag around. In a real application, these bubbles would provide
              quick access to important features like admin controls, notifications, live chat,
              or settings.
            </Text>
            <Box h="400px" bg="gray.50" borderRadius="md" mt={4} p={4}>
              <Text color="gray.500" textAlign="center" pt={20}>
                Drag the chat bubbles around this area to test the functionality
              </Text>
            </Box>
          </CardBody>
        </Card>
      </VStack>

      {/* Chat Bubbles */}
      {showBubbles.admin && (
        <DraggableChatBubble
          colorScheme="orange"
          icon={FaUserShield}
          text="Admin Mode"
          onClick={() => handleBubbleClick('Admin')}
        />
      )}

      {showBubbles.notification && (
        <Box position="fixed" top={4} right={200} zIndex={9998}>
          <DraggableChatBubble
            colorScheme="blue"
            icon={FaBell}
            text="Notifications"
            onClick={() => handleBubbleClick('Notification')}
          />
        </Box>
      )}

      {showBubbles.chat && (
        <Box position="fixed" top={100} right={4} zIndex={9997}>
          <DraggableChatBubble
            colorScheme="green"
            icon={FaComments}
            text="Live Chat"
            onClick={() => handleBubbleClick('Chat')}
          />
        </Box>
      )}

      {showBubbles.settings && (
        <Box position="fixed" top={200} right={4} zIndex={9996}>
          <DraggableChatBubble
            colorScheme="purple"
            icon={FaCog}
            text="Settings"
            onClick={() => handleBubbleClick('Settings')}
          />
        </Box>
      )}
    </Container>
  );
};

export default ChatBubbleDemo;
