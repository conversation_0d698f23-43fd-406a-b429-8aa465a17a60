{"title": "Notifications", "markAllRead": "<PERSON>", "markAsRead": "<PERSON> <PERSON>", "markAsUnread": "<PERSON> as Unread", "delete": "Delete", "deleteAll": "Delete All", "noNotifications": "No notifications", "loading": "Loading notifications...", "error": "Error loading notifications", "refresh": "Refresh", "settings": {"title": "Notification Settings", "subtitle": "Choose which notifications you want to receive", "email": {"title": "Email Notifications", "enabled": "Enable email notifications", "investment": "Investment notifications", "transaction": "Transaction notifications", "security": "Security notifications", "promotion": "Promotional notifications", "news": "News notifications"}, "push": {"title": "Push Notifications", "enabled": "Enable push notifications", "investment": "Investment notifications", "transaction": "Transaction notifications", "security": "Security notifications", "promotion": "Promotional notifications"}, "sms": {"title": "SMS Notifications", "enabled": "Enable SMS notifications", "security": "Security notifications", "transaction": "Important transaction notifications"}, "frequency": {"title": "Notification Frequency", "instant": "Instant", "hourly": "Hourly", "daily": "Daily", "weekly": "Weekly"}, "quietHours": {"title": "Quiet Hours", "enabled": "Enable quiet hours", "start": "Start time", "end": "End time"}, "save": "Save Settings", "reset": "Reset to De<PERSON>ult", "test": "Send Test Notification"}, "preferences": "Preferences", "types": {"all": "All", "investment": "Investment", "transaction": "Transaction", "security": "Security", "system": "System", "promotion": "Promotion", "news": "News", "update": "Update"}, "investment": {"created": "New investment created", "completed": "Investment completed", "earning": "Investment earning added", "expired": "Investment expired", "cancelled": "Investment cancelled", "extended": "Investment extended", "reinvested": "Investment reinvested", "matured": "Investment matured", "reminder": "Investment reminder", "lowBalance": "Insufficient balance for investment"}, "transaction": {"deposit": {"received": "<PERSON><PERSON><PERSON><PERSON> received", "confirmed": "<PERSON><PERSON><PERSON><PERSON> confirmed", "failed": "Depo<PERSON><PERSON> failed", "pending": "Deposit pending", "cancelled": "Deposit cancelled"}, "withdrawal": {"requested": "<PERSON><PERSON><PERSON> requested", "approved": "<PERSON><PERSON><PERSON> approved", "processed": "Withdrawal processed", "rejected": "<PERSON><PERSON><PERSON> rejected", "cancelled": "Withdrawal cancelled", "failed": "<PERSON><PERSON><PERSON> failed"}, "transfer": {"sent": "Transfer sent", "received": "Transfer received", "failed": "Transfer failed", "pending": "Transfer pending"}, "fee": {"charged": "Transaction fee charged", "refunded": "Transaction fee refunded"}}, "security": {"login": {"success": "Successful login", "failed": "Failed login attempt", "newDevice": "Login from new device", "suspicious": "Suspicious login attempt"}, "password": {"changed": "Password changed", "resetRequested": "Password reset requested", "resetCompleted": "Password reset completed"}, "twoFactor": {"enabled": "Two-factor authentication enabled", "disabled": "Two-factor authentication disabled", "codeUsed": "Two-factor authentication code used"}, "account": {"locked": "Account locked", "unlocked": "Account unlocked", "suspended": "Account suspended", "verified": "Account verified"}, "api": {"keyCreated": "API key created", "keyDeleted": "API key deleted", "keyUsed": "API key used"}}, "system": {"maintenance": {"scheduled": "Scheduled maintenance announcement", "started": "Maintenance started", "completed": "Maintenance completed", "extended": "Maintenance extended"}, "update": {"available": "New update available", "installed": "Update installed", "failed": "Update failed"}, "service": {"down": "Service down", "restored": "Service restored", "degraded": "Service degraded"}, "backup": {"completed": "Backup completed", "failed": "Backup failed", "restored": "Backup restored"}}, "promotion": {"bonus": {"received": "Bonus received", "expired": "Bonus expired", "used": "Bonus used"}, "referral": {"earned": "Referral commission earned", "newReferral": "New referral registered", "bonusReceived": "Referral bonus received"}, "campaign": {"started": "New campaign started", "ending": "Campaign ending", "ended": "Campaign ended"}, "reward": {"earned": "<PERSON><PERSON> earned", "claimed": "<PERSON><PERSON> claimed", "expired": "Reward expired"}}, "news": {"market": {"update": "Market update", "alert": "Market alert", "analysis": "Market analysis"}, "crypto": {"news": "Crypto news", "price": "Price alert", "listing": "New listing"}, "platform": {"feature": "New feature", "improvement": "Platform improvement", "announcement": "Platform announcement"}}, "actions": {"view": "View", "dismiss": "<PERSON><PERSON><PERSON>", "snooze": "Snooze", "archive": "Archive", "unarchive": "Unarchive", "report": "Report", "block": "Block"}, "filters": {"all": "All", "unread": "Unread", "read": "Read", "archived": "Archived", "today": "Today", "week": "This Week", "month": "This Month", "important": "Important", "starred": "Starred"}, "time": {"now": "Now", "minutesAgo": "{{count}} minutes ago", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago", "weeksAgo": "{{count}} weeks ago", "monthsAgo": "{{count}} months ago"}}