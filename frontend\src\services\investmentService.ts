import { apiClient } from '../utils/apiClient';

/**
 * API service for investment-related endpoints
 */
export const investmentService = {
  /**
   * Create a new investment
   * @param data Investment data
   */
  createInvestment: async (data: {
    currency: string;
    amount: number;
    description?: string;
    network?: string;
  }) => {
    try {
      // Reset circuit breaker before making the request
      apiClient.resetCircuitBreaker();
      console.log('Creating investment with data:', data);

      // Make the API call to create investment
      const response = await apiClient.post('/investments', data);
      console.log('Investment creation response:', response);

      // Validate response format
      const responseData = (response as any)?.data;
      if (!responseData || !responseData.investment || !responseData.investment._id) {
        throw new Error('Invalid response format from investment API');
      }

      return response;
    } catch (error: any) {
      console.error('Investment creation failed:', error);
      throw error;
    }
  },

  /**
   * Upload receipt for an investment
   * @param id Investment ID
   * @param formData FormData containing the receipt file
   */
  uploadReceipt: async (id: string, formData: FormData) => {
    try {
      console.log('Uploading receipt for investment ID:', id);
      const response = await apiClient.post(`/investments/${id}/receipt`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      console.log('Receipt upload response:', response);
      return response;
    } catch (error: any) {
      console.error('Receipt upload failed:', error);
      throw error;
    }
  },

  /**
   * Get all investments for the current user
   * @param params Optional query parameters
   */
  getInvestments: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    currency?: string;
    grouped?: boolean;
  }) => apiClient.get('/investments', { params }),

  /**
   * Get grouped investments for the current user
   * This is a convenience method that sets grouped=true
   */
  getGroupedInvestments: (params?: {
    status?: string;
    currency?: string;
  }) => apiClient.get('/investments', { params: { ...params, grouped: true } }),

  /**
   * Get investment by ID
   * @param id Investment ID
   */
  getInvestmentById: (id: string) => apiClient.get(`/investments/${id}`),

  /**
   * Update transaction hash for an investment
   * @param id Investment ID
   * @param txHash Transaction hash
   */
  updateTransactionHash: async (id: string, txHash: string) => {
    try {
      console.log('Updating transaction hash for investment ID:', id, 'with hash:', txHash);
      const response = await apiClient.put(`/investments/${id}/txhash`, { txHash });
      console.log('Transaction hash update response:', response);
      return response;
    } catch (error: any) {
      console.error('Transaction hash update failed:', error);
      throw error;
    }
  },

  /**
   * Get deposit address for a currency
   * @param currency Currency symbol (e.g., BTC, ETH)
   * @param network Optional network ID
   */
  getDepositAddress: async (currency: string, network?: string) => {
    try {
      // Use shared cache service
      const { cryptoAddressCache } = await import('./cryptoAddressCache');
      const result = await cryptoAddressCache.getDepositAddress(currency, network);

      // Transform to match expected format for investment service
      if (result.data && result.data.data && result.data.data.address) {
        return {
          data: {
            address: result.data.data.address,
            currency: currency.toUpperCase(),
            network: network || 'mainnet',
            enabled: true
          }
        };
      }

      // If no address found, throw error
      throw new Error(`No address found for currency ${currency}`);

    } catch (error: any) {
      console.error('Failed to get deposit address:', error);
      throw error;
    }
  },

  /**
   * Get available wallet addresses for all currencies or a specific currency with network information
   * @param currency Optional currency symbol (e.g., BTC, ETH)
   */
  getAvailableWallets: (() => {
    // Cache for wallet data
    const cache: { [key: string]: { data: any, timestamp: number } } = {};
    const CACHE_DURATION = 60 * 1000; // 1 minute cache

    return (currency?: string) => {
      const cacheKey = currency || 'all';
      const now = Date.now();

      // Check if we have cached data that's still valid
      if (cache[cacheKey] && (now - cache[cacheKey].timestamp) < CACHE_DURATION) {
        console.log(`Using cached wallet data for ${cacheKey}`);
        return Promise.resolve(cache[cacheKey].data);
      }

      // Otherwise make the API call
      return apiClient.get('/wallets/available', {
        params: currency ? { currency } : undefined
      }).then(response => {
        // Cache the response
        cache[cacheKey] = {
          data: response,
          timestamp: now
        };
        return response;
      });
    };
  })(),
};

export default investmentService;
