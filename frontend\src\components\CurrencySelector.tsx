import React, { useState } from 'react';
import {
  Box,
  <PERSON><PERSON>,
  <PERSON>u,
  <PERSON>u<PERSON>utton,
  MenuList,
  MenuItem,
  MenuDivider,
  Text,
  HStack,
  Icon,
  Image,
  useColorModeValue,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  SimpleGrid
} from '@chakra-ui/react';
import { ChevronDownIcon, SearchIcon } from '@chakra-ui/icons';
import { FaDollarSign, FaEuroSign, FaPoundSign, FaYenSign, FaRubleSign, FaLiraSign } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { cryptoIcons, cryptoNames, cryptoColors } from '../utils/cryptoIcons';

// Currency types and interfaces
export type CurrencyType = 'crypto' | 'fiat';

export interface Currency {
  code: string;
  name: string;
  symbol: string;
  type: CurrencyType;
  icon: React.ElementType | string;
  decimals: number;
}

// Sample currency data
const currencies: Currency[] = [
  // Crypto currencies
  ...Object.keys(cryptoIcons).map(code => ({
    code,
    name: cryptoNames[code] || code,
    symbol: code === 'BTC' ? '₿' : code === 'ETH' ? 'Ξ' : code === 'USDT' ? '₮' : code === 'DOGE' ? 'Ð' : code,
    type: 'crypto' as CurrencyType,
    icon: cryptoIcons[code],
    decimals: code === 'ETH' || code === 'BNB' ? 18 : code === 'BTC' || code === 'DOGE' ? 8 : 6
  })),

  // Fiat currencies
  { code: 'USD', name: 'US Dollar', symbol: '$', type: 'fiat', icon: FaDollarSign, decimals: 2 },
  { code: 'EUR', name: 'Euro', symbol: '€', type: 'fiat', icon: FaEuroSign, decimals: 2 },
  { code: 'GBP', name: 'British Pound', symbol: '£', type: 'fiat', icon: FaPoundSign, decimals: 2 },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥', type: 'fiat', icon: FaYenSign, decimals: 0 },
  { code: 'TRY', name: 'Turkish Lira', symbol: '₺', type: 'fiat', icon: FaLiraSign, decimals: 2 },
  { code: 'RUB', name: 'Russian Ruble', symbol: '₽', type: 'fiat', icon: FaRubleSign, decimals: 2 },
];

interface CurrencySelectorProps {
  selectedCurrency: Currency;
  onCurrencyChange: (currency: Currency) => void;
  allowedTypes?: CurrencyType[];
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'full';
}

const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  selectedCurrency,
  onCurrencyChange,
  allowedTypes = ['crypto', 'fiat'],
  size = 'md',
  variant = 'default'
}) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<CurrencyType>(allowedTypes[0]);

  // Colors
  const bgColor = "#1E2329";
  const menuBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";

  // Filter currencies based on search query and active tab
  const filteredCurrencies = currencies
    .filter(currency => allowedTypes.includes(currency.type))
    .filter(currency => activeTab === currency.type)
    .filter(currency =>
      currency.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
      currency.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

  // Get icon component for the selected currency
  const CurrencyIcon = typeof selectedCurrency.icon === 'string'
    ? () => <Image src={selectedCurrency.icon as string} boxSize={size === 'sm' ? '16px' : '20px'} />
    : selectedCurrency.icon;

  // Render different button variants
  const renderButton = () => {
    switch (variant) {
      case 'minimal':
        return (
          <MenuButton
            as={Button}
            variant="ghost"
            size={size}
            color={textColor}
            _hover={{ bg: "rgba(43, 49, 57, 0.5)" }}
            _active={{ bg: "rgba(43, 49, 57, 0.7)" }}
            p={size === 'sm' ? 1 : 2}
          >
            <Icon as={CurrencyIcon} color={primaryColor} boxSize={size === 'sm' ? '16px' : '20px'} />
          </MenuButton>
        );

      case 'full':
        return (
          <MenuButton
            as={Button}
            variant="outline"
            size={size}
            borderColor={borderColor}
            color={textColor}
            _hover={{ borderColor: primaryColor }}
            _active={{ bg: "rgba(43, 49, 57, 0.7)" }}
            w="100%"
            justifyContent="space-between"
          >
            <HStack spacing={2}>
              <Icon as={CurrencyIcon} color={primaryColor} boxSize={size === 'sm' ? '16px' : '20px'} />
              <Text>{selectedCurrency.name}</Text>
              <Text color={secondaryTextColor}>({selectedCurrency.code})</Text>
            </HStack>
            <ChevronDownIcon />
          </MenuButton>
        );

      default: // 'default'
        return (
          <MenuButton
            as={Button}
            variant="outline"
            size={size}
            borderColor={borderColor}
            color={textColor}
            _hover={{ borderColor: primaryColor }}
            _active={{ bg: "rgba(43, 49, 57, 0.7)" }}
          >
            <HStack spacing={2}>
              <Icon as={CurrencyIcon} color={primaryColor} boxSize={size === 'sm' ? '16px' : '20px'} />
              <Text>{selectedCurrency.code}</Text>
              <ChevronDownIcon />
            </HStack>
          </MenuButton>
        );
    }
  };

  return (
    <Menu closeOnSelect={true}>
      {renderButton()}

      <MenuList
        bg={menuBgColor}
        borderColor={borderColor}
        boxShadow="lg"
        minW={variant === 'minimal' ? '240px' : 'auto'}
        zIndex={10}
      >
        {/* Search input */}
        <Box px={4} pt={2} pb={3}>
          <InputGroup size="sm">
            <InputLeftElement pointerEvents="none">
              <SearchIcon color={secondaryTextColor} />
            </InputLeftElement>
            <Input
              placeholder={t('currency.search', 'Search currency')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              bg={bgColor}
              borderColor={borderColor}
              color={textColor}
              _hover={{ borderColor: primaryColor }}
              _focus={{ borderColor: primaryColor, boxShadow: "none" }}
            />
          </InputGroup>
        </Box>

        {/* Tabs for currency types */}
        {allowedTypes.length > 1 && (
          <Tabs
            variant="soft-rounded"
            colorScheme="yellow"
            size="sm"
            isFitted
            onChange={(index) => setActiveTab(allowedTypes[index])}
            mx={4}
            mb={3}
          >
            <TabList>
              {allowedTypes.includes('crypto') && (
                <Tab
                  color={activeTab === 'crypto' ? primaryColor : secondaryTextColor}
                  _selected={{ bg: 'rgba(240, 185, 11, 0.1)' }}
                >
                  {t('currency.crypto', 'Crypto')}
                </Tab>
              )}
              {allowedTypes.includes('fiat') && (
                <Tab
                  color={activeTab === 'fiat' ? primaryColor : secondaryTextColor}
                  _selected={{ bg: 'rgba(240, 185, 11, 0.1)' }}
                >
                  {t('currency.fiat', 'Fiat')}
                </Tab>
              )}
            </TabList>
          </Tabs>
        )}

        <MenuDivider borderColor={borderColor} />

        {/* Currency list */}
        <Box maxH="300px" overflowY="auto" px={2}>
          {filteredCurrencies.length > 0 ? (
            filteredCurrencies.map((currency) => {
              const CurrIcon = typeof currency.icon === 'string'
                ? () => <Image src={currency.icon as string} boxSize="20px" />
                : currency.icon;

              return (
                <MenuItem
                  key={currency.code}
                  onClick={() => onCurrencyChange(currency)}
                  bg="transparent"
                  _hover={{ bg: bgColor }}
                  _focus={{ bg: bgColor }}
                  icon={<Icon as={CurrIcon} color={primaryColor} boxSize="20px" />}
                  isDisabled={currency.code === selectedCurrency.code}
                >
                  <Flex justify="space-between" align="center" w="100%">
                    <Text color={textColor}>{currency.name}</Text>
                    <Text color={secondaryTextColor} fontSize="sm">{currency.code}</Text>
                  </Flex>
                </MenuItem>
              );
            })
          ) : (
            <Box py={4} textAlign="center">
              <Text color={secondaryTextColor}>
                {t('currency.noResults', 'No currencies found')}
              </Text>
            </Box>
          )}
        </Box>
      </MenuList>
    </Menu>
  );
};

export default CurrencySelector;
