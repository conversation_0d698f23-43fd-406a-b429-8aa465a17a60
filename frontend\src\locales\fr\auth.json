{"login": {"title": "Connexion", "subtitle": "Connectez-vous à votre compte", "email": "E-mail", "password": "Mot de passe", "rememberMe": "Se souvenir de moi", "forgotPassword": "Mot de passe oublié", "loginButton": "Se connecter", "noAccount": "Vous n'avez pas de compte ?", "registerLink": "S'inscrire", "welcomeBack": "Bon retour !", "loginSuccess": "Connexion réussie", "loginError": "Échec de la connexion", "invalidCredentials": "E-mail ou mot de passe invalide", "accountLocked": "Votre compte est verrouillé", "accountNotVerified": "Votre compte n'est pas vérifié", "tooManyAttempts": "Trop de tentatives échouées", "sessionExpired": "Votre session a expiré", "loginRequired": "Vous devez vous connecter pour accéder à cette page"}, "register": {"title": "S'inscrire", "subtitle": "<PERSON><PERSON>er un nouveau compte", "firstName": "Prénom", "lastName": "Nom", "email": "E-mail", "password": "Mot de passe", "confirmPassword": "Confirmer le mot de passe", "phone": "Téléphone", "country": "Pays", "city": "Ville", "referralCode": "Code de parrainage (Optionnel)", "referralSection": "Code de parrainage (Optionnel)", "referralHelp": "Entrez un code de parrainage pour gagner des bonus pour vous et votre parrain", "referralValid": "Code de parrainage valide", "referralValidDesc": "Parrainé par {referrerName}", "referralInvalid": "Code de parrainage invalide", "referralInvalidDesc": "Ce code de parrainage n'existe pas", "agreeTerms": "J'accepte les conditions d'utilisation", "agreePrivacy": "J'accepte la politique de confidentialité", "marketingConsent": "J'accepte de recevoir des e-mails marketing", "registerButton": "S'inscrire", "haveAccount": "Vous avez déjà un compte ?", "loginLink": "Se connecter", "registrationSuccess": "Inscription réussie ! Vérifiez votre e-mail", "registrationError": "Échec de l'inscription", "emailExists": "Cette adresse e-mail est déjà utilisée", "weakPassword": "Le mot de passe est trop faible", "invalidEmail": "Adresse e-mail invalide", "termsRequired": "<PERSON><PERSON> accepter les conditions d'utilisation", "privacyRequired": "<PERSON><PERSON> <PERSON> accepter la politique de confidentialité"}, "forgotPassword": {"title": "Mot de passe oublié", "subtitle": "Nous vous enverrons un lien de réinitialisation du mot de passe", "email": "E-mail", "sendButton": "Envoyer", "backToLogin": "Retour à la connexion", "emailSent": "Lien de réinitialisation du mot de passe envoyé à votre e-mail", "emailNotFound": "<PERSON>tte adresse e-mail n'a pas été trouvée", "rateLimited": "Trop de demandes. Veuillez patienter"}, "resetPassword": {"title": "Réinitialiser le mot de passe", "subtitle": "Définissez votre nouveau mot de passe", "newPassword": "Nouveau mot de passe", "confirmPassword": "Confirmer le mot de passe", "resetButton": "Réinitialiser le mot de passe", "resetSuccess": "Votre mot de passe a été réinitialisé avec succès", "resetError": "Échec de la réinitialisation du mot de passe", "invalidToken": "Lien invalide ou expiré", "passwordMismatch": "Les mots de passe ne correspondent pas"}, "verification": {"title": "Vérification de l'e-mail", "subtitle": "Entrez le code envoyé à votre e-mail", "code": "Code de vérification", "verifyButton": "Vérifier", "resendCode": "Renvoyer le code", "verificationSuccess": "E-mail vérifié avec succès", "verificationError": "Échec de la vérification", "invalidCode": "Code de vérification invalide", "expiredCode": "Le code de vérification a expiré", "codeSent": "Code de vérification envoyé à votre e-mail"}, "logout": {"title": "Déconnexion", "message": "Êtes-vous sûr de vouloir vous déconnecter ?", "confirmButton": "Se déconnecter", "cancelButton": "Annuler", "logoutSuccess": "Déconnexion réussie", "logoutError": "Erreur lors de la déconnexion"}, "twoFactor": {"title": "Authentification à deux facteurs", "subtitle": "Entrez votre code de sécurité", "code": "Code de sécurité", "verifyButton": "Vérifier", "backupCode": "Utiliser le code de sauvegarde", "trustDevice": "Faire confiance à cet appareil", "verificationSuccess": "Authentification à deux facteurs réussie", "verificationError": "Échec de la vérification", "invalidCode": "Code de sécurité invalide", "setup": {"title": "Configuration de l'authentification à deux facteurs", "step1": "Télécharger une application d'authentification", "step2": "Scanner le code QR", "step3": "Entrer le code de vérification", "qrCode": "Code QR", "manualEntry": "<PERSON><PERSON>", "secretKey": "<PERSON>lé secrète", "verificationCode": "Code de vérification", "enableButton": "Activer", "setupSuccess": "Authentification à deux facteurs activée", "setupError": "Échec de la configuration"}}, "errors": {"loginFailed": "Échec de la connexion. Veuillez réessayer.", "networkError": "Erreur de connexion réseau. Veuillez vérifier votre connexion Internet.", "invalidCredentials": "E-mail ou mot de passe invalide.", "serverError": "<PERSON>rreur du serveur. Veuillez réessayer plus tard.", "registrationFailed": "Échec de l'inscription", "profileUpdateFailed": "Échec de la mise à jour du profil", "adminLoginFailed": "Compte ou mot de passe invalide"}, "success": {"loginSuccess": "Utilisateur connecté avec succès avec l'authentification par cookie", "registrationSuccess": "Utilisateur inscrit avec succès avec l'authentification par cookie", "logoutSuccess": "Utilisateur déconnecté, token supprimé du localStorage et des en-têtes axios", "adminLoginSuccess": "Connexion administrateur réussie"}, "info": {"axiosConfigured": "Axios configuré pour inclure les identifiants dans toutes les requêtes", "userLoadedFromStorage": "Utilisateur chargé depuis localStorage", "onLoginPage": "Sur la page de connexion, ne charge pas automatiquement l'utilisateur depuis localStorage", "websocketSkipped": "Connexion WebSocket ignorée pour le débogage", "websocketConnected": "Connexion WebSocket établie après la connexion", "websocketConnectedAdmin": "Connexion WebSocket établie après la connexion administrateur", "websocketClosed": "Connexion WebSocket fermée après la déconnexion", "adminVerification": "Vérification du statut administrateur avec le serveur...", "adminConfirmed": "Le serveur a confirmé que l'utilisateur est administrateur", "adminDenied": "Le serveur a explicitement refusé l'accès administrateur", "adminCookieFound": "<PERSON><PERSON> administrateur trouvé, l'utilisateur est administrateur", "adminTokenFound": "Token administrateur trouvé dans localStorage", "adminAlreadyMarked": "L'utilisateur est déjà marqué comme administrateur dans le contexte", "noUserLoggedIn": "Aucun utilisateur connecté, ne peut pas être administrateur", "adminCookieNotFound": "<PERSON><PERSON> administrateur non trouvé après la connexion, définition manuelle", "adminCookieNotFoundVerification": "<PERSON><PERSON> administrateur non trouvé après la vérification, définition manuelle"}}