import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import cors from 'cors';
import { body, validationResult } from 'express-validator';
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { logger } from '../utils/logger';

// Rate limiting configurations
export const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts',
    message: 'Please try again later',
    retryAfter: 15 * 60 // 15 minutes in seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn(`Rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      error: 'Too many authentication attempts',
      message: 'Please try again later',
      retryAfter: 15 * 60
    });
  }
});

export const withdrawalLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 withdrawal requests per hour
  message: {
    error: 'Too many withdrawal attempts',
    message: 'Please try again later',
    retryAfter: 60 * 60
  },
  standardHeaders: true,
  legacyHeaders: false,
});

export const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests',
    message: 'Please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Security headers
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", process.env.FRONTEND_URL || "http://localhost:3000"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for API compatibility
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  }
});

// CORS configuration
export const corsOptions = {
  origin: function (origin: string | undefined, callback: Function) {
    const allowedOrigins = [
      process.env.FRONTEND_URL,
      'http://localhost:3000',
      'https://localhost:3000'
    ].filter(Boolean);

    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      logger.warn(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
};

// Input validation middleware
export const validateInput = (validations: any[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Run all validations
    await Promise.all(validations.map(validation => validation.run(req)));

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.warn(`Validation failed for ${req.method} ${req.path}:`, errors.array());
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    next();
  };
};

// 2FA Middleware
export const require2FA = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Skip 2FA for non-sensitive operations
    const sensitiveOperations = ['/withdraw', '/transfer', '/settings/security'];
    const isSensitiveOperation = sensitiveOperations.some(op => req.path.includes(op));

    if (!isSensitiveOperation || !user.twoFactorEnabled) {
      return next();
    }

    const twoFactorCode = req.headers['x-2fa-code'] as string;
    
    if (!twoFactorCode) {
      return res.status(403).json({
        success: false,
        message: '2FA code required',
        requires2FA: true
      });
    }

    // Verify 2FA code
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token: twoFactorCode,
      window: 2 // Allow 2 time steps (60 seconds) tolerance
    });

    if (!verified) {
      logger.warn(`Invalid 2FA attempt for user ${user._id} from IP ${req.ip}`);
      return res.status(403).json({
        success: false,
        message: 'Invalid 2FA code'
      });
    }

    next();
  } catch (error) {
    logger.error('2FA verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Generate 2FA secret and QR code
export const generate2FASecret = async (userEmail: string, serviceName: string = 'CryptoYield') => {
  const secret = speakeasy.generateSecret({
    name: userEmail,
    issuer: serviceName,
    length: 32
  });

  const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

  return {
    secret: secret.base32,
    qrCode: qrCodeUrl,
    manualEntryKey: secret.base32
  };
};

// IP Whitelist middleware
export const ipWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (allowedIPs.includes(clientIP!)) {
      next();
    } else {
      logger.warn(`Blocked request from unauthorized IP: ${clientIP}`);
      res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
  };
};

// Device fingerprinting middleware
export const deviceFingerprint = (req: Request, res: Response, next: NextFunction) => {
  const userAgent = req.headers['user-agent'];
  const acceptLanguage = req.headers['accept-language'];
  const acceptEncoding = req.headers['accept-encoding'];
  const ip = req.ip;

  // Create device fingerprint
  const fingerprint = Buffer.from(
    `${userAgent}|${acceptLanguage}|${acceptEncoding}|${ip}`
  ).toString('base64');

  (req as any).deviceFingerprint = fingerprint;
  next();
};

// Suspicious activity detection
export const detectSuspiciousActivity = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    const ip = req.ip;
    const userAgent = req.headers['user-agent'];
    const fingerprint = (req as any).deviceFingerprint;

    if (!user) return next();

    // Check for multiple login attempts from different IPs
    const recentLogins = await getRecentLogins(user._id, 24); // Last 24 hours
    const uniqueIPs = new Set(recentLogins.map((login: any) => login.ip));

    if (uniqueIPs.size > 5) {
      logger.warn(`Suspicious activity: Multiple IPs for user ${user._id}`);
      // Could trigger additional verification
    }

    // Check for unusual location (simplified)
    const lastKnownLocation = await getLastKnownLocation(user._id);
    if (lastKnownLocation && isUnusualLocation(ip, lastKnownLocation)) {
      logger.warn(`Suspicious activity: Unusual location for user ${user._id}`);
      // Could trigger email notification
    }

    next();
  } catch (error) {
    logger.error('Suspicious activity detection error:', error);
    next(); // Don't block request on error
  }
};

// Helper functions (simplified implementations)
async function getRecentLogins(userId: string, hours: number) {
  // Implementation would query audit logs
  return [];
}

async function getLastKnownLocation(userId: string) {
  // Implementation would get user's last known location
  return null;
}

function isUnusualLocation(currentIP: string, lastLocation: any) {
  // Implementation would check if IP geolocation is significantly different
  return false;
}

// Password strength validation
export const passwordValidation = [
  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
    .custom((value) => {
      // Check against common passwords
      const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'letmein'];
      if (commonPasswords.includes(value.toLowerCase())) {
        throw new Error('Password is too common');
      }
      return true;
    })
];

// Email validation
export const emailValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address')
    .custom(async (value) => {
      // Check against disposable email domains
      const disposableDomains = ['10minutemail.com', 'tempmail.org', 'guerrillamail.com'];
      const domain = value.split('@')[1];
      if (disposableDomains.includes(domain)) {
        throw new Error('Disposable email addresses are not allowed');
      }
      return true;
    })
];

// Audit logging middleware
export const auditLog = (action: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = (req as any).user;
    const ip = req.ip;
    const userAgent = req.headers['user-agent'];
    const fingerprint = (req as any).deviceFingerprint;

    // Log the action
    logger.info('Audit Log', {
      userId: user?._id,
      action,
      ip,
      userAgent,
      fingerprint,
      timestamp: new Date(),
      path: req.path,
      method: req.method
    });

    next();
  };
};
