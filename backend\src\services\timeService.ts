import { createClient } from 'redis';

interface TimeZoneInfo {
  utc: Date;
  turkey: Date;
  offset: number;
  isDST: boolean;
}

interface TimeLockStatus {
  isLocked: boolean;
  nextUnlockTime: Date;
  reason: string;
  lockType: 'daily' | 'maintenance' | 'emergency';
}

class TimeService {
  private redisClient: any;
  private readonly TURKEY_TIMEZONE = 'Europe/Istanbul';
  private readonly DAILY_UNLOCK_HOUR = 3; // 03:00
  private readonly DAILY_UNLOCK_MINUTE = 0;
  private readonly NTP_TOLERANCE_MS = 1000; // ±1 second tolerance

  constructor() {
    this.initializeRedis();
  }

  private async initializeRedis() {
    try {
      this.redisClient = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379'
      });
      
      this.redisClient.on('error', (err: any) => {
        console.error('Redis Client Error:', err);
      });
      
      await this.redisClient.connect();
      console.log('✅ Redis connected for TimeService');
    } catch (error) {
      console.error('❌ Redis connection failed:', error);
      // Continue without Redis for development
    }
  }

  /**
   * Get current Turkey time (UTC+3)
   */
  getTurkeyTime(): Date {
    const now = new Date();
    // Turkey is UTC+3 (no DST consideration for simplicity)
    return new Date(now.getTime() + (3 * 60 * 60 * 1000));
  }

  /**
   * Get timezone information
   */
  getTimeZoneInfo(): TimeZoneInfo {
    const utc = new Date();
    const turkey = this.getTurkeyTime();
    
    return {
      utc,
      turkey,
      offset: 3, // UTC+3
      isDST: false // Simplified - Turkey doesn't use DST anymore
    };
  }

  /**
   * Check if current time is within allowed trading hours
   * Withdrawals/transfers are blocked before 03:00 Turkey time
   */
  isWithinTradingHours(): boolean {
    const turkeyTime = this.getTurkeyTime();
    const currentHour = turkeyTime.getHours();
    const currentMinute = turkeyTime.getMinutes();
    
    // Allow trading from 03:00 to 02:59 next day
    if (currentHour > this.DAILY_UNLOCK_HOUR) {
      return true;
    } else if (currentHour === this.DAILY_UNLOCK_HOUR && currentMinute >= this.DAILY_UNLOCK_MINUTE) {
      return true;
    }
    
    return false;
  }

  /**
   * Get next unlock time (next 03:00 Turkey time)
   */
  getNextUnlockTime(): Date {
    const turkeyTime = this.getTurkeyTime();
    const nextUnlock = new Date(turkeyTime);
    
    nextUnlock.setHours(this.DAILY_UNLOCK_HOUR, this.DAILY_UNLOCK_MINUTE, 0, 0);
    
    // If current time is after 03:00, set to tomorrow 03:00
    if (turkeyTime.getHours() >= this.DAILY_UNLOCK_HOUR) {
      nextUnlock.setDate(nextUnlock.getDate() + 1);
    }
    
    return nextUnlock;
  }

  /**
   * Get time until next unlock
   */
  getTimeUntilUnlock(): {
    hours: number;
    minutes: number;
    seconds: number;
    totalMs: number;
  } {
    const now = this.getTurkeyTime();
    const nextUnlock = this.getNextUnlockTime();
    const diffMs = nextUnlock.getTime() - now.getTime();
    
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);
    
    return {
      hours,
      minutes,
      seconds,
      totalMs: diffMs
    };
  }

  /**
   * Check time lock status
   */
  async getTimeLockStatus(): Promise<TimeLockStatus> {
    try {
      // Check Redis for emergency locks first
      if (this.redisClient) {
        const emergencyLock = await this.redisClient.get('emergency_time_lock');
        if (emergencyLock) {
          const lockData = JSON.parse(emergencyLock);
          return {
            isLocked: true,
            nextUnlockTime: new Date(lockData.unlockTime),
            reason: lockData.reason || 'Emergency maintenance',
            lockType: 'emergency'
          };
        }

        const maintenanceLock = await this.redisClient.get('maintenance_time_lock');
        if (maintenanceLock) {
          const lockData = JSON.parse(maintenanceLock);
          return {
            isLocked: true,
            nextUnlockTime: new Date(lockData.unlockTime),
            reason: lockData.reason || 'Scheduled maintenance',
            lockType: 'maintenance'
          };
        }
      }
    } catch (error) {
      console.error('Redis time lock check error:', error);
    }

    // Check daily time lock
    const isWithinHours = this.isWithinTradingHours();
    
    if (!isWithinHours) {
      return {
        isLocked: true,
        nextUnlockTime: this.getNextUnlockTime(),
        reason: 'Daily time lock - Trading resumes at 03:00 Turkey time',
        lockType: 'daily'
      };
    }

    return {
      isLocked: false,
      nextUnlockTime: this.getNextUnlockTime(),
      reason: 'Trading hours active',
      lockType: 'daily'
    };
  }

  /**
   * Set emergency time lock
   */
  async setEmergencyLock(durationHours: number, reason: string = 'Emergency maintenance'): Promise<void> {
    if (!this.redisClient) {
      throw new Error('Redis not available for emergency lock');
    }

    const unlockTime = new Date(Date.now() + (durationHours * 60 * 60 * 1000));
    
    const lockData = {
      unlockTime: unlockTime.toISOString(),
      reason,
      setAt: new Date().toISOString(),
      durationHours
    };

    await this.redisClient.setEx(
      'emergency_time_lock',
      durationHours * 3600, // TTL in seconds
      JSON.stringify(lockData)
    );

    console.log(`🚨 Emergency time lock set for ${durationHours} hours: ${reason}`);
  }

  /**
   * Clear emergency time lock
   */
  async clearEmergencyLock(): Promise<void> {
    if (!this.redisClient) {
      throw new Error('Redis not available');
    }

    await this.redisClient.del('emergency_time_lock');
    console.log('✅ Emergency time lock cleared');
  }

  /**
   * Set maintenance lock
   */
  async setMaintenanceLock(durationMinutes: number, reason: string = 'Scheduled maintenance'): Promise<void> {
    if (!this.redisClient) {
      throw new Error('Redis not available for maintenance lock');
    }

    const unlockTime = new Date(Date.now() + (durationMinutes * 60 * 1000));
    
    const lockData = {
      unlockTime: unlockTime.toISOString(),
      reason,
      setAt: new Date().toISOString(),
      durationMinutes
    };

    await this.redisClient.setEx(
      'maintenance_time_lock',
      durationMinutes * 60, // TTL in seconds
      JSON.stringify(lockData)
    );

    console.log(`🔧 Maintenance lock set for ${durationMinutes} minutes: ${reason}`);
  }

  /**
   * Get activation time for new investment (next 03:00)
   */
  getInvestmentActivationTime(): Date {
    return this.getNextUnlockTime();
  }

  /**
   * Check if time is synchronized (NTP check simulation)
   */
  async checkTimeSync(): Promise<{
    isSynced: boolean;
    offset: number;
    tolerance: number;
  }> {
    // In production, this would check against NTP servers
    // For now, we'll simulate it
    const simulatedOffset = Math.random() * 2000 - 1000; // ±1 second random offset
    
    return {
      isSynced: Math.abs(simulatedOffset) <= this.NTP_TOLERANCE_MS,
      offset: simulatedOffset,
      tolerance: this.NTP_TOLERANCE_MS
    };
  }

  /**
   * Format Turkey time for display
   */
  formatTurkeyTime(date?: Date): string {
    const turkeyTime = date || this.getTurkeyTime();
    
    return turkeyTime.toLocaleString('tr-TR', {
      timeZone: 'Europe/Istanbul',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * Get business day info (excluding weekends for some operations)
   */
  isBusinessDay(date?: Date): boolean {
    const checkDate = date || this.getTurkeyTime();
    const dayOfWeek = checkDate.getDay();
    
    // 0 = Sunday, 6 = Saturday
    return dayOfWeek >= 1 && dayOfWeek <= 5;
  }

  /**
   * Calculate next business day
   */
  getNextBusinessDay(date?: Date): Date {
    const startDate = date || this.getTurkeyTime();
    const nextDay = new Date(startDate);
    
    do {
      nextDay.setDate(nextDay.getDate() + 1);
    } while (!this.isBusinessDay(nextDay));
    
    return nextDay;
  }

  /**
   * Cleanup method
   */
  async cleanup(): Promise<void> {
    if (this.redisClient) {
      await this.redisClient.quit();
      console.log('TimeService Redis connection closed');
    }
  }
}

export default new TimeService();
