/**
 * Custom build script for backend
 * This script compiles TypeScript files to JavaScript without type checking
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Paths
const rootDir = path.resolve(__dirname);
const srcDir = path.join(rootDir, 'src');
const distDir = path.join(rootDir, 'dist');
const uploadsDir = path.join(rootDir, 'uploads');
const distUploadsDir = path.join(rootDir, '..', 'uploads');

/**
 * Log a message to the console
 * @param {string} message - The message to log
 * @param {string} color - The color to use
 */
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

/**
 * Create a directory if it doesn't exist
 * @param {string} dir - The directory to create
 */
function createDirIfNotExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    log(`Created directory: ${dir}`, colors.green);
  }
}

/**
 * Clean the dist directory
 */
function cleanDist() {
  try {
    if (fs.existsSync(distDir)) {
      log('Cleaning dist directory...', colors.cyan);
      fs.rmSync(distDir, { recursive: true, force: true });
    }
    createDirIfNotExists(distDir);
    log('Dist directory cleaned', colors.green);
    return true;
  } catch (error) {
    log(`Error cleaning dist directory: ${error.message}`, colors.red);
    return false;
  }
}

/**
 * Compile TypeScript files to JavaScript
 */
function compileTypeScript() {
  try {
    log('Compiling TypeScript files...', colors.cyan);
    
    // Use ts-node to transpile TypeScript files without type checking
    execSync('npx tsc-silent --project tsconfig.json --suppress @', {
      stdio: 'inherit',
      cwd: rootDir
    });
    
    log('TypeScript compilation completed', colors.green);
    return true;
  } catch (error) {
    log(`Error compiling TypeScript: ${error.message}`, colors.red);
    return false;
  }
}

/**
 * Copy environment file to dist
 */
function copyEnvFile() {
  const envProdPath = path.join(rootDir, '.env.production');
  const envDistPath = path.join(distDir, '.env');

  if (fs.existsSync(envProdPath)) {
    try {
      fs.copyFileSync(envProdPath, envDistPath);
      log('Copied .env.production to dist/.env', colors.green);
    } catch (error) {
      log(`Error copying .env file: ${error.message}`, colors.red);
    }
  } else {
    log('No .env.production file found, creating default .env', colors.yellow);
    
    // Create a default .env file
    const defaultEnv = `
# Node environment
NODE_ENV=production

# Server port
PORT=5000

# MongoDB connection
MONGO_URI=*****************************************************************************************
MONGO_USER=cryptoyield_admin
MONGO_PASSWORD=secure_password123

# JWT configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# CORS
FRONTEND_URL=http://localhost

# Blockchain
CONTRACT_ADDRESS=******************************************
PROVIDER_URL=https://mainnet.infura.io/v3/********************************

# Logging
LOG_LEVEL=info

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=60

# Cache
CACHE_TTL=600
`;
    
    fs.writeFileSync(envDistPath, defaultEnv.trim());
    log('Created default .env file', colors.green);
  }
}

/**
 * Setup uploads directory
 */
function setupUploads() {
  createDirIfNotExists(distUploadsDir);
  
  // Copy any existing files from uploads to dist/uploads
  if (fs.existsSync(uploadsDir)) {
    const files = fs.readdirSync(uploadsDir);
    for (const file of files) {
      const sourcePath = path.join(uploadsDir, file);
      const destPath = path.join(distUploadsDir, file);
      
      // Skip directories, only copy files
      if (fs.statSync(sourcePath).isFile()) {
        try {
          fs.copyFileSync(sourcePath, destPath);
          log(`Copied: ${sourcePath} -> ${destPath}`, colors.green);
        } catch (error) {
          log(`Error copying ${sourcePath}: ${error.message}`, colors.red);
        }
      }
    }
  }
}

/**
 * Create a simplified package.json for production
 */
function createPackageJson() {
  const packageJsonPath = path.join(rootDir, 'package.json');
  const distPackageJsonPath = path.join(distDir, 'package.json');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Create a simplified version for production
    const distPackageJson = {
      name: packageJson.name,
      version: packageJson.version,
      main: "index.js",
      scripts: {
        start: "node index.js"
      },
      dependencies: {
        "express": "^5.1.0",
        "cors": "^2.8.5",
        "mongoose": "^8.14.1",
        "morgan": "^1.10.0",
        "dotenv": "^16.5.0",
        "jsonwebtoken": "^9.0.2",
        "bcrypt": "^5.1.1",
        "compression": "^1.8.0",
        "helmet": "^8.1.0",
        "winston": "^3.17.0"
      }
    };
    
    fs.writeFileSync(distPackageJsonPath, JSON.stringify(distPackageJson, null, 2));
    log('Created simplified package.json for production', colors.green);
  } catch (error) {
    log(`Error creating package.json: ${error.message}`, colors.red);
  }
}

/**
 * Main function
 */
function main() {
  log('=== Building Backend ===', colors.bright + colors.blue);
  
  try {
    // Clean dist directory
    if (!cleanDist()) {
      process.exit(1);
    }
    
    // Compile TypeScript
    if (!compileTypeScript()) {
      process.exit(1);
    }
    
    // Copy environment file
    copyEnvFile();
    
    // Setup uploads directory
    setupUploads();
    
    // Create simplified package.json
    createPackageJson();
    
    log('=== Backend Build Completed Successfully ===', colors.bright + colors.green);
  } catch (error) {
    log(`Unexpected error: ${error.message}`, colors.red);
    process.exit(1);
  }
}

// Run the main function
main();
