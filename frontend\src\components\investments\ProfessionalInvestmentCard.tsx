import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardBody,
  VStack,
  HStack,
  Text,
  Badge,
  Button,
  Progress,
  Divider,
  Icon,
  Flex,
  Tooltip,
  useColorModeValue,
  useToast,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Grid,
  GridItem,
  CircularProgress,
  CircularProgressLabel,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FaCoins,
  FaChartLine,
  FaCalendarAlt,
  FaPercentage,
  FaShieldAlt,
  FaTrophy,
  FaInfoCircle,
  FaArrowUp,
  FaArrowDown,
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { formatCurrency } from '../../utils/formatters';
import { getCryptoIcon, getCryptoColor } from '../../utils/cryptoIcons';
import useAuth from '../../hooks/useAuth';

const MotionCard = motion(Card);

interface InvestmentPackage {
  id: string;
  currency: string;
  amount: number;
  totalEarned: number;
  dailyInterest: number;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  activatedAt: Date;
  nextInterestTime: Date;
  withdrawalEligibleTime: Date;
  minimumWithdrawalUSDT: number;
  realTimeUSDTValue: number;
  activeDays: number;
  canWithdraw: boolean;
  interestRate: number;
  tier?: string;
  annualRate?: number;
  projectedEarnings?: number;
  riskLevel?: 'low' | 'medium' | 'high';
  marketCondition?: string;
}

interface ProfessionalInvestmentCardProps {
  package: InvestmentPackage;
  onWithdraw?: (packageId: string) => void;
  onViewDetails?: (packageId: string) => void;
  onReinvest?: (packageId: string) => void;
  onDepositClick?: (currency: string, packageData?: any) => void;
  onWithdrawClick?: (currency: string, packageData?: any) => void;
}

const ProfessionalInvestmentCard: React.FC<ProfessionalInvestmentCardProps> = ({
  package: pkg,
  onWithdraw,
  onViewDetails,
  onReinvest,
  onDepositClick,
  onWithdrawClick,
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();
  const [timeToNextInterest, setTimeToNextInterest] = useState<string>('');

  // Theme colors - Binance inspired
  const bgColor = useColorModeValue('#FFFFFF', '#1E2026');
  const cardBgColor = useColorModeValue('#FAFAFA', '#1E2026');
  const borderColor = useColorModeValue('#E2E8F0', '#2D3748');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#718096', '#848E9C');
  const primaryColor = '#FCD535';
  const successColor = '#02C076';
  const errorColor = '#F84960';

  // Calculate performance metrics
  const totalReturn = pkg.amount + pkg.totalEarned;
  const returnPercentage = ((pkg.totalEarned / pkg.amount) * 100);
  const dailyReturnPercentage = (pkg.interestRate * 100);
  const annualReturnPercentage = (pkg.annualRate || pkg.interestRate * 365) * 100;

  // Get tier information
  const getTierInfo = (tier?: string) => {
    const tiers = {
      tier1: { name: 'Starter', color: '#718096', icon: FaCoins },
      tier2: { name: 'Bronze', color: '#CD7F32', icon: FaShieldAlt },
      tier3: { name: 'Silver', color: '#C0C0C0', icon: FaChartLine },
      tier4: { name: 'Gold', color: '#FFD700', icon: FaTrophy },
      tier5: { name: 'Platinum', color: '#E5E4E2', icon: FaTrophy },
    };
    return tiers[tier as keyof typeof tiers] || tiers.tier1;
  };

  const tierInfo = getTierInfo(pkg.tier);

  // Status configuration
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'active':
        return { color: 'green', label: t('investment.active', 'Active'), bg: successColor };
      case 'pending':
        return { color: 'yellow', label: t('investment.pending', 'Pending'), bg: primaryColor };
      case 'completed':
        return { color: 'blue', label: t('investment.completed', 'Completed'), bg: '#3375BB' };
      case 'withdrawn':
        return { color: 'gray', label: t('investment.withdrawn', 'Withdrawn'), bg: '#718096' };
      default:
        return { color: 'gray', label: status, bg: '#718096' };
    }
  };

  const statusConfig = getStatusConfig(pkg.status);

  // Risk level configuration
  const getRiskConfig = (risk?: string) => {
    switch (risk) {
      case 'low':
        return { color: successColor, label: t('investment.lowRisk', 'Low Risk'), icon: FaShieldAlt };
      case 'medium':
        return { color: primaryColor, label: t('investment.mediumRisk', 'Medium Risk'), icon: FaInfoCircle };
      case 'high':
        return { color: errorColor, label: t('investment.highRisk', 'High Risk'), icon: FaArrowUp };
      default:
        return { color: primaryColor, label: t('investment.mediumRisk', 'Medium Risk'), icon: FaInfoCircle };
    }
  };

  const riskConfig = getRiskConfig(pkg.riskLevel);

  // Update countdown timer
  useEffect(() => {
    const updateTimer = () => {
      const now = new Date();
      const nextInterest = new Date(pkg.nextInterestTime);
      const diff = nextInterest.getTime() - now.getTime();

      if (diff > 0) {
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        setTimeToNextInterest(`${hours}h ${minutes}m`);
      } else {
        setTimeToNextInterest(t('investment.calculating', 'Calculating...'));
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [pkg.nextInterestTime, t]);

  // Enhanced deposit button click handler
  const handleDepositClick = () => {
    if (!user) {
      toast({
        title: t('common.loginRequired', 'Login Required'),
        description: t('common.loginRequiredDesc', 'Please log in to make a deposit.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const packageData = {
      id: pkg.id,
      currency: pkg.currency,
      amount: pkg.amount,
      totalEarned: pkg.totalEarned,
      status: pkg.status,
      activeDays: pkg.activeDays
    };

    onDepositClick?.(pkg.currency, packageData);
  };

  // Enhanced withdraw button click handler with Total Earned data
  const handleWithdrawClick = () => {
    if (!user) {
      toast({
        title: t('common.loginRequired', 'Login Required'),
        description: t('common.loginRequiredDesc', 'Please log in to withdraw.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const packageData = {
      id: pkg.id,
      currency: pkg.currency,
      amount: pkg.amount,
      totalEarned: pkg.totalEarned,
      status: pkg.status,
      activeDays: pkg.activeDays
    };

    console.log(`Opening withdraw modal for ${pkg.currency}:`, {
      currency: pkg.currency,
      totalEarned: pkg.totalEarned,
      packageData: packageData
    });

    onWithdrawClick?.(pkg.currency, packageData);
  };

  return (
    <MotionCard
      bg={bgColor}
      borderColor={borderColor}
      borderWidth="1px"
      borderRadius="2xl"
      overflow="hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      boxShadow="0 4px 12px rgba(0, 0, 0, 0.1)"
      _hover={{
        transform: 'translateY(-6px)',
        boxShadow: `0 16px 32px rgba(0, 0, 0, 0.2), 0 0 0 1px ${primaryColor}40`,
        borderColor: primaryColor
      }}
      position="relative"
    >
      {/* Enhanced status indicator with gradient */}
      <Box
        position="absolute"
        top={0}
        left={0}
        right={0}
        height="4px"
        bg={`linear-gradient(90deg, ${statusConfig.bg} 0%, ${statusConfig.bg}80 50%, ${statusConfig.bg}40 100%)`}
      />

      <CardBody p={4}>
        <VStack spacing={4} align="stretch">
          {/* Enhanced Header */}
          <Flex justify="space-between" align="center">
            <HStack spacing={4}>
              <Box
                bg={`${getCryptoColor(pkg.currency, primaryColor)}15`}
                p={4}
                borderRadius="xl"
                border="2px solid"
                borderColor={`${getCryptoColor(pkg.currency, primaryColor)}30`}
                position="relative"
                overflow="hidden"
              >
                <Box
                  position="absolute"
                  top={0}
                  left={0}
                  right={0}
                  bottom={0}
                  bg={`linear-gradient(135deg, ${getCryptoColor(pkg.currency, primaryColor)}20 0%, transparent 100%)`}
                />
                <Icon
                  as={getCryptoIcon(pkg.currency)}
                  color={getCryptoColor(pkg.currency, primaryColor)}
                  boxSize={7}
                  zIndex={1}
                />
              </Box>
              <VStack align="start" spacing={1} flex={1} minW={0}>
                <Text
                  fontWeight="800"
                  color={textColor}
                  fontSize={{ base: "lg", md: "xl" }}
                  lineHeight="1.2"
                  letterSpacing="-0.02em"
                >
                  {pkg.currency} Investment
                </Text>
                <HStack spacing={3} flexWrap="wrap">
                  <Badge
                    bg={`linear-gradient(135deg, ${statusConfig.bg} 0%, ${statusConfig.bg}80 100%)`}
                    color="white"
                    variant="solid"
                    borderRadius="full"
                    fontSize="xs"
                    fontWeight="600"
                    px={3}
                    py={1}
                    boxShadow={`0 2px 8px ${statusConfig.bg}30`}
                  >
                    {statusConfig.label}
                  </Badge>
                  <Badge
                    bg={`${tierInfo.color}20`}
                    color={tierInfo.color}
                    borderRadius="full"
                    fontSize="xs"
                    fontWeight="600"
                    px={3}
                    py={1}
                    border="1px solid"
                    borderColor={`${tierInfo.color}40`}
                  >
                    <Icon as={tierInfo.icon} mr={1} boxSize={3} />
                    {tierInfo.name}
                  </Badge>
                </HStack>
              </VStack>
            </HStack>

            <VStack align="end" spacing={1} minW="140px">
              <Text
                color={secondaryTextColor}
                fontSize={{ base: "sm", md: "md" }}
                fontWeight="600"
                textTransform="uppercase"
                letterSpacing="0.05em"
              >
                {t('investment.totalValue', 'Total Value')}
              </Text>
              <Text
                color={textColor}
                fontWeight="800"
                fontSize={{ base: "xl", md: "2xl" }}
                lineHeight="1.1"
                letterSpacing="-0.02em"
              >
                ${pkg.realTimeUSDTValue.toFixed(2)}
              </Text>
            </VStack>
          </Flex>

          {/* Performance Overview */}
          <Grid templateColumns="repeat(2, 1fr)" gap={4}>
            <GridItem>
              <Stat minW={0}>
                <StatLabel
                  color={secondaryTextColor}
                  fontSize={{ base: "xs", md: "sm" }}
                  isTruncated
                >
                  {t('investment.principal', 'Principal')}
                </StatLabel>
                <StatNumber
                  color={textColor}
                  fontSize={{ base: "md", md: "lg" }}
                  isTruncated
                >
                  {formatCurrency(pkg.amount, pkg.currency)}
                </StatNumber>
              </Stat>
            </GridItem>
            <GridItem>
              <Stat minW={0}>
                <StatLabel
                  color={secondaryTextColor}
                  fontSize={{ base: "xs", md: "sm" }}
                  isTruncated
                >
                  {t('investment.totalEarned', 'Total Earned')}
                </StatLabel>
                <StatNumber
                  color={successColor}
                  fontSize={{ base: "md", md: "lg" }}
                  isTruncated
                >
                  {formatCurrency(pkg.totalEarned, pkg.currency)}
                </StatNumber>
                <StatHelpText
                  color={successColor}
                  fontSize={{ base: "xs", md: "sm" }}
                  isTruncated
                >
                  <StatArrow type="increase" />
                  {returnPercentage.toFixed(2)}%
                </StatHelpText>
              </Stat>
            </GridItem>
          </Grid>

          <Divider borderColor={borderColor} />

          {/* Interest Information */}
          <VStack spacing={3} align="stretch">
            <HStack justify="space-between">
              <HStack>
                <Icon as={FaPercentage} color={primaryColor} />
                <Text color={textColor} fontSize="sm">
                  {t('investment.dailyRate', 'Daily Rate')}
                </Text>
              </HStack>
              <Text color={successColor} fontWeight="bold">
                {dailyReturnPercentage.toFixed(3)}%
              </Text>
            </HStack>

            <HStack justify="space-between">
              <HStack>
                <Icon as={FaChartLine} color={primaryColor} />
                <Text color={textColor} fontSize="sm">
                  {t('investment.annualRate', 'Annual Rate')}
                </Text>
              </HStack>
              <Text color={successColor} fontWeight="bold">
                {annualReturnPercentage.toFixed(1)}% APY
              </Text>
            </HStack>

            <HStack justify="space-between">
              <HStack>
                <Icon as={FaCalendarAlt} color={primaryColor} />
                <Text color={textColor} fontSize="sm">
                  {t('investment.activeDays', 'Active Days')}
                </Text>
              </HStack>
              <Text color={textColor} fontWeight="bold">
                {pkg.activeDays}
              </Text>
            </HStack>
          </VStack>

          {/* Next Interest Timer */}
          {pkg.status === 'active' && (
            <Box
              bg={cardBgColor}
              p={4}
              borderRadius="lg"
              borderWidth="1px"
              borderColor={borderColor}
            >
              <HStack justify="space-between" align="center">
                <VStack align="start" spacing={1}>
                  <Text color={secondaryTextColor} fontSize="sm">
                    {t('investment.nextInterest', 'Next Interest')}
                  </Text>
                  <Text color={textColor} fontWeight="bold">
                    {timeToNextInterest}
                  </Text>
                </VStack>
                <CircularProgress
                  value={75} // This would be calculated based on time remaining
                  color={primaryColor}
                  size="50px"
                  thickness="8px"
                >
                  <CircularProgressLabel fontSize="xs" color={textColor}>
                    75%
                  </CircularProgressLabel>
                </CircularProgress>
              </HStack>
            </Box>
          )}

          {/* Enhanced Action Buttons with Professional Design */}
          <VStack spacing={4} align="stretch">
            {/* Primary Action Buttons Row */}
            <HStack spacing={4}>
              {/* Enhanced Deposit Button */}
              <Button
                leftIcon={<Icon as={FaArrowDown} boxSize={4} />}
                bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
                color="#0B0E11"
                _hover={{
                  bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
                  boxShadow: "0 8px 25px rgba(240, 185, 11, 0.4)",
                  transform: "translateY(-2px)"
                }}
                _active={{
                  bg: "linear-gradient(135deg, #E6C200 0%, #F0B90B 100%)",
                  transform: 'translateY(0px)',
                }}
                size="sm"
                flex={1}
                minH="44px"
                fontSize="md"
                fontWeight="700"
                borderRadius="xl"
                onClick={handleDepositClick}
                transition="all 0.3s ease"
                boxShadow="0 4px 12px rgba(240, 185, 11, 0.2)"
                sx={{
                  '@media (max-width: 767px)': {
                    touchAction: 'manipulation',
                    WebkitTapHighlightColor: 'transparent',
                    WebkitTouchCallout: 'none',
                    cursor: 'pointer',
                    minHeight: '48px',
                    minWidth: '48px',
                  }
                }}
              >
                {t('common.deposit', 'Deposit')}
              </Button>

              {/* Enhanced Withdraw Button */}
              <Button
                leftIcon={<Icon as={FaArrowUp} boxSize={4} />}
                bg="rgba(2, 192, 118, 0.1)"
                color="#02C076"
                border="2px solid"
                borderColor="#02C076"
                _hover={{
                  bg: "rgba(2, 192, 118, 0.2)",
                  borderColor: "#02C076",
                  boxShadow: "0 8px 25px rgba(2, 192, 118, 0.3)",
                  transform: "translateY(-2px)"
                }}
                _active={{
                  bg: "rgba(2, 192, 118, 0.3)",
                  transform: 'translateY(0px)',
                  borderColor: "#02C076",
                }}
                size="sm"
                flex={1}
                minH="44px"
                fontSize="md"
                fontWeight="700"
                borderRadius="xl"
                onClick={handleWithdrawClick}
                transition="all 0.3s ease"
                boxShadow="0 4px 12px rgba(2, 192, 118, 0.1)"
                sx={{
                  '@media (max-width: 767px)': {
                    touchAction: 'manipulation',
                    WebkitTapHighlightColor: 'transparent',
                    WebkitTouchCallout: 'none',
                    cursor: 'pointer',
                    minHeight: '48px',
                    minWidth: '48px',
                  }
                }}
              >
                {t('common.withdraw', 'Withdraw')}
              </Button>
            </HStack>

            {/* Enhanced Secondary Action Button */}
            <Button
              leftIcon={<Icon as={FaInfoCircle} boxSize={4} />}
              bg="rgba(240, 185, 11, 0.1)"
              color="#FCD535"
              border="2px solid"
              borderColor="rgba(240, 185, 11, 0.3)"
              _hover={{
                bg: "rgba(240, 185, 11, 0.2)",
                borderColor: "rgba(240, 185, 11, 0.5)",
                boxShadow: "0 6px 20px rgba(240, 185, 11, 0.2)",
                transform: "translateY(-1px)"
              }}
              _active={{
                transform: "translateY(0px)"
              }}
              size="sm"
              minH="40px"
              fontSize="sm"
              fontWeight="600"
              borderRadius="xl"
              onClick={() => onViewDetails?.(pkg.id)}
              transition="all 0.3s ease"
            >
              {t('investment.viewDetails', 'View Details')}
            </Button>
          </VStack>
        </VStack>
      </CardBody>
    </MotionCard>
  );
};

export default ProfessionalInvestmentCard;
