import React, { useEffect, useMemo } from 'react';
import { Box } from '@chakra-ui/react';
import { useImpersonation } from '../hooks/useImpersonation';

interface ImpersonationLayoutWrapperProps {
  children: React.ReactNode;
}

const ImpersonationLayoutWrapper: React.FC<ImpersonationLayoutWrapperProps> = ({ children }) => {
  const { shouldAdjustLayout } = useImpersonation();

  // Apply body class for global layout adjustments
  useEffect(() => {
    const bodyClassList = document.body.classList;
    const className = 'impersonation-banner-active';

    if (shouldAdjustLayout) {
      bodyClassList.add(className);
    } else {
      bodyClassList.remove(className);
    }

    // Cleanup on unmount
    return () => {
      bodyClassList.remove(className);
    };
  }, [shouldAdjustLayout]);

  // Memoize the padding value to prevent unnecessary re-renders
  const paddingTop = useMemo(() => {
    return shouldAdjustLayout ? { base: "80px", md: "60px" } : 0;
  }, [shouldAdjustLayout]);

  return (
    <Box
      minH="100vh"
      transition="padding-top 0.3s ease-in-out"
      pt={paddingTop}
    >
      {children}
    </Box>
  );
};

export default React.memo(ImpersonationLayoutWrapper);
