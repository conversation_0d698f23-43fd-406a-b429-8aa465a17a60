/**
 * Performance configuration for the application
 */

// Feature flags for performance optimizations
export const PERFORMANCE_CONFIG = {
  // Enable/disable lazy loading for components and routes
  ENABLE_LAZY_LOADING: true,
  
  // Enable/disable image optimization
  ENABLE_IMAGE_OPTIMIZATION: true,
  
  // Enable/disable API response caching
  ENABLE_API_CACHING: true,
  
  // Enable/disable prefetching of routes
  ENABLE_ROUTE_PREFETCHING: true,
  
  // Enable/disable code splitting
  ENABLE_CODE_SPLITTING: true,
  
  // Enable/disable service worker for offline support
  ENABLE_SERVICE_WORKER: true,
  
  // Cache expiration times (in milliseconds)
  CACHE_EXPIRATION: {
    // User data cache (5 minutes)
    USER_DATA: 5 * 60 * 1000,
    
    // Transaction data cache (2 minutes)
    TRANSACTION_DATA: 2 * 60 * 1000,
    
    // Static content cache (1 hour)
    STATIC_CONTENT: 60 * 60 * 1000,
    
    // Market data cache (30 seconds)
    MARKET_DATA: 30 * 1000
  },
  
  // Prefetch settings
  PREFETCH: {
    // Delay before prefetching routes (in milliseconds)
    DELAY: 1000,
    
    // Routes to prefetch on application load
    ROUTES: ['/login', '/register', '/about', '/contact']
  },
  
  // Image optimization settings
  IMAGE_OPTIMIZATION: {
    // Default image quality (0-100)
    QUALITY: 80,
    
    // Image sizes for responsive images
    SIZES: {
      SMALL: 480,
      MEDIUM: 768,
      LARGE: 1200
    },
    
    // Image formats to generate
    FORMATS: ['webp', 'avif']
  }
};

// Helper functions for performance optimizations
export const shouldLazyLoad = (componentName: string): boolean => {
  // List of components that should always be eagerly loaded
  const EAGER_COMPONENTS = ['Navbar', 'Footer', 'LoadingSpinner'];
  
  return PERFORMANCE_CONFIG.ENABLE_LAZY_LOADING && !EAGER_COMPONENTS.includes(componentName);
};

export const getCacheExpiration = (dataType: keyof typeof PERFORMANCE_CONFIG.CACHE_EXPIRATION): number => {
  return PERFORMANCE_CONFIG.CACHE_EXPIRATION[dataType] || PERFORMANCE_CONFIG.CACHE_EXPIRATION.STATIC_CONTENT;
};

export const shouldPrefetch = (route: string): boolean => {
  return PERFORMANCE_CONFIG.ENABLE_ROUTE_PREFETCHING && PERFORMANCE_CONFIG.PREFETCH.ROUTES.includes(route);
};

export default PERFORMANCE_CONFIG;
