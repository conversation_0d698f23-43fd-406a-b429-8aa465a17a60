import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the context interface
interface ThemeContextType {
  isDarkMode: boolean;
  toggleTheme: () => void;
  themeTransition: string;
}

// Create the context with default values
const ThemeContext = createContext<ThemeContextType>({
  isDarkMode: true,
  toggleTheme: () => {},
  themeTransition: 'all 0.3s ease',
});

// Hook for using the theme context
export const useTheme = () => useContext(ThemeContext);

// Props for the provider component
interface ThemeProviderProps {
  children: ReactNode;
}

// Theme provider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Initialize theme state from localStorage or default to dark mode
  const [isDarkMode, setIsDarkMode] = useState<boolean>(() => {
    const savedTheme = localStorage.getItem('theme');
    return savedTheme ? savedTheme === 'dark' : true; // Default to dark mode
  });

  // Define transition for smooth theme changes
  const themeTransition = 'all 0.3s ease';

  // Toggle between light and dark mode
  const toggleTheme = () => {
    setIsDarkMode(prev => !prev);
  };

  // Update localStorage and apply theme when isDarkMode changes
  useEffect(() => {
    // Save theme preference to localStorage
    localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');

    // Apply theme to document
    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');

    // Apply theme-specific styles
    if (isDarkMode) {
      document.documentElement.classList.add('dark-theme');
      document.documentElement.classList.remove('light-theme');
    } else {
      document.documentElement.classList.add('light-theme');
      document.documentElement.classList.remove('dark-theme');
    }
  }, [isDarkMode]);

  // Provide the theme context to children
  return (
    <ThemeContext.Provider value={{ isDarkMode, toggleTheme, themeTransition }}>
      {children}
    </ThemeContext.Provider>
  );
};
