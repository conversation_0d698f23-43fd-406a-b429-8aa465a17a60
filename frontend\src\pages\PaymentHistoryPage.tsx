import React, { useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Flex,
  Button,
  Select,
  Input,
  FormControl,
  FormLabel,
  useColorModeValue,
  Divider,
  Badge
} from '@chakra-ui/react';
import { FaCalendarAlt, FaDownload, FaFilter } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import PaymentHistory from '../components/PaymentHistory';

const PaymentHistoryPage: React.FC = () => {
  const { t } = useTranslation();
  const [dateRange, setDateRange] = useState<string>('all');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [currency, setCurrency] = useState<string>('all');
  
  // Colors
  const bgColor = useColorModeValue('#0B0E11', '#0B0E11');
  const cardBgColor = useColorModeValue('#1E2329', '#1E2329');
  const textColor = useColorModeValue('#EAECEF', '#EAECEF');
  const borderColor = useColorModeValue('#2B3139', '#2B3139');
  const primaryColor = '#F0B90B';
  
  // Handle date range change
  const handleDateRangeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setDateRange(value);
    
    // Set start and end dates based on selection
    const today = new Date();
    const start = new Date();
    
    switch (value) {
      case '7days':
        start.setDate(today.getDate() - 7);
        break;
      case '30days':
        start.setDate(today.getDate() - 30);
        break;
      case '90days':
        start.setDate(today.getDate() - 90);
        break;
      case 'custom':
        // Don't change dates for custom range
        return;
      default:
        // 'all' - clear dates
        setStartDate('');
        setEndDate('');
        return;
    }
    
    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(today.toISOString().split('T')[0]);
  };
  
  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="lg" color={primaryColor} mb={2}>
            {t('payments.title', 'Payment History')}
          </Heading>
          <Text color={textColor}>
            {t('payments.description', 'View and track all your payments and earnings.')}
          </Text>
        </Box>
        
        {/* Filters */}
        <Box bg={cardBgColor} p={6} borderRadius="xl" borderWidth="1px" borderColor={borderColor}>
          <Heading size="md" color={textColor} mb={4}>
            {t('payments.filters', 'Filters')}
          </Heading>
          
          <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
            <FormControl>
              <FormLabel color={textColor}>{t('payments.dateRange', 'Date Range')}</FormLabel>
              <Select
                value={dateRange}
                onChange={handleDateRangeChange}
                bg={bgColor}
                color={textColor}
                borderColor={borderColor}
              >
                <option value="all">{t('payments.allTime', 'All Time')}</option>
                <option value="7days">{t('payments.last7Days', 'Last 7 Days')}</option>
                <option value="30days">{t('payments.last30Days', 'Last 30 Days')}</option>
                <option value="90days">{t('payments.last90Days', 'Last 90 Days')}</option>
                <option value="custom">{t('payments.custom', 'Custom Range')}</option>
              </Select>
            </FormControl>
            
            {dateRange === 'custom' && (
              <>
                <FormControl>
                  <FormLabel color={textColor}>{t('payments.startDate', 'Start Date')}</FormLabel>
                  <Input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    bg={bgColor}
                    color={textColor}
                    borderColor={borderColor}
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel color={textColor}>{t('payments.endDate', 'End Date')}</FormLabel>
                  <Input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    bg={bgColor}
                    color={textColor}
                    borderColor={borderColor}
                  />
                </FormControl>
              </>
            )}
            
            <FormControl>
              <FormLabel color={textColor}>{t('payments.currency', 'Currency')}</FormLabel>
              <Select
                value={currency}
                onChange={(e) => setCurrency(e.target.value)}
                bg={bgColor}
                color={textColor}
                borderColor={borderColor}
              >
                <option value="all">{t('payments.allCurrencies', 'All Currencies')}</option>
                <option value="BTC">Bitcoin (BTC)</option>
                <option value="ETH">Ethereum (ETH)</option>
                <option value="USDT">Tether (USDT)</option>
              </Select>
            </FormControl>
            
            <FormControl alignSelf="flex-end">
              <Button
                leftIcon={<FaFilter />}
                colorScheme="yellow"
                variant="solid"
                w="100%"
              >
                {t('payments.applyFilters', 'Apply Filters')}
              </Button>
            </FormControl>
          </Flex>
        </Box>
        
        {/* Payment History */}
        <Box bg={cardBgColor} p={6} borderRadius="xl" borderWidth="1px" borderColor={borderColor}>
          <PaymentHistory limit={20} />
        </Box>
        
        {/* Export Options */}
        <Box bg={cardBgColor} p={6} borderRadius="xl" borderWidth="1px" borderColor={borderColor}>
          <Heading size="md" color={textColor} mb={4}>
            {t('payments.exportOptions', 'Export Options')}
          </Heading>
          
          <HStack spacing={4}>
            <Button
              leftIcon={<FaDownload />}
              colorScheme="yellow"
              variant="outline"
            >
              {t('payments.exportCSV', 'Export as CSV')}
            </Button>
            
            <Button
              leftIcon={<FaDownload />}
              colorScheme="yellow"
              variant="outline"
            >
              {t('payments.exportPDF', 'Export as PDF')}
            </Button>
          </HStack>
        </Box>
      </VStack>
    </Container>
  );
};

export default PaymentHistoryPage;
