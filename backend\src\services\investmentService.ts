import Investment from '../models/investmentModel';
import Transaction from '../models/transactionModel';
import Wallet from '../models/walletModel';
import { logger } from '../utils/logger';
import realTimeDataService from './realTimeDataService';

/**
 * Service for handling investment operations
 */
class InvestmentService {
  /**
   * Create a new investment
   * @param userId User ID
   * @param data Investment data
   * @returns Created investment
   */
  async createInvestment(userId: string, data: any): Promise<any> {
    try {
      const { currency, amount, description, network } = data;
      
      // Create investment
      const investment = await Investment.create({
        userId,
        currency: currency.toUpperCase(),
        amount,
        description,
        cryptoAddress: data.cryptoAddress || '',
        network: network || 'default',
        status: 'pending',
        userName: data.userName,
        userEmail: data.userEmail
      });
      
      logger.info(`New investment created: ${investment._id} for user: ${userId}`);
      
      // Create transaction record
      const transaction = await Transaction.create({
        userId,
        type: 'deposit',
        source: 'investment',
        amount: investment.amount,
        asset: investment.currency,
        status: investment.status,
        cryptoAddress: investment.cryptoAddress,
        blockchainNetwork: investment.network,
        description: investment.description || 'Investment deposit',
        investmentId: investment._id,
        txHash: investment.txHash || '',
        userName: data.userName,
        userEmail: data.userEmail
      });
      
      logger.info(`Transaction created for investment: ${transaction._id}`);
      
      // Send real-time notification
      await realTimeDataService.notifyNewInvestment(investment);
      await realTimeDataService.notifyNewDeposit(transaction);
      
      return investment;
    } catch (error) {
      logger.error('Error creating investment:', error);
      throw error;
    }
  }
  
  /**
   * Approve an investment
   * @param investmentId Investment ID
   * @param adminId Admin user ID
   * @param adminNote Optional admin note
   * @returns Updated investment
   */
  async approveInvestment(investmentId: string, adminId: string, adminNote?: string): Promise<any> {
    try {
      // Find the investment
      const investment = await Investment.findById(investmentId);
      if (!investment) {
        throw new Error('Investment not found');
      }
      
      // Check if already approved
      if (investment.status === 'approved') {
        return investment;
      }
      
      // Update investment status
      investment.status = 'approved';
      investment.adminId = adminId;
      if (adminNote) investment.adminNote = adminNote;
      investment.approvedAt = new Date();
      
      await investment.save();
      
      logger.info(`Investment ${investmentId} approved by admin ${adminId}`);
      
      // Find related transaction
      const transaction = await Transaction.findOne({ investmentId });
      if (transaction) {
        transaction.status = 'approved';
        if (adminNote) {
          if (!transaction.metadata) transaction.metadata = {};
          transaction.metadata.adminNote = adminNote;
        }
        await transaction.save();
        
        // Send real-time notification for transaction update
        await realTimeDataService.notifyDepositStatusUpdate(transaction);
      }
      
      // Update user wallet
      await this.updateUserWallet(investment.userId.toString(), investment.currency, investment.amount);
      
      return investment;
    } catch (error) {
      logger.error(`Error approving investment ${investmentId}:`, error);
      throw error;
    }
  }
  
  /**
   * Reject an investment
   * @param investmentId Investment ID
   * @param adminId Admin user ID
   * @param adminNote Optional admin note
   * @returns Updated investment
   */
  async rejectInvestment(investmentId: string, adminId: string, adminNote?: string): Promise<any> {
    try {
      // Find the investment
      const investment = await Investment.findById(investmentId);
      if (!investment) {
        throw new Error('Investment not found');
      }
      
      // Check if already rejected
      if (investment.status === 'rejected') {
        return investment;
      }
      
      // Update investment status
      investment.status = 'rejected';
      investment.adminId = adminId;
      if (adminNote) investment.adminNote = adminNote;
      investment.rejectedAt = new Date();
      
      await investment.save();
      
      logger.info(`Investment ${investmentId} rejected by admin ${adminId}`);
      
      // Find related transaction
      const transaction = await Transaction.findOne({ investmentId });
      if (transaction) {
        transaction.status = 'rejected';
        if (adminNote) {
          if (!transaction.metadata) transaction.metadata = {};
          transaction.metadata.adminNote = adminNote;
        }
        await transaction.save();
        
        // Send real-time notification for transaction update
        await realTimeDataService.notifyDepositStatusUpdate(transaction);
      }
      
      return investment;
    } catch (error) {
      logger.error(`Error rejecting investment ${investmentId}:`, error);
      throw error;
    }
  }
  
  /**
   * Update user wallet balance after investment approval
   * @param userId User ID
   * @param currency Currency symbol
   * @param amount Amount to add
   */
  private async updateUserWallet(userId: string, currency: string, amount: number): Promise<void> {
    try {
      // Find or create wallet
      let wallet = await Wallet.findOne({ userId });
      
      if (!wallet) {
        wallet = await Wallet.create({
          userId,
          assets: [
            {
              symbol: currency,
              balance: amount,
              commissionBalance: 0,
              interestBalance: 0
            }
          ]
        });
        
        logger.info(`Created new wallet for user ${userId} with initial balance ${amount} ${currency}`);
        
        // Send real-time notification
        await realTimeDataService.notifyWalletUpdate(userId, currency, amount);
        return;
      }
      
      // Find asset in wallet
      const assetIndex = wallet.assets.findIndex(asset => asset.symbol === currency);
      
      if (assetIndex >= 0) {
        // Update existing asset
        wallet.assets[assetIndex].balance += amount;
        logger.info(`Updated wallet balance for user ${userId}: +${amount} ${currency}`);
      } else {
        // Add new asset
        wallet.assets.push({
          symbol: currency,
          balance: amount,
          commissionBalance: 0,
          interestBalance: 0
        });
        logger.info(`Added new asset ${currency} to wallet of user ${userId} with balance ${amount}`);
      }
      
      await wallet.save();
      
      // Send real-time notification
      const newBalance = assetIndex >= 0 ? wallet.assets[assetIndex].balance : amount;
      await realTimeDataService.notifyWalletUpdate(userId, currency, newBalance);
      
    } catch (error) {
      logger.error(`Error updating wallet for user ${userId}:`, error);
      throw error;
    }
  }
}

export const investmentService = new InvestmentService();
export default investmentService;