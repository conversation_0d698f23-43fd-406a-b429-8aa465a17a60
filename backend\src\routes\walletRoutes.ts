import express from 'express';
import {
  connectWallet,
  getWalletBalance,
  toggleMode,
  depositAsset,
  getTransactionHistory,
  getDepositAddress,
  getAvailableWallets,
  getUserAddresses,
  getCurrencyBalance,
  getAllBalances,
  startDepositMonitoring,
  getDepositHistory,
  getWalletEarnedBalance,
  getAssetEarnedBalance,
  getWithdrawableBalance
} from '../controllers/walletController';
import { protect } from '../middleware/authMiddleware';
import { cacheMiddleware, clearCache } from '../middleware/cacheMiddleware';
import { clearWalletCache } from '../utils/cacheUtils';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

// Middleware to clear wallet cache after operations
const clearWalletCacheMiddleware = (req: any, res: any, next: any) => {
  const originalSend = res.send;
  res.send = function(data: any) {
    // Clear cache after successful response
    if (res.statusCode >= 200 && res.statusCode < 300 && req.user?._id) {
      clearWalletCache(req.user._id, `${req.method} ${req.originalUrl}`);
    }
    return originalSend.call(this, data);
  };
  next();
};

// All routes are protected
router.post('/connect', protect, clearWalletCacheMiddleware, wrapController(connectWallet));
router.get('/info', protect, wrapController(getWalletBalance)); // No cache for real-time accuracy
router.post('/toggle-mode', protect, clearWalletCacheMiddleware, wrapController(toggleMode));
router.post('/deposit', protect, clearWalletCacheMiddleware, wrapController(depositAsset));

router.get('/transactions', protect, cacheMiddleware({
  keyPrefix: 'api:wallet:transactions:',
  keyGenerator: (req) => `${req.user._id}:${req.originalUrl}`
}), wrapController(getTransactionHistory));

// Get deposit address for a currency
router.get('/deposit-address/:currency', protect, wrapController(getDepositAddress));

// Get available wallet addresses for all currencies or a specific currency
router.get('/available', protect, wrapController(getAvailableWallets));

// ===== NEW CRYPTO DEPOSIT SYSTEM ROUTES =====

// Get user's crypto wallet addresses
router.get('/user-addresses', protect, wrapController(getUserAddresses));

// Get balance for a specific currency
router.get('/:currency/balance', protect, wrapController(getCurrencyBalance));

// Get all wallet balances with USDT values
router.get('/balances', protect, wrapController(getAllBalances));

// Start monitoring deposits for user addresses
router.post('/monitor', protect, clearWalletCacheMiddleware, wrapController(startDepositMonitoring));

// Get deposit history for user
router.get('/deposits/history', protect, wrapController(getDepositHistory));



// ===== EARNED BALANCE VIRTUAL PROPERTY ROUTES =====

// Get wallet with earned balance breakdown and summary
router.get('/earned-balance', protect, wrapController(getWalletEarnedBalance));

// Get earned balance for a specific asset
router.get('/earned-balance/:asset', protect, wrapController(getAssetEarnedBalance));

// Get withdrawable balance for a specific cryptocurrency
router.get('/withdrawable-balance/:crypto', protect, wrapController(getWithdrawableBalance));

export default router;
