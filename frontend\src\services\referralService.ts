import api from './api';

// Use the centralized API service instead of creating a new instance

export interface ReferralHistoryItem {
  _id: string;
  referredUser: {
    id: string;
    name: string;
    email: string; // Anonymized email (e.g., user***@email.com)
  };
  registrationDate: string;
  commissionAmount: number;
  commissionStatus: 'pending' | 'approved' | 'paid' | 'rejected';
  investmentAmount: number;
  currency: string;
  level: number;
  createdAt: string;
  updatedAt: string;
}

export interface ReferralStats {
  totalReferrals: number;
  activeReferrals: number;
  totalCommissionEarned: number;
  pendingCommission: number;
  availableForWithdrawal: number;
  referralCode: string;
  referralLink: string;
  currentTier: string;
  commissionRate: number;
  nextTierRequirement?: number;
}

export interface ReferralCommissionBalance {
  currency: string;
  totalEarned: number;
  availableForWithdrawal: number;
  totalWithdrawn: number;
  pendingAmount: number;
  lastCommissionDate?: string;
}

export interface ReferralWithdrawalRequest {
  amount: number;
  currency: string;
  targetAddress: string;
  network?: string;
  memo?: string;
}

export interface ReferralAnalytics {
  monthlyReferrals: Array<{
    month: string;
    count: number;
    commissions: number;
  }>;
  conversionRate: number;
  averageCommissionPerReferral: number;
  topPerformingMonths: Array<{
    month: string;
    performance: number;
  }>;
}

class ReferralService {
  /**
   * Get user's referral history with pagination
   */
  async getReferralHistory(params?: {
    page?: number;
    limit?: number;
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{
    referrals: ReferralHistoryItem[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    try {
      const response = await api.get('/user/referrals/history', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching referral history:', error);
      throw error;
    }
  }

  /**
   * Get user's referral statistics
   */
  async getReferralStats(): Promise<ReferralStats> {
    try {
      const response = await api.get('/user/referrals/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching referral stats:', error);
      throw error;
    }
  }

  /**
   * Get user's referral code
   */
  async getReferralCode(): Promise<{
    referralCode: string;
    referralLink: string;
  }> {
    try {
      const response = await api.get('/user/referrals/code');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching referral code:', error);
      throw error;
    }
  }

  /**
   * Generate new referral code
   */
  async generateReferralCode(): Promise<{
    referralCode: string;
    referralLink: string;
    message: string;
  }> {
    try {
      const response = await api.post('/user/referrals/generate-code');
      return response.data.data;
    } catch (error) {
      console.error('Error generating referral code:', error);
      throw error;
    }
  }

  /**
   * Get referral commission balance for withdrawals
   */
  async getCommissionBalance(): Promise<ReferralCommissionBalance[]> {
    try {
      const response = await api.get('/user/referrals/commission-balance');
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching commission balance:', error);
      throw error;
    }
  }

  /**
   * Get commission balance for a specific currency
   */
  async getCommissionBalanceByCurrency(currency: string): Promise<ReferralCommissionBalance | null> {
    try {
      const response = await api.get(`/user/referrals/commission-balance/${currency.toUpperCase()}`);
      return response.data.data || null;
    } catch (error) {
      console.error(`Error fetching commission balance for ${currency}:`, error);
      throw error;
    }
  }

  /**
   * Request withdrawal of referral commissions
   */
  async requestCommissionWithdrawal(params: ReferralWithdrawalRequest): Promise<{
    success: boolean;
    transactionId: string;
    estimatedProcessingTime: string;
    message: string;
  }> {
    try {
      const response = await api.post('/user/referrals/withdraw-commission', params);
      return response.data;
    } catch (error) {
      console.error('Error requesting commission withdrawal:', error);
      throw error;
    }
  }

  /**
   * Validate referral commission withdrawal
   */
  async validateCommissionWithdrawal(params: {
    amount: number;
    currency: string;
    targetAddress: string;
  }): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    availableBalance: number;
    minimumRequired: number;
  }> {
    try {
      const response = await api.post('/user/referrals/validate-withdrawal', params);
      return response.data.data;
    } catch (error) {
      console.error('Error validating commission withdrawal:', error);
      throw error;
    }
  }

  /**
   * Get referral analytics data
   */
  async getReferralAnalytics(timeframe: 'month' | 'quarter' | 'year' = 'month'): Promise<ReferralAnalytics> {
    try {
      const response = await api.get('/user/referrals/analytics', {
        params: { timeframe }
      });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching referral analytics:', error);
      throw error;
    }
  }

  /**
   * Get commission withdrawal history
   */
  async getCommissionWithdrawalHistory(params?: {
    page?: number;
    limit?: number;
    currency?: string;
    status?: string;
  }): Promise<{
    withdrawals: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    try {
      const response = await api.get('/user/referrals/withdrawal-history', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching commission withdrawal history:', error);
      throw error;
    }
  }

  /**
   * Check if referral code is valid (for registration)
   */
  async validateReferralCode(code: string): Promise<{
    isValid: boolean;
    referrerInfo?: {
      name: string;
      tier: string;
    };
    message: string;
  }> {
    try {
      const response = await api.post('/referrals/validate', { referralCode: code });
      return response.data;
    } catch (error) {
      console.error('Error validating referral code:', error);
      throw error;
    }
  }

  /**
   * Apply referral code during registration
   */
  async applyReferralCode(code: string): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      const response = await api.post('/referrals/apply', { referralCode: code });
      return response.data;
    } catch (error) {
      console.error('Error applying referral code:', error);
      throw error;
    }
  }
}

export const referralService = new ReferralService();
export default referralService;
