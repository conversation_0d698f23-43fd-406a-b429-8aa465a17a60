import mongoose, { Document, Schema } from 'mongoose';

export interface IUserWallet extends Document {
  userId: mongoose.Types.ObjectId;
  currency: string;
  address: string;
  balance: number;
  frozenBalance?: number;
  network: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastUpdated?: Date;
}

const userWalletSchema = new Schema<IUserWallet>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  currency: {
    type: String,
    required: true,
    uppercase: true,
    enum: ['BTC', 'ETH', 'USDT', 'TRX', 'DOGE', 'BNB', 'SOL'],
    index: true
  },
  address: {
    type: String,
    required: true,
    unique: true
  },
  balance: {
    type: Number,
    default: 0,
    min: 0
  },
  frozenBalance: {
    type: Number,
    default: 0,
    min: 0
  },
  network: {
    type: String,
    required: true,
    default: 'mainnet',
    enum: ['mainnet', 'testnet']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  collection: 'userwallets'
});

// Compound indexes for efficient queries
userWalletSchema.index({ userId: 1, currency: 1 }, { unique: true });
userWalletSchema.index({ currency: 1, isActive: 1 });
userWalletSchema.index({ address: 1 }, { unique: true });

// Virtual for total balance (balance + frozen)
userWalletSchema.virtual('totalBalance').get(function() {
  return this.balance + (this.frozenBalance || 0);
});

// Instance methods
userWalletSchema.methods.updateBalance = async function(newBalance: number) {
  this.balance = newBalance;
  this.lastUpdated = new Date();
  return this.save();
};

userWalletSchema.methods.freezeAmount = async function(amount: number) {
  if (this.balance < amount) {
    throw new Error('Insufficient balance to freeze');
  }
  this.balance -= amount;
  this.frozenBalance = (this.frozenBalance || 0) + amount;
  this.lastUpdated = new Date();
  return this.save();
};

userWalletSchema.methods.unfreezeAmount = async function(amount: number) {
  if ((this.frozenBalance || 0) < amount) {
    throw new Error('Insufficient frozen balance to unfreeze');
  }
  this.frozenBalance = (this.frozenBalance || 0) - amount;
  this.balance += amount;
  this.lastUpdated = new Date();
  return this.save();
};

// Static methods
userWalletSchema.statics.findByUserAndCurrency = function(userId: string, currency: string) {
  return this.findOne({ 
    userId: new mongoose.Types.ObjectId(userId), 
    currency: currency.toUpperCase(),
    isActive: true 
  });
};

userWalletSchema.statics.getUserWallets = function(userId: string) {
  return this.find({ 
    userId: new mongoose.Types.ObjectId(userId),
    isActive: true 
  }).sort({ currency: 1 });
};

userWalletSchema.statics.createUserWallet = async function(data: {
  userId: string;
  currency: string;
  address: string;
  network?: string;
}) {
  const wallet = new this({
    userId: new mongoose.Types.ObjectId(data.userId),
    currency: data.currency.toUpperCase(),
    address: data.address,
    network: data.network || 'mainnet',
    balance: 0,
    frozenBalance: 0,
    isActive: true
  });

  return wallet.save();
};

userWalletSchema.statics.getActiveWalletsByCurrency = function(currency: string) {
  return this.find({
    currency: currency.toUpperCase(),
    isActive: true
  }).sort({ createdAt: -1 });
};

userWalletSchema.statics.getWalletByAddress = function(address: string) {
  return this.findOne({
    address: address,
    isActive: true
  });
};

// Pre-save middleware
userWalletSchema.pre('save', function(next) {
  this.lastUpdated = new Date();
  next();
});

// Export the model
const UserWallet = mongoose.models.UserWallet || mongoose.model<IUserWallet>('UserWallet', userWalletSchema);

export default UserWallet;
