import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, Spinner, Center, Alert, AlertIcon, AlertTitle, AlertDescription, Button, VStack } from '@chakra-ui/react';
import useAuth from '../../hooks/useAuth';
import { useToast } from '@chakra-ui/react';

interface AdminRouteProps {
  children: React.ReactNode;
  redirectPath?: string;
  showFeedback?: boolean;
}

/**
 * Admin Route component
 * 
 * Features:
 * - Authentication and admin role check with loading state
 * - Customizable redirect path
 * - Saves original URL for post-login redirect
 * - Optional feedback messages
 */
const AdminRoute: React.FC<AdminRouteProps> = ({
  children,
  redirectPath = '/login',
  showFeedback = true
}) => {
  const { user, loading } = useAuth();
  const location = useLocation();
  const toast = useToast();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <Center h="100vh">
        <VStack spacing={4}>
          <Spinner size="xl" color="brand.500" thickness="4px" />
          <Box color="gray.500">Verifying administrator access...</Box>
        </VStack>
      </Center>
    );
  }

  // Check if user is authenticated
  if (!user) {
    // Show feedback toast if enabled
    if (showFeedback) {
      toast({
        title: "Authentication Required",
        description: "Please log in to access the admin area.",
        status: "warning",
        duration: 5000,
        isClosable: true,
        position: "top-right"
      });
    }

    // Redirect to login with the original URL saved in state
    return <Navigate to={redirectPath} state={{ from: location.pathname }} replace />;
  }

  // Check if user has admin role
  if (!user.isAdmin) {
    // Show feedback toast if enabled
    if (showFeedback) {
      toast({
        title: "Access Denied",
        description: "You don't have permission to access the admin area.",
        status: "error",
        duration: 5000,
        isClosable: true,
        position: "top-right"
      });
    }

    return (
      <Center h="100vh">
        <Alert
          status="error"
          variant="subtle"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          textAlign="center"
          height="auto"
          maxW="md"
          p={6}
          borderRadius="md"
        >
          <AlertIcon boxSize="40px" mr={0} />
          <AlertTitle mt={4} mb={1} fontSize="lg">
            Administrator Access Required
          </AlertTitle>
          <AlertDescription maxWidth="sm" mb={4}>
            You don't have permission to access this page. This area is restricted to administrators only.
          </AlertDescription>
          <Button colorScheme="blue" onClick={() => window.location.href = '/dashboard'}>
            Return to Dashboard
          </Button>
        </Alert>
      </Center>
    );
  }

  // User is authenticated and has admin role
  return <>{children}</>;
};

export default AdminRoute;
