import { Component, ErrorInfo, ReactNode } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  Icon,
  Code,
  Collapse,
  Badge
} from '@chakra-ui/react';
import { FaExclamationTriangle, FaHome, FaArrowLeft, FaBug, FaRedoAlt } from 'react-icons/fa';
import i18next from 'i18next';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
}

export default class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
    showDetails: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
      showDetails: false
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    try {
      // Log error to console
      console.error('React Error Boundary caught error:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        url: window.location.href
      });

      // Track error metrics
      const w = window as any;
      if (w.gtag) {
        w.gtag('event', 'error', {
          error_name: error.name,
          error_message: error.message,
          error_stack: error.stack,
          url: window.location.href
        });
      }
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }
  }

  private handleReload = () => {
    window.location.reload();
  };

  private handleBack = () => {
    window.history.back();
  };

  private handleHome = () => {
    window.location.href = '/';
  };

  private toggleDetails = () => {
    this.setState(prev => ({
      showDetails: !prev.showDetails
    }));
  };

  public render() {
    if (this.state.hasError) {
      // Always show error details, but provide more detailed information only in development mode
      const isDevelopment = process.env.NODE_ENV === 'development';

      return (
        <Container maxW="container.md" py={20}>
          <VStack spacing={6} align="center" textAlign="center">
            <Icon as={FaExclamationTriangle} w={16} h={16} color="red.500" />

            <Badge colorScheme="red" fontSize="sm">
              {this.state.error?.name || 'Error'}
            </Badge>

            <Heading size="xl" color="gray.100">
              {i18next.t('common.error.title', 'An Unexpected Error Occurred')}
            </Heading>

            <Text color="gray.400">
              {i18next.t('common.error.message', 'We\'re sorry, an error occurred during your operation. Our technical team has been notified and will fix it as soon as possible.')}
            </Text>

            {/* Error details - always show but more detailed in development mode */}
            <Box w="100%">
              <Button
                size="sm"
                variant="ghost"
                leftIcon={<Icon as={FaBug} />}
                onClick={this.toggleDetails}
                mb={4}
              >
                {this.state.showDetails
                  ? i18next.t('common.error.hideDetails', 'Hide Error Details')
                  : i18next.t('common.error.showDetails', 'Show Error Details')}
              </Button>

              <Collapse in={this.state.showDetails}>
                <Box
                  bg="whiteAlpha.100"
                  p={4}
                  borderRadius="md"
                  fontSize="sm"
                  fontFamily="mono"
                  whiteSpace="pre-wrap"
                  mb={4}
                  color="gray.300"
                  overflowX="auto"
                >
                  <Text color="red.300" mb={2} fontWeight="bold">
                    {this.state.error?.message || "Unknown error"}
                  </Text>

                  {isDevelopment ? (
                    <>
                      <Text color="yellow.300" fontWeight="bold" mt={2} mb={1}>Stack Trace:</Text>
                      <Code variant="subtle" display="block" whiteSpace="pre" p={2}>
                        {this.state.error?.stack || "Stack trace not available"}
                      </Code>

                      {this.state.errorInfo && (
                        <>
                          <Text color="yellow.300" fontWeight="bold" mt={3} mb={1}>Component Stack:</Text>
                          <Code variant="subtle" display="block" whiteSpace="pre" p={2} mt={2}>
                            {this.state.errorInfo.componentStack}
                          </Code>
                        </>
                      )}
                    </>
                  ) : (
                    <Text color="gray.400" mt={2}>
                      For more details, please check the browser console or contact our technical support team.
                    </Text>
                  )}
                </Box>
              </Collapse>
            </Box>

            <HStack spacing={4} mt={4}>
              <Button
                colorScheme="yellow"
                leftIcon={<Icon as={FaRedoAlt} />}
                onClick={this.handleReload}
              >
                {i18next.t('common.error.refresh', 'Refresh Page')}
              </Button>

              <Button
                variant="outline"
                leftIcon={<Icon as={FaArrowLeft} />}
                onClick={this.handleBack}
              >
                {i18next.t('common.error.back', 'Go Back')}
              </Button>

              <Button
                variant="ghost"
                leftIcon={<Icon as={FaHome} />}
                onClick={this.handleHome}
              >
                {i18next.t('common.error.home', 'Ana Sayfa')}
              </Button>
            </HStack>
          </VStack>
        </Container>
      );
    }

    return this.props.children;
  }
}