import React, { useState, useEffect } from 'react';
import {
  <PERSON>ert,
  AlertIcon,
  Box,
  Text,
  HStack,
  VStack,
  Badge,
  Progress,
  Tooltip,
  Button,
  useColorModeValue,
  Divider,
  Icon,
  Flex
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { FaClock, FaLock, FaUnlock, FaExclamationTriangle, FaCoins, FaArrowUp } from 'react-icons/fa';

const MotionBox = motion(Box);

interface TimeLockStatusProps {
  status?: {
    isLocked: boolean;
    nextUnlockTime: string;
    reason: string;
    lockType: 'daily' | 'maintenance' | 'emergency';
  };
  timeUntilNext?: {
    hours: number;
    minutes: number;
    seconds: number;
    totalMs: number;
  } | null;
  pendingDeposits?: number;
  nextActivationTime?: string;
}

const TimeLockStatus: React.FC<TimeLockStatusProps> = ({
  status,
  timeUntilNext: initialTimeUntilNext,
  pendingDeposits = 0,
  nextActivationTime
}) => {
  const [timeUntilNext, setTimeUntilNext] = useState(initialTimeUntilNext);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [depositInfo, setDepositInfo] = useState<any>(null);

  // Fetch deposit information
  useEffect(() => {
    const fetchDepositInfo = async () => {
      try {
        const response = await fetch('/api/wallets/deposits/summary', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.status === 'success') {
            setDepositInfo(data.data);
          }
        }
      } catch (error) {
        console.error('Error fetching deposit info:', error);
      }
    };

    fetchDepositInfo();
  }, []);

  // Update countdown every second
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);

      const nextUnlock = new Date(status.nextUnlockTime);
      const diffMs = nextUnlock.getTime() - now.getTime();

      if (diffMs > 0) {
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

        setTimeUntilNext({
          hours,
          minutes,
          seconds,
          totalMs: diffMs
        });
      } else {
        setTimeUntilNext({
          hours: 0,
          minutes: 0,
          seconds: 0,
          totalMs: 0
        });
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [status.nextUnlockTime]);

  const getStatusConfig = () => {
    if (!status.isLocked) {
      return {
        alertStatus: 'success' as const,
        icon: FaUnlock,
        color: 'green.400',
        bgColor: 'green.900',
        borderColor: 'green.400',
        title: '🟢 İşlemler Açık',
        description: 'Yatırım ve çekim işlemleri yapabilirsiniz'
      };
    }

    switch (status.lockType) {
      case 'emergency':
        return {
          alertStatus: 'error' as const,
          icon: FaExclamationTriangle,
          color: 'red.400',
          bgColor: 'red.900',
          borderColor: 'red.400',
          title: '🔴 Acil Durum Kilidi',
          description: 'Sistem bakımı nedeniyle işlemler geçici olarak durduruldu'
        };
      case 'maintenance':
        return {
          alertStatus: 'warning' as const,
          icon: FaLock,
          color: 'orange.400',
          bgColor: 'orange.900',
          borderColor: 'orange.400',
          title: '🟡 Bakım Kilidi',
          description: 'Planlı bakım nedeniyle işlemler geçici olarak durduruldu'
        };
      case 'daily':
      default:
        return {
          alertStatus: 'info' as const,
          icon: FaClock,
          color: 'blue.400',
          bgColor: 'blue.900',
          borderColor: 'blue.400',
          title: '🔵 Günlük Zaman Kilidi',
          description: 'İşlemler her gün 03:00 Türkiye saatinde açılır'
        };
    }
  };

  const config = getStatusConfig();

  // Calculate progress for daily lock
  const getProgress = () => {
    if (!status.isLocked || !timeUntilNext) return 100;

    if (status.lockType === 'daily') {
      // 24 hours total, show progress until next 03:00
      const totalDayMs = 24 * 60 * 60 * 1000;
      const remainingMs = timeUntilNext.totalMs;
      return Math.max(0, Math.min(100, ((totalDayMs - remainingMs) / totalDayMs) * 100));
    }

    return 0;
  };

  const formatNextUnlockTime = () => {
    return new Date(status.nextUnlockTime).toLocaleString('tr-TR', {
      timeZone: 'Europe/Istanbul',
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatCurrentTime = () => {
    return currentTime.toLocaleString('tr-TR', {
      timeZone: 'Europe/Istanbul',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <MotionBox
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Alert
        status={config.alertStatus}
        bg={config.bgColor}
        borderColor={config.borderColor}
        borderWidth="2px"
        borderRadius="lg"
        p={6}
      >
        <AlertIcon as={config.icon} color={config.color} boxSize={6} />

        <VStack align="start" spacing={4} flex={1} ml={4}>
          {/* Header */}
          <HStack justify="space-between" w="full">
            <VStack align="start" spacing={1}>
              <Text fontSize="lg" fontWeight="bold" color="white">
                {config.title}
              </Text>
              <Text fontSize="sm" color="gray.300">
                {config.description}
              </Text>
            </VStack>

            <Badge
              colorScheme={status.isLocked ? "red" : "green"}
              variant="solid"
              fontSize="sm"
              px={3}
              py={1}
            >
              {status.isLocked ? "KİLİTLİ" : "AÇIK"}
            </Badge>
          </HStack>

          {/* Time Information */}
          <HStack spacing={8} w="full" wrap="wrap">
            <VStack align="start" spacing={1}>
              <Text fontSize="xs" color="gray.400" fontWeight="bold">
                MEVCUT ZAMAN (TR)
              </Text>
              <Text fontSize="md" color="white" fontFamily="mono">
                {formatCurrentTime()}
              </Text>
            </VStack>

            <VStack align="start" spacing={1}>
              <Text fontSize="xs" color="gray.400" fontWeight="bold">
                {status.isLocked ? "AÇILMA ZAMANI" : "SONRAKİ AÇILMA"}
              </Text>
              <Text fontSize="md" color={config.color} fontFamily="mono">
                {formatNextUnlockTime()}
              </Text>
            </VStack>
          </HStack>

          {/* Countdown */}
          {timeUntilNext && timeUntilNext.totalMs > 0 && (
            <VStack align="start" spacing={3} w="full">
              <Text fontSize="xs" color="gray.400" fontWeight="bold">
                KALAN SÜRE
              </Text>

              <HStack spacing={6}>
                <VStack spacing={1}>
                  <Text fontSize="2xl" fontWeight="bold" color={config.color} fontFamily="mono">
                    {timeUntilNext.hours.toString().padStart(2, '0')}
                  </Text>
                  <Text fontSize="xs" color="gray.400">SAAT</Text>
                </VStack>

                <Text fontSize="2xl" color="gray.500">:</Text>

                <VStack spacing={1}>
                  <Text fontSize="2xl" fontWeight="bold" color={config.color} fontFamily="mono">
                    {timeUntilNext.minutes.toString().padStart(2, '0')}
                  </Text>
                  <Text fontSize="xs" color="gray.400">DAKİKA</Text>
                </VStack>

                <Text fontSize="2xl" color="gray.500">:</Text>

                <VStack spacing={1}>
                  <Text fontSize="2xl" fontWeight="bold" color={config.color} fontFamily="mono">
                    {timeUntilNext.seconds.toString().padStart(2, '0')}
                  </Text>
                  <Text fontSize="xs" color="gray.400">SANİYE</Text>
                </VStack>
              </HStack>

              {/* Progress Bar for Daily Lock */}
              {status.lockType === 'daily' && (
                <Box w="full">
                  <HStack justify="space-between" mb={2}>
                    <Text fontSize="xs" color="gray.400">Günlük İlerleme</Text>
                    <Text fontSize="xs" color="gray.400">
                      %{getProgress().toFixed(1)}
                    </Text>
                  </HStack>
                  <Progress
                    value={getProgress()}
                    colorScheme={status.isLocked ? "blue" : "green"}
                    size="sm"
                    borderRadius="full"
                    bg="gray.700"
                  />
                </Box>
              )}
            </VStack>
          )}

          {/* Deposit Information */}
          {depositInfo && (depositInfo.pendingDeposits > 0 || depositInfo.confirmedDeposits > 0) && (
            <Box w="full" pt={4} borderTop="1px" borderColor="gray.600">
              <VStack spacing={3} align="stretch">
                <Flex justify="space-between" align="center">
                  <HStack>
                    <Icon as={FaCoins} color="gold.400" />
                    <Text color="white" fontSize="sm" fontWeight="semibold">
                      Para Yatırma Durumu
                    </Text>
                  </HStack>
                </Flex>

                <HStack justify="space-between" wrap="wrap" gap={4}>
                  {depositInfo.pendingDeposits > 0 && (
                    <VStack spacing={1} align="center">
                      <Text color="yellow.400" fontSize="xs" fontWeight="bold">
                        Bekleyen
                      </Text>
                      <Badge colorScheme="yellow" variant="subtle">
                        {depositInfo.pendingDeposits}
                      </Badge>
                    </VStack>
                  )}

                  {depositInfo.confirmedDeposits > 0 && (
                    <VStack spacing={1} align="center">
                      <Text color="green.400" fontSize="xs" fontWeight="bold">
                        Onaylanan
                      </Text>
                      <Badge colorScheme="green" variant="subtle">
                        {depositInfo.confirmedDeposits}
                      </Badge>
                    </VStack>
                  )}

                  {depositInfo.totalUSDTValue > 0 && (
                    <VStack spacing={1} align="center">
                      <Text color="gold.400" fontSize="xs" fontWeight="bold">
                        Toplam Değer
                      </Text>
                      <Text color="gold.400" fontSize="sm" fontWeight="bold">
                        ${depositInfo.totalUSDTValue.toFixed(2)}
                      </Text>
                    </VStack>
                  )}
                </HStack>

                {depositInfo.pendingDeposits > 0 && (
                  <Alert status="info" bg="blue.900" borderColor="blue.400" borderWidth="1px" size="sm">
                    <AlertIcon color="blue.400" boxSize={4} />
                    <Text color="white" fontSize="xs">
                      Bekleyen para yatırma işlemleri onaylandığında otomatik olarak yatırım paketine dönüştürülecek
                    </Text>
                  </Alert>
                )}
              </VStack>
            </Box>
          )}

          {/* Additional Info */}
          <Box w="full" pt={2} borderTop="1px" borderColor="gray.600">
            <Text fontSize="xs" color="gray.500">
              <strong>Sebep:</strong> {status.reason}
            </Text>
            {status.lockType === 'daily' && (
              <Text fontSize="xs" color="gray.500" mt={1}>
                💡 <strong>Bilgi:</strong> Faiz hesaplamaları her gün 03:00'da Türkiye saati ile yapılır
              </Text>
            )}
          </Box>
        </VStack>
      </Alert>
    </MotionBox>
  );
};

export default TimeLockStatus;
