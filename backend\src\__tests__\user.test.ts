import mongoose from 'mongoose';
import User from '../models/userModel';
import bcrypt from 'bcrypt';

describe('User Model Test', () => {
  const validUserData = {
    email: '<EMAIL>',
    password: 'Test123!@#',
    firstName: 'Test',
    lastName: 'User'
  };

  it('should create a new user successfully', async () => {
    const user = new User(validUserData);
    const savedUser = await user.save();
    
    expect(savedUser._id).toBeDefined();
    expect(savedUser.email).toBe(validUserData.email);
    expect(savedUser.firstName).toBe(validUserData.firstName);
    expect(savedUser.lastName).toBe(validUserData.lastName);
    expect(savedUser.password).not.toBe(validUserData.password); // Password should be hashed
  });

  it('should fail to create user without required fields', async () => {
    const userWithoutRequired = new User({ email: '<EMAIL>' });
    let err;
    try {
      await userWithoutRequired.save();
    } catch (error) {
      err = error;
    }
    expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
  });

  it('should fail to create user with invalid email', async () => {
    const userWithInvalidEmail = new User({
      ...validUserData,
      email: 'invalid-email'
    });
    let err;
    try {
      await userWithInvalidEmail.save();
    } catch (error) {
      err = error;
    }
    expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
  });

  it('should fail to create user with weak password', async () => {
    const userWithWeakPassword = new User({
      ...validUserData,
      password: '123'
    });
    let err;
    try {
      await userWithWeakPassword.save();
    } catch (error) {
      err = error;
    }
    expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
  });

  it('should correctly compare password', async () => {
    const user = new User(validUserData);
    await user.save();

    const isMatch = await user.comparePassword(validUserData.password);
    expect(isMatch).toBe(true);

    const isNotMatch = await user.comparePassword('wrongpassword');
    expect(isNotMatch).toBe(false);
  });

  it('should fail to create duplicate user', async () => {
    const firstUser = new User(validUserData);
    await firstUser.save();

    const duplicateUser = new User(validUserData);
    let err;
    try {
      await duplicateUser.save();
    } catch (error) {
      err = error;
    }
    expect(err).toBeDefined();
    expect(err.code).toBe(11000); // MongoDB duplicate key error code
  });

  it('should validate wallet address format', async () => {
    const userWithValidWallet = new User({
      ...validUserData,
      walletAddress: '******************************************'
    });
    const savedUser = await userWithValidWallet.save();
    expect(savedUser.walletAddress).toBe('******************************************');

    const userWithInvalidWallet = new User({
      ...validUserData,
      walletAddress: 'invalid-address'
    });
    let err;
    try {
      await userWithInvalidWallet.save();
    } catch (error) {
      err = error;
    }
    expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
  });

  it('should hash password before saving', async () => {
    const user = new User(validUserData);
    const savedUser = await user.save();
    
    // Verify that the password is hashed
    const isHashed = await bcrypt.compare(validUserData.password, savedUser.password);
    expect(isHashed).toBe(true);
    expect(savedUser.password).not.toBe(validUserData.password);
  });
});