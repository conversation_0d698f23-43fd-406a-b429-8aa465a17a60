import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || '/api';

// Enhanced withdrawal system interfaces
export interface WithdrawalEligibilityCheck {
  isEligible: boolean;
  availableBalance: number;
  minimumRequired: number;
  timeLockStatus: {
    isLocked: boolean;
    message: string;
    unlockDate?: string;
  };
}

export interface CryptocurrencyConfig {
  precision: number;
  networkFee: number;
  commissionRate: number;
  confirmationTime: string;
  networks: string[];
  addressPattern: RegExp;
  minimumWithdrawal: number;
}

export interface WithdrawalFeeCalculation {
  networkFee: number;
  commissionFee: number;
  totalFee: number;
  netAmount: number;
}

export interface TimeBasedRestriction {
  canWithdraw: boolean;
  nextEligibleTime: Date | null;
  timeRemaining: string;
  message: string;
}

export interface EnhancedWithdrawalRequest {
  asset: string;
  amount: number;
  address: string;
  memo?: string;
  withdrawalType: 'interest' | 'commission' | 'referral';
  network: string;
  feeCalculation: WithdrawalFeeCalculation;
  eligibilityCheck: WithdrawalEligibilityCheck;
}

class EnhancedWithdrawalService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Check withdrawal eligibility for a specific cryptocurrency and amount
   */
  async checkWithdrawalEligibility(
    cryptocurrency: string,
    amount: number,
    withdrawalType: 'interest' | 'commission' | 'referral' = 'interest'
  ): Promise<WithdrawalEligibilityCheck> {
    try {
      const response = await axios.post(
        `${API_URL}/withdrawals/check-eligibility`,
        {
          cryptocurrency,
          amount,
          withdrawalType
        },
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Error checking withdrawal eligibility:', error);
      throw error;
    }
  }

  /**
   * Get cryptocurrency-specific configuration
   */
  getCryptocurrencyConfig(crypto: string): CryptocurrencyConfig {
    const configs: { [key: string]: CryptocurrencyConfig } = {
      BTC: {
        precision: 8,
        networkFee: 0.0005,
        commissionRate: 0.01,
        confirmationTime: '30-60 minutes',
        networks: ['bitcoin'],
        addressPattern: /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/,
        minimumWithdrawal: 0.001
      },
      ETH: {
        precision: 6,
        networkFee: 0.01,
        commissionRate: 0.01,
        confirmationTime: '5-15 minutes',
        networks: ['ethereum'],
        addressPattern: /^0x[a-fA-F0-9]{40}$/,
        minimumWithdrawal: 0.02
      },
      USDT: {
        precision: 2,
        networkFee: 2.0,
        commissionRate: 0.01,
        confirmationTime: '5-15 minutes',
        networks: ['ethereum', 'tron', 'bsc'],
        addressPattern: /^0x[a-fA-F0-9]{40}$|^T[A-Za-z1-9]{33}$/,
        minimumWithdrawal: 50.0
      },
      BNB: {
        precision: 4,
        networkFee: 0.001,
        commissionRate: 0.01,
        confirmationTime: '3-5 minutes',
        networks: ['bsc'],
        addressPattern: /^0x[a-fA-F0-9]{40}$/,
        minimumWithdrawal: 0.15
      },
      SOL: {
        precision: 4,
        networkFee: 0.00025,
        commissionRate: 0.01,
        confirmationTime: '1-3 minutes',
        networks: ['solana'],
        addressPattern: /^[1-9A-HJ-NP-Za-km-z]{32,44}$/,
        minimumWithdrawal: 0.5
      },
      DOGE: {
        precision: 2,
        networkFee: 1.0,
        commissionRate: 0.01,
        confirmationTime: '10-20 minutes',
        networks: ['dogecoin'],
        addressPattern: /^D{1}[5-9A-HJ-NP-U]{1}[1-9A-HJ-NP-Za-km-z]{32}$/,
        minimumWithdrawal: 500
      },
      TRX: {
        precision: 4,
        networkFee: 1.0,
        commissionRate: 0.01,
        confirmationTime: '3-5 minutes',
        networks: ['tron'],
        addressPattern: /^T[A-Za-z1-9]{33}$/,
        minimumWithdrawal: 500
      }
    };
    return configs[crypto] || configs.USDT;
  }

  /**
   * Calculate withdrawal fees including network fee and 1% commission
   */
  calculateWithdrawalFees(cryptocurrency: string, amount: number): WithdrawalFeeCalculation {
    const config = this.getCryptocurrencyConfig(cryptocurrency);
    const networkFee = config.networkFee;
    const commissionFee = amount * config.commissionRate; // 1% commission
    const totalFee = networkFee + commissionFee;
    const netAmount = Math.max(0, amount - totalFee);

    return {
      networkFee,
      commissionFee,
      totalFee,
      netAmount
    };
  }

  /**
   * Check time-based withdrawal restrictions (03:00 UTC+3)
   */
  checkTimeBasedRestrictions(): TimeBasedRestriction {
    // Get current UTC time
    const nowUTC = new Date();

    // Convert to UTC+3 timezone (Turkey/Istanbul time)
    const utc3Offset = 3 * 60; // UTC+3 in minutes
    const utc3Time = new Date(nowUTC.getTime() + (utc3Offset * 60 * 1000));

    // Get current hour in UTC+3
    const currentHour = utc3Time.getUTCHours();

    // Withdrawals allowed after 03:00 UTC+3 daily interest distribution
    if (currentHour >= 3) {
      return {
        canWithdraw: true,
        nextEligibleTime: null,
        timeRemaining: '',
        message: 'Withdrawals are currently available'
      };
    }

    // Calculate next eligible time (03:00 UTC+3 today)
    const nextEligibleUTC3 = new Date(utc3Time);
    nextEligibleUTC3.setUTCHours(3, 0, 0, 0);

    // Calculate time remaining in milliseconds
    const timeRemainingMs = nextEligibleUTC3.getTime() - utc3Time.getTime();

    // Handle edge case: if we're exactly at 03:00:00, allow withdrawal
    if (timeRemainingMs <= 0) {
      return {
        canWithdraw: true,
        nextEligibleTime: null,
        timeRemaining: '',
        message: 'Withdrawals are currently available'
      };
    }

    const timeRemaining = this.formatTimeRemaining(timeRemainingMs);

    return {
      canWithdraw: false,
      nextEligibleTime: nextEligibleUTC3,
      timeRemaining,
      message: 'Withdrawals are only available after 03:00 UTC+3 daily interest distribution'
    };
  }

  /**
   * Format time remaining until next withdrawal window
   */
  private formatTimeRemaining(milliseconds: number): string {
    // Handle negative values (should not happen with fixed logic, but safety check)
    if (milliseconds <= 0) {
      return '00:00:00';
    }

    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * Validate wallet address for specific cryptocurrency and network
   */
  validateWalletAddress(address: string, cryptocurrency: string, network?: string): {
    isValid: boolean;
    message: string;
    networkMatch: boolean;
  } {
    if (!address) {
      return { isValid: false, message: 'Wallet address is required', networkMatch: false };
    }

    const config = this.getCryptocurrencyConfig(cryptocurrency);
    const isValidFormat = config.addressPattern.test(address);

    if (!isValidFormat) {
      return {
        isValid: false,
        message: `Invalid ${cryptocurrency} address format`,
        networkMatch: false
      };
    }

    // Network-specific validation
    let networkMatch = true;
    if (network && cryptocurrency === 'USDT') {
      if (network === 'ethereum' && !address.startsWith('0x')) {
        networkMatch = false;
      } else if (network === 'tron' && !address.startsWith('T')) {
        networkMatch = false;
      }
    }

    return {
      isValid: isValidFormat && networkMatch,
      message: isValidFormat && networkMatch
        ? 'Valid address format'
        : 'Address does not match selected network',
      networkMatch
    };
  }

  /**
   * Submit enhanced withdrawal request
   */
  async submitWithdrawal(request: EnhancedWithdrawalRequest): Promise<any> {
    try {
      const response = await axios.post(
        `${API_URL}/api/withdrawals/enhanced-withdraw`,
        request,
        { headers: this.getAuthHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Error submitting enhanced withdrawal:', error);
      throw error;
    }
  }
}

export const enhancedWithdrawalService = new EnhancedWithdrawalService();
