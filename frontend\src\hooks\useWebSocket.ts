import { useEffect, useRef, useState, useCallback } from 'react';
import { useToast } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import io, { Socket } from 'socket.io-client';
import useAuth from './useAuth';

/**
 * Enhanced WebSocket Hook for Real-time Updates
 * Handles connection, reconnection, and message processing
 */

interface WebSocketMessage {
  type: string;
  payload: any;
}

interface WalletUpdate {
  currency: string;
  balance: number;
  previousBalance: number;
  changeAmount: number;
  changeType: 'credit' | 'debit';
  timestamp: string;
}

interface TransactionUpdate {
  transaction: any;
  updateType: 'new' | 'status_change';
  timestamp: string;
}

interface InterestUpdate {
  investmentId: string;
  interestAmount: number;
  currency: string;
  totalEarned: number;
  timestamp: string;
}

interface PriceUpdate {
  symbol: string;
  price: number;
  change24h: number;
  changePercent24h: number;
  timestamp: string;
}

interface UseWebSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastMessage: WebSocketMessage | null;
  sendMessage: (type: string, payload?: any) => void;
  subscribe: (eventType: string, callback: (data: any) => void) => void;
  unsubscribe: (eventType: string) => void;
}

export const useWebSocket = (): UseWebSocketReturn => {
  const { user, token } = useAuth();
  const { t } = useTranslation();
  const toast = useToast();

  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);

  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const eventCallbacks = useRef<Map<string, ((data: any) => void)[]>>(new Map());

  // Initialize WebSocket connection
  const initializeSocket = useCallback(() => {
    if (!user || !token) {
      console.log('WebSocket: No user or token available');
      return;
    }

    setConnectionStatus('connecting');

    const socketInstance = io(process.env.REACT_APP_BACKEND_URL || 'http://localhost:3003', {
      path: '/ws',
      transports: ['websocket', 'polling'],
      auth: {
        token,
        userId: user.id
      },
      query: {
        token,
        userId: user.id
      },
      reconnection: true,
      reconnectionAttempts: maxReconnectAttempts,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      timeout: 20000,
      forceNew: true
    });

    // Connection event handlers
    socketInstance.on('connect', () => {
      console.log('WebSocket connected:', socketInstance.id);
      setIsConnected(true);
      setConnectionStatus('connected');
      reconnectAttempts.current = 0;

      toast({
        title: t('websocket.connected', 'Connected'),
        description: t('websocket.connectedDesc', 'Real-time updates enabled'),
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    });

    socketInstance.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      setIsConnected(false);
      setConnectionStatus('disconnected');

      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        toast({
          title: t('websocket.disconnected', 'Disconnected'),
          description: t('websocket.serverDisconnect', 'Server disconnected the connection'),
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
      }
    });

    socketInstance.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setConnectionStatus('error');
      reconnectAttempts.current++;

      if (reconnectAttempts.current >= maxReconnectAttempts) {
        toast({
          title: t('websocket.connectionFailed', 'Connection Failed'),
          description: t('websocket.maxRetriesReached', 'Maximum reconnection attempts reached'),
          status: 'error',
          duration: 10000,
          isClosable: true,
        });
      }
    });

    // Message handlers
    socketInstance.on('wallet_update', (data: WalletUpdate) => {
      console.log('Wallet update received:', data);
      setLastMessage({ type: 'wallet_update', payload: data });

      // Show notification for significant changes
      if (Math.abs(data.changeAmount) >= 10) {
        toast({
          title: t('wallet.balanceUpdated', 'Balance Updated'),
          description: `${data.changeType === 'credit' ? '+' : '-'}${data.changeAmount} ${data.currency}`,
          status: data.changeType === 'credit' ? 'success' : 'info',
          duration: 5000,
          isClosable: true,
        });
      }

      // Trigger callbacks
      triggerCallbacks('wallet_update', data);
    });

    socketInstance.on('transaction_update', (data: TransactionUpdate) => {
      console.log('Transaction update received:', data);
      setLastMessage({ type: 'transaction_update', payload: data });

      if (data.updateType === 'new') {
        toast({
          title: t('transaction.newTransaction', 'New Transaction'),
          description: t('transaction.transactionCreated', 'A new transaction has been created'),
          status: 'info',
          duration: 5000,
          isClosable: true,
        });
      }

      triggerCallbacks('transaction_update', data);
    });

    socketInstance.on('interest_earned', (data: InterestUpdate) => {
      console.log('Interest update received:', data);
      setLastMessage({ type: 'interest_earned', payload: data });

      toast({
        title: t('investment.interestEarned', 'Interest Earned!'),
        description: `+${data.interestAmount} ${data.currency}`,
        status: 'success',
        duration: 8000,
        isClosable: true,
      });

      triggerCallbacks('interest_earned', data);
    });

    socketInstance.on('price_update', (data: PriceUpdate) => {
      setLastMessage({ type: 'price_update', payload: data });
      triggerCallbacks('price_update', data);
    });

    socketInstance.on('deposit_confirmed', (data: any) => {
      console.log('Deposit confirmed:', data);
      setLastMessage({ type: 'deposit_confirmed', payload: data });

      toast({
        title: t('deposit.confirmed', 'Deposit Confirmed'),
        description: `${data.amount} ${data.currency} ${t('deposit.hasBeenConfirmed', 'has been confirmed')}`,
        status: 'success',
        duration: 10000,
        isClosable: true,
      });

      triggerCallbacks('deposit_confirmed', data);
    });

    socketInstance.on('withdrawal_update', (data: any) => {
      console.log('Withdrawal update:', data);
      setLastMessage({ type: 'withdrawal_update', payload: data });

      toast({
        title: t('withdrawal.statusUpdate', 'Withdrawal Update'),
        description: `${t('withdrawal.status', 'Status')}: ${data.status}`,
        status: data.status === 'completed' ? 'success' : 'info',
        duration: 8000,
        isClosable: true,
      });

      triggerCallbacks('withdrawal_update', data);
    });

    socketInstance.on('investment_created', (data: any) => {
      console.log('Investment created:', data);
      setLastMessage({ type: 'investment_created', payload: data });

      toast({
        title: t('investment.created', 'Investment Created'),
        description: `${data.amount} ${data.currency} ${t('investment.investmentCreated', 'investment created successfully')}`,
        status: 'success',
        duration: 8000,
        isClosable: true,
      });

      triggerCallbacks('investment_created', data);
    });

    socketInstance.on('system_notification', (data: any) => {
      console.log('System notification:', data);
      setLastMessage({ type: 'system_notification', payload: data });

      toast({
        title: t('system.notification', 'System Notification'),
        description: data.message,
        status: data.type === 'error' ? 'error' : 'info',
        duration: 10000,
        isClosable: true,
      });

      triggerCallbacks('system_notification', data);
    });

    socketInstance.on('error_notification', (data: any) => {
      console.log('Error notification:', data);
      setLastMessage({ type: 'error_notification', payload: data });

      toast({
        title: data.title || t('error.occurred', 'Error Occurred'),
        description: data.message,
        status: 'error',
        duration: data.retryable ? 8000 : 15000,
        isClosable: true,
      });

      triggerCallbacks('error_notification', data);
    });

    setSocket(socketInstance);

    return socketInstance;
  }, [user, token, toast, t]);

  // Trigger event callbacks
  const triggerCallbacks = useCallback((eventType: string, data: any) => {
    const callbacks = eventCallbacks.current.get(eventType);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in WebSocket callback for ${eventType}:`, error);
        }
      });
    }
  }, []);

  // Send message to server
  const sendMessage = useCallback((type: string, payload?: any) => {
    if (socket && isConnected) {
      socket.emit('message', { type, payload });
    } else {
      console.warn('WebSocket not connected, cannot send message:', type);
    }
  }, [socket, isConnected]);

  // Subscribe to events
  const subscribe = useCallback((eventType: string, callback: (data: any) => void) => {
    if (!eventCallbacks.current.has(eventType)) {
      eventCallbacks.current.set(eventType, []);
    }
    eventCallbacks.current.get(eventType)!.push(callback);
  }, []);

  // Unsubscribe from events
  const unsubscribe = useCallback((eventType: string) => {
    eventCallbacks.current.delete(eventType);
  }, []);

  // Initialize connection when user is available
  useEffect(() => {
    if (user && token) {
      const socketInstance = initializeSocket();

      return () => {
        if (socketInstance) {
          socketInstance.disconnect();
        }
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
        }
      };
    }
  }, [user, token, initializeSocket]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (socket) {
        socket.disconnect();
      }
      eventCallbacks.current.clear();
    };
  }, [socket]);

  return {
    socket,
    isConnected,
    connectionStatus,
    lastMessage,
    sendMessage,
    subscribe,
    unsubscribe
  };
};
