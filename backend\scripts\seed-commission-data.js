const mongoose = require('mongoose');

// Define Commission schema directly
const commissionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  asset: {
    type: String,
    required: true,
    uppercase: true
  },
  type: {
    type: String,
    required: true,
    enum: ['commission', 'referral_commission', 'platform_commission']
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'approved', 'completed', 'rejected', 'failed'],
    default: 'pending'
  },
  description: {
    type: String,
    required: true
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Define User schema directly
const userSchema = new mongoose.Schema({
  email: String,
  firstName: String,
  lastName: String
}, {
  timestamps: true
});

const Commission = mongoose.model('Commission', commissionSchema);
const User = mongoose.model('User', userSchema);

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield');
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample commission data
const sampleCommissions = [
  {
    amount: 50.00,
    asset: 'USDT',
    type: 'referral_commission',
    status: 'completed',
    description: 'Referral commission from user investment',
    metadata: {
      referralLevel: 1,
      investmentAmount: 1000,
      commissionRate: 0.05
    }
  },
  {
    amount: 25.50,
    asset: 'BTC',
    type: 'commission',
    status: 'pending',
    description: 'Trading commission',
    metadata: {
      tradeId: 'trade_123456',
      tradeAmount: 0.5
    }
  },
  {
    amount: 75.25,
    asset: 'ETH',
    type: 'platform_commission',
    status: 'approved',
    description: 'Platform usage commission',
    metadata: {
      platformFee: 0.02,
      transactionAmount: 3762.5
    }
  },
  {
    amount: 100.00,
    asset: 'USDT',
    type: 'referral_commission',
    status: 'completed',
    description: 'Level 2 referral commission',
    metadata: {
      referralLevel: 2,
      investmentAmount: 5000,
      commissionRate: 0.02
    }
  },
  {
    amount: 15.75,
    asset: 'BNB',
    type: 'commission',
    status: 'failed',
    description: 'Failed commission transaction',
    metadata: {
      errorCode: 'INSUFFICIENT_BALANCE',
      retryCount: 3
    }
  },
  {
    amount: 200.00,
    asset: 'USDT',
    type: 'referral_commission',
    status: 'rejected',
    description: 'Rejected referral commission - policy violation',
    metadata: {
      rejectionReason: 'Suspicious activity detected',
      reviewedBy: 'admin'
    }
  },
  {
    amount: 35.80,
    asset: 'ETH',
    type: 'platform_commission',
    status: 'completed',
    description: 'Staking rewards commission',
    metadata: {
      stakingPeriod: '30 days',
      stakingAmount: 1790
    }
  },
  {
    amount: 12.50,
    asset: 'BTC',
    type: 'commission',
    status: 'pending',
    description: 'Mining pool commission',
    metadata: {
      poolId: 'pool_btc_001',
      hashRate: '1.2 TH/s'
    }
  }
];

const seedCommissionData = async () => {
  try {
    await connectDB();

    // Find a user to associate commissions with
    const user = await User.findOne();
    if (!user) {
      console.log('No users found. Please create a user first.');
      process.exit(1);
    }

    console.log(`Found user: ${user.email}`);

    // Clear existing commission data
    await Commission.deleteMany({});
    console.log('Cleared existing commission data');

    // Create commissions with different timestamps
    const commissions = [];
    for (let i = 0; i < sampleCommissions.length; i++) {
      const commissionData = {
        ...sampleCommissions[i],
        user: user._id,
        createdAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)), // Spread over last 8 days
        updatedAt: new Date(Date.now() - (i * 24 * 60 * 60 * 1000))
      };
      commissions.push(commissionData);
    }

    // Insert commission data
    const insertedCommissions = await Commission.insertMany(commissions);
    console.log(`Successfully inserted ${insertedCommissions.length} commission records`);

    // Display summary
    console.log('\nCommission Summary:');
    const statusCounts = await Commission.aggregate([
      { $match: { user: user._id } },
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);
    
    const typeCounts = await Commission.aggregate([
      { $match: { user: user._id } },
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);

    console.log('By Status:', statusCounts);
    console.log('By Type:', typeCounts);

    process.exit(0);
  } catch (error) {
    console.error('Error seeding commission data:', error);
    process.exit(1);
  }
};

// Run the seeding function
seedCommissionData();
