import React, { useState, useEffect } from 'react';
import { Box, useDisclosure, useToast } from '@chakra-ui/react';
import { useWalletSync } from '../hooks/useWalletSync';
import { useCryptoPrices } from '../hooks/useCryptoPrices';
import WithdrawModal from './modals/WithdrawModal';

// Crypto icons and colors mapping
const cryptoConfig = {
  TRX: {
    icon: '⚡',
    name: 'TRON',
    color: '#FF073A',
    bgColor: 'rgba(255, 7, 58, 0.1)',
    borderColor: 'rgba(255, 7, 58, 0.2)'
  },
  USDT: {
    icon: '💵',
    name: 'Tether',
    color: '#26A17B',
    bgColor: 'rgba(38, 161, 123, 0.1)',
    borderColor: 'rgba(38, 161, 123, 0.2)'
  },
  BTC: {
    icon: '₿',
    name: 'Bitcoin',
    color: '#F7931A',
    bgColor: 'rgba(247, 147, 26, 0.1)',
    borderColor: 'rgba(247, 147, 26, 0.2)'
  },
  ETH: {
    icon: '⟠',
    name: 'Ether<PERSON>',
    color: '#627EEA',
    bgColor: 'rgba(98, 126, 234, 0.1)',
    borderColor: 'rgba(98, 126, 234, 0.2)'
  },
  BNB: {
    icon: '🔶',
    name: 'Binance',
    color: '#F3BA2F',
    bgColor: 'rgba(243, 186, 47, 0.1)',
    borderColor: 'rgba(243, 186, 47, 0.2)'
  },
  SOL: {
    icon: '☀️',
    name: 'Solana',
    color: '#9945FF',
    bgColor: 'rgba(153, 69, 255, 0.1)',
    borderColor: 'rgba(153, 69, 255, 0.2)'
  },
  DOGE: {
    icon: '🐕',
    name: 'Dogecoin',
    color: '#C2A633',
    bgColor: 'rgba(194, 166, 51, 0.1)',
    borderColor: 'rgba(194, 166, 51, 0.2)'
  }
};

interface WalletAsset {
  symbol: string;
  balance: number;
  interestBalance: number;
  commissionBalance: number;
  // Enhanced fields from unified system
  principalAmount?: number;
  totalEarned?: number;
  dailyInterestRate?: number;
  lastInterestDate?: string;
  isLocked?: boolean;
  lockExpiryDate?: string;
  daysUntilUnlock?: number;
  totalInvestments?: number;
  activePackages?: number;
}

interface ModernWalletCardProps {
  asset: WalletAsset;
  onDeposit?: (currency: string, walletData?: any) => void;
  onWithdraw?: (currency: string, walletData?: any) => void;
}

const ModernWalletCard: React.FC<ModernWalletCardProps> = ({
  asset,
  onDeposit,
  onWithdraw,
}) => {
  const [countdown, setCountdown] = useState('--:--:--');
  const [progressPercent, setProgressPercent] = useState(0);
  const [localRealTimeData, setLocalRealTimeData] = useState<any>(null);

  // Fallback withdraw modal
  const { isOpen: isWithdrawModalOpen, onOpen: onWithdrawModalOpen, onClose: onWithdrawModalClose } = useDisclosure();
  const toast = useToast();

  // Use wallet sync hook
  const {
    enhancedData,
    realTimeData: hookRealTimeData,
    loading,
    isConnected,
    getWithdrawableBalance
  } = useWalletSync();

  // Use crypto prices hook for accurate USD conversion
  const { getPrice, convertToUSD, isLoading: pricesLoading } = useCryptoPrices();

  // Get crypto configuration
  const crypto = cryptoConfig[asset.symbol as keyof typeof cryptoConfig] || cryptoConfig.BTC;

  // Enhanced calculations with unified system data
  const mainBalance = asset.balance || 0;
  const principalAmount = asset.principalAmount || mainBalance;
  const dailyInterestRate = asset.dailyInterestRate || 0.01; // 1% daily from unified system
  const dailyInterest = Math.floor(principalAmount * dailyInterestRate);
  const totalInterest = asset.interestBalance || 0;
  const totalReferral = asset.commissionBalance || 0;
  const activePackages = asset.activePackages || 0;
  const isLocked = asset.isLocked || false;
  const daysUntilUnlock = asset.daysUntilUnlock || 0;

  // Calculate USD values using real crypto prices
  const currentBalance = localRealTimeData?.currentBalance || mainBalance;
  const currentPrice = getPrice(asset.symbol);
  const totalBalanceUSD = convertToUSD(currentBalance, asset.symbol);
  const dailyInterestUSD = convertToUSD(dailyInterest, asset.symbol);
  const totalInterestUSD = convertToUSD(localRealTimeData?.interestBalance || totalInterest, asset.symbol);
  const totalReferralUSD = convertToUSD(localRealTimeData?.commissionBalance || totalReferral, asset.symbol);

  // Update real-time data from wallet sync hook
  useEffect(() => {
    if (hookRealTimeData && hookRealTimeData[asset.symbol]) {
      const cryptoData = hookRealTimeData[asset.symbol];
      setLocalRealTimeData({
        currentBalance: asset.balance || 0,
        interestBalance: cryptoData.totalEarned || 0,
        commissionBalance: asset.commissionBalance || 0,
        lastUpdated: cryptoData.lastUpdated?.toISOString() || new Date().toISOString()
      });

      console.log(`🔄 ModernWalletCard: Real-time data updated for ${asset.symbol}:`, cryptoData);
    }
  }, [hookRealTimeData, asset.symbol, asset.balance, asset.commissionBalance]);

  // Update countdown and progress
  useEffect(() => {
    const updateCountdown = () => {
      const now = new Date();
      const utcTime = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));

      const tomorrow = new Date(utcTime);
      tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
      tomorrow.setUTCHours(3, 0, 0, 0); // 03:00 UTC for interest distribution

      if (tomorrow <= utcTime) {
        tomorrow.setUTCDate(tomorrow.getUTCDate() + 1);
      }

      const timeLeft = tomorrow.getTime() - utcTime.getTime();

      const hours = Math.floor(timeLeft / (1000 * 60 * 60));
      const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

      setCountdown(
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      );

      // Calculate progress (from 03:00 UTC to 03:00 UTC next day)
      const dayStart = new Date(utcTime);
      dayStart.setUTCHours(3, 0, 0, 0);
      if (dayStart > utcTime) {
        dayStart.setUTCDate(dayStart.getUTCDate() - 1);
      }

      const dayProgress = (utcTime.getTime() - dayStart.getTime()) / (24 * 60 * 60 * 1000);
      setProgressPercent(Math.floor(dayProgress * 100));
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);
    return () => clearInterval(interval);
  }, []);

  const handleDeposit = async () => {
    console.log(`💰 ModernWalletCard: Deposit clicked for ${asset.symbol}`);

    if (onDeposit) {
      // Pass enhanced asset data including real-time information
      const enhancedAssetData = {
        ...asset,
        realTimeData: localRealTimeData,
        enhancedData,
        isConnected,
        principalAmount,
        dailyInterestRate,
        activePackages,
        isLocked,
        daysUntilUnlock
      };

      onDeposit(asset.symbol, enhancedAssetData);
    }
  };

  const handleWithdraw = async () => {
    console.log(`💸 ModernWalletCard: Withdraw clicked for ${asset.symbol}`);
    console.log(`💸 ModernWalletCard: onWithdraw prop exists:`, !!onWithdraw);

    try {
      // Get current withdrawable balance
      const withdrawableBalance = await getWithdrawableBalance(asset.symbol);
      console.log(`💸 ModernWalletCard: Withdrawable balance:`, withdrawableBalance);

      if (onWithdraw) {
        // Pass enhanced asset data including withdrawable amounts
        const enhancedAssetData = {
          ...asset,
          realTimeData: localRealTimeData,
          enhancedData,
          isConnected,
          withdrawableBalance,
          principalAmount,
          dailyInterestRate,
          activePackages,
          isLocked,
          daysUntilUnlock
        };

        console.log(`💸 ModernWalletCard: Calling onWithdraw with data:`, enhancedAssetData);
        onWithdraw(asset.symbol, enhancedAssetData);
      } else {
        // Fallback: Open withdraw modal directly
        console.log(`🔄 ModernWalletCard: Using fallback withdraw modal for ${asset.symbol}`);
        onWithdrawModalOpen();
      }
    } catch (error) {
      console.error(`❌ ModernWalletCard: Error in handleWithdraw for ${asset.symbol}:`, error);

      // Still try to open modal even if balance fetch failed
      if (onWithdraw) {
        // Pass basic asset data without withdrawable balance
        const basicAssetData = {
          ...asset,
          realTimeData: localRealTimeData,
          enhancedData,
          isConnected,
          withdrawableBalance: null, // Indicate error
          principalAmount,
          dailyInterestRate,
          activePackages,
          isLocked,
          daysUntilUnlock
        };

        console.log(`🔄 ModernWalletCard: Calling onWithdraw with basic data due to error`);
        onWithdraw(asset.symbol, basicAssetData);
      } else {
        // Fallback: Open withdraw modal directly
        console.log(`🔄 ModernWalletCard: Using fallback withdraw modal due to error`);
        onWithdrawModalOpen();
      }
    }
  };

  return (
    <Box
      maxW="420px"
      w="100%"
      mx="auto"
      bg="#1E1E1E"
      borderRadius="12px"
      border="1px solid #2B2B2B"
      overflow="hidden"
      boxShadow="0 4px 12px rgba(0, 0, 0, 0.15)"
      fontFamily="'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
      color="#FFFFFF"
      position="relative"
    >
      {/* Header */}
      <Box
        bg="#2B2B2B"
        p="16px"
        borderBottom="1px solid #3B3B3B"
        display="flex"
        alignItems="center"
        justifyContent="space-between"
      >
        <Box display="flex" alignItems="center" gap="12px">
          <Box
            w="40px"
            h="40px"
            bg={crypto.bgColor}
            border={`1px solid ${crypto.borderColor}`}
            borderRadius="8px"
            display="flex"
            alignItems="center"
            justifyContent="center"
            fontWeight="700"
            color={crypto.color}
            fontSize="18px"
          >
            {crypto.icon}
          </Box>
          <Box>
            <Box color="#FFFFFF" fontSize="16px" fontWeight="600">
              {crypto.name} Wallet
            </Box>
            <Box color="#B7BDC6" fontSize="12px" mt="1px" fontWeight="500">
              {asset.symbol} • Investment
            </Box>
          </Box>
        </Box>
        <Box
          w="32px"
          h="32px"
          bg="#3B3B3B"
          border="1px solid #4B4B4B"
          borderRadius="6px"
          color="#B7BDC6"
          cursor="pointer"
          display="flex"
          alignItems="center"
          justifyContent="center"
          fontSize="14px"
          transition="all 0.2s ease"
          _hover={{
            bg: "#4B4B4B",
            color: "#FFFFFF"
          }}
        >
          ⚙
        </Box>
      </Box>

      {/* Content */}
      <Box p="12px">
        {/* Main Balance */}
        <Box
          bg="#2B2B2B"
          borderRadius="8px"
          p="20px"
          mb="12px"
          border="1px solid #3B3B3B"
          textAlign="center"
        >
          <Box
            color="#B7BDC6"
            fontSize="12px"
            mb="8px"
            fontWeight="500"
            textTransform="uppercase"
            letterSpacing="0.5px"
          >
            Total Balance
          </Box>
          <Box
            color="#FFFFFF"
            fontSize="28px"
            fontWeight="700"
            mb="6px"
            letterSpacing="-0.5px"
          >
            {currentBalance.toLocaleString()} {asset.symbol}
          </Box>
          <Box color="#B7BDC6" fontSize="14px" fontWeight="500">
            {pricesLoading ? (
              <Box display="flex" alignItems="center" justifyContent="center" gap="4px">
                🔄 Loading price...
              </Box>
            ) : (
              `≈ $${totalBalanceUSD.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              })} USD`
            )}
          </Box>
          {loading && (
            <Box
              color="#F0B90B"
              fontSize="10px"
              fontWeight="500"
              mt="4px"
              display="flex"
              alignItems="center"
              justifyContent="center"
              gap="4px"
            >
              🔄 Loading...
            </Box>
          )}
          {localRealTimeData?.lastUpdated && (
            <Box
              color="#0ECB81"
              fontSize="10px"
              fontWeight="500"
              mt="2px"
              textAlign="center"
            >
              ✅ Real-time data
            </Box>
          )}
          {!pricesLoading && currentPrice > 0 && (
            <Box
              color="#B7BDC6"
              fontSize="10px"
              fontWeight="500"
              mt="4px"
              textAlign="center"
              display="flex"
              alignItems="center"
              justifyContent="center"
              gap="4px"
            >
              💰 Current Price: ${currentPrice.toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 6
              })}
            </Box>
          )}
        </Box>

        {/* Daily Interest */}
        <Box
          bg="#2B2B2B"
          border="1px solid #3B3B3B"
          borderRadius="8px"
          p="16px"
          mb="12px"
        >
          <Box display="flex" justifyContent="space-between" alignItems="center" mb="12px">
            <Box color="#FFFFFF" fontSize="14px" fontWeight="600">
              Daily Interest
            </Box>
            <Box
              bg="#0ECB81"
              color="#FFFFFF"
              fontSize="10px"
              fontWeight="600"
              px="8px"
              py="4px"
              borderRadius="4px"
              textTransform="uppercase"
              letterSpacing="0.5px"
            >
              Active
            </Box>
          </Box>
          <Box
            color="#FFFFFF"
            fontSize="20px"
            fontWeight="600"
            textAlign="center"
            mb="6px"
          >
            {dailyInterest.toLocaleString()} {asset.symbol}
          </Box>
          <Box
            color="#0ECB81"
            fontSize="12px"
            textAlign="center"
            fontWeight="500"
            mb="4px"
          >
            ≈ ${dailyInterestUSD.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            })} USD daily
          </Box>
          <Box
            color="#B7BDC6"
            fontSize="12px"
            textAlign="center"
            fontWeight="500"
          >
            Automatically credited at 00:00 UTC
          </Box>
        </Box>

        {/* Countdown */}
        <Box
          bg="#2B2B2B"
          border="1px solid #3B3B3B"
          borderRadius="8px"
          p="16px"
          mb="12px"
        >
          <Box display="flex" justifyContent="space-between" alignItems="center" mb="12px">
            <Box color="#FFFFFF" fontSize="14px" fontWeight="600">
              Next Payment
            </Box>
            <Box
              color="#F0B90B"
              fontSize="16px"
              fontWeight="700"
              fontFamily="'Inter', monospace"
              letterSpacing="1px"
            >
              {countdown}
            </Box>
          </Box>
          <Box
            bg="#3B3B3B"
            h="6px"
            borderRadius="3px"
            overflow="hidden"
            mb="8px"
          >
            <Box
              h="100%"
              bg="#F0B90B"
              transition="width 1s ease"
              borderRadius="3px"
              width={`${progressPercent}%`}
            />
          </Box>
          <Box display="flex" justifyContent="space-between" fontSize="12px">
            <Box color="#B7BDC6" fontWeight="500">
              Progress: {progressPercent}%
            </Box>
            <Box color="#0ECB81" fontWeight="600">
              +{dailyInterest.toLocaleString()} {asset.symbol}
            </Box>
          </Box>
        </Box>

        {/* Total Earnings */}
        <Box
          bg="#2B2B2B"
          border="1px solid #3B3B3B"
          borderRadius="8px"
          p="16px"
          mb="12px"
        >
          <Box display="flex" justifyContent="space-between" alignItems="center" mb="16px">
            <Box color="#FFFFFF" fontSize="14px" fontWeight="600">
              Total Earnings
            </Box>
            <Box
              bg="#F0B90B"
              color="#000000"
              fontSize="10px"
              fontWeight="600"
              px="8px"
              py="4px"
              borderRadius="4px"
              textTransform="uppercase"
              letterSpacing="0.5px"
            >
              Lifetime
            </Box>
          </Box>

          <Box display="grid" gridTemplateColumns="1fr 1fr" gap="12px" mb="16px">
            <Box
              bg="#3B3B3B"
              border="1px solid #4B4B4B"
              borderRadius="6px"
              p="12px"
              textAlign="center"
            >
              <Box
                color="#B7BDC6"
                fontSize="10px"
                mb="6px"
                fontWeight="500"
                textTransform="uppercase"
                letterSpacing="0.5px"
              >
                Interest Earned
              </Box>
              <Box
                color="#FFFFFF"
                fontSize="14px"
                fontWeight="600"
                mb="2px"
              >
                {(localRealTimeData?.interestBalance || totalInterest).toLocaleString()} {asset.symbol}
              </Box>
              <Box color="#0ECB81" fontSize="10px" fontWeight="500" mb="2px">
                ≈ ${totalInterestUSD.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })} USD
              </Box>
            </Box>
            <Box
              bg="#3B3B3B"
              border="1px solid #4B4B4B"
              borderRadius="6px"
              p="12px"
              textAlign="center"
            >
              <Box
                color="#B7BDC6"
                fontSize="10px"
                mb="6px"
                fontWeight="500"
                textTransform="uppercase"
                letterSpacing="0.5px"
              >
                Commission Earned
              </Box>
              <Box
                color="#FFFFFF"
                fontSize="14px"
                fontWeight="600"
                mb="2px"
              >
                {(localRealTimeData?.commissionBalance || totalReferral).toLocaleString()} {asset.symbol}
              </Box>
              <Box color="#F0B90B" fontSize="10px" fontWeight="500" mb="2px">
                ≈ ${totalReferralUSD.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })} USD
              </Box>
            </Box>
          </Box>
          
          <Box
            borderTop="1px solid #4B4B4B"
            pt="16px"
            textAlign="center"
            bg="#3B3B3B"
            borderRadius="6px"
            p="16px"
            mt="12px"
            border="1px solid #4B4B4B"
          >
            <Box
              color="#B7BDC6"
              fontSize="12px"
              mb="8px"
              fontWeight="500"
              textTransform="uppercase"
              letterSpacing="0.5px"
            >
              Total Lifetime Earnings
            </Box>
            <Box
              fontSize="24px"
              fontWeight="700"
              letterSpacing="-0.5px"
              color="#FFFFFF"
              mb="4px"
            >
              {((localRealTimeData?.interestBalance || totalInterest) + (localRealTimeData?.commissionBalance || totalReferral)).toLocaleString()} {asset.symbol}
            </Box>
            <Box
              fontSize="16px"
              fontWeight="600"
              color="#0ECB81"
            >
              ≈ ${(totalInterestUSD + totalReferralUSD).toLocaleString(undefined, {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
              })} USD
            </Box>
          </Box>
        </Box>

        {/* Action Buttons */}
        <Box display="grid" gridTemplateColumns="1fr 1fr" gap="12px">
          <Box
            bg="#0ECB81"
            borderRadius="6px"
            p="16px 12px"
            textAlign="center"
            cursor="pointer"
            transition="all 0.2s ease"
            _hover={{
              bg: "#0BB574",
              transform: 'translateY(-1px)'
            }}
            onClick={handleDeposit}
          >
            <Box
              w="32px"
              h="32px"
              bg="rgba(255, 255, 255, 0.2)"
              borderRadius="6px"
              display="flex"
              alignItems="center"
              justifyContent="center"
              mx="auto"
              mb="8px"
              fontSize="16px"
              color="#FFFFFF"
              fontWeight="600"
            >
              ↓
            </Box>
            <Box color="#FFFFFF" fontSize="14px" fontWeight="600" mb="2px">
              Deposit
            </Box>
            <Box color="rgba(255, 255, 255, 0.8)" fontSize="11px" fontWeight="500">
              Add funds
            </Box>
          </Box>

          <Box
            bg="#2B2B2B"
            border="1px solid #3B3B3B"
            borderRadius="6px"
            p="16px 12px"
            textAlign="center"
            cursor="pointer"
            transition="all 0.2s ease"
            _hover={{
              bg: "#3B3B3B",
              borderColor: "#4B4B4B"
            }}
            onClick={handleWithdraw}
          >
            <Box
              w="32px"
              h="32px"
              bg="#3B3B3B"
              borderRadius="6px"
              display="flex"
              alignItems="center"
              justifyContent="center"
              mx="auto"
              mb="8px"
              fontSize="16px"
              color="#FFFFFF"
              fontWeight="600"
            >
              ↑
            </Box>
            <Box color="#FFFFFF" fontSize="14px" fontWeight="600" mb="2px">
              Withdraw
            </Box>
            <Box color="#B7BDC6" fontSize="11px" fontWeight="500">
              Transfer out
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Fallback Withdraw Modal */}
      <WithdrawModal
        isOpen={isWithdrawModalOpen}
        onClose={onWithdrawModalClose}
        initialCrypto={asset.symbol.toLowerCase()}
        initialWithdrawalType="principal"
        onSuccess={() => {
          onWithdrawModalClose();
          toast({
            title: "Withdrawal Successful",
            description: `Your ${asset.symbol} withdrawal has been processed.`,
            status: "success",
            duration: 5000,
            isClosable: true,
          });
        }}
      />
    </Box>
  );
};

export default ModernWalletCard;
