# Vite environment variables for production
VITE_API_URL=https://api.shpnfinance.com/api
VITE_SOCKET_URL=https://api.shpnfinance.com
VITE_SOCKET_PATH=/ws
VITE_ENVIRONMENT=production
VITE_VERSION=$npm_package_version
VITE_SENTRY_DSN=
VITE_GOOGLE_ANALYTICS=
VITE_RECAPTCHA_SITE_KEY=
VITE_MOCK_API=false

# Contract Address
VITE_CONTRACT_ADDRESS=0x742d35Cc6634C0532925a3b844Bc454e4438f44e

# Infura ID for Web3
VITE_INFURA_ID=********************************

# Storage encryption key
VITE_STORAGE_KEY=shpnfinance_secure_storage_key

# Feature flags
VITE_ENABLE_TESTNET=false
VITE_ENABLE_FAUCET=false
VITE_ENABLE_ANALYTICS=true

# React environment variables (for backward compatibility)
REACT_APP_API_URL=https://api.shpnfinance.com/api
REACT_APP_SOCKET_URL=https://api.shpnfinance.com
REACT_APP_SOCKET_PATH=/ws
REACT_APP_ENVIRONMENT=production
REACT_APP_VERSION=$npm_package_version
REACT_APP_SENTRY_DSN=
REACT_APP_GOOGLE_ANALYTICS=
REACT_APP_RECAPTCHA_SITE_KEY=
REACT_APP_MOCK_API=false
