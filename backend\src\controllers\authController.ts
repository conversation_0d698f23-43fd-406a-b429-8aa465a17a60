import { Request, Response } from 'express';
import User from '../models/User';
import jwt from 'jsonwebtoken';
import { loginUserSchema } from '../validators/userValidator';

const authController = {
  async login(req: Request, res: Response) {
    try {
      const { error } = loginUserSchema.validate(req.body);
      if (error) return res.status(400).json({ error: error.details[0].message });

      const user = await User.findOne({ email: req.body.email }).select('+password');
      if (!user) return res.status(404).json({ error: 'Kullanıcı bulunamadı' });

      const validPassword = await user.comparePassword(req.body.password);
      if (!validPassword) return res.status(401).json({ error: 'Geçersiz kimlik bilgileri' });

      const token = jwt.sign(
        { userId: user._id, isAdmin: user.isAdmin },
        process.env.JWT_SECRET!,
        { expiresIn: '1h' }
      );

      res.json({
        token,
        user: {
          id: user._id,
          email: user.email,
          isAdmin: user.isAdmin
        }
      });

    } catch (error: any) {
      console.error('Oturum açma hatası:', error);
      // Provide more specific error messages based on the error type
      if (error.name === 'ValidationError') {
        return res.status(400).json({ error: error.message });
      } else if (error.message && error.message.includes('buffering timed out')) {
        return res.status(503).json({ error: 'Veritabanı bağlantı sorunu. Lütfen daha sonra tekrar deneyin.' });
      }
      res.status(500).json({ error: 'Sunucu hatası oluştu. Lütfen daha sonra tekrar deneyin.', details: error.message });
    }
  }
};

export default authController;