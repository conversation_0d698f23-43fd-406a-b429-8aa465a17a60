import { Request, Response } from 'express';
import InvestmentPackage from '../models/investmentPackageModel';
import { logger } from '../utils/logger';

/**
 * @desc    Get investment balances for all currencies
 * @route   GET /api/investment-packages/balances
 * @access  Private
 */
export const getInvestmentBalances = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        status: 'error',
        message: 'User not authenticated'
      });
      return;
    }

    const userId = req.user._id;

    // Get all active investment packages for the user
    const packages = await InvestmentPackage.find({
      userId,
      status: 'active'
    });

    // Group by currency and calculate totals
    const balanceMap = new Map();

    packages.forEach(pkg => {
      const currency = pkg.currency.toUpperCase();
      const existing = balanceMap.get(currency) || {
        currency,
        totalEarnings: 0,
        availableForWithdrawal: 0,
        totalWithdrawn: 0,
        activePackages: 0,
        lastEarningDate: null,
        principalAmount: 0,
        interestAmount: 0,
        commissionAmount: 0,
        totalBalance: 0,
        dailyInterest: 0,
        isLocked: false,
        daysUntilUnlock: 0,
        canWithdraw: false,
        minimumThreshold: 50, // Default minimum threshold
        lastUpdated: new Date().toISOString()
      };

      existing.totalEarnings += pkg.totalEarned || 0;
      existing.availableForWithdrawal += pkg.totalEarned || 0;
      existing.totalWithdrawn += pkg.totalWithdrawn || 0;
      existing.activePackages += 1;
      existing.principalAmount += pkg.amount || 0;
      existing.interestAmount += pkg.totalEarned || 0;
      existing.totalBalance += pkg.amount + (pkg.totalEarned || 0);
      existing.dailyInterest += pkg.calculateDailyInterest();

      if (pkg.lastEarningDate && (!existing.lastEarningDate || pkg.lastEarningDate > existing.lastEarningDate)) {
        existing.lastEarningDate = pkg.lastEarningDate;
      }

      balanceMap.set(currency, existing);
    });

    const balances = Array.from(balanceMap.values()).map(balance => ({
      ...balance,
      canWithdraw: balance.availableForWithdrawal >= balance.minimumThreshold,
      usdValue: balance.currency === 'USDT' ? balance.totalBalance : 0 // Would need price service for accurate conversion
    }));

    res.json({
      status: 'success',
      data: balances
    });
  } catch (error: any) {
    logger.error('Get investment balances error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch investment balances',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get investment balance for specific currency
 * @route   GET /api/investment-packages/balances/:currency
 * @access  Private
 */
export const getInvestmentBalanceByCurrency = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const { currency } = req.params;

    // Get active investment packages for the specific currency
    const packages = await InvestmentPackage.find({
      userId,
      currency: currency.toUpperCase(),
      status: 'active'
    });

    const balance = {
      currency: currency.toUpperCase(),
      totalEarnings: 0,
      availableForWithdrawal: 0,
      totalWithdrawn: 0,
      activePackages: packages.length,
      lastEarningDate: null as Date | null,
      principalAmount: 0,
      interestAmount: 0,
      commissionAmount: 0,
      totalBalance: 0,
      dailyInterest: 0,
      isLocked: false,
      daysUntilUnlock: 0,
      canWithdraw: false,
      minimumThreshold: 50, // Default minimum threshold
      lastUpdated: new Date().toISOString()
    };

    packages.forEach(pkg => {
      balance.totalEarnings += pkg.totalEarned || 0;
      balance.availableForWithdrawal += pkg.totalEarned || 0;
      balance.totalWithdrawn += pkg.totalWithdrawn || 0;
      balance.principalAmount += pkg.amount || 0;
      balance.interestAmount += pkg.totalEarned || 0;
      balance.totalBalance += pkg.amount + (pkg.totalEarned || 0);
      balance.dailyInterest += pkg.calculateDailyInterest();

      if (pkg.lastEarningDate && (!balance.lastEarningDate || pkg.lastEarningDate > balance.lastEarningDate)) {
        balance.lastEarningDate = pkg.lastEarningDate;
      }
    });

    balance.canWithdraw = balance.availableForWithdrawal >= balance.minimumThreshold;

    res.json({
      status: 'success',
      data: balance
    });
  } catch (error: any) {
    logger.error('Get investment balance by currency error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch investment balance',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export default {
  getInvestmentBalances,
  getInvestmentBalanceByCurrency
};