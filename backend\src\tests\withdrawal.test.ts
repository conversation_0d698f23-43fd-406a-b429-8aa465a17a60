import request from 'supertest';
import mongoose from 'mongoose';
import app from '../index';
import User from '../models/userModel';
import Wallet from '../models/walletModel';
import Withdrawal from '../models/withdrawalModel';
import InvestmentPackage from '../models/investmentPackageModel';
import { generateToken } from '../utils/generateToken';

describe('Withdrawal System', () => {
  let userToken: string;
  let adminToken: string;
  let userId: string;
  let walletId: string;

  beforeAll(async () => {
    // Connect to test database
    await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/cryptoyield_test');
  });

  beforeEach(async () => {
    // Clean up database
    await User.deleteMany({});
    await Wallet.deleteMany({});
    await Withdrawal.deleteMany({});
    await InvestmentPackage.deleteMany({});

    // Create test user
    const user = await User.create({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'password123',
      isEmailVerified: true
    });

    userId = user._id.toString();
    userToken = generateToken(user._id.toString());

    // Create admin user
    const admin = await User.create({
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'password123',
      isEmailVerified: true,
      isAdmin: true
    });

    adminToken = generateToken(admin._id.toString());

    // Create test wallet
    const wallet = await Wallet.create({
      userId: user._id,
      assets: [
        {
          symbol: 'BTC',
          balance: 1.0,
          interestBalance: 0.5,
          commissionBalance: 0.3,
          mode: 'live',
          network: 'Bitcoin'
        },
        {
          symbol: 'ETH',
          balance: 10.0,
          interestBalance: 5.0,
          commissionBalance: 2.0,
          mode: 'live',
          network: 'Ethereum'
        }
      ]
    });

    walletId = wallet._id.toString();

    // Create investment package for 30-day lock testing
    await InvestmentPackage.create({
      userId: user._id,
      amount: 1000,
      currency: 'USDT',
      packageType: 'basic',
      duration: 30,
      interestRate: 10,
      status: 'active',
      createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000) // 20 days ago
    });
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('POST /api/withdrawals/validate', () => {
    it('should validate a valid withdrawal request', async () => {
      const response = await request(app)
        .post('/api/withdrawals/validate')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          cryptocurrency: 'ETH',
          withdrawalType: 'interest',
          amount: 2.0,
          walletAddress: '******************************************',
          network: 'Ethereum'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(true);
      expect(response.body.data.availableBalance).toBe(5.0);
    });

    it('should reject withdrawal with insufficient balance', async () => {
      const response = await request(app)
        .post('/api/withdrawals/validate')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          cryptocurrency: 'BTC',
          withdrawalType: 'interest',
          amount: 1.0,
          walletAddress: '**********************************',
          network: 'Bitcoin'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(false);
      expect(response.body.data.errors).toContain(
        expect.stringContaining('Insufficient interest balance')
      );
    });

    it('should reject main balance withdrawal within 30-day lock', async () => {
      const response = await request(app)
        .post('/api/withdrawals/validate')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          cryptocurrency: 'BTC',
          withdrawalType: 'balance',
          amount: 0.5,
          walletAddress: '**********************************',
          network: 'Bitcoin'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(false);
      expect(response.body.data.lockInfo.isLocked).toBe(true);
      expect(response.body.data.lockInfo.daysRemaining).toBeGreaterThan(0);
    });

    it('should reject withdrawal with invalid wallet address', async () => {
      const response = await request(app)
        .post('/api/withdrawals/validate')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          cryptocurrency: 'ETH',
          withdrawalType: 'interest',
          amount: 1.0,
          walletAddress: 'invalid-address',
          network: 'Ethereum'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.isValid).toBe(false);
      expect(response.body.data.errors).toContain(
        expect.stringContaining('Invalid ETH wallet address format')
      );
    });
  });

  describe('POST /api/withdrawals/submit', () => {
    it('should submit a valid withdrawal request', async () => {
      const response = await request(app)
        .post('/api/withdrawals/submit')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          cryptocurrency: 'ETH',
          withdrawalType: 'interest',
          amount: 2.0,
          walletAddress: '******************************************',
          network: 'Ethereum'
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.cryptocurrency).toBe('ETH');
      expect(response.body.data.withdrawalType).toBe('interest');
      expect(response.body.data.amount).toBe(2.0);
      expect(response.body.data.status).toBe('pending');

      // Check if balance was deducted
      const wallet = await Wallet.findById(walletId);
      const ethAsset = wallet?.assets.find(a => a.symbol === 'ETH');
      expect(ethAsset?.interestBalance).toBe(3.0); // 5.0 - 2.0
    });

    it('should reject withdrawal submission with insufficient balance', async () => {
      const response = await request(app)
        .post('/api/withdrawals/submit')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          cryptocurrency: 'BTC',
          withdrawalType: 'interest',
          amount: 1.0,
          walletAddress: '**********************************',
          network: 'Bitcoin'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/withdrawals/history', () => {
    beforeEach(async () => {
      // Create test withdrawal
      await Withdrawal.create({
        userId,
        walletId,
        cryptocurrency: 'ETH',
        withdrawalType: 'interest',
        amount: 1.0,
        usdValue: 2000,
        networkFee: 0.001,
        netAmount: 0.999,
        walletAddress: '******************************************',
        network: 'Ethereum',
        status: 'pending'
      });
    });

    it('should get user withdrawal history', async () => {
      const response = await request(app)
        .get('/api/withdrawals/history')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.withdrawals).toHaveLength(1);
      expect(response.body.data.withdrawals[0].cryptocurrency).toBe('ETH');
    });
  });

  describe('GET /api/withdrawals/balance', () => {
    it('should get withdrawable balances', async () => {
      const response = await request(app)
        .get('/api/withdrawals/balance')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.balances).toHaveLength(2);
      
      const btcBalance = response.body.data.balances.find((b: any) => b.cryptocurrency === 'BTC');
      expect(btcBalance.balances.main.amount).toBe(1.0);
      expect(btcBalance.balances.main.isLocked).toBe(true);
      expect(btcBalance.balances.interest.amount).toBe(0.5);
      expect(btcBalance.balances.commission.amount).toBe(0.3);
    });
  });

  describe('Admin Withdrawal Management', () => {
    let withdrawalId: string;

    beforeEach(async () => {
      const withdrawal = await Withdrawal.create({
        userId,
        walletId,
        cryptocurrency: 'ETH',
        withdrawalType: 'interest',
        amount: 1.0,
        usdValue: 2000,
        networkFee: 0.001,
        netAmount: 0.999,
        walletAddress: '******************************************',
        network: 'Ethereum',
        status: 'pending'
      });
      withdrawalId = withdrawal._id.toString();
    });

    describe('GET /api/admin/withdrawals', () => {
      it('should get all withdrawals for admin', async () => {
        const response = await request(app)
          .get('/api/admin/withdrawals')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.data.withdrawals).toHaveLength(1);
      });

      it('should filter withdrawals by status', async () => {
        const response = await request(app)
          .get('/api/admin/withdrawals?status=pending')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.data.withdrawals).toHaveLength(1);
      });
    });

    describe('PUT /api/admin/withdrawals/:id/status', () => {
      it('should approve a withdrawal', async () => {
        const response = await request(app)
          .put(`/api/admin/withdrawals/${withdrawalId}/status`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            status: 'approved',
            adminNotes: 'Approved by admin'
          });

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);

        const withdrawal = await Withdrawal.findById(withdrawalId);
        expect(withdrawal?.status).toBe('approved');
        expect(withdrawal?.adminNotes).toBe('Approved by admin');
      });

      it('should reject a withdrawal and restore funds', async () => {
        const response = await request(app)
          .put(`/api/admin/withdrawals/${withdrawalId}/status`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            status: 'rejected',
            adminNotes: 'Rejected due to suspicious activity'
          });

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);

        // Check if funds were restored
        const wallet = await Wallet.findById(walletId);
        const ethAsset = wallet?.assets.find(a => a.symbol === 'ETH');
        expect(ethAsset?.interestBalance).toBe(5.0); // Should be restored
      });
    });
  });

  describe('Security Tests', () => {
    it('should require authentication for withdrawal endpoints', async () => {
      const response = await request(app)
        .post('/api/withdrawals/validate')
        .send({
          cryptocurrency: 'ETH',
          withdrawalType: 'interest',
          amount: 1.0,
          walletAddress: '******************************************',
          network: 'Ethereum'
        });

      expect(response.status).toBe(401);
    });

    it('should require admin role for admin endpoints', async () => {
      const response = await request(app)
        .get('/api/admin/withdrawals')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
    });

    it('should prevent users from accessing other users\' withdrawals', async () => {
      // Create another user
      const otherUser = await User.create({
        firstName: 'Other',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'password123',
        isEmailVerified: true
      });

      const otherUserToken = generateToken(otherUser._id.toString());

      // Try to access first user's withdrawal history
      const response = await request(app)
        .get('/api/withdrawals/history')
        .set('Authorization', `Bearer ${otherUserToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.withdrawals).toHaveLength(0);
    });
  });
});
