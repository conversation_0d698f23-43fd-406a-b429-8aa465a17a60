#!/usr/bin/env node

/**
 * Test script for First Deposit Referral Commission System
 * This script tests the new first-time deposit referral commission functionality
 */

import mongoose from 'mongoose';
import User from '../models/userModel';
import Wallet from '../models/walletModel';
import Transaction from '../models/transactionModel';
import { FirstDepositCommissionService } from '../services/firstDepositCommissionService';
import { logger } from '../utils/logger';

// Database connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield';
    await mongoose.connect(mongoURI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Test data
const testUsers = {
  referrer: {
    firstName: 'John',
    lastName: 'Referrer',
    email: '<EMAIL>',
    password: 'Test123!@#'
  },
  newUser: {
    firstName: 'Jane',
    lastName: 'NewUser',
    email: '<EMAIL>',
    password: 'Test123!@#'
  }
};

const testDepositAmount = 1000; // $1000 USDT
const testCryptocurrency = 'USDT';
const expectedCommission = testDepositAmount * 0.03; // 3% = $30

// Cleanup function
const cleanup = async () => {
  try {
    await User.deleteMany({
      email: { $in: [testUsers.referrer.email, testUsers.newUser.email] }
    });
    
    await Wallet.deleteMany({
      userId: { $exists: true }
    });
    
    await Transaction.deleteMany({
      'metadata.isFirstDepositCommission': true
    });
    
    console.log('🧹 Cleaned up test data');
  } catch (error) {
    console.error('❌ Cleanup error:', error);
  }
};

// Test functions
const testFirstDepositCommissionSystem = async () => {
  console.log('\n🧪 Testing First Deposit Referral Commission System');
  console.log('=' .repeat(70));

  const results = [];

  try {
    // Test 1: Create referrer user
    console.log('\n📋 Test 1: Create Referrer User');
    console.log('-' .repeat(50));

    const referrer = new User({
      ...testUsers.referrer,
      referralCode: 'TEST1234'
    });
    await referrer.save();
    
    console.log(`✅ Referrer created: ${referrer.firstName} ${referrer.lastName}`);
    console.log(`   Referral Code: ${referrer.referralCode}`);
    results.push({ test: 'Create Referrer', status: 'PASS' });

    // Test 2: Create new user with referrer
    console.log('\n📋 Test 2: Create New User with Referrer');
    console.log('-' .repeat(50));

    const newUser = new User({
      ...testUsers.newUser,
      referrerId: referrer._id,
      referredBy: referrer.referralCode
    });
    await newUser.save();
    
    console.log(`✅ New user created: ${newUser.firstName} ${newUser.lastName}`);
    console.log(`   Referred by: ${referrer.referralCode}`);
    console.log(`   Has first deposit approved: ${newUser.hasFirstDepositApproved}`);
    results.push({ test: 'Create New User with Referrer', status: 'PASS' });

    // Test 3: Check eligibility before first deposit
    console.log('\n📋 Test 3: Check First Deposit Eligibility');
    console.log('-' .repeat(50));

    const eligibility = await FirstDepositCommissionService.checkFirstDepositEligibility(newUser._id);
    console.log(`   Eligible: ${eligibility.eligible}`);
    console.log(`   Reason: ${eligibility.reason}`);
    console.log(`   Referrer ID: ${eligibility.referrerId}`);
    
    if (eligibility.eligible) {
      results.push({ test: 'Check Eligibility', status: 'PASS' });
    } else {
      results.push({ test: 'Check Eligibility', status: 'FAIL', reason: eligibility.reason });
    }

    // Test 4: Create deposit transaction
    console.log('\n📋 Test 4: Create Deposit Transaction');
    console.log('-' .repeat(50));

    const depositTransaction = new Transaction({
      userId: newUser._id,
      walletId: new mongoose.Types.ObjectId(), // Mock wallet ID
      type: 'deposit',
      asset: testCryptocurrency,
      amount: testDepositAmount,
      status: 'pending',
      description: 'Test first deposit'
    });
    await depositTransaction.save();
    
    console.log(`✅ Deposit transaction created: ${depositTransaction._id}`);
    console.log(`   Amount: ${testDepositAmount} ${testCryptocurrency}`);
    results.push({ test: 'Create Deposit Transaction', status: 'PASS' });

    // Test 5: Process first deposit commission
    console.log('\n📋 Test 5: Process First Deposit Commission');
    console.log('-' .repeat(50));

    const commissionResult = await FirstDepositCommissionService.processFirstDepositCommission(
      newUser._id,
      testDepositAmount,
      testCryptocurrency,
      depositTransaction._id
    );

    console.log(`   Success: ${commissionResult.success}`);
    console.log(`   Message: ${commissionResult.message}`);
    
    if (commissionResult.success) {
      console.log(`   Commission Amount: ${commissionResult.commissionAmount} ${testCryptocurrency}`);
      console.log(`   Referrer ID: ${commissionResult.referrerId}`);
      console.log(`   Transaction ID: ${commissionResult.transactionId}`);
      results.push({ test: 'Process Commission', status: 'PASS' });
    } else {
      results.push({ test: 'Process Commission', status: 'FAIL', reason: commissionResult.message });
    }

    // Test 6: Verify commission transaction was created
    console.log('\n📋 Test 6: Verify Commission Transaction');
    console.log('-' .repeat(50));

    const commissionTransaction = await Transaction.findOne({
      type: 'commission',
      'metadata.isFirstDepositCommission': true,
      'metadata.referredUserId': newUser._id.toString()
    });

    if (commissionTransaction) {
      console.log(`✅ Commission transaction found: ${commissionTransaction._id}`);
      console.log(`   Amount: ${commissionTransaction.amount} ${commissionTransaction.asset}`);
      console.log(`   Status: ${commissionTransaction.status}`);
      console.log(`   Referrer: ${commissionTransaction.userId}`);
      results.push({ test: 'Verify Commission Transaction', status: 'PASS' });
    } else {
      console.log(`❌ Commission transaction not found`);
      results.push({ test: 'Verify Commission Transaction', status: 'FAIL' });
    }

    // Test 7: Verify referrer wallet was updated
    console.log('\n📋 Test 7: Verify Referrer Wallet Update');
    console.log('-' .repeat(50));

    const referrerWallet = await Wallet.findOne({ userId: referrer._id });
    if (referrerWallet) {
      const usdtAsset = referrerWallet.assets.find(asset => asset.symbol === testCryptocurrency);
      if (usdtAsset && usdtAsset.commissionBalance >= expectedCommission) {
        console.log(`✅ Referrer wallet updated correctly`);
        console.log(`   Commission Balance: ${usdtAsset.commissionBalance} ${testCryptocurrency}`);
        console.log(`   Total Commission Earned: ${referrerWallet.totalCommissionEarned}`);
        results.push({ test: 'Verify Referrer Wallet', status: 'PASS' });
      } else {
        console.log(`❌ Referrer wallet commission balance incorrect`);
        console.log(`   Expected: ${expectedCommission}, Got: ${usdtAsset?.commissionBalance || 0}`);
        results.push({ test: 'Verify Referrer Wallet', status: 'FAIL' });
      }
    } else {
      console.log(`❌ Referrer wallet not found`);
      results.push({ test: 'Verify Referrer Wallet', status: 'FAIL' });
    }

    // Test 8: Verify user first deposit flag was set
    console.log('\n📋 Test 8: Verify User First Deposit Flag');
    console.log('-' .repeat(50));

    const updatedNewUser = await User.findById(newUser._id);
    if (updatedNewUser?.hasFirstDepositApproved) {
      console.log(`✅ User first deposit flag set correctly`);
      console.log(`   Has First Deposit Approved: ${updatedNewUser.hasFirstDepositApproved}`);
      console.log(`   First Deposit Approved At: ${updatedNewUser.firstDepositApprovedAt}`);
      results.push({ test: 'Verify First Deposit Flag', status: 'PASS' });
    } else {
      console.log(`❌ User first deposit flag not set`);
      results.push({ test: 'Verify First Deposit Flag', status: 'FAIL' });
    }

    // Test 9: Test duplicate commission prevention
    console.log('\n📋 Test 9: Test Duplicate Commission Prevention');
    console.log('-' .repeat(50));

    const duplicateResult = await FirstDepositCommissionService.processFirstDepositCommission(
      newUser._id,
      testDepositAmount,
      testCryptocurrency,
      depositTransaction._id
    );

    if (!duplicateResult.success && duplicateResult.message.includes('already had their first deposit approved')) {
      console.log(`✅ Duplicate commission prevention working`);
      console.log(`   Message: ${duplicateResult.message}`);
      results.push({ test: 'Duplicate Prevention', status: 'PASS' });
    } else {
      console.log(`❌ Duplicate commission prevention failed`);
      results.push({ test: 'Duplicate Prevention', status: 'FAIL' });
    }

    // Test 10: Get referral stats
    console.log('\n📋 Test 10: Get Referral Statistics');
    console.log('-' .repeat(50));

    const stats = await FirstDepositCommissionService.getReferralStats(referrer._id);
    console.log(`   Total Commission Earned: ${stats.totalCommissionEarned}`);
    console.log(`   Total Referrals: ${stats.totalReferrals}`);
    console.log(`   First Deposit Commissions: ${stats.firstDepositCommissions}`);
    console.log(`   Commission Transactions: ${stats.commissionTransactions.length}`);
    results.push({ test: 'Get Referral Stats', status: 'PASS' });

  } catch (error) {
    console.error('❌ Test error:', error);
    results.push({ test: 'System Error', status: 'FAIL', error: error.message });
  }

  // Print results summary
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('=' .repeat(70));
  
  const passCount = results.filter(r => r.status === 'PASS').length;
  const failCount = results.filter(r => r.status === 'FAIL').length;
  
  results.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${status} ${result.test}: ${result.status}`);
    if (result.reason) {
      console.log(`   Reason: ${result.reason}`);
    }
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  console.log('\n' + '=' .repeat(70));
  console.log(`📈 TOTAL: ${results.length} tests`);
  console.log(`✅ PASSED: ${passCount}`);
  console.log(`❌ FAILED: ${failCount}`);
  console.log(`📊 SUCCESS RATE: ${((passCount / results.length) * 100).toFixed(1)}%`);
  
  if (failCount === 0) {
    console.log('\n🎉 ALL TESTS PASSED! First Deposit Commission System is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.');
  }
};

// Main execution
const main = async () => {
  try {
    await connectDB();
    await cleanup();
    await testFirstDepositCommissionSystem();
  } catch (error) {
    console.error('❌ Main execution error:', error);
  } finally {
    await cleanup();
    await mongoose.disconnect();
    console.log('\n👋 Disconnected from MongoDB');
    process.exit(0);
  }
};

// Run the test
if (require.main === module) {
  main();
}

export default main;
