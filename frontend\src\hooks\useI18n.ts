import { useTranslation } from 'react-i18next';
import { useCallback, useMemo } from 'react';
import {
  changeLanguage,
  getCurrentLanguage,
  getSupportedLanguages,
  formatCurrency,
  formatNumber,
  formatDate,
  formatDateTime,
  isRTL,
  getLanguageDirection
} from '../i18n';

/**
 * Enhanced i18n hook with additional utilities
 */
export const useI18n = () => {
  const { t, i18n } = useTranslation();

  // Get current language
  const currentLanguage = useMemo(() => getCurrentLanguage(), [i18n.language]);

  // Get supported languages
  const supportedLanguages = useMemo(() => getSupportedLanguages(), []);

  // Check if current language is RTL
  const isRightToLeft = useMemo(() => isRTL(currentLanguage), [currentLanguage]);

  // Get language direction
  const direction = useMemo(() => getLanguageDirection(currentLanguage), [currentLanguage]);

  // Change language function
  const setLanguage = useCallback(async (lng: string) => {
    try {
      await changeLanguage(lng);
      return true;
    } catch (error) {
      console.error('Failed to change language:', error);
      return false;
    }
  }, []);

  // Format currency with current locale
  const formatCurrencyValue = useCallback((amount: number, currency?: string) => {
    if (currency) {
      return new Intl.NumberFormat(currentLanguage, {
        style: 'currency',
        currency: currency.toUpperCase(),
      }).format(amount);
    }
    return formatCurrency(amount, currentLanguage);
  }, [currentLanguage]);

  // Format number with current locale
  const formatNumberValue = useCallback((number: number, options?: Intl.NumberFormatOptions) => {
    return new Intl.NumberFormat(currentLanguage, options).format(number);
  }, [currentLanguage]);

  // Format date with current locale
  const formatDateValue = useCallback((date: Date | string, options?: Intl.DateTimeFormatOptions) => {
    return formatDate(date, currentLanguage, options);
  }, [currentLanguage]);

  // Format datetime with current locale
  const formatDateTimeValue = useCallback((date: Date | string) => {
    return formatDateTime(date, currentLanguage);
  }, [currentLanguage]);

  // Format relative time (e.g., "2 hours ago")
  const formatRelativeTime = useCallback((date: Date | string) => {
    const now = new Date();
    const targetDate = new Date(date);
    const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return t('time.now');
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return t('time.minutesAgo', { count: diffInMinutes });
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return t('time.hoursAgo', { count: diffInHours });
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
      return t('time.daysAgo', { count: diffInDays });
    }

    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) {
      return t('time.weeksAgo', { count: diffInWeeks });
    }

    const diffInMonths = Math.floor(diffInDays / 30);
    return t('time.monthsAgo', { count: diffInMonths });
  }, [t]);

  // Get translation with namespace
  const tWithNamespace = useCallback((namespace: string, key: string, options?: any) => {
    return t(`${namespace}:${key}`, options);
  }, [t]);

  // Get translation with fallback
  const tWithFallback = useCallback((key: string, fallback: string, options?: any) => {
    const translation = t(key, options);
    return translation === key ? fallback : translation;
  }, [t]);

  // Pluralization helper
  const plural = useCallback((key: string, count: number, options?: any) => {
    return t(key, { count, ...options });
  }, [t]);

  // Check if translation exists
  const hasTranslation = useCallback((key: string, namespace?: string) => {
    const fullKey = namespace ? `${namespace}:${key}` : key;
    return i18n.exists(fullKey);
  }, [i18n]);

  // Get all translations for a namespace
  const getNamespaceTranslations = useCallback((namespace: string) => {
    return i18n.getResourceBundle(currentLanguage, namespace) || {};
  }, [i18n, currentLanguage]);

  // Language info
  const languageInfo = useMemo(() => {
    const languageMap = {
      en: { name: 'English', nativeName: 'English', flag: '🇺🇸' },
      de: { name: 'German', nativeName: 'Deutsch', flag: '🇩🇪' },
      fr: { name: 'French', nativeName: 'Français', flag: '🇫🇷' }
    };

    const langInfo = languageMap[currentLanguage as keyof typeof languageMap] || languageMap.en;

    return {
      code: currentLanguage,
      name: langInfo.name,
      nativeName: langInfo.nativeName,
      flag: langInfo.flag,
      isRTL: isRightToLeft,
      direction,
    };
  }, [currentLanguage, isRightToLeft, direction]);

  return {
    // Basic translation functions
    t,
    tWithNamespace,
    tWithFallback,
    plural,

    // Language management
    currentLanguage,
    supportedLanguages,
    setLanguage,
    languageInfo,

    // Formatting functions
    formatCurrency: formatCurrencyValue,
    formatNumber: formatNumberValue,
    formatDate: formatDateValue,
    formatDateTime: formatDateTimeValue,
    formatRelativeTime,

    // Utility functions
    hasTranslation,
    getNamespaceTranslations,

    // Layout helpers
    isRTL: isRightToLeft,
    direction,

    // i18n instance
    i18n,
  };
};

/**
 * Hook for specific namespace translations
 */
export const useNamespaceTranslation = (namespace: string) => {
  const { t, i18n } = useTranslation(namespace);
  const { currentLanguage, setLanguage, formatCurrency, formatNumber, formatDate } = useI18n();

  return {
    t,
    currentLanguage,
    setLanguage,
    formatCurrency,
    formatNumber,
    formatDate,
    i18n,
  };
};

/**
 * Hook for common translations (most frequently used)
 */
export const useCommonTranslation = () => {
  return useNamespaceTranslation('common');
};

/**
 * Hook for auth translations
 */
export const useAuthTranslation = () => {
  return useNamespaceTranslation('auth');
};

/**
 * Hook for dashboard translations
 */
export const useDashboardTranslation = () => {
  return useNamespaceTranslation('dashboard');
};

/**
 * Hook for admin translations
 */
export const useAdminTranslation = () => {
  return useNamespaceTranslation('admin');
};

/**
 * Hook for investment translations
 */
export const useInvestmentTranslation = () => {
  return useNamespaceTranslation('investment');
};

/**
 * Hook for notifications translations
 */
export const useNotificationsTranslation = () => {
  return useNamespaceTranslation('notifications');
};

/**
 * Hook for wallet translations
 */
export const useWalletTranslation = () => {
  return useNamespaceTranslation('wallet');
};

export default useI18n;
