import * as nodemailer from 'nodemailer';
import { logger } from '../utils/logger';
import { IUser } from '../models/userModel';

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  tls: {
    rejectUnauthorized: boolean;
  };
}

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export class EmailService {
  private transporter: nodemailer.Transporter;
  private fromEmail: string;
  private frontendUrl: string;

  constructor() {
    this.fromEmail = process.env.EMAIL_FROM || '<EMAIL>';
    this.frontendUrl = process.env.FRONTEND_URL || 'https://shpnfinance.com';

    // Configure email transporter
    this.transporter = this.createTransporter();
  }

  private createTransporter(): nodemailer.Transporter {
    const config: EmailConfig = {
      host: process.env.EMAIL_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER || '',
        pass: process.env.EMAIL_PASS || ''
      },
      tls: {
        rejectUnauthorized: false, // Tùy chọn: bỏ qua lỗi cert self-signed
      }
    };

    logger.info('Email service configuration:', {
      host: config.host,
      port: config.port,
      secure: config.secure,
      user: config.auth.user ? '***configured***' : 'not configured'
    });

    return nodemailer.createTransport(config);
  }

  /**
   * Send email verification email
   */
  async sendVerificationEmail(user: IUser, token: string): Promise<boolean> {
    try {
      const verificationUrl = `${this.frontendUrl}/verify-email?token=${token}`;

      const template = this.getVerificationEmailTemplate(user, verificationUrl);

      const mailOptions = {
        from: `"SHPN Finance" <${this.fromEmail}>`,
        to: user.email,
        subject: template.subject,
        html: template.html
      };
      const result = await this.transporter.sendMail(mailOptions);

      logger.info('Verification email sent successfully', {
        userId: user._id,
        email: user.email,
        messageId: result.messageId
      });

      return true;
    } catch (error) {
      logger.error('Failed to send verification email', {
        userId: user._id,
        email: user.email,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Send welcome email after verification
   */
  async sendWelcomeEmail(user: IUser): Promise<boolean> {
    try {
      const template = this.getWelcomeEmailTemplate(user);

      const mailOptions = {
        from: this.fromEmail,
        to: user.email,
        subject: template.subject,
        html: template.html,
        text: template.text
      };

      const result = await this.transporter.sendMail(mailOptions);

      logger.info('Welcome email sent successfully', {
        userId: user._id,
        email: user.email,
        messageId: result.messageId
      });

      return true;
    } catch (error) {
      logger.error('Failed to send welcome email', {
        userId: user._id,
        email: user.email,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Get email verification template
   */
  private getVerificationEmailTemplate(user: IUser, verificationUrl: string): EmailTemplate {
    const subject = 'Verify Your Email - SHPN Finance';

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #F0B90B, #FCD535); padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #fff; padding: 30px; border: 1px solid #ddd; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; }
          .btn { display: inline-block; padding: 12px 30px; background: #F0B90B; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold; }
          .btn:hover { background: #FCD535; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1 style="color: #000; margin: 0;">SHPN Finance</h1>
            <p style="color: #000; margin: 10px 0 0 0;">Welcome to the Future of Finance</p>
          </div>
          <div class="content">
            <h2>Hi ${user.firstName},</h2>
            <p>Thank you for registering with SHPN Finance! To complete your registration and start using our platform, please verify your email address.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationUrl}" class="btn">Verify Email Address</a>
            </div>
            
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 5px;">${verificationUrl}</p>
            
            <p><strong>This link will expire in 24 hours.</strong></p>
            
            <p>If you didn't create an account with us, please ignore this email.</p>
            
            <p>Best regards,<br>The SHPN Finance Team</p>
          </div>
          <div class="footer">
            <p style="margin: 0; color: #666; font-size: 14px;">
              © 2024 SHPN Finance. All rights reserved.<br>
              <a href="${this.frontendUrl}" style="color: #F0B90B;">Visit our website</a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Hi ${user.firstName},

      Thank you for registering with SHPN Finance! To complete your registration, please verify your email address by clicking the link below:

      ${verificationUrl}

      This link will expire in 24 hours.

      If you didn't create an account with us, please ignore this email.

      Best regards,
      The SHPN Finance Team
    `;

    return { subject, html, text };
  }

  /**
   * Get welcome email template
   */
  private getWelcomeEmailTemplate(user: IUser): EmailTemplate {
    const subject = 'Welcome to SHPN Finance - Your Account is Ready!';

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to SHPN Finance</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #F0B90B, #FCD535); padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #fff; padding: 30px; border: 1px solid #ddd; }
          .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; }
          .btn { display: inline-block; padding: 12px 30px; background: #F0B90B; color: #000; text-decoration: none; border-radius: 5px; font-weight: bold; margin: 10px; }
          .feature { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1 style="color: #000; margin: 0;">🎉 Welcome to SHPN Finance!</h1>
            <p style="color: #000; margin: 10px 0 0 0;">Your account is now verified and ready to use</p>
          </div>
          <div class="content">
            <h2>Hi ${user.firstName},</h2>
            <p>Congratulations! Your email has been successfully verified and your SHPN Finance account is now active.</p>
            
            <h3>What's Next?</h3>
            <div class="feature">
              <strong>🔐 Complete KYC Verification</strong><br>
              Verify your identity to unlock higher limits and full platform access.
            </div>
            <div class="feature">
              <strong>💰 Make Your First Deposit</strong><br>
              Start earning with our competitive interest rates and investment packages.
            </div>
            <div class="feature">
              <strong>👥 Invite Friends</strong><br>
              Earn referral commissions by inviting friends to join SHPN Finance.
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${this.frontendUrl}/profile" class="btn">Go to Dashboard</a>
              <a href="${this.frontendUrl}/about" class="btn">Learn More</a>
            </div>
            
            <p>If you have any questions, our support team is here to help!</p>
            
            <p>Best regards,<br>The SHPN Finance Team</p>
          </div>
          <div class="footer">
            <p style="margin: 0; color: #666; font-size: 14px;">
              © 2024 SHPN Finance. All rights reserved.<br>
              <a href="${this.frontendUrl}" style="color: #F0B90B;">Visit our website</a> | 
              <a href="${this.frontendUrl}/contact" style="color: #F0B90B;">Contact Support</a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      Hi ${user.firstName},

      Congratulations! Your email has been successfully verified and your SHPN Finance account is now active.

      What's Next?
      - Complete KYC Verification to unlock higher limits
      - Make your first deposit and start earning
      - Invite friends and earn referral commissions

      Visit your dashboard: ${this.frontendUrl}/profile

      Best regards,
      The SHPN Finance Team
    `;

    return { subject, html, text };
  }

  /**
   * Test email configuration
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('Email service connection verified successfully');
      return true;
    } catch (error) {
      logger.error('Email service connection failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();
