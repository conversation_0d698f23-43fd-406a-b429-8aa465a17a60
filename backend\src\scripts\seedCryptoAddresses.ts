import mongoose from 'mongoose';
import dotenv from 'dotenv';
import SystemConfig, { AddressWithNetwork } from '../models/systemConfigModel';
import { logger } from '../utils/logger';
import path from 'path';
import { CRYPTO_NETWORKS } from '../utils/cryptoNetworks';

// Load environment variables from the correct path
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// Connect to MongoDB
const connectDB = async () => {
  try {
    // Use the MONGO_URI from environment variables or fallback to a default
    const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/shipping-Finance';

    console.log('Connecting to MongoDB with URI:', mongoURI);

    // Connect with authentication if needed
    const options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      // If you have authentication credentials in your connection string, they will be used automatically
    };

    const conn = await mongoose.connect(mongoURI);
    logger.info(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error: any) {
    logger.error(`Error connecting to MongoDB: ${error.message}`);
    process.exit(1);
  }
};

// Seed crypto addresses
const seedCryptoAddresses = async () => {
  try {
    // Find or create system config
    const config = await SystemConfig.findOneOrCreate();

    // Define crypto addresses to seed with network information
    const cryptoAddresses = [
      // Bitcoin (BTC) addresses with networks
      {
        currency: 'BTC',
        addresses: [
          { address: '**********************************', network: 'bitcoin' },
          { address: '', network: 'lightning' },
          { address: '**********************************', network: 'brc20' },
          { address: '******************************************', network: 'bitcoin' },
          { address: '******************************************', network: 'brc20' }
        ],
        currentIndex: 0,
        enabled: true
      },

      // Ethereum (ETH) addresses with networks
      {
        currency: 'ETH',
        addresses: [
          { address: '******************************************', network: 'erc20' },
          { address: '******************************************', network: 'erc20' },
          { address: '******************************************', network: 'eth2' },
          { address: '******************************************', network: 'eth2' },
          { address: '******************************************', network: 'erc20' }
        ],
        currentIndex: 0,
        enabled: true
      },

      // Tether (USDT) addresses with networks
      {
        currency: 'USDT',
        addresses: [
          { address: 'TKQvCcXdgmAQwu4U5RgQJdFfvGbmzXxC9X', network: 'trc20' },
          { address: 'TUrMmF9Gd4rzrXsQ34ui3Wou94E7HFuJQh', network: 'trc20' },
          { address: '******************************************', network: 'erc20' },
          { address: '******************************************', network: 'bep20' },
          { address: 'TYASr5UV6HEcXatwdFQfmLVUqQQQMUxHLS', network: 'trc20' }
        ],
        currentIndex: 0,
        enabled: true
      },

      // Dogecoin (DOGE) addresses with networks
      {
        currency: 'DOGE',
        addresses: [
          { address: 'DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L', network: 'dogecoin' },
          { address: 'DNgY5iCXLhNqMhVJ9nXnWZG1TJjGxSXz5Q', network: 'dogecoin' },
          { address: 'DDTtqnuZ5kfRT5qh2c7sNtqrJmV3iXYdGG', network: 'dogecoin' },
          { address: 'DFQc4NVAK7GvFQsXQQT5sCpray8VeJzwPr', network: 'drc20' },
          { address: 'D5oKvYNZdD8FLMaJ9N9pK92CKDJApVKJZx', network: 'drc20' }
        ],
        currentIndex: 0,
        enabled: true
      },

      // TRON (TRX) addresses with networks
      {
        currency: 'TRX',
        addresses: [
          { address: 'TLPuNinqS5qHuVMHWadqA7RZ2LcxdjCWzb', network: 'tron' },
          { address: 'TKQvCcXdgmAQwu4U5RgQJdFfvGbmzXxC9X', network: 'tron' },
          { address: 'TUrMmF9Gd4rzrXsQ34ui3Wou94E7HFuJQh', network: 'tron' },
          { address: 'TYASr5UV6HEcXatwdFQfmLVUqQQQMUxHLS', network: 'trc20' },
          { address: 'TLsV52sRDL79HXGGm9yzwKibb6BeruhUzy', network: 'trc20' }
        ],
        currentIndex: 0,
        enabled: true
      },

      // Binance Coin (BNB) addresses with networks
      {
        currency: 'BNB',
        addresses: [
          { address: 'bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m', network: 'bep2' },
          { address: 'bnb1grpf0955h0ykzq3ar5nmum7y6gdfl6lxfn46h2', network: 'bep2' },
          { address: 'bnb16k0gajcczwgypwf3d7x5qvfq9f2k0tjf8q37jk', network: 'bep2' },
          { address: '0x3f5CE5FBFe3E9af3971dD833D26bA9b5C936f0bE', network: 'bep20' },
          { address: '0xB8c77482e45F1F44dE1745F52C74426C631bDD52', network: 'bep20' }
        ],
        currentIndex: 0,
        enabled: true
      },

      // Solana (SOL) addresses with networks
      {
        currency: 'SOL',
        addresses: [
          { address: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU', network: 'solana' },
          { address: 'DjVE6JNiYqPL2QXyCUUh8rNjHrbz9hXHNYt99MQ59qw1', network: 'solana' },
          { address: 'So11111111111111111111111111111111111111112', network: 'solana' },
          { address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', network: 'spl' },
          { address: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', network: 'spl' }
        ],
        currentIndex: 0,
        enabled: true
      }
    ];

    // Update crypto addresses in system config
    config.cryptoAddresses = cryptoAddresses;

    // Make sure all currencies are in the supported currencies list
    const currencies = cryptoAddresses.map(ca => ca.currency);
    config.supportedCurrencies = [...new Set([...config.supportedCurrencies, ...currencies])];

    // Save the updated config
    await config.save();

    logger.info('Crypto addresses seeded successfully');
  } catch (error: any) {
    logger.error(`Error seeding crypto addresses: ${error.message}`);
  }
};

// Main function
const main = async () => {
  await connectDB();
  await seedCryptoAddresses();
  process.exit(0);
};

// Run the script
main();
