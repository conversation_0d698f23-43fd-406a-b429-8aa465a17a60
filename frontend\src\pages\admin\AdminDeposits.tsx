import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  HStack,
  VStack,
  useToast,
  Text,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  FormHelperText,
  Textarea,
  Image,
  useDisclosure,
  Spinner,
  Center,
  Tooltip,
  InputRightElement,
  Stat,
  StatLabel,
  StatNumber,
  StatGroup,
  StatHelpText,
  StatArrow,
  Grid,
  GridItem,
  Card,
  CardBody,
  Divider,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  useColorModeValue
} from '@chakra-ui/react';
import { SearchIcon, CopyIcon, CheckIcon, WarningIcon, DownloadIcon, RepeatIcon, ChevronDownIcon, EditIcon, CloseIcon } from '@chakra-ui/icons';
import { FaCheck, FaTimes, FaEye, FaReceipt, FaExternalLinkAlt, FaFileExport, FaChartBar, FaFilter } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { adminApiService } from '../../services/adminApi';
import { formatAmount } from '../../utils/formatters';
import { SocketService } from '../../utils/socketService';
import useSocket from '../../hooks/useSocket';
import ReceiptImageViewer from '../../components/common/ReceiptImageViewer';

// Simple logger for frontend
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args)
};

// Interface for deposit data
interface Deposit {
  id: string;
  user: string;
  userId: string;
  email: string;
  phoneNumber?: string;
  country?: string;
  city?: string;
  wallet: string;
  amount: number;
  currency: string;
  date: string;
  status: 'pending' | 'processing' | 'approved' | 'rejected';
  receiptUrl?: string;
  txHash?: string;
  network?: string;
  adminNotes?: string;
  source?: 'investment' | 'transaction';

  // Amount verification fields
  originalAmount?: number;
  adminVerifiedAmount?: number;
  amountModifiedBy?: string;
  amountModifiedAt?: string;
  amountCorrectionReason?: string;

  _refreshKey?: number; // Added for forcing re-render
}

// Interface for WebSocket payload
interface WebSocketPayload {
  id?: string;
  user?: string;
  userId?: string;
  email?: string;
  phoneNumber?: string;
  country?: string;
  city?: string;
  wallet?: string;
  amount?: number;
  currency?: string;
  date?: string;
  updatedAt?: string;
  status?: 'pending' | 'processing' | 'approved' | 'rejected' | string;
  receiptUrl?: string;
  txHash?: string;
  network?: string;
  adminNotes?: string;
  source?: 'investment' | 'transaction' | string;
  [key: string]: any; // For any other properties
}

// Interface for API error
interface ApiError {
  userMessage?: string;
  message?: string;
  response?: {
    data?: {
      message?: string;
      error?: string;
    };
    status?: number;
    headers?: any;
  };
  request?: any;
}



const AdminDeposits = () => {
  const toast = useToast();
  const { t } = useTranslation();
  const {
    isConnected,
    subscribe,
    subscribeToDepositUpdates
  } = useSocket();

  const [deposits, setDeposits] = useState<Deposit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterCurrency, setFilterCurrency] = useState('all');
  const [selectedDeposit, setSelectedDeposit] = useState<Deposit | null>(null);
  const [adminNotes, setAdminNotes] = useState('');
  const [txHash, setTxHash] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Amount editing state (unlimited edits allowed)
  const [isEditingAmount, setIsEditingAmount] = useState(false);
  const [editedAmount, setEditedAmount] = useState<number>(0);
  const [amountCorrectionReason, setAmountCorrectionReason] = useState('');
  const [isAmountSubmitting, setIsAmountSubmitting] = useState(false);
  const [amountEditHistory, setAmountEditHistory] = useState<Array<{
    amount: number;
    reason: string;
    timestamp: string;
    adminId: string;
  }>>([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalDeposits, setTotalDeposits] = useState(0);
  const [isCopied, setIsCopied] = useState(false);
  const [statsData, setStatsData] = useState({
    totalAmount: 0,
    pendingAmount: 0,
    approvedAmount: 0,
    rejectedAmount: 0,
    pendingCount: 0,
    approvedCount: 0,
    rejectedCount: 0,
    processingCount: 0
  });
  const [showStats, setShowStats] = useState(false);

  // Use refs to keep track of current deposits without triggering re-renders
  const depositsRef = useRef<Deposit[]>([]);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isReceiptOpen, onOpen: onReceiptOpen, onClose: onReceiptClose } = useDisclosure();
  const { isOpen: isStatsOpen, onOpen: onStatsOpen, onClose: onStatsClose } = useDisclosure();

  // Colors
  const bgColor = useColorModeValue("#FFFFFF", "#1E2329");
  const cardBgColor = useColorModeValue("#F7FAFC", "#0B0E11");
  const borderColor = useColorModeValue("#E2E8F0", "#2B3139");
  const textColor = useColorModeValue("#1A202C", "#EAECEF");
  const secondaryTextColor = useColorModeValue("#718096", "#848E9C");
  const primaryColor = "#F0B90B";

  // Initialize Socket.IO connection
  const socketService = SocketService.getInstance();

  // Calculate statistics from deposits data
  const calculateStats = useCallback(() => {
    const stats = {
      totalAmount: 0,
      pendingAmount: 0,
      approvedAmount: 0,
      rejectedAmount: 0,
      pendingCount: 0,
      approvedCount: 0,
      rejectedCount: 0,
      processingCount: 0
    };

    deposits.forEach(deposit => {
      stats.totalAmount += deposit.amount;

      switch (deposit.status) {
        case 'pending':
          stats.pendingAmount += deposit.amount;
          stats.pendingCount++;
          break;
        case 'approved':
          stats.approvedAmount += deposit.amount;
          stats.approvedCount++;
          break;
        case 'rejected':
          stats.rejectedAmount += deposit.amount;
          stats.rejectedCount++;
          break;
        case 'processing':
          stats.processingCount++;
          break;
      }
    });

    setStatsData(stats);
  }, [deposits]);

  // Define fetchDeposits function
  const fetchDeposits = useCallback(async () => {
    setIsLoading(true);
    try {
      // Fetch from API
      const params: Record<string, string | number> = {
        page,
        limit: 10
      };

      if (filterStatus !== 'all') params.status = filterStatus;
      if (filterCurrency !== 'all') params.currency = filterCurrency;
      if (searchQuery) params.search = searchQuery;

      const response = await adminApiService.getDeposits(undefined, params);

      if (response.data && response.data.success) {
        setDeposits(response.data.deposits);
        setTotalDeposits(response.data.pagination.total);
        setTotalPages(response.data.pagination.pages);
      }
    } catch (error) {
      console.error('Error fetching deposits:', error);
      toast({
        title: t('admin.deposits.fetchError', 'Error fetching deposits'),
        status: 'error',
        duration: 3000,
        isClosable: true,
        id: 'fetch-deposits-error' // Add ID to prevent duplicate toasts
      });
    } finally {
      setIsLoading(false);
    }
  }, [page, filterStatus, filterCurrency, searchQuery, t, toast]);

  // Load deposits from API
  useEffect(() => {
    fetchDeposits();
  }, [fetchDeposits]);

  // Set up Socket.IO event listeners for real-time updates
  useEffect(() => {
    console.log('Setting up Socket.IO event handlers for deposits page');

    // Update the deposits ref whenever the deposits state changes
    depositsRef.current = deposits;

    // Function to handle new deposit events
    const handleNewDeposit = (payload: WebSocketPayload) => {
      console.log('New deposit received via Socket.IO:', payload);

      // Check if payload is valid
      if (!payload || !payload.id) {
        console.error('Invalid deposit payload received:', payload);
        return;
      }

      // Convert _id to id if necessary
      const depositId = payload._id || payload.id;
      console.log(`Looking for deposit with ID: ${depositId}`);

      // Check if the deposit already exists in our list - use the ref for current state
      const currentDeposits = depositsRef.current;
      const depositExists = currentDeposits.some(d => d.id === depositId);
      console.log(`Deposit exists in list: ${depositExists}`);

      if (!depositExists) {
        try {
          // Ensure status is a valid enum value
          let status: 'pending' | 'processing' | 'approved' | 'rejected' = 'pending';
          if (payload.status === 'pending' || payload.status === 'processing' ||
              payload.status === 'approved' || payload.status === 'rejected') {
            status = payload.status;
          }

          // Ensure source is a valid enum value
          let source: 'investment' | 'transaction' = 'transaction';
          if (payload.source === 'investment' || payload.source === 'transaction') {
            source = payload.source;
          }

          // Add the new deposit to the beginning of the list
          const newDeposit: Deposit = {
            id: depositId,
            user: payload.user || 'Unknown User',
            userId: payload.userId || '',
            email: payload.email || '<EMAIL>',
            phoneNumber: payload.phoneNumber || '',
            country: payload.country || '',
            city: payload.city || '',
            wallet: payload.wallet || 'N/A',
            amount: payload.amount || 0,
            currency: payload.currency || 'BTC',
            date: new Date(payload.date || new Date()).toISOString(),
            status: status,
            receiptUrl: payload.receiptUrl || '',
            txHash: payload.txHash || '',
            network: payload.network || '',
            adminNotes: payload.adminNotes || '',
            source: source,
            _refreshKey: Date.now()
          };

          console.log('Adding new deposit to list:', newDeposit);

          // Use functional update to ensure we're working with the latest state
          setDeposits(prevDeposits => {
            // Double-check that the deposit doesn't already exist
            if (prevDeposits.some(d => d.id === depositId)) {
              return prevDeposits;
            }
            return [newDeposit, ...prevDeposits];
          });

          // Play notification sound
          try {
            const audio = new Audio('/sounds/notification.mp3');
            audio.play().catch(e => console.log('Error playing sound:', e));
          } catch (e) {
            console.log('Error with notification sound:', e);
          }

          // Show notification
          toast({
            title: t('admin.deposits.newDeposit', 'New Deposit Received'),
            description: `${newDeposit.user} deposited ${formatAmount(newDeposit.amount)} ${newDeposit.currency}`,
            status: 'info',
            duration: 5000,
            isClosable: true,
            position: 'top-right',
            id: `new-deposit-${depositId}` // Add unique ID to prevent duplicate toasts
          });

          // Update statistics
          calculateStats();
        } catch (error) {
          console.error('Error processing new deposit:', error);
        }
      } else {
        console.log('Deposit already exists in list, skipping:', depositId);
      }
    };

    // Function to handle deposit update events
    const handleDepositUpdate = (payload: WebSocketPayload) => {
      console.log('Deposit update received via Socket.IO:', payload);

      // Validate payload
      if (!payload || !payload.id) {
        console.error('Invalid deposit update payload received:', payload);
        return;
      }

      // Play notification sound
      try {
        const audio = new Audio('/sounds/notification.mp3');
        audio.play().catch(e => console.log('Error playing sound:', e));
      } catch (e) {
        console.log('Error with notification sound:', e);
      }

      // Convert _id to id if necessary
      const depositId = payload._id || payload.id;
      console.log(`Looking for deposit with ID: ${depositId}`);

      // Find the deposit in our list - use the ref for current state
      const currentDeposits = depositsRef.current;
      const depositIndex = currentDeposits.findIndex(d => d.id === depositId);
      console.log(`Deposit index in list: ${depositIndex}`);

      if (depositIndex !== -1) {
        try {
          // Ensure status is a valid enum value
          let status: 'pending' | 'processing' | 'approved' | 'rejected';
          if (payload.status === 'pending' || payload.status === 'processing' ||
              payload.status === 'approved' || payload.status === 'rejected') {
            status = payload.status;
          } else {
            status = currentDeposits[depositIndex].status;
          }

          // Ensure source is a valid enum value
          let source: 'investment' | 'transaction' | undefined;
          if (payload.source === 'investment' || payload.source === 'transaction') {
            source = payload.source;
          } else {
            source = currentDeposits[depositIndex].source;
          }

          // Update the deposit in the list
          const updatedDeposit: Deposit = {
            ...currentDeposits[depositIndex], // Keep existing data
            status: status,
            txHash: payload.txHash || currentDeposits[depositIndex].txHash,
            adminNotes: payload.adminNotes || currentDeposits[depositIndex].adminNotes,
            wallet: payload.wallet || currentDeposits[depositIndex].wallet,
            network: payload.network || currentDeposits[depositIndex].network,
            receiptUrl: payload.receiptUrl || currentDeposits[depositIndex].receiptUrl,
            // Make sure we keep all required fields
            user: payload.user || currentDeposits[depositIndex].user,
            userId: payload.userId || currentDeposits[depositIndex].userId,
            email: payload.email || currentDeposits[depositIndex].email,
            amount: payload.amount || currentDeposits[depositIndex].amount,
            currency: payload.currency || currentDeposits[depositIndex].currency,
            date: currentDeposits[depositIndex].date,
            // Optional fields
            phoneNumber: payload.phoneNumber || currentDeposits[depositIndex].phoneNumber,
            country: payload.country || currentDeposits[depositIndex].country,
            city: payload.city || currentDeposits[depositIndex].city,
            source: source,
            // Add a refresh key to force re-render
            _refreshKey: Date.now()
          };

          console.log('Updating deposit in list:', updatedDeposit);

          // Use functional update to ensure we're working with the latest state
          setDeposits(prevDeposits => {
            // Find the deposit again in the current state
            const currentIndex = prevDeposits.findIndex(d => d.id === depositId);
            if (currentIndex === -1) {
              return prevDeposits;
            }

            // Create a new array with the updated deposit
            const newDeposits = [...prevDeposits];
            newDeposits[currentIndex] = updatedDeposit;
            return newDeposits;
          });

          // Show notification
          toast({
            title: t('admin.deposits.statusUpdated', 'Deposit Status Updated'),
            description: `${updatedDeposit.user}'s deposit is now ${updatedDeposit.status}`,
            status: updatedDeposit.status === 'approved' ? 'success' :
                   updatedDeposit.status === 'rejected' ? 'error' : 'info',
            duration: 5000,
            isClosable: true,
            position: 'top-right',
            id: `deposit-update-${depositId}-${Date.now()}` // Add unique ID with timestamp to prevent duplicate toasts
          });

          // Update statistics
          calculateStats();
        } catch (error) {
          console.error('Error updating deposit:', error);
        }
      } else {
        // If the deposit is not in our list, it might be a new one or from another page
        console.log('Deposit not found in current list, treating as new deposit');

        // Create a new deposit object from the payload
        try {
          // Ensure status is a valid enum value
          let status: 'pending' | 'processing' | 'approved' | 'rejected' = 'pending';
          if (payload.status === 'pending' || payload.status === 'processing' ||
              payload.status === 'approved' || payload.status === 'rejected') {
            status = payload.status;
          }

          // Ensure source is a valid enum value
          let source: 'investment' | 'transaction' = 'transaction';
          if (payload.source === 'investment' || payload.source === 'transaction') {
            source = payload.source;
          }

          const newDeposit: Deposit = {
            id: depositId,
            user: payload.user || 'Unknown User',
            userId: payload.userId || '',
            email: payload.email || '<EMAIL>',
            phoneNumber: payload.phoneNumber || '',
            country: payload.country || '',
            city: payload.city || '',
            wallet: payload.wallet || 'N/A',
            amount: payload.amount || 0,
            currency: payload.currency || 'BTC',
            date: new Date(payload.date || payload.updatedAt || new Date()).toISOString(),
            status: status,
            receiptUrl: payload.receiptUrl || '',
            txHash: payload.txHash || '',
            network: payload.network || '',
            adminNotes: payload.adminNotes || '',
            source: source,
            _refreshKey: Date.now()
          };

          console.log('Adding new deposit from update event:', newDeposit);

          // Add to the beginning of the list using functional update
          setDeposits(prevDeposits => {
            // Double-check that the deposit doesn't already exist
            if (prevDeposits.some(d => d.id === depositId)) {
              return prevDeposits;
            }
            return [newDeposit, ...prevDeposits];
          });

          // Show notification
          toast({
            title: t('admin.deposits.depositUpdated', 'Deposit Updated'),
            description: `${newDeposit.user}'s deposit status: ${newDeposit.status}`,
            status: 'info',
            duration: 5000,
            isClosable: true,
            position: 'top-right'
          });

          // Update statistics
          calculateStats();
        } catch (error) {
          console.error('Error processing new deposit from update event:', error);
        }
      }
    };

    // Subscribe to Socket.IO events using the useSocket hook
    console.log('Subscribing to Socket.IO events for deposits page');

    // Subscribe to deposit-specific events
    const unsubscribeNewDeposit = subscribe('new_deposit', handleNewDeposit);
    const unsubscribeDepositUpdate = subscribe('deposit_updated', handleDepositUpdate);
    const unsubscribeDepositStatusUpdate = subscribe('deposit_status_updated', handleDepositUpdate);

    // Subscribe to transaction events that might include deposits
    const unsubscribeTransactionUpdate = subscribe('transaction_update', (payload) => {
      // Only process transaction updates that are deposits
      if (payload && payload.type === 'deposit') {
        console.log('Received deposit via transaction_update:', payload);
        handleDepositUpdate(payload);
      }
    });

    // Subscribe to deposit subscription success event
    const unsubscribeDepositSubscriptionSuccess = subscribe('deposit_subscription_success', (data) => {
      console.log('Successfully subscribed to deposit updates:', data);
      toast({
        title: t('admin.deposits.subscriptionSuccess', 'Real-time Updates Active'),
        description: t('admin.deposits.subscriptionSuccessDesc', 'You will now receive real-time deposit updates'),
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'bottom-right',
        id: 'deposit-subscription-success'
      });
    });

    // Subscribe to cross-section amount updates
    const unsubscribeDepositAmountUpdate = subscribe('admin_deposit_amount_updated', (data) => {
      console.log('Real-time deposit amount update received:', data);

      // Update deposits list if the updated deposit is in current view
      setDeposits(prev => prev.map(deposit =>
        deposit.id === data.depositId
          ? {
              ...deposit,
              amount: data.newAmount,
              adminVerifiedAmount: data.newAmount,
              amountModifiedAt: data.modifiedAt,
              amountModifiedBy: data.modifiedBy,
              amountCorrectionReason: data.amountCorrectionReason
            }
          : deposit
      ));

      // Update selected deposit if it's the one being modified
      if (selectedDeposit && selectedDeposit.id === data.depositId) {
        setSelectedDeposit(prev => prev ? {
          ...prev,
          amount: data.newAmount,
          adminVerifiedAmount: data.newAmount,
          amountModifiedAt: data.modifiedAt,
          amountModifiedBy: data.modifiedBy,
          amountCorrectionReason: data.amountCorrectionReason
        } : null);
      }
    });

    const unsubscribeWithdrawalAmountUpdate = subscribe('admin_withdrawal_amount_updated', (data) => {
      console.log('Cross-section withdrawal amount update received:', data);

      // Show notification about related withdrawal amount change
      if (data.userId && selectedDeposit && selectedDeposit.userId === data.userId) {
        toast({
          title: t('admin.deposits.crossSectionUpdate', 'Related Withdrawal Updated'),
          description: t('admin.deposits.crossSectionDesc', `Withdrawal amount changed to ${data.newAmount} ${data.cryptocurrency}`),
          status: 'info',
          duration: 8000,
          isClosable: true,
          position: 'top-right',
        });
      }

      // Update any related data if needed
      if (data.userId) {
        // Refresh deposits list to show any related changes
        loadDeposits();
      }
    });

    // Subscribe to real-time deposit updates with admin filter
    subscribeToDepositUpdates({ admin: true });

    // Clean up event listeners when component unmounts
    return () => {
      console.log('Cleaning up Socket.IO event listeners');
      unsubscribeNewDeposit();
      unsubscribeDepositUpdate();
      unsubscribeDepositStatusUpdate();
      unsubscribeTransactionUpdate();
      unsubscribeDepositSubscriptionSuccess();
      unsubscribeDepositAmountUpdate();
      unsubscribeWithdrawalAmountUpdate();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [toast, t, subscribe, subscribeToDepositUpdates, calculateStats]); // Removed deposits from dependencies to avoid infinite loop



  // Filter deposits based on search query, status, and currency
  const filteredDeposits = deposits.filter(deposit => {
    const matchesSearch =
      deposit.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
      deposit.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      deposit.wallet.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = filterStatus === 'all' || deposit.status === filterStatus;
    const matchesCurrency = filterCurrency === 'all' || deposit.currency === filterCurrency;

    return matchesSearch && matchesStatus && matchesCurrency;
  });

  const handleViewDeposit = (deposit: Deposit) => {
    setSelectedDeposit(deposit);
    setAdminNotes('');
    setTxHash(deposit.txHash || '');
    setWalletAddress(deposit.wallet || '');

    // Initialize amount editing state
    setEditedAmount(deposit.adminVerifiedAmount || deposit.amount);
    setAmountCorrectionReason(deposit.amountCorrectionReason || '');
    setIsEditingAmount(false);

    onOpen();
  };

  const handleViewReceipt = (deposit: Deposit) => {
    // Ensure the URL is properly formatted
    let receiptUrl = deposit.receiptUrl || '';

    // If the URL doesn't start with http or /, add a leading /
    if (!receiptUrl.startsWith('http') && !receiptUrl.startsWith('/')) {
      receiptUrl = '/' + receiptUrl;
    }

    // Update deposit with normalized URL
    const updatedDeposit = {
      ...deposit,
      receiptUrl: receiptUrl
    };

    // Set selected deposit and open modal
    setSelectedDeposit(updatedDeposit);
    onReceiptOpen();

    // Log receipt URL for debugging
    if (receiptUrl) {
      const fullUrl = getImageUrl(receiptUrl);
      console.log('Original Receipt URL:', deposit.receiptUrl);
      console.log('Normalized Receipt URL:', receiptUrl);
      console.log('Full Receipt URL:', fullUrl);
    }
  };

  // Handle amount editing - unlimited editing for all statuses
  const handleEditAmount = () => {
    if (!selectedDeposit) return;

    // Admin can edit amounts for any status - no restrictions
    logger.info(`Admin editing amount for deposit ${selectedDeposit.id} with status: ${selectedDeposit.status}`);

    setIsEditingAmount(true);
  };

  const handleCancelAmountEdit = () => {
    setIsEditingAmount(false);
    setEditedAmount(selectedDeposit?.adminVerifiedAmount || selectedDeposit?.amount || 0);
    setAmountCorrectionReason(selectedDeposit?.amountCorrectionReason || '');
  };

  const handleSaveAmount = async () => {
    if (!selectedDeposit || editedAmount <= 0) {
      toast({
        title: t('admin.deposits.error', 'Error'),
        description: t('admin.deposits.invalidAmount', 'Please enter a valid amount'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsAmountSubmitting(true);
    try {
      const response = await adminApiService.updateDepositAmount(selectedDeposit.id, {
        adminVerifiedAmount: editedAmount,
        amountCorrectionReason: amountCorrectionReason || undefined
      });

      toast({
        title: t('admin.deposits.amountUpdateSuccess', 'Amount Updated Successfully'),
        description: t('admin.deposits.amountUpdateDesc', `Amount changed from $${selectedDeposit.originalAmount || selectedDeposit.amount} to $${editedAmount}. Changes synchronized across all admin sections.`),
        status: 'success',
        duration: 8000,
        isClosable: true,
        position: 'top-right',
      });

      // Update local state
      setSelectedDeposit(prev => prev ? {
        ...prev,
        amount: editedAmount,
        adminVerifiedAmount: editedAmount,
        originalAmount: prev.originalAmount || prev.amount,
        amountCorrectionReason: amountCorrectionReason,
        amountModifiedAt: new Date().toISOString()
      } : null);

      // Add to edit history
      setAmountEditHistory(prev => [...prev, {
        amount: editedAmount,
        reason: amountCorrectionReason || 'No reason provided',
        timestamp: new Date().toISOString(),
        adminId: 'current_admin' // This would come from auth context in real implementation
      }]);

      setIsEditingAmount(false);

      // Refresh deposits list
      fetchDeposits();
    } catch (error: any) {
      console.error('Error updating deposit amount:', error);

      let errorMessage = t('admin.deposits.amountUpdateError', 'Failed to update deposit amount');
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      toast({
        title: t('admin.deposits.error', 'Error'),
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsAmountSubmitting(false);
    }
  };

  // Handle approve deposit
  const handleApprove = async () => {
    if (!selectedDeposit) return;

    setIsSubmitting(true);
    try {
      // Call API
      const response = await adminApiService.updateDepositStatus(selectedDeposit.id, {
        status: 'approved',
        adminNotes: adminNotes || undefined,
        txHash: txHash || undefined,
        walletAddress: walletAddress || undefined
      });

      toast({
        title: t('admin.deposits.approveSuccess', 'Deposit approved successfully'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Update the deposit in the local state immediately
      // This ensures the UI updates even if the WebSocket update is delayed
      if (response.data && response.data.deposit) {
        setDeposits(prevDeposits => {
          const depositIndex = prevDeposits.findIndex(d => d.id === selectedDeposit.id);
          if (depositIndex === -1) return prevDeposits;

          const newDeposits = [...prevDeposits];
          newDeposits[depositIndex] = {
            ...prevDeposits[depositIndex],
            status: 'approved',
            adminNotes: adminNotes || prevDeposits[depositIndex].adminNotes,
            txHash: txHash || prevDeposits[depositIndex].txHash,
            wallet: walletAddress || prevDeposits[depositIndex].wallet,
            _refreshKey: Date.now()
          };

          return newDeposits;
        });
      } else {
        // If we don't get the updated deposit in the response, refresh the list
        fetchDeposits();
      }

      onClose();
    } catch (error: unknown) {
      console.error('Error approving deposit:', error);

      // Display a more specific error message if available
      const errorObj = error as ApiError;
      const errorMessage = errorObj.userMessage ||
                          errorObj.response?.data?.message ||
                          'Error approving deposit. Please try again.';

      toast({
        title: t('admin.deposits.approveError', 'Error approving deposit'),
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });

      // Log detailed error information for debugging
      if (errorObj.response) {
        console.error('Response error data:', errorObj.response.data);
        console.error('Response error status:', errorObj.response.status);
        console.error('Response error headers:', errorObj.response.headers);
      } else if (errorObj.request) {
        console.error('Request error:', errorObj.request);
      } else {
        console.error('Error message:', errorObj.message);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle reject deposit
  const handleReject = async () => {
    if (!selectedDeposit) return;

    setIsSubmitting(true);
    try {
      // Call API
      const response = await adminApiService.updateDepositStatus(selectedDeposit.id, {
        status: 'rejected',
        adminNotes: adminNotes || undefined,
        walletAddress: walletAddress || undefined
      });

      toast({
        title: t('admin.deposits.rejectSuccess', 'Deposit rejected successfully'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Update the deposit in the local state immediately
      // This ensures the UI updates even if the WebSocket update is delayed
      if (response.data && response.data.deposit) {
        setDeposits(prevDeposits => {
          const depositIndex = prevDeposits.findIndex(d => d.id === selectedDeposit.id);
          if (depositIndex === -1) return prevDeposits;

          const newDeposits = [...prevDeposits];
          newDeposits[depositIndex] = {
            ...prevDeposits[depositIndex],
            status: 'rejected',
            adminNotes: adminNotes || prevDeposits[depositIndex].adminNotes,
            wallet: walletAddress || prevDeposits[depositIndex].wallet,
            _refreshKey: Date.now()
          };

          return newDeposits;
        });
      } else {
        // If we don't get the updated deposit in the response, refresh the list
        fetchDeposits();
      }

      onClose();
    } catch (error: unknown) {
      console.error('Error rejecting deposit:', error);

      // Display a more specific error message if available
      const errorObj = error as ApiError;
      const errorMessage = errorObj.userMessage ||
                          errorObj.response?.data?.message ||
                          'Error rejecting deposit. Please try again.';

      toast({
        title: t('admin.deposits.rejectError', 'Error rejecting deposit'),
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });

      // Log detailed error information for debugging
      if (errorObj.response) {
        console.error('Response error data:', errorObj.response.data);
        console.error('Response error status:', errorObj.response.status);
        console.error('Response error headers:', errorObj.response.headers);
      } else if (errorObj.request) {
        console.error('Request error:', errorObj.request);
      } else {
        console.error('Error message:', errorObj.message);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Copy wallet address to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };

  // Helper function to get the correct image URL
  const getImageUrl = (url?: string): string => {
    if (!url) return '';

    // If URL already starts with http, return as is
    if (url.startsWith('http')) {
      return url;
    }

    // Ensure URL starts with /
    if (!url.startsWith('/')) {
      url = '/' + url;
    }

    // Add API URL prefix
    const baseUrl = import.meta.env.VITE_API_URL || '';

    // Create full URL
    const fullUrl = `${baseUrl}${url}`;

    console.log('Generated image URL:', fullUrl);
    return fullUrl;
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'green';
      case 'pending': return 'yellow';
      case 'processing': return 'blue';
      case 'rejected': return 'red';
      default: return 'gray';
    }
  };

  // Handle search
  const handleSearch = () => {
    setPage(1);
    fetchDeposits();
  };



  // Export deposits to CSV
  const exportToCSV = () => {
    // Create CSV content
    const headers = ['ID', 'User', 'Email', 'Wallet', 'Amount', 'Currency', 'Date', 'Status', 'Network', 'Transaction Hash'];

    const csvContent = [
      headers.join(','),
      ...filteredDeposits.map(deposit => [
        deposit.id,
        `"${deposit.user}"`,
        `"${deposit.email}"`,
        `"${deposit.wallet}"`,
        deposit.amount,
        deposit.currency,
        new Date(deposit.date).toLocaleString(),
        deposit.status,
        deposit.network || '',
        deposit.txHash || ''
      ].join(','))
    ].join('\n');

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    // Create a link and click it
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `deposits_export_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Refresh data
  const handleRefresh = () => {
    fetchDeposits();
    toast({
      title: t('admin.deposits.refreshing', 'Refreshing data...'),
      status: 'info',
      duration: 2000,
      isClosable: true,
      id: 'refresh-deposits-toast' // Add ID to prevent duplicate toasts
    });
  };

  // Test Socket.IO connection
  const testSocketConnection = () => {
    console.log('Testing Socket.IO connection...');

    // Check if Socket.IO is connected
    if (!isConnected) {
      console.log('Socket.IO not connected');

      toast({
        title: 'Socket.IO Not Connected',
        description: 'Socket.IO is not currently connected. Please wait for automatic reconnection.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
        id: 'socket-reconnect-toast' // Add ID to prevent duplicate toasts
      });

      // Return early
      return;
    }

    // Subscribe to real-time deposit updates with admin filter
    subscribeToDepositUpdates({ admin: true });

    console.log('Subscribed to deposit updates');

    // Test deposit update handling by simulating a deposit update
    if (deposits.length > 0) {
      const testDeposit = deposits[0];
      console.log('Testing deposit update handling with deposit:', testDeposit.id);

      // Create a simulated update payload
      const simulatedStatus = testDeposit.status === 'approved' ? 'pending' : 'approved';
      const updatePayload = {
        type: 'deposit_updated',
        payload: {
          id: testDeposit.id,
          _id: testDeposit.id,
          status: simulatedStatus,
          user: testDeposit.user,
          userId: testDeposit.userId,
          email: testDeposit.email,
          amount: testDeposit.amount,
          currency: testDeposit.currency,
          wallet: testDeposit.wallet,
          date: testDeposit.date,
          updatedAt: new Date().toISOString(),
          _refreshKey: Date.now()
        }
      };

      // Log the simulated update
      console.log('Simulating deposit update with payload:', updatePayload);

      // Manually trigger the Socket.IO message handler
      socketService.simulateMessage(updatePayload);
    }

    toast({
      title: 'Socket.IO Test',
      description: 'Socket.IO connection test initiated. Check console for details.',
      status: 'info',
      duration: 3000,
      isClosable: true,
      id: 'socket-test-toast' // Add ID to prevent duplicate toasts
    });
  };

  // Calculate statistics when deposits change
  useEffect(() => {
    if (deposits.length > 0) {
      calculateStats();
    }

    // Update the deposits ref whenever deposits state changes
    depositsRef.current = deposits;
  }, [deposits, calculateStats]);

  // Biến để theo dõi trạng thái kết nối trước đó
  const prevConnectedRef = useRef(isConnected);

  // Display connection status when it changes
  useEffect(() => {
    console.log('Socket.IO connection status changed:', isConnected);

    if (isConnected && !prevConnectedRef.current) {
      // Chỉ hiển thị thông báo khi trạng thái thay đổi từ ngắt kết nối sang kết nối
      console.log('Socket.IO connection is active');

      // Resubscribe to deposit updates when connection is established
      subscribeToDepositUpdates({ admin: true });

      toast({
        title: t('admin.socket.connected', 'Socket.IO Connected'),
        description: t('admin.socket.updatesActive', 'Real-time updates are now active'),
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top-right',
        id: 'socket-connected-toast'
      });
    } else if (!isConnected && prevConnectedRef.current) {
      // Chỉ hiển thị thông báo khi trạng thái thay đổi từ kết nối sang ngắt kết nối
      console.log('Socket.IO is disconnected');

      toast({
        title: t('admin.socket.disconnected', 'Socket.IO Disconnected'),
        description: t('admin.socket.updatesUnavailable', 'Real-time updates are currently unavailable. Reconnecting...'),
        status: 'warning',
        duration: 5000,
        isClosable: true,
        position: 'top-right',
        id: 'socket-disconnected-toast'
      });
    }

    // Cập nhật trạng thái kết nối trước đó
    prevConnectedRef.current = isConnected;
  }, [isConnected, subscribeToDepositUpdates, toast, t]);

  return (
    <Box>
      <Flex justify="space-between" align="center" mb={6}>
        <HStack>
          <Heading as="h1" size="lg" color={textColor}>
            {t('admin.deposits.title', 'Deposit Management')}
          </Heading>
          <Tooltip
            label={isConnected
              ? t('admin.socket.connectedTooltip', 'Connected to real-time updates')
              : t('admin.socket.disconnectedTooltip', 'Disconnected from real-time updates. Attempting to reconnect...')}
          >
            <Box
              w="10px"
              h="10px"
              borderRadius="full"
              bg={isConnected ? "green.400" : "red.400"}
              display="inline-block"
              ml={2}
              animation={!isConnected ? "pulse 1.5s infinite" : "none"}
              sx={{
                "@keyframes pulse": {
                  "0%": { opacity: 0.5 },
                  "50%": { opacity: 1 },
                  "100%": { opacity: 0.5 }
                }
              }}
            />
          </Tooltip>
        </HStack>

        <HStack spacing={2}>
          <Tooltip label={t('admin.deposits.refresh', 'Refresh Data')}>
            <IconButton
              aria-label="Refresh data"
              icon={<RepeatIcon />}
              onClick={handleRefresh}
              colorScheme="blue"
              variant="outline"
              size="sm"
            />
          </Tooltip>

          <Tooltip label={t('admin.deposits.exportCSV', 'Export to CSV')}>
            <IconButton
              aria-label="Export to CSV"
              icon={<FaFileExport />}
              onClick={exportToCSV}
              colorScheme="green"
              variant="outline"
              size="sm"
            />
          </Tooltip>

          <Tooltip label={t('admin.deposits.viewStats', 'View Statistics')}>
            <IconButton
              aria-label="View Statistics"
              icon={<FaChartBar />}
              onClick={onStatsOpen}
              colorScheme="purple"
              variant="outline"
              size="sm"
            />
          </Tooltip>
        </HStack>
      </Flex>

      {/* Statistics Cards */}
      {showStats && (
        <Grid templateColumns={{ base: "1fr", md: "repeat(4, 1fr)" }} gap={4} mb={6}>
          <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
            <CardBody>
              <Stat>
                <StatLabel color={secondaryTextColor}>{t('admin.deposits.totalDeposits', 'Total Deposits')}</StatLabel>
                <StatNumber color={textColor}>{totalDeposits}</StatNumber>
                <StatHelpText color={primaryColor}>
                  {formatAmount(statsData.totalAmount)} {filterCurrency !== 'all' ? filterCurrency : ''}
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
            <CardBody>
              <Stat>
                <StatLabel color={secondaryTextColor}>{t('admin.deposits.pendingDeposits', 'Pending')}</StatLabel>
                <StatNumber color={textColor}>{statsData.pendingCount}</StatNumber>
                <StatHelpText color="yellow.500">
                  {formatAmount(statsData.pendingAmount)} {filterCurrency !== 'all' ? filterCurrency : ''}
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
            <CardBody>
              <Stat>
                <StatLabel color={secondaryTextColor}>{t('admin.deposits.approvedDeposits', 'Approved')}</StatLabel>
                <StatNumber color={textColor}>{statsData.approvedCount}</StatNumber>
                <StatHelpText color="green.500">
                  {formatAmount(statsData.approvedAmount)} {filterCurrency !== 'all' ? filterCurrency : ''}
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
            <CardBody>
              <Stat>
                <StatLabel color={secondaryTextColor}>{t('admin.deposits.rejectedDeposits', 'Rejected')}</StatLabel>
                <StatNumber color={textColor}>{statsData.rejectedCount}</StatNumber>
                <StatHelpText color="red.500">
                  {formatAmount(statsData.rejectedAmount)} {filterCurrency !== 'all' ? filterCurrency : ''}
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>
        </Grid>
      )}

      <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
        <Flex justify="space-between" align="center" mb={4} flexDir={{ base: "column", md: "row" }} gap={4}>
          <InputGroup maxW={{ base: "100%", md: "300px" }}>
            <InputLeftElement pointerEvents="none">
              <SearchIcon color="#848E9C" />
            </InputLeftElement>
            <Input
              placeholder={t('admin.deposits.searchPlaceholder', 'Search by user, email or wallet')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
            />
          </InputGroup>

          <HStack spacing={4}>
            <Menu>
              <MenuButton as={Button} rightIcon={<ChevronDownIcon />} bg={cardBgColor} borderColor={borderColor} color={textColor} size="md">
                <HStack>
                  <FaFilter />
                  <Text>{t('admin.deposits.filters', 'Filters')}</Text>
                </HStack>
              </MenuButton>
              <MenuList bg={cardBgColor} borderColor={borderColor}>
                <MenuItem onClick={() => setShowStats(!showStats)} bg={cardBgColor} color={textColor} _hover={{ bg: borderColor }}>
                  {showStats ? t('admin.deposits.hideStats', 'Hide Statistics') : t('admin.deposits.showStats', 'Show Statistics')}
                </MenuItem>
                <MenuItem onClick={exportToCSV} bg={cardBgColor} color={textColor} _hover={{ bg: borderColor }}>
                  {t('admin.deposits.exportCSV', 'Export to CSV')}
                </MenuItem>
                <MenuItem onClick={handleRefresh} bg={cardBgColor} color={textColor} _hover={{ bg: borderColor }}>
                  {t('admin.deposits.refresh', 'Refresh Data')}
                </MenuItem>
                <MenuItem onClick={testSocketConnection} bg={cardBgColor} color={textColor} _hover={{ bg: borderColor }}>
                  Test Socket.IO
                </MenuItem>
              </MenuList>
            </Menu>

            <Select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
              w={{ base: "full", md: "150px" }}
            >
              <option value="all">{t('admin.deposits.allStatuses', 'All Statuses')}</option>
              <option value="pending">{t('admin.deposits.pending', 'Pending')}</option>
              <option value="processing">{t('admin.deposits.processing', 'Processing')}</option>
              <option value="approved">{t('admin.deposits.approved', 'Approved')}</option>
              <option value="rejected">{t('admin.deposits.rejected', 'Rejected')}</option>
            </Select>

            <Select
              value={filterCurrency}
              onChange={(e) => setFilterCurrency(e.target.value)}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
              w={{ base: "full", md: "150px" }}
            >
              <option value="all">{t('admin.deposits.allCurrencies', 'All Currencies')}</option>
              <option value="BTC">Bitcoin (BTC)</option>
              <option value="ETH">Ethereum (ETH)</option>
              <option value="USDT">Tether (USDT)</option>
              <option value="DOGE">Dogecoin (DOGE)</option>
              <option value="XRP">Ripple (XRP)</option>
            </Select>
          </HStack>
        </Flex>

        {isLoading ? (
          <Center py={10}>
            <Spinner size="xl" color={primaryColor} />
          </Center>
        ) : (
          <>
            <Box overflowX="auto">
              <Table variant="simple" size="md">
                <Thead>
                  <Tr>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.deposits.id', 'ID')}</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.deposits.user', 'User')}</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.deposits.amount', 'Amount')}</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.deposits.currency', 'Currency')}</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.deposits.date', 'Date')}</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.deposits.status', 'Status')}</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.deposits.actions', 'Actions')}</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {filteredDeposits.length > 0 ? (
                    filteredDeposits.map((deposit) => (
                      <Tr key={deposit.id}>
                        <Td color={textColor} borderColor={borderColor}>{deposit.id}</Td>
                        <Td color={textColor} borderColor={borderColor}>
                          <VStack align="start" spacing={1}>
                            <Text fontWeight="medium">{deposit.user}</Text>
                            <Text fontSize="xs" color={secondaryTextColor}>{deposit.email}</Text>
                            {deposit.phoneNumber && (
                              <Text fontSize="xs" color={secondaryTextColor}>{deposit.phoneNumber}</Text>
                            )}
                            {(deposit.country || deposit.city) && (
                              <Text fontSize="xs" color={secondaryTextColor}>
                                {[deposit.country, deposit.city].filter(Boolean).join(', ')}
                              </Text>
                            )}
                          </VStack>
                        </Td>
                        <Td color={textColor} borderColor={borderColor}>{formatAmount(deposit.amount)}</Td>
                        <Td color={textColor} borderColor={borderColor}>{deposit.currency}</Td>
                        <Td color={textColor} borderColor={borderColor}>
                          {new Date(deposit.date).toLocaleDateString()}
                        </Td>
                        <Td borderColor={borderColor}>
                          <Badge
                            colorScheme={getStatusColor(deposit.status)}
                            variant="subtle"
                            borderRadius="full"
                            px={2}
                          >
                            {deposit.status}
                          </Badge>
                        </Td>
                        <Td borderColor={borderColor}>
                          <HStack spacing={2}>
                            <Button
                              size="sm"
                              colorScheme="blue"
                              leftIcon={<FaEye />}
                              onClick={() => handleViewDeposit(deposit)}
                            >
                              {t('admin.deposits.view', 'View')}
                            </Button>

                            {deposit.receiptUrl && (
                              <Button
                                size="sm"
                                colorScheme="teal"
                                leftIcon={<FaReceipt />}
                                onClick={() => handleViewReceipt(deposit)}
                              >
                                {t('admin.deposits.receipt', 'Receipt')}
                              </Button>
                            )}

                            {(deposit.status === 'pending' || deposit.status === 'processing') && (
                              <>
                                <Button
                                  size="sm"
                                  colorScheme="green"
                                  leftIcon={<FaCheck />}
                                  onClick={() => handleViewDeposit(deposit)}
                                >
                                  {deposit.status === 'pending' ? t('admin.deposits.process', 'Process') : t('admin.deposits.approve', 'Approve')}
                                </Button>
                                <Button
                                  size="sm"
                                  colorScheme="red"
                                  leftIcon={<FaTimes />}
                                  onClick={() => {
                                    setSelectedDeposit(deposit);
                                    setAdminNotes('');
                                    handleReject();
                                  }}
                                >
                                  {t('admin.deposits.reject', 'Reject')}
                                </Button>
                              </>
                            )}
                          </HStack>
                        </Td>
                      </Tr>
                    ))
                  ) : (
                    <Tr>
                      <Td colSpan={7} textAlign="center" py={4} color={secondaryTextColor} borderColor={borderColor}>
                        {t('admin.deposits.noDeposits', 'No deposits found')}
                      </Td>
                    </Tr>
                  )}
                </Tbody>
              </Table>
            </Box>

            {/* Pagination */}
            <Flex justify="space-between" align="center" mt={4}>
              <Text color={secondaryTextColor}>
                {t('admin.deposits.showing', 'Showing')} {filteredDeposits.length} {t('admin.deposits.of', 'of')} {totalDeposits} {t('admin.deposits.deposits', 'deposits')}
              </Text>
              <HStack>
                <Button
                  size="sm"
                  onClick={() => setPage(p => Math.max(1, p - 1))}
                  isDisabled={page === 1}
                  colorScheme="gray"
                >
                  {t('admin.deposits.previous', 'Previous')}
                </Button>
                <Text color={textColor}>
                  {page} / {totalPages}
                </Text>
                <Button
                  size="sm"
                  onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                  isDisabled={page === totalPages}
                  colorScheme="gray"
                >
                  {t('admin.deposits.next', 'Next')}
                </Button>
              </HStack>
            </Flex>
          </>
        )}
      </Box>

      {/* Deposit Detail Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay backdropFilter="blur(5px)" />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor} borderWidth="1px">
          <ModalHeader borderBottomWidth="1px" borderColor={borderColor}>
            {t('admin.deposits.depositDetails', 'Deposit Details')}
          </ModalHeader>
          <ModalCloseButton color={textColor} />
          <ModalBody py={4}>
            {selectedDeposit && (
              <Box>
                <Flex direction="column" gap={4}>
                  <Box>
                    <Flex justify="space-between" mb={2}>
                      <Text color={secondaryTextColor}>{t('admin.deposits.id', 'ID')}:</Text>
                      <Text>{selectedDeposit.id}</Text>
                    </Flex>
                    <Flex justify="space-between" mb={2}>
                      <Text color={secondaryTextColor}>{t('admin.deposits.user', 'User')}:</Text>
                      <Text fontWeight="medium">{selectedDeposit.user}</Text>
                    </Flex>
                    <Flex justify="space-between" mb={2}>
                      <Text color={secondaryTextColor}>{t('admin.deposits.email', 'Email')}:</Text>
                      <Text>{selectedDeposit.email}</Text>
                    </Flex>
                    {selectedDeposit.phoneNumber && (
                      <Flex justify="space-between" mb={2}>
                        <Text color={secondaryTextColor}>{t('admin.deposits.phone', 'Phone')}:</Text>
                        <Text>{selectedDeposit.phoneNumber}</Text>
                      </Flex>
                    )}
                    {(selectedDeposit.country || selectedDeposit.city) && (
                      <Flex justify="space-between" mb={2}>
                        <Text color={secondaryTextColor}>{t('admin.deposits.location', 'Location')}:</Text>
                        <Text>{[selectedDeposit.country, selectedDeposit.city].filter(Boolean).join(', ')}</Text>
                      </Flex>
                    )}
                    <Flex justify="space-between" mb={2}>
                      <Text color={secondaryTextColor}>{t('admin.deposits.wallet', 'Wallet')}:</Text>
                      <HStack>
                        <Text isTruncated maxW="200px">{selectedDeposit.wallet}</Text>
                        <Tooltip label={isCopied ? "Copied!" : "Copy to clipboard"}>
                          <Button
                            size="xs"
                            variant="ghost"
                            onClick={() => copyToClipboard(selectedDeposit.wallet)}
                          >
                            {isCopied ? <CheckIcon /> : <CopyIcon />}
                          </Button>
                        </Tooltip>
                      </HStack>
                    </Flex>
                    {/* Amount Section with Admin Verification */}
                    <Box mb={4} p={4} bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                      <Text fontWeight="bold" mb={3} color={textColor}>
                        {t('admin.deposits.amountVerification', 'Amount Verification')}
                      </Text>

                      {/* User Declared Amount */}
                      <Flex justify="space-between" mb={2}>
                        <Text color={secondaryTextColor}>
                          {t('admin.deposits.userDeclaredAmount', 'User Declared Amount')}:
                        </Text>
                        <Text color={selectedDeposit.originalAmount ? "orange.400" : textColor}>
                          {formatAmount(selectedDeposit.originalAmount || selectedDeposit.amount)} {selectedDeposit.currency}
                        </Text>
                      </Flex>

                      {/* Admin Verified Amount */}
                      {selectedDeposit.adminVerifiedAmount && (
                        <Flex justify="space-between" mb={2}>
                          <Text color={secondaryTextColor}>
                            {t('admin.deposits.adminVerifiedAmount', 'Admin Verified Amount')}:
                          </Text>
                          <Text color="green.400" fontWeight="bold">
                            {formatAmount(selectedDeposit.adminVerifiedAmount)} {selectedDeposit.currency}
                          </Text>
                        </Flex>
                      )}

                      {/* Current Effective Amount */}
                      <Flex justify="space-between" mb={3}>
                        <Text color={secondaryTextColor} fontWeight="bold">
                          {t('admin.deposits.currentAmount', 'Current Amount')}:
                        </Text>
                        <Text fontWeight="bold" color={primaryColor}>
                          {formatAmount(selectedDeposit.amount)} {selectedDeposit.currency}
                        </Text>
                      </Flex>

                      {/* Amount Correction Reason */}
                      {selectedDeposit.amountCorrectionReason && (
                        <Box mb={3} p={2} bg="yellow.50" borderRadius="md" borderWidth="1px" borderColor="yellow.200">
                          <Text fontSize="sm" color="yellow.800">
                            <strong>{t('admin.deposits.correctionReason', 'Correction Reason')}:</strong> {selectedDeposit.amountCorrectionReason}
                          </Text>
                        </Box>
                      )}

                      {/* Amount Editing Interface */}
                      {!isEditingAmount ? (
                        <Box>
                          <Tooltip
                            label={t('admin.deposits.editAmountTooltip', 'Click to edit the deposit amount - unlimited editing enabled for all statuses')}
                            placement="top"
                            hasArrow
                          >
                            <Button
                              size="sm"
                              colorScheme="yellow"
                              variant="outline"
                              leftIcon={<EditIcon />}
                              onClick={handleEditAmount}
                            >
                              {t('admin.deposits.editAmount', 'Edit Amount')}
                            </Button>
                          </Tooltip>
                          <Text fontSize="xs" color="green.400" mt={1}>
                            {t('admin.deposits.editAlwaysEnabled', `✅ Unlimited editing enabled - Current status: ${selectedDeposit.status}`)}
                          </Text>

                          {/* Amount Edit History */}
                          {amountEditHistory.length > 0 && (
                            <Box mt={4} p={3} bg="gray.800" borderRadius="md">
                              <Text fontSize="sm" fontWeight="bold" color="blue.300" mb={2}>
                                {t('admin.deposits.editHistory', 'Amount Edit History')}
                              </Text>
                              <VStack spacing={2} align="stretch">
                                {amountEditHistory.slice(-3).map((edit, index) => (
                                  <Box key={index} p={2} bg="gray.700" borderRadius="sm">
                                    <HStack justify="space-between">
                                      <Text fontSize="xs" color="white">
                                        ${edit.amount.toFixed(2)}
                                      </Text>
                                      <Text fontSize="xs" color="gray.400">
                                        {new Date(edit.timestamp).toLocaleString()}
                                      </Text>
                                    </HStack>
                                    {edit.reason && (
                                      <Text fontSize="xs" color="gray.300" mt={1}>
                                        {edit.reason}
                                      </Text>
                                    )}
                                  </Box>
                                ))}
                              </VStack>
                            </Box>
                          )}
                        </Box>
                      ) : (
                        <VStack spacing={3} align="stretch">
                          <FormControl>
                            <FormLabel fontSize="sm">
                              {t('admin.deposits.adminVerifiedAmount', 'Admin Verified Amount')}
                            </FormLabel>
                            <NumberInput
                              value={editedAmount}
                              onChange={(valueString) => setEditedAmount(parseFloat(valueString) || 0)}
                              min={0}
                              precision={8}
                              step={0.00000001}
                            >
                              <NumberInputField
                                bg={bgColor}
                                borderColor={borderColor}
                                placeholder="Enter verified amount"
                              />
                              <NumberInputStepper>
                                <NumberIncrementStepper />
                                <NumberDecrementStepper />
                              </NumberInputStepper>
                            </NumberInput>
                          </FormControl>

                          <FormControl>
                            <FormLabel fontSize="sm">
                              {t('admin.deposits.correctionReason', 'Correction Reason')} (Optional)
                            </FormLabel>
                            <Textarea
                              value={amountCorrectionReason}
                              onChange={(e) => setAmountCorrectionReason(e.target.value)}
                              placeholder="Explain why the amount was corrected..."
                              size="sm"
                              rows={2}
                              bg={bgColor}
                              borderColor={borderColor}
                            />
                          </FormControl>

                          <HStack spacing={2}>
                            <Button
                              size="sm"
                              colorScheme="green"
                              onClick={handleSaveAmount}
                              isLoading={isAmountSubmitting}
                              loadingText="Updating & Syncing..."
                              leftIcon={<CheckIcon />}
                              isDisabled={editedAmount <= 0}
                              minH={{ base: "44px", md: "auto" }}
                              fontSize={{ base: "16px", md: "14px" }}
                            >
                              {t('admin.deposits.saveAmount', 'Save & Sync Amount')}
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={handleCancelAmountEdit}
                              leftIcon={<CloseIcon />}
                            >
                              {t('admin.deposits.cancel', 'Cancel')}
                            </Button>
                          </HStack>
                        </VStack>
                      )}

                      {/* Modification History */}
                      {selectedDeposit.amountModifiedAt && (
                        <Box mt={3} pt={3} borderTopWidth="1px" borderColor={borderColor}>
                          <Text fontSize="xs" color={secondaryTextColor}>
                            {t('admin.deposits.lastModified', 'Last modified')}: {new Date(selectedDeposit.amountModifiedAt).toLocaleString()}
                          </Text>
                        </Box>
                      )}
                    </Box>
                    <Flex justify="space-between" mb={2}>
                      <Text color={secondaryTextColor}>{t('admin.deposits.date', 'Date')}:</Text>
                      <Text>{new Date(selectedDeposit.date).toLocaleString()}</Text>
                    </Flex>
                    <Flex justify="space-between" mb={2}>
                      <Text color={secondaryTextColor}>{t('admin.deposits.status', 'Status')}:</Text>
                      <Badge colorScheme={getStatusColor(selectedDeposit.status)}>
                        {selectedDeposit.status}
                      </Badge>
                    </Flex>
                    {selectedDeposit.network && (
                      <Flex justify="space-between" mb={2}>
                        <Text color={secondaryTextColor}>{t('admin.deposits.network', 'Network')}:</Text>
                        <Text>{selectedDeposit.network}</Text>
                      </Flex>
                    )}
                    {selectedDeposit.txHash && (
                      <Flex justify="space-between" mb={2}>
                        <Text color={secondaryTextColor}>{t('admin.deposits.txHash', 'Transaction Hash')}:</Text>
                        <HStack>
                          <Text isTruncated maxW="150px">{selectedDeposit.txHash}</Text>
                          <Tooltip label={isCopied ? "Copied!" : "Copy to clipboard"}>
                            <Button
                              size="xs"
                              variant="ghost"
                              onClick={() => copyToClipboard(selectedDeposit.txHash!)}
                            >
                              {isCopied ? <CheckIcon /> : <CopyIcon />}
                            </Button>
                          </Tooltip>
                          <Tooltip label="View on blockchain explorer">
                            <Button
                              size="xs"
                              variant="ghost"
                              as="a"
                              href={`https://etherscan.io/tx/${selectedDeposit.txHash}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <FaExternalLinkAlt />
                            </Button>
                          </Tooltip>
                        </HStack>
                      </Flex>
                    )}
                    {selectedDeposit.receiptUrl && (
                      <Flex justify="center" mt={4}>
                        <Button
                          colorScheme="teal"
                          leftIcon={<FaReceipt />}
                          onClick={() => handleViewReceipt(selectedDeposit)}
                        >
                          {t('admin.deposits.viewReceipt', 'View Receipt')}
                        </Button>
                      </Flex>
                    )}
                  </Box>

                  {(selectedDeposit.status === 'pending' || selectedDeposit.status === 'processing') && (
                    <Box>
                      <FormControl mb={4}>
                        <FormLabel>{t('admin.deposits.walletAddress', 'Wallet Address')}</FormLabel>
                        <Input
                          placeholder="Enter wallet address"
                          value={walletAddress}
                          onChange={(e) => setWalletAddress(e.target.value)}
                          bg={bgColor}
                          borderColor={borderColor}
                        />
                        <FormHelperText>
                          {t('admin.deposits.walletAddressHelper', 'Enter the wallet address for this deposit')}
                        </FormHelperText>
                      </FormControl>

                      <FormControl mb={4}>
                        <FormLabel>{t('admin.deposits.txHash', 'Transaction Hash')} (Optional)</FormLabel>
                        <Input
                          placeholder="Enter blockchain transaction hash"
                          value={txHash}
                          onChange={(e) => setTxHash(e.target.value)}
                          bg={bgColor}
                          borderColor={borderColor}
                        />
                        <FormHelperText>
                          {t('admin.deposits.txHashHelper', 'Enter the blockchain transaction hash if available')}
                        </FormHelperText>
                      </FormControl>

                      <FormControl>
                        <FormLabel>{t('admin.deposits.adminNotes', 'Admin Notes')}</FormLabel>
                        <Textarea
                          placeholder={t('admin.deposits.notesPlaceholder', 'Add notes about this deposit...')}
                          value={adminNotes}
                          onChange={(e) => setAdminNotes(e.target.value)}
                          bg={bgColor}
                          borderColor={borderColor}
                          rows={4}
                        />
                      </FormControl>
                    </Box>
                  )}
                </Flex>
              </Box>
            )}
          </ModalBody>

          <ModalFooter>
            {(selectedDeposit?.status === 'pending' || selectedDeposit?.status === 'processing') && (
              <>
                <Button
                  colorScheme="green"
                  mr={3}
                  onClick={handleApprove}
                  isLoading={isSubmitting}
                >
                  {t('admin.deposits.approve', 'Approve Deposit')}
                </Button>
                <Button
                  colorScheme="red"
                  mr={3}
                  onClick={handleReject}
                  isLoading={isSubmitting}
                >
                  {t('admin.deposits.reject', 'Reject Deposit')}
                </Button>
              </>
            )}
            <Button variant="ghost" onClick={onClose}>
              {t('admin.deposits.close', 'Close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Receipt Image Modal - Using ReceiptImageViewer component */}
      {selectedDeposit && (
        <ReceiptImageViewer
          receiptUrl={selectedDeposit.receiptUrl}
          isOpen={isReceiptOpen}
          onClose={onReceiptClose}
          showDownloadButton={true}
          maxHeight="70vh"
        />
      )}

      {/* Statistics Modal */}
      <Modal isOpen={isStatsOpen} onClose={onStatsClose} size="xl">
        <ModalOverlay backdropFilter="blur(5px)" />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor} borderWidth="1px">
          <ModalHeader borderBottomWidth="1px" borderColor={borderColor}>
            {t('admin.deposits.depositStatistics', 'Deposit Statistics')}
          </ModalHeader>
          <ModalCloseButton color={textColor} />

          <ModalBody py={4}>
            <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={6}>
              <Box>
                <Heading size="md" mb={4} color={textColor}>
                  {t('admin.deposits.byStatus', 'By Status')}
                </Heading>
                <VStack align="stretch" spacing={4}>
                  <Flex justify="space-between">
                    <Text color={secondaryTextColor}>{t('admin.deposits.pending', 'Pending')}:</Text>
                    <Text color="yellow.500">{statsData.pendingCount} ({formatAmount(statsData.pendingAmount)})</Text>
                  </Flex>
                  <Flex justify="space-between">
                    <Text color={secondaryTextColor}>{t('admin.deposits.processing', 'Processing')}:</Text>
                    <Text color="blue.500">{statsData.processingCount}</Text>
                  </Flex>
                  <Flex justify="space-between">
                    <Text color={secondaryTextColor}>{t('admin.deposits.approved', 'Approved')}:</Text>
                    <Text color="green.500">{statsData.approvedCount} ({formatAmount(statsData.approvedAmount)})</Text>
                  </Flex>
                  <Flex justify="space-between">
                    <Text color={secondaryTextColor}>{t('admin.deposits.rejected', 'Rejected')}:</Text>
                    <Text color="red.500">{statsData.rejectedCount} ({formatAmount(statsData.rejectedAmount)})</Text>
                  </Flex>
                  <Divider borderColor={borderColor} />
                  <Flex justify="space-between">
                    <Text color={secondaryTextColor} fontWeight="bold">{t('admin.deposits.total', 'Total')}:</Text>
                    <Text color={primaryColor} fontWeight="bold">{totalDeposits} ({formatAmount(statsData.totalAmount)})</Text>
                  </Flex>
                </VStack>
              </Box>

              <Box>
                <Heading size="md" mb={4} color={textColor}>
                  {t('admin.deposits.byCurrency', 'By Currency')}
                </Heading>
                <VStack align="stretch" spacing={4}>
                  {/* Calculate currency stats dynamically */}
                  {(() => {
                    const currencyStats: Record<string, { count: number, amount: number }> = {};

                    deposits.forEach(deposit => {
                      if (!currencyStats[deposit.currency]) {
                        currencyStats[deposit.currency] = { count: 0, amount: 0 };
                      }
                      currencyStats[deposit.currency].count++;
                      currencyStats[deposit.currency].amount += deposit.amount;
                    });

                    return Object.entries(currencyStats).map(([currency, stats]) => (
                      <Flex key={currency} justify="space-between">
                        <Text color={secondaryTextColor}>{currency}:</Text>
                        <Text color={textColor}>{stats.count} ({formatAmount(stats.amount)} {currency})</Text>
                      </Flex>
                    ));
                  })()}
                </VStack>
              </Box>
            </Grid>
          </ModalBody>

          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={exportToCSV}>
              {t('admin.deposits.exportCSV', 'Export to CSV')}
            </Button>
            <Button variant="ghost" onClick={onStatsClose}>
              {t('admin.deposits.close', 'Close')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default AdminDeposits;
