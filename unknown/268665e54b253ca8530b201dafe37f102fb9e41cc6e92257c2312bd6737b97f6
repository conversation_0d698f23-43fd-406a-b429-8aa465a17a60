import { Request, Response } from 'express';
import SystemConfig from '../models/systemConfigModel';
import { logger } from '../utils/logger';

/**
 * Get public system configuration for users
 * This endpoint returns only the information that users need to see
 * Sensitive admin information is filtered out
 */
export const getUserSystemConfig = async (req: Request, res: Response): Promise<void> => {
  try {
    // Find the system configuration
    const config = await SystemConfig.findOneOrCreate();

    // Filter out sensitive admin information
    const userConfig = {
      siteName: config.siteName,
      siteDescription: config.siteDescription,
      maintenanceMode: config.maintenanceMode,
      maintenanceMessage: config.maintenanceMessage,
      commissionRate: config.commissionRate,
      referralRate: config.referralRate,
      minimumDeposit: config.minimumDeposit,
      minimumWithdrawal: config.minimumWithdrawal,
      withdrawalsEnabled: config.withdrawalsEnabled,
      depositsEnabled: config.depositsEnabled,
      supportedCurrencies: config.supportedCurrencies,
      // Filter crypto addresses to only show enabled ones and basic info
      cryptoAddresses: config.cryptoAddresses
        .filter(ca => ca.enabled)
        .map(ca => ({
          currency: ca.currency,
          enabled: ca.enabled,
          network: ca.network,
          // Don't expose actual addresses for security
          hasAddresses: ca.addresses && ca.addresses.length > 0
        }))
    };

    res.status(200).json({
      success: true,
      data: userConfig,
    });
  } catch (error: any) {
    logger.error('Error fetching user system configuration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system configuration',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get basic system information (completely public, no auth required)
 * This is for maintenance page and other public information
 */
export const getPublicSystemInfo = async (req: Request, res: Response): Promise<void> => {
  try {
    // Find the system configuration
    const config = await SystemConfig.findOneOrCreate();

    // Return only basic public information
    const publicInfo = {
      siteName: config.siteName,
      siteDescription: config.siteDescription,
      maintenanceMode: config.maintenanceMode,
      maintenanceMessage: config.maintenanceMessage,
      supportedCurrencies: config.supportedCurrencies,
    };

    res.status(200).json({
      success: true,
      data: publicInfo,
    });
  } catch (error: any) {
    logger.error('Error fetching public system information:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system information',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get commission rates for users (for calculation purposes)
 * This endpoint is used by users to calculate potential earnings
 */
export const getCommissionRates = async (req: Request, res: Response): Promise<void> => {
  try {
    // Find the system configuration
    const config = await SystemConfig.findOneOrCreate();

    // Return only commission-related information
    const commissionInfo = {
      commissionRate: config.commissionRate,
      referralRate: config.referralRate,
      minimumDeposit: config.minimumDeposit,
      minimumWithdrawal: config.minimumWithdrawal,
    };

    res.status(200).json({
      success: true,
      data: commissionInfo,
    });
  } catch (error: any) {
    logger.error('Error fetching commission rates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch commission rates',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get deposit/withdrawal settings for users
 * This endpoint tells users if deposits/withdrawals are currently enabled
 */
export const getTransactionSettings = async (req: Request, res: Response): Promise<void> => {
  try {
    // Find the system configuration
    const config = await SystemConfig.findOneOrCreate();

    // Return only transaction-related settings
    const transactionSettings = {
      depositsEnabled: config.depositsEnabled,
      withdrawalsEnabled: config.withdrawalsEnabled,
      minimumDeposit: config.minimumDeposit,
      minimumWithdrawal: config.minimumWithdrawal,
      supportedCurrencies: config.supportedCurrencies,
    };

    res.status(200).json({
      success: true,
      data: transactionSettings,
    });
  } catch (error: any) {
    logger.error('Error fetching transaction settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch transaction settings',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
