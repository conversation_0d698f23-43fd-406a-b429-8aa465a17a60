const { MongoClient } = require('mongodb');
require('dotenv').config();

async function setupProductionDatabase() {
  const client = new MongoClient(process.env.MONGO_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db();
    
    // Create indexes for optimal performance
    console.log('📊 Creating database indexes...');
    
    // Users collection indexes
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ referralCode: 1 }, { unique: true });
    await db.collection('users').createIndex({ createdAt: -1 });
    await db.collection('users').createIndex({ isActive: 1 });
    await db.collection('users').createIndex({ emailVerified: 1 });
    await db.collection('users').createIndex({ kycVerified: 1 });
    console.log('✅ Users indexes created');
    
    // Investments collection indexes
    await db.collection('investments').createIndex({ userId: 1, createdAt: -1 });
    await db.collection('investments').createIndex({ status: 1 });
    await db.collection('investments').createIndex({ cryptocurrency: 1 });
    await db.collection('investments').createIndex({ maturityDate: 1 });
    await db.collection('investments').createIndex({ isActive: 1 });
    console.log('✅ Investments indexes created');
    
    // Transactions collection indexes
    await db.collection('transactions').createIndex({ userId: 1, createdAt: -1 });
    await db.collection('transactions').createIndex({ type: 1, status: 1 });
    await db.collection('transactions').createIndex({ txHash: 1 }, { unique: true, sparse: true });
    await db.collection('transactions').createIndex({ walletAddress: 1 });
    await db.collection('transactions').createIndex({ cryptocurrency: 1 });
    console.log('✅ Transactions indexes created');
    
    // Withdrawals collection indexes
    await db.collection('withdrawals').createIndex({ userId: 1, createdAt: -1 });
    await db.collection('withdrawals').createIndex({ status: 1 });
    await db.collection('withdrawals').createIndex({ cryptocurrency: 1 });
    await db.collection('withdrawals').createIndex({ walletAddress: 1 });
    await db.collection('withdrawals').createIndex({ adminId: 1 });
    console.log('✅ Withdrawals indexes created');
    
    // Referrals collection indexes
    await db.collection('referrals').createIndex({ referrerId: 1 });
    await db.collection('referrals').createIndex({ referredUserId: 1 });
    await db.collection('referrals').createIndex({ createdAt: -1 });
    console.log('✅ Referrals indexes created');
    
    // KYC documents collection indexes
    await db.collection('kycdocuments').createIndex({ userId: 1 });
    await db.collection('kycdocuments').createIndex({ status: 1 });
    await db.collection('kycdocuments').createIndex({ createdAt: -1 });
    console.log('✅ KYC documents indexes created');
    
    // Audit logs collection indexes
    await db.collection('auditlogs').createIndex({ userId: 1, timestamp: -1 });
    await db.collection('auditlogs').createIndex({ action: 1 });
    await db.collection('auditlogs').createIndex({ ipAddress: 1 });
    await db.collection('auditlogs').createIndex({ timestamp: -1 });
    console.log('✅ Audit logs indexes created');
    
    // Sessions collection indexes (for user sessions)
    await db.collection('sessions').createIndex({ userId: 1 });
    await db.collection('sessions').createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });
    await db.collection('sessions').createIndex({ sessionToken: 1 }, { unique: true });
    console.log('✅ Sessions indexes created');
    
    // Create admin user if not exists
    console.log('👤 Checking for admin user...');
    const adminExists = await db.collection('users').findOne({ isAdmin: true });
    
    if (!adminExists) {
      console.log('🔐 Creating default admin user...');
      const bcrypt = require('bcryptjs');
      const adminPassword = process.env.ADMIN_DEFAULT_PASSWORD || 'ChangeThisPassword123!';
      const hashedPassword = await bcrypt.hash(adminPassword, 12);
      
      const adminUser = {
        email: process.env.ADMIN_EMAIL || '<EMAIL>',
        password: hashedPassword,
        firstName: 'System',
        lastName: 'Administrator',
        country: 'Global',
        city: 'System',
        referralCode: 'ADMIN001',
        isAdmin: true,
        isActive: true,
        emailVerified: true,
        kycVerified: true,
        twoFactorEnabled: false,
        createdAt: new Date(),
        lastLogin: null,
        referralCount: 0,
        referralEarnings: 0,
        marketingConsent: false
      };
      
      await db.collection('users').insertOne(adminUser);
      console.log('✅ Admin user created');
      console.log(`📧 Admin email: ${adminUser.email}`);
      console.log(`🔑 Admin password: ${adminPassword}`);
      console.log('⚠️  IMPORTANT: Change the admin password after first login!');
    } else {
      console.log('✅ Admin user already exists');
    }
    
    // Create application settings
    console.log('⚙️ Setting up application configuration...');
    const settingsExists = await db.collection('settings').findOne({ type: 'application' });
    
    if (!settingsExists) {
      const appSettings = {
        type: 'application',
        settings: {
          siteName: 'CryptoYield',
          siteUrl: process.env.FRONTEND_URL || 'https://cryptoyield.com',
          supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
          maintenanceMode: false,
          registrationEnabled: true,
          kycRequired: true,
          minimumWithdrawal: {
            BTC: 0.001,
            ETH: 0.01,
            USDT: 10,
            BNB: 0.1,
            XRP: 20
          },
          withdrawalFees: {
            BTC: 0.0005,
            ETH: 0.002,
            USDT: 1,
            BNB: 0.001,
            XRP: 0.2
          },
          supportedCryptocurrencies: ['BTC', 'ETH', 'USDT', 'BNB', 'XRP'],
          referralCommissionRate: 0.05, // 5%
          maxReferralLevels: 3
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await db.collection('settings').insertOne(appSettings);
      console.log('✅ Application settings created');
    } else {
      console.log('✅ Application settings already exist');
    }
    
    // Create cryptocurrency data collection
    console.log('💰 Setting up cryptocurrency data...');
    const cryptoData = [
      {
        symbol: 'BTC',
        name: 'Bitcoin',
        network: 'Bitcoin',
        decimals: 8,
        isActive: true,
        minDeposit: 0.0001,
        minWithdrawal: 0.001,
        withdrawalFee: 0.0005,
        confirmationsRequired: 3
      },
      {
        symbol: 'ETH',
        name: 'Ethereum',
        network: 'Ethereum',
        decimals: 18,
        isActive: true,
        minDeposit: 0.001,
        minWithdrawal: 0.01,
        withdrawalFee: 0.002,
        confirmationsRequired: 12
      },
      {
        symbol: 'USDT',
        name: 'Tether USD',
        network: 'Tron',
        decimals: 6,
        isActive: true,
        minDeposit: 1,
        minWithdrawal: 10,
        withdrawalFee: 1,
        confirmationsRequired: 20
      },
      {
        symbol: 'BNB',
        name: 'Binance Coin',
        network: 'BSC',
        decimals: 18,
        isActive: true,
        minDeposit: 0.01,
        minWithdrawal: 0.1,
        withdrawalFee: 0.001,
        confirmationsRequired: 15
      },
      {
        symbol: 'XRP',
        name: 'Ripple',
        network: 'XRP Ledger',
        decimals: 6,
        isActive: true,
        minDeposit: 1,
        minWithdrawal: 20,
        withdrawalFee: 0.2,
        confirmationsRequired: 5
      }
    ];
    
    for (const crypto of cryptoData) {
      await db.collection('cryptocurrencies').updateOne(
        { symbol: crypto.symbol },
        { $set: { ...crypto, updatedAt: new Date() } },
        { upsert: true }
      );
    }
    console.log('✅ Cryptocurrency data setup complete');
    
    // Create investment packages
    console.log('📦 Setting up investment packages...');
    const investmentPackages = [
      {
        name: 'Starter Package',
        description: 'Perfect for beginners',
        minAmount: 100,
        maxAmount: 1000,
        duration: 30, // days
        interestRate: 0.05, // 5%
        isActive: true,
        features: ['Daily returns', 'Basic support', 'Mobile app access']
      },
      {
        name: 'Growth Package',
        description: 'For growing your portfolio',
        minAmount: 1000,
        maxAmount: 10000,
        duration: 60, // days
        interestRate: 0.08, // 8%
        isActive: true,
        features: ['Daily returns', 'Priority support', 'Advanced analytics', 'Mobile app access']
      },
      {
        name: 'Premium Package',
        description: 'Maximum returns for serious investors',
        minAmount: 10000,
        maxAmount: 100000,
        duration: 90, // days
        interestRate: 0.12, // 12%
        isActive: true,
        features: ['Daily returns', 'VIP support', 'Advanced analytics', 'Personal advisor', 'Mobile app access']
      }
    ];
    
    for (const package of investmentPackages) {
      await db.collection('investmentpackages').updateOne(
        { name: package.name },
        { $set: { ...package, createdAt: new Date(), updatedAt: new Date() } },
        { upsert: true }
      );
    }
    console.log('✅ Investment packages setup complete');
    
    console.log('\n🎉 Production database setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Database indexes created');
    console.log('✅ Admin user configured');
    console.log('✅ Application settings initialized');
    console.log('✅ Cryptocurrency data setup');
    console.log('✅ Investment packages configured');
    console.log('\n⚠️  Next steps:');
    console.log('1. Change the default admin password');
    console.log('2. Configure email service');
    console.log('3. Setup SSL certificates');
    console.log('4. Configure monitoring');
    console.log('5. Run security audit');
    
  } catch (error) {
    console.error('❌ Error setting up production database:', error);
    process.exit(1);
  } finally {
    await client.close();
  }
}

// Run the setup
setupProductionDatabase();
