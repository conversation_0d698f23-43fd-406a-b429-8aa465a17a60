import mongoose from 'mongoose';
import Wallet from '../models/walletModel';
import { logger } from '../utils/logger';

interface MigrationResult {
  success: boolean;
  migratedUsers: number;
  migratedWallets: number;
  errors: string[];
  skippedWallets: number;
}

interface MigrationStats {
  totalUserWallets: number;
  processedWallets: number;
  successfulMigrations: number;
  failedMigrations: number;
  skippedWallets: number;
}

class UserWalletMigrationService {
  /**
   * Migrate all UserWallet data to Wallet model
   */
  async migrateAllUserWallets(): Promise<MigrationResult> {
    const session = await mongoose.startSession();
    const result: MigrationResult = {
      success: false,
      migratedUsers: 0,
      migratedWallets: 0,
      errors: [],
      skippedWallets: 0
    };

    try {
      await session.withTransaction(async () => {
        logger.info('🚀 Starting UserWallet to Wallet migration...');

        // Get all UserWallets grouped by userId
        const userWallets = await UserWallet.aggregate([
          { $match: { isActive: true } },
          {
            $group: {
              _id: '$userId',
              wallets: { $push: '$$ROOT' }
            }
          }
        ]).session(session);

        logger.info(`📊 Found ${userWallets.length} users with UserWallets`);

        for (const userGroup of userWallets) {
          try {
            await this.migrateUserWallets(userGroup._id, userGroup.wallets, session);
            result.migratedUsers++;
            result.migratedWallets += userGroup.wallets.length;
          } catch (error: any) {
            logger.error(`❌ Failed to migrate user ${userGroup._id}:`, error);
            result.errors.push(`User ${userGroup._id}: ${error.message}`);
          }
        }

        result.success = result.errors.length === 0;
        logger.info(`✅ Migration completed: ${result.migratedUsers} users, ${result.migratedWallets} wallets`);
      });
    } catch (error: any) {
      logger.error('❌ Migration transaction failed:', error);
      result.errors.push(`Transaction failed: ${error.message}`);
    } finally {
      await session.endSession();
    }

    return result;
  }

  /**
   * Migrate UserWallets for a specific user
   */
  private async migrateUserWallets(
    userId: mongoose.Types.ObjectId,
    userWallets: any[],
    session: mongoose.ClientSession
  ): Promise<void> {
    // Check if user already has a Wallet
    let wallet = await Wallet.findOne({ userId }).session(session);

    if (!wallet) {
      // Create new Wallet
      wallet = new Wallet({
        userId,
        assets: [],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });
    }

    // Process each UserWallet
    for (const userWallet of userWallets) {
      try {
        await this.migrateUserWallet(wallet, userWallet);
      } catch (error: any) {
        logger.error(`❌ Failed to migrate wallet ${userWallet._id}:`, error);
        throw error;
      }
    }

    // Save the updated wallet
    await wallet.save({ session });
    logger.info(`✅ Migrated ${userWallets.length} wallets for user ${userId}`);
  }

  /**
   * Migrate a single UserWallet to Wallet asset
   */
  private async migrateUserWallet(wallet: any, userWallet: any): Promise<void> {
    const symbol = userWallet.currency.toUpperCase();

    // Check if asset already exists
    let asset = wallet.assets.find((a: any) => a.symbol === symbol);

    if (!asset) {
      // Create new asset
      asset = {
        symbol,
        balance: userWallet.balance || 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'commission',
        network: userWallet.network || 'mainnet',
        address: userWallet.address,
        addresses: []
      };
      wallet.assets.push(asset);
    }

    // Add address to asset
    const addressData = {
      address: userWallet.address,
      network: userWallet.network || 'mainnet',
      isDefault: userWallet.isDefault || true,
      isActive: userWallet.isActive !== false,
      qrCodeUrl: userWallet.qrCodeUrl,
      label: userWallet.label,
      privateKey: userWallet.privateKey,
      addressIndex: userWallet.addressIndex || 0,
      lastUpdated: userWallet.lastUpdated || new Date(),
      withdrawalEnabled: userWallet.withdrawalEnabled !== false
    };

    // Check if address already exists
    const existingAddress = asset.addresses?.find((addr: any) => addr.address === userWallet.address);
    if (!existingAddress) {
      if (!asset.addresses) {
        asset.addresses = [];
      }
      asset.addresses.push(addressData);
    }

    logger.debug(`✅ Migrated UserWallet ${userWallet._id} to asset ${symbol}`);
  }

  /**
   * Validate migration results
   */
  async validateMigration(): Promise<MigrationStats> {
    const stats: MigrationStats = {
      totalUserWallets: 0,
      processedWallets: 0,
      successfulMigrations: 0,
      failedMigrations: 0,
      skippedWallets: 0
    };

    try {
      // Count total UserWallets
      stats.totalUserWallets = await UserWallet.countDocuments({ isActive: true });

      // Count migrated wallets in Wallet model
      const wallets = await Wallet.find({});
      for (const wallet of wallets) {
        for (const asset of wallet.assets) {
          if (asset.addresses && asset.addresses.length > 0) {
            stats.successfulMigrations += asset.addresses.length;
          }
        }
      }

      stats.processedWallets = stats.successfulMigrations;
      stats.failedMigrations = stats.totalUserWallets - stats.successfulMigrations;

      logger.info('📊 Migration validation results:', stats);
    } catch (error) {
      logger.error('❌ Migration validation failed:', error);
    }

    return stats;
  }

  /**
   * Rollback migration (restore UserWallets from Wallet)
   */
  async rollbackMigration(): Promise<boolean> {
    const session = await mongoose.startSession();

    try {
      await session.withTransaction(async () => {
        logger.info('🔄 Starting migration rollback...');

        // This would restore UserWallets from Wallet data
        // Implementation depends on specific rollback requirements
        logger.warn('⚠️ Rollback not implemented - use database backup instead');
      });

      return true;
    } catch (error) {
      logger.error('❌ Rollback failed:', error);
      return false;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Clean up UserWallet collection after successful migration
   */
  async cleanupUserWallets(): Promise<boolean> {
    try {
      logger.info('🧹 Starting UserWallet cleanup...');

      // Mark UserWallets as inactive instead of deleting
      const result = await UserWallet.updateMany(
        { isActive: true },
        { 
          isActive: false,
          migratedAt: new Date(),
          migrationNote: 'Migrated to Wallet model'
        }
      );

      logger.info(`✅ Marked ${result.modifiedCount} UserWallets as inactive`);
      return true;
    } catch (error) {
      logger.error('❌ UserWallet cleanup failed:', error);
      return false;
    }
  }

  /**
   * Get migration progress
   */
  async getMigrationProgress(): Promise<{
    userWalletsCount: number;
    walletsCount: number;
    migrationPercentage: number;
  }> {
    try {
      const userWalletsCount = await UserWallet.countDocuments({ isActive: true });
      const walletsCount = await Wallet.countDocuments({});
      
      const migrationPercentage = userWalletsCount > 0 
        ? Math.round((walletsCount / userWalletsCount) * 100)
        : 100;

      return {
        userWalletsCount,
        walletsCount,
        migrationPercentage
      };
    } catch (error) {
      logger.error('❌ Failed to get migration progress:', error);
      return {
        userWalletsCount: 0,
        walletsCount: 0,
        migrationPercentage: 0
      };
    }
  }
}

export default new UserWalletMigrationService();
