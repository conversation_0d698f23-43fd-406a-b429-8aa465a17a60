#!/usr/bin/env npx ts-node

/**
 * <PERSON>ript để seed dữ liệu cryptocurrency vào database
 * 
 * Chạy script: npx ts-node backend/scripts/seed-cryptocurrencies.ts
 */

import mongoose from 'mongoose';
import { config } from 'dotenv';
import CryptoCurrency from '../src/models/cryptoCurrencyModel';
import { logger } from '../src/utils/logger';

// Load environment variables
config();

const cryptocurrencies = [
  {
    symbol: 'BTC',
    name: 'Bitcoin',
    icon: 'bitcoin.png',
    description: 'The first and most well-known cryptocurrency',
    decimals: 8,
    minAmount: 0.0001,
    maxAmount: 100,
    enabled: true,
    networks: [
      {
        id: 'bitcoin',
        name: 'Bitcoin Network',
        description: 'Native Bitcoin blockchain',
        fee: 0.0005,
        processingTime: '10-60 minutes',
        isDefault: true,
        enabled: true,
        addressFormat: 'Legacy/SegWit',
        requiredMemo: false,
        minConfirmations: 1,
        withdrawalEnabled: true,
        depositEnabled: true,
        addresses: [],
        currentAddressIndex: 0
      }
    ]
  },
  {
    symbol: 'ETH',
    name: 'Ethereum',
    icon: 'ethereum.png',
    description: 'Smart contract platform and cryptocurrency',
    decimals: 18,
    minAmount: 0.001,
    maxAmount: 1000,
    enabled: true,
    networks: [
      {
        id: 'ethereum',
        name: 'Ethereum Network',
        description: 'Native Ethereum blockchain',
        fee: 0.005,
        processingTime: '2-5 minutes',
        isDefault: true,
        enabled: true,
        addressFormat: 'ERC-20',
        requiredMemo: false,
        minConfirmations: 12,
        withdrawalEnabled: true,
        depositEnabled: true,
        addresses: [],
        currentAddressIndex: 0
      }
    ]
  },
  {
    symbol: 'USDT',
    name: 'Tether USD',
    icon: 'tether.png',
    description: 'USD-pegged stablecoin',
    decimals: 6,
    minAmount: 1,
    maxAmount: 100000,
    enabled: true,
    networks: [
      {
        id: 'ethereum',
        name: 'Ethereum (ERC-20)',
        description: 'USDT on Ethereum blockchain',
        fee: 5,
        processingTime: '2-5 minutes',
        isDefault: true,
        enabled: true,
        addressFormat: 'ERC-20',
        requiredMemo: false,
        minConfirmations: 12,
        withdrawalEnabled: true,
        depositEnabled: true,
        addresses: [],
        currentAddressIndex: 0
      },
      {
        id: 'tron',
        name: 'Tron (TRC-20)',
        description: 'USDT on Tron blockchain',
        fee: 1,
        processingTime: '1-3 minutes',
        isDefault: false,
        enabled: true,
        addressFormat: 'TRC-20',
        requiredMemo: false,
        minConfirmations: 19,
        withdrawalEnabled: true,
        depositEnabled: true,
        addresses: [],
        currentAddressIndex: 0
      }
    ]
  },
  {
    symbol: 'BNB',
    name: 'Binance Coin',
    icon: 'binance.png',
    description: 'Binance exchange native token',
    decimals: 18,
    minAmount: 0.01,
    maxAmount: 10000,
    enabled: true,
    networks: [
      {
        id: 'bsc',
        name: 'Binance Smart Chain',
        description: 'BNB on BSC network',
        fee: 0.001,
        processingTime: '1-3 minutes',
        isDefault: true,
        enabled: true,
        addressFormat: 'BEP-20',
        requiredMemo: false,
        minConfirmations: 15,
        withdrawalEnabled: true,
        depositEnabled: true,
        addresses: [],
        currentAddressIndex: 0
      }
    ]
  },
  {
    symbol: 'SOL',
    name: 'Solana',
    icon: 'solana.png',
    description: 'High-performance blockchain platform',
    decimals: 9,
    minAmount: 0.01,
    maxAmount: 10000,
    enabled: true,
    networks: [
      {
        id: 'solana',
        name: 'Solana Network',
        description: 'Native Solana blockchain',
        fee: 0.000005,
        processingTime: '1-2 minutes',
        isDefault: true,
        enabled: true,
        addressFormat: 'Solana',
        requiredMemo: false,
        minConfirmations: 1,
        withdrawalEnabled: true,
        depositEnabled: true,
        addresses: [],
        currentAddressIndex: 0
      }
    ]
  },
  {
    symbol: 'DOGE',
    name: 'Dogecoin',
    icon: 'dogecoin.png',
    description: 'The meme cryptocurrency',
    decimals: 8,
    minAmount: 1,
    maxAmount: 1000000,
    enabled: true,
    networks: [
      {
        id: 'dogecoin',
        name: 'Dogecoin Network',
        description: 'Native Dogecoin blockchain',
        fee: 1,
        processingTime: '1-10 minutes',
        isDefault: true,
        enabled: true,
        addressFormat: 'Dogecoin',
        requiredMemo: false,
        minConfirmations: 6,
        withdrawalEnabled: true,
        depositEnabled: true,
        addresses: [],
        currentAddressIndex: 0
      }
    ]
  },
  {
    symbol: 'TRX',
    name: 'TRON',
    icon: 'tron.png',
    description: 'Decentralized entertainment ecosystem',
    decimals: 6,
    minAmount: 1,
    maxAmount: 1000000,
    enabled: true,
    networks: [
      {
        id: 'tron',
        name: 'TRON Network',
        description: 'Native TRON blockchain',
        fee: 1,
        processingTime: '1-3 minutes',
        isDefault: true,
        enabled: true,
        addressFormat: 'TRON',
        requiredMemo: false,
        minConfirmations: 19,
        withdrawalEnabled: true,
        depositEnabled: true,
        addresses: [],
        currentAddressIndex: 0
      }
    ]
  }
];

async function seedCryptocurrencies() {
  try {
    // Kết nối database
    const mongoURI = process.env.MONGO_URI || '*******************************************************************';
    console.log(`🔗 Đang kết nối database...`);
    await mongoose.connect(mongoURI);
    console.log('✅ Kết nối database thành công');

    // Xóa dữ liệu cũ (nếu có)
    console.log('🗑️ Xóa dữ liệu cryptocurrency cũ...');
    await CryptoCurrency.deleteMany({});

    // Thêm dữ liệu mới
    console.log('📝 Thêm dữ liệu cryptocurrency mới...');
    const results = await CryptoCurrency.insertMany(cryptocurrencies);
    
    console.log(`✅ Đã thêm ${results.length} cryptocurrency thành công:`);
    results.forEach(crypto => {
      console.log(`   - ${crypto.symbol} (${crypto.name})`);
    });

    // Kiểm tra dữ liệu
    const count = await CryptoCurrency.countDocuments();
    console.log(`📊 Tổng số cryptocurrency trong database: ${count}`);

    console.log('\n🎉 Seed dữ liệu cryptocurrency hoàn thành!');

  } catch (error: any) {
    console.error('❌ Lỗi khi seed dữ liệu:', error.message);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Đã ngắt kết nối database');
  }
}

// Chạy script
seedCryptocurrencies().catch(console.error);
