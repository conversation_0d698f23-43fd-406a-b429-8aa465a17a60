import { Server as HttpServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import logger from '../utils/logger';
import { IUser } from '../models/userModel';

/**
 * Enhanced Socket Service for Real-Time Transaction Management
 * Provides comprehensive real-time updates for deposits, withdrawals, and wallet synchronization
 */
export class EnhancedSocketService {
  private io: SocketIOServer;
  private connectedUsers: Map<string, Socket> = new Map();
  private adminSockets: Map<string, Socket> = new Map();
  private userRooms: Map<string, Set<string>> = new Map(); // userId -> Set of socketIds

  constructor(server: HttpServer) {
    this.io = new SocketIOServer(server, {
      path: '/ws',
      serveClient: false,
      cors: {
        origin: process.env.NODE_ENV === 'development' 
          ? '*' 
          : [
              process.env.FRONTEND_URL || 'http://localhost:3000',
              'http://localhost:3000',
              'http://localhost:3001',
              'http://localhost:5173'
            ],
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 25000,
      upgradeTimeout: 30000,
      allowUpgrades: true
    });

    this.setupMiddleware();
    this.setupEventHandlers();
    
    logger.info('Enhanced Socket Service initialized');
  }

  /**
   * Setup authentication middleware
   */
  private setupMiddleware(): void {
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth?.token || socket.handshake.query?.token;
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
        
        // Attach user info to socket
        socket.data.userId = decoded.id;
        socket.data.userEmail = decoded.email;
        socket.data.isAdmin = decoded.isAdmin || false;
        
        logger.info(`Socket authenticated for user: ${decoded.email}`);
        next();
      } catch (error) {
        logger.error('Socket authentication failed:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.io.on('connection', (socket: Socket) => {
      this.handleConnection(socket);
    });
  }

  /**
   * Handle new socket connection
   */
  private handleConnection(socket: Socket): void {
    const userId = socket.data.userId;
    const isAdmin = socket.data.isAdmin;
    
    logger.info(`User connected: ${socket.data.userEmail} (${socket.id})`);

    // Store socket reference
    this.connectedUsers.set(socket.id, socket);
    
    // Add to user rooms for multi-device support
    if (!this.userRooms.has(userId)) {
      this.userRooms.set(userId, new Set());
    }
    this.userRooms.get(userId)!.add(socket.id);

    // Add to admin room if user is admin
    if (isAdmin) {
      this.adminSockets.set(socket.id, socket);
      socket.join('admin_room');
      logger.info(`Admin user joined admin room: ${socket.data.userEmail}`);
    }

    // Join user-specific room
    socket.join(`user_${userId}`);

    // Setup event listeners
    this.setupSocketEventListeners(socket);

    // Handle disconnection
    socket.on('disconnect', () => {
      this.handleDisconnection(socket);
    });

    // Send connection confirmation
    socket.emit('connection_confirmed', {
      socketId: socket.id,
      userId,
      isAdmin,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Setup individual socket event listeners
   */
  private setupSocketEventListeners(socket: Socket): void {
    const userId = socket.data.userId;

    // Heartbeat for connection monitoring
    socket.on('heartbeat', () => {
      socket.emit('heartbeat_response', { timestamp: new Date().toISOString() });
    });

    // Join specific rooms for targeted updates
    socket.on('join_transaction_updates', () => {
      socket.join(`transactions_${userId}`);
      logger.info(`User ${userId} joined transaction updates room`);
    });

    socket.on('join_wallet_updates', () => {
      socket.join(`wallet_${userId}`);
      logger.info(`User ${userId} joined wallet updates room`);
    });

    socket.on('join_admin_updates', () => {
      if (socket.data.isAdmin) {
        socket.join('admin_updates');
        logger.info(`Admin ${userId} joined admin updates room`);
      }
    });

    // Request current status
    socket.on('request_status', () => {
      this.sendUserStatus(socket);
    });
  }

  /**
   * Handle socket disconnection
   */
  private handleDisconnection(socket: Socket): void {
    const userId = socket.data.userId;
    
    logger.info(`User disconnected: ${socket.data.userEmail} (${socket.id})`);

    // Remove from connected users
    this.connectedUsers.delete(socket.id);
    
    // Remove from admin sockets if applicable
    this.adminSockets.delete(socket.id);

    // Remove from user rooms
    if (this.userRooms.has(userId)) {
      this.userRooms.get(userId)!.delete(socket.id);
      if (this.userRooms.get(userId)!.size === 0) {
        this.userRooms.delete(userId);
      }
    }
  }

  /**
   * Send user status information
   */
  private sendUserStatus(socket: Socket): void {
    const userId = socket.data.userId;
    const status = {
      userId,
      socketId: socket.id,
      isAdmin: socket.data.isAdmin,
      connectedDevices: this.userRooms.get(userId)?.size || 0,
      timestamp: new Date().toISOString()
    };

    socket.emit('user_status', status);
  }

  // ===========================================
  // PUBLIC METHODS FOR TRANSACTION EVENTS
  // ===========================================

  /**
   * Notify about new deposit transaction
   */
  public notifyDepositCreated(userId: string, transaction: any): void {
    const event = 'deposit_created';
    const data = {
      transactionId: transaction.transactionId,
      userId,
      cryptocurrency: transaction.cryptocurrency,
      amount: transaction.amount,
      usdValue: transaction.usdValue,
      status: transaction.status,
      timestamp: new Date().toISOString()
    };

    // Notify user
    this.io.to(`user_${userId}`).emit(event, data);
    
    // Notify admins
    this.io.to('admin_room').emit('admin_deposit_created', data);
    
    logger.info(`Deposit created notification sent: ${transaction.transactionId}`);
  }

  /**
   * Notify about deposit approval
   */
  public notifyDepositApproved(userId: string, transaction: any): void {
    const event = 'deposit_approved';
    const data = {
      transactionId: transaction.transactionId,
      userId,
      cryptocurrency: transaction.cryptocurrency,
      amount: transaction.amount,
      usdValue: transaction.usdValue,
      approvedAt: transaction.approvedAt,
      timestamp: new Date().toISOString()
    };

    // Notify user with wallet update trigger
    this.io.to(`user_${userId}`).emit(event, data);
    this.io.to(`wallet_${userId}`).emit('wallet_balance_updated', {
      ...data,
      balanceChange: transaction.amount,
      changeType: 'credit'
    });

    // Notify admins
    this.io.to('admin_room').emit('admin_deposit_approved', data);
    
    logger.info(`Deposit approved notification sent: ${transaction.transactionId}`);
  }

  /**
   * Notify about withdrawal request
   */
  public notifyWithdrawalCreated(userId: string, transaction: any): void {
    const event = 'withdrawal_created';
    const data = {
      transactionId: transaction.transactionId,
      userId,
      cryptocurrency: transaction.cryptocurrency,
      amount: transaction.amount,
      netAmount: transaction.netAmount,
      walletAddress: transaction.walletAddress,
      status: transaction.status,
      timestamp: new Date().toISOString()
    };

    // Notify user
    this.io.to(`user_${userId}`).emit(event, data);
    
    // Notify admins
    this.io.to('admin_room').emit('admin_withdrawal_created', data);
    
    logger.info(`Withdrawal created notification sent: ${transaction.transactionId}`);
  }

  /**
   * Notify about withdrawal approval
   */
  public notifyWithdrawalApproved(userId: string, transaction: any): void {
    const event = 'withdrawal_approved';
    const data = {
      transactionId: transaction.transactionId,
      userId,
      cryptocurrency: transaction.cryptocurrency,
      amount: transaction.amount,
      netAmount: transaction.netAmount,
      approvedAt: transaction.approvedAt,
      timestamp: new Date().toISOString()
    };

    // Notify user
    this.io.to(`user_${userId}`).emit(event, data);
    
    // Notify admins
    this.io.to('admin_room').emit('admin_withdrawal_approved', data);
    
    logger.info(`Withdrawal approved notification sent: ${transaction.transactionId}`);
  }

  /**
   * Notify about transaction amount updates (admin panel)
   */
  public notifyTransactionAmountUpdated(userId: string, transaction: any, previousAmount: number): void {
    const event = 'transaction_amount_updated';
    const data = {
      transactionId: transaction.transactionId,
      userId,
      type: transaction.type,
      cryptocurrency: transaction.cryptocurrency,
      previousAmount,
      newAmount: transaction.amount,
      updatedBy: transaction.adminUserId,
      timestamp: new Date().toISOString()
    };

    // Notify user with wallet sync trigger
    this.io.to(`user_${userId}`).emit(event, data);
    this.io.to(`wallet_${userId}`).emit('wallet_sync_required', data);

    // Notify all admins for cross-section updates
    this.io.to('admin_room').emit('admin_transaction_amount_updated', data);
    
    logger.info(`Transaction amount update notification sent: ${transaction.transactionId}`);
  }

  /**
   * Notify about wallet balance changes
   */
  public notifyWalletBalanceUpdated(userId: string, balanceData: any): void {
    const event = 'wallet_balance_updated';
    
    // Notify user
    this.io.to(`user_${userId}`).emit(event, balanceData);
    this.io.to(`wallet_${userId}`).emit(event, balanceData);
    
    logger.info(`Wallet balance update notification sent to user: ${userId}`);
  }

  /**
   * Notify about investment package creation
   */
  public notifyInvestmentCreated(userId: string, investmentData: any): void {
    const event = 'investment_created';
    
    // Notify user
    this.io.to(`user_${userId}`).emit(event, investmentData);
    
    // Notify admins
    this.io.to('admin_room').emit('admin_investment_created', investmentData);
    
    logger.info(`Investment creation notification sent: ${investmentData.packageId}`);
  }

  /**
   * Broadcast system-wide notifications
   */
  public broadcastSystemNotification(message: string, type: 'info' | 'warning' | 'error' = 'info'): void {
    const data = {
      message,
      type,
      timestamp: new Date().toISOString()
    };

    this.io.emit('system_notification', data);
    logger.info(`System notification broadcasted: ${message}`);
  }

  /**
   * Get connection statistics
   */
  public getConnectionStats(): any {
    return {
      totalConnections: this.connectedUsers.size,
      adminConnections: this.adminSockets.size,
      uniqueUsers: this.userRooms.size,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get Socket.IO instance for external use
   */
  public getIO(): SocketIOServer {
    return this.io;
  }
}

// Singleton instance
let enhancedSocketService: EnhancedSocketService;

export const initializeEnhancedSocketService = (server: HttpServer): EnhancedSocketService => {
  if (!enhancedSocketService) {
    enhancedSocketService = new EnhancedSocketService(server);
  }
  return enhancedSocketService;
};

export const getEnhancedSocketService = (): EnhancedSocketService => {
  if (!enhancedSocketService) {
    throw new Error('Enhanced Socket Service not initialized');
  }
  return enhancedSocketService;
};

export default EnhancedSocketService;
