import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@chakra-ui/react';
import axios from 'axios';

interface ApiConfig {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  body?: any;
  headers?: Record<string, string>;
  withCredentials?: boolean;
  cacheTime?: number;
  retryCount?: number;
  retryDelay?: number;
}

interface CacheItem {
  data: any;
  timestamp: number;
}

const cache = new Map<string, CacheItem>();

export const useApi = <T>({
  url,
  method = 'GET',
  body,
  headers = {},
  withCredentials = true,
  cacheTime = 5 * 60 * 1000, // 5 minutes default cache time
  retryCount = 3,
  retryDelay = 1000,
}: ApiConfig) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const toast = useToast();

  const clearCache = useCallback(() => {
    cache.delete(url);
  }, [url]);

  const fetchData = useCallback(async (retries = retryCount) => {
    try {
      setLoading(true);
      setError(null);

      // Check cache first
      const cachedData = cache.get(url);
      if (cachedData && Date.now() - cachedData.timestamp < cacheTime) {
        setData(cachedData.data);
        setLoading(false);
        return;
      }

      // Ensure URL doesn't start with a slash if it's being appended to API_URL
      const apiUrl = import.meta.env.VITE_API_URL || '';
      const requestUrl = url.startsWith('/') ? `${apiUrl}${url.substring(1)}` : `${apiUrl}${url}`;

      const response = await axios({
        url: requestUrl,
        method,
        data: body,
        headers: {
          'Content-Type': 'application/json',
          ...headers,
        },
        withCredentials,
      });

      // Cache the response
      cache.set(url, {
        data: response.data,
        timestamp: Date.now(),
      });

      setData(response.data);
    } catch (err: any) {
      if (retries > 0 && err.response?.status >= 500) {
        // Retry on server errors
        setTimeout(() => {
          fetchData(retries - 1);
        }, retryDelay);
        return;
      }

      setError(err);
      toast({
        title: 'Error',
        description: err.response?.data?.message || 'Something went wrong',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  }, [url, method, body, headers, withCredentials, cacheTime, retryCount, retryDelay, toast]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    clearCache,
  };
};