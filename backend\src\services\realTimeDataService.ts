import { logger } from '../utils/logger';
import { getSocketService } from './socketService';

/**
 * Service for handling real-time data updates across the application
 */
class RealTimeDataService {
  /**
   * Notify about a new investment to both user and admin
   * @param investment The investment data
   */
  async notifyNewInvestment(investment: any): Promise<void> {
    try {
      const socketService = getSocketService();
      const userId = investment.userId?.toString();
      
      if (!userId) {
        logger.error('Cannot notify about investment: missing userId', { investment });
        return;
      }
      
      // Format investment data for notification
      const formattedInvestment = {
        id: investment._id?.toString(),
        userId: userId,
        amount: investment.amount,
        currency: investment.currency,
        status: investment.status,
        packageId: investment.packageId?.toString(),
        packageName: investment.packageName,
        createdAt: investment.createdAt,
        duration: investment.duration,
        interestRate: investment.interestRate,
        expectedReturn: investment.expectedReturn
      };
      
      // Notify user about their new investment
      socketService.broadcastToUserRoom(userId, 'investment_updates', {
        type: 'investment_created',
        payload: formattedInvestment
      });
      
      // Notify admins about new investment
      socketService.broadcastToAdminRoom('investment_updates', {
        type: 'new_investment',
        payload: {
          ...formattedInvestment,
          userName: investment.userName || 'Unknown User',
          userEmail: investment.userEmail || '<EMAIL>'
        }
      });
      
      logger.info(`Real-time notification sent for new investment ${investment._id}`, {
        userId,
        amount: investment.amount,
        currency: investment.currency
      });
    } catch (error) {
      logger.error('Error sending real-time investment notification:', error);
    }
  }
  
  /**
   * Notify about a deposit transaction to both user and admin
   * @param deposit The deposit transaction data
   */
  async notifyNewDeposit(deposit: any): Promise<void> {
    try {
      const socketService = getSocketService();
      const userId = deposit.userId?.toString();
      
      if (!userId) {
        logger.error('Cannot notify about deposit: missing userId', { deposit });
        return;
      }
      
      // Format deposit data for notification
      const formattedDeposit = {
        id: deposit._id?.toString(),
        userId: userId,
        amount: deposit.amount,
        asset: deposit.asset || deposit.currency,
        status: deposit.status,
        createdAt: deposit.createdAt,
        txHash: deposit.txHash,
        walletAddress: deposit.walletAddress,
        blockchainNetwork: deposit.blockchainNetwork || deposit.network
      };
      
      // Notify user about their new deposit
      socketService.broadcastToUserRoom(userId, 'deposit_updates', {
        type: 'deposit_created',
        payload: formattedDeposit
      });
      
      // Notify user about transaction update
      socketService.broadcastToUserRoom(userId, 'transaction_updates', {
        type: 'transaction_update',
        payload: {
          ...formattedDeposit,
          type: 'deposit'
        }
      });
      
      // Notify admins about new deposit
      socketService.notifyAdminsAboutNewDeposit(deposit);
      
      logger.info(`Real-time notification sent for new deposit ${deposit._id}`, {
        userId,
        amount: deposit.amount,
        asset: deposit.asset || deposit.currency
      });
    } catch (error) {
      logger.error('Error sending real-time deposit notification:', error);
    }
  }
  
  /**
   * Notify about a deposit status update to both user and admin
   * @param deposit The updated deposit transaction data
   */
  async notifyDepositStatusUpdate(deposit: any): Promise<void> {
    try {
      const socketService = getSocketService();
      const userId = deposit.userId?.toString();
      
      if (!userId) {
        logger.error('Cannot notify about deposit update: missing userId', { deposit });
        return;
      }
      
      // Format deposit data for notification
      const formattedDeposit = {
        id: deposit._id?.toString(),
        userId: userId,
        amount: deposit.amount,
        asset: deposit.asset || deposit.currency,
        status: deposit.status,
        createdAt: deposit.createdAt,
        updatedAt: deposit.updatedAt || new Date(),
        txHash: deposit.txHash,
        walletAddress: deposit.walletAddress,
        blockchainNetwork: deposit.blockchainNetwork || deposit.network
      };
      
      // Notify user about their deposit status update
      socketService.broadcastToUserRoom(userId, 'deposit_updates', {
        type: 'deposit_updated',
        payload: formattedDeposit
      });
      
      // Notify user about transaction update
      socketService.broadcastToUserRoom(userId, 'transaction_updates', {
        type: 'transaction_update',
        payload: {
          ...formattedDeposit,
          type: 'deposit'
        }
      });
      
      // If deposit is approved, notify user about wallet update
      if (deposit.status === 'approved' || deposit.status === 'completed') {
        socketService.broadcastToUserRoom(userId, 'wallet_updates', {
          type: 'wallet_updated',
          payload: {
            asset: deposit.asset || deposit.currency,
            transactionType: 'deposit',
            amount: deposit.amount,
            timestamp: new Date()
          }
        });
      }
      
      // Notify admins about deposit update
      socketService.notifyAdminsAboutDepositUpdate(deposit);
      
      logger.info(`Real-time notification sent for deposit status update ${deposit._id}`, {
        userId,
        status: deposit.status,
        amount: deposit.amount,
        asset: deposit.asset || deposit.currency
      });
    } catch (error) {
      logger.error('Error sending real-time deposit status update notification:', error);
    }
  }
  
  /**
   * Notify about a withdrawal request to both user and admin
   * @param withdrawal The withdrawal transaction data
   */
  async notifyNewWithdrawal(withdrawal: any): Promise<void> {
    try {
      const socketService = getSocketService();
      const userId = withdrawal.userId?.toString();
      
      if (!userId) {
        logger.error('Cannot notify about withdrawal: missing userId', { withdrawal });
        return;
      }
      
      // Format withdrawal data for notification
      const formattedWithdrawal = {
        id: withdrawal._id?.toString(),
        userId: userId,
        amount: withdrawal.amount,
        asset: withdrawal.asset || withdrawal.currency,
        status: withdrawal.status,
        createdAt: withdrawal.createdAt,
        txHash: withdrawal.txHash,
        walletAddress: withdrawal.walletAddress,
        blockchainNetwork: withdrawal.blockchainNetwork || withdrawal.network
      };
      
      // Notify user about their new withdrawal
      socketService.broadcastToUserRoom(userId, 'withdrawal_updates', {
        type: 'withdrawal_created',
        payload: formattedWithdrawal
      });
      
      // Notify user about transaction update
      socketService.broadcastToUserRoom(userId, 'transaction_updates', {
        type: 'transaction_update',
        payload: {
          ...formattedWithdrawal,
          type: 'withdrawal'
        }
      });
      
      // Notify user about wallet update (balance already deducted)
      socketService.broadcastToUserRoom(userId, 'wallet_updates', {
        type: 'wallet_updated',
        payload: {
          asset: withdrawal.asset || withdrawal.currency,
          transactionType: 'withdrawal',
          amount: -withdrawal.amount,
          timestamp: new Date()
        }
      });
      
      // Notify admins about new withdrawal
      socketService.notifyAdminsAboutNewWithdrawal(withdrawal);
      
      logger.info(`Real-time notification sent for new withdrawal ${withdrawal._id}`, {
        userId,
        amount: withdrawal.amount,
        asset: withdrawal.asset || withdrawal.currency
      });
    } catch (error) {
      logger.error('Error sending real-time withdrawal notification:', error);
    }
  }
  
  /**
   * Notify about a withdrawal status update to both user and admin
   * @param withdrawal The updated withdrawal transaction data
   * @param previousStatus The previous status before update
   */
  async notifyWithdrawalStatusUpdate(withdrawal: any, previousStatus?: string): Promise<void> {
    try {
      const socketService = getSocketService();
      const userId = withdrawal.userId?.toString();
      
      if (!userId) {
        logger.error('Cannot notify about withdrawal update: missing userId', { withdrawal });
        return;
      }
      
      // Format withdrawal data for notification
      const formattedWithdrawal = {
        id: withdrawal._id?.toString(),
        userId: userId,
        amount: withdrawal.amount,
        asset: withdrawal.asset || withdrawal.currency,
        status: withdrawal.status,
        createdAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt || new Date(),
        txHash: withdrawal.txHash,
        walletAddress: withdrawal.walletAddress,
        blockchainNetwork: withdrawal.blockchainNetwork || withdrawal.network,
        previousStatus
      };
      
      // Notify user about their withdrawal status update
      socketService.broadcastToUserRoom(userId, 'withdrawal_updates', {
        type: 'withdrawal_updated',
        payload: formattedWithdrawal
      });
      
      // Notify user about transaction update
      socketService.broadcastToUserRoom(userId, 'transaction_updates', {
        type: 'transaction_update',
        payload: {
          ...formattedWithdrawal,
          type: 'withdrawal'
        }
      });
      
      // If withdrawal is rejected, notify user about wallet update (refund)
      if (withdrawal.status === 'rejected' && previousStatus !== 'rejected') {
        socketService.broadcastToUserRoom(userId, 'wallet_updates', {
          type: 'wallet_updated',
          payload: {
            asset: withdrawal.asset || withdrawal.currency,
            transactionType: 'refund',
            amount: withdrawal.amount,
            timestamp: new Date()
          }
        });
      }
      
      // Notify admins about withdrawal update
      socketService.broadcastToAdminRoom('withdrawal_updates', {
        type: 'withdrawal_updated',
        payload: {
          ...formattedWithdrawal,
          userName: withdrawal.userName || 'Unknown User',
          userEmail: withdrawal.userEmail || '<EMAIL>'
        }
      });
      
      logger.info(`Real-time notification sent for withdrawal status update ${withdrawal._id}`, {
        userId,
        status: withdrawal.status,
        previousStatus,
        amount: withdrawal.amount,
        asset: withdrawal.asset || withdrawal.currency
      });
    } catch (error) {
      logger.error('Error sending real-time withdrawal status update notification:', error);
    }
  }
  
  /**
   * Notify about wallet balance update
   * @param userId User ID
   * @param asset Asset symbol
   * @param balance New balance
   */
  async notifyWalletUpdate(userId: string, asset: string, balance: number): Promise<void> {
    try {
      const socketService = getSocketService();
      
      // Notify user about wallet update
      socketService.broadcastToUserRoom(userId, 'wallet_updates', {
        type: 'wallet_balance_updated',
        payload: {
          asset,
          balance,
          timestamp: new Date()
        }
      });
      
      logger.info(`Real-time notification sent for wallet update`, {
        userId,
        asset,
        balance
      });
    } catch (error) {
      logger.error('Error sending real-time wallet update notification:', error);
    }
  }
}

export const realTimeDataService = new RealTimeDataService();
export default realTimeDataService;