import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * CORS Debug Middleware
 * Helps debug CORS issues by logging request details
 */
export const corsDebugMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Only log CORS-related requests in development
  if (process.env.NODE_ENV === 'development') {
    const corsInfo = {
      method: req.method,
      path: req.path,
      origin: req.headers.origin,
      host: req.headers.host,
      userAgent: req.headers['user-agent']?.substring(0, 50),
      referer: req.headers.referer,
      contentType: req.headers['content-type'],
      isPreflightRequest: req.method === 'OPTIONS'
    };

    // Log preflight requests
    if (req.method === 'OPTIONS') {
      logger.info('🔄 CORS Preflight Request:', corsInfo);
    } else if (req.headers.origin) {
      logger.info('🌐 CORS Request:', corsInfo);
    }
  }

  // Add CORS debug headers in development
  if (process.env.NODE_ENV === 'development') {
    res.setHeader('X-CORS-Debug', 'enabled');
    res.setHeader('X-Server-Time', new Date().toISOString());
  }

  next();
};

/**
 * CORS Response Logger
 * Logs CORS response headers for debugging
 */
export const corsResponseLogger = (req: Request, res: Response, next: NextFunction) => {
  if (process.env.NODE_ENV === 'development') {
    const originalSend = res.send;
    
    res.send = function(body) {
      // Log CORS response headers
      if (req.headers.origin || req.method === 'OPTIONS') {
        logger.info('📤 CORS Response Headers:', {
          path: req.path,
          method: req.method,
          statusCode: res.statusCode,
          corsHeaders: {
            'Access-Control-Allow-Origin': res.getHeader('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': res.getHeader('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': res.getHeader('Access-Control-Allow-Headers'),
            'Access-Control-Allow-Credentials': res.getHeader('Access-Control-Allow-Credentials'),
            'Access-Control-Max-Age': res.getHeader('Access-Control-Max-Age')
          }
        });
      }
      
      return originalSend.call(this, body);
    };
  }
  
  next();
};
