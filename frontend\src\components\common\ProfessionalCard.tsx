import React from 'react';
import {
  Box,
  Flex,
  Text,
  Icon,
  Badge,
  HStack,
  VStack,
  Button,
  useColorModeValue,
  Tooltip,
  Divider,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { IconType } from 'react-icons';

const MotionBox = motion(Box);

interface ProfessionalCardProps {
  // Header props
  title: string;
  subtitle?: string;
  icon?: IconType;
  iconColor?: string;
  status?: {
    label: string;
    color: string;
    variant?: 'solid' | 'outline' | 'subtle';
  };
  
  // Content props
  primaryValue?: {
    label: string;
    value: string;
    color?: string;
  };
  secondaryValue?: {
    label: string;
    value: string;
    color?: string;
  };
  
  // Stats props
  stats?: Array<{
    label: string;
    value: string;
    color?: string;
    icon?: IconType;
  }>;
  
  // Action buttons
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
    icon?: IconType;
    isDisabled?: boolean;
    tooltip?: string;
  }>;
  
  // Card styling
  variant?: 'default' | 'elevated' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  accentColor?: string;
  isLoading?: boolean;
  
  // Layout
  orientation?: 'vertical' | 'horizontal';
  
  // Interaction
  onClick?: () => void;
  isHoverable?: boolean;
}

const ProfessionalCard: React.FC<ProfessionalCardProps> = ({
  title,
  subtitle,
  icon,
  iconColor = '#FCD535',
  status,
  primaryValue,
  secondaryValue,
  stats = [],
  actions = [],
  variant = 'default',
  size = 'md',
  accentColor = '#FCD535',
  isLoading = false,
  orientation = 'vertical',
  onClick,
  isHoverable = true,
}) => {
  // Theme colors
  const bgColor = useColorModeValue('#FFFFFF', '#1E2026');
  const borderColor = useColorModeValue('#E2E8F0', '#2B3139');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#718096', '#848E9C');
  const cardBgColor = useColorModeValue('#FAFAFA', '#0B0E11');

  // Size configurations
  const sizeConfig = {
    sm: {
      padding: 4,
      borderRadius: 'lg',
      titleSize: 'md',
      subtitleSize: 'sm',
      spacing: 3,
    },
    md: {
      padding: 6,
      borderRadius: 'xl',
      titleSize: 'lg',
      subtitleSize: 'md',
      spacing: 4,
    },
    lg: {
      padding: 8,
      borderRadius: '2xl',
      titleSize: 'xl',
      subtitleSize: 'lg',
      spacing: 5,
    },
  };

  const config = sizeConfig[size];

  // Variant configurations
  const variantConfig = {
    default: {
      bg: bgColor,
      borderWidth: '1px',
      borderColor: borderColor,
      shadow: 'sm',
      hoverShadow: 'lg',
    },
    elevated: {
      bg: bgColor,
      borderWidth: '0px',
      borderColor: 'transparent',
      shadow: 'lg',
      hoverShadow: 'xl',
    },
    minimal: {
      bg: 'transparent',
      borderWidth: '1px',
      borderColor: borderColor,
      shadow: 'none',
      hoverShadow: 'sm',
    },
  };

  const variantStyle = variantConfig[variant];

  // Button variant styles
  const getButtonStyle = (buttonVariant: string = 'primary') => {
    switch (buttonVariant) {
      case 'primary':
        return {
          bg: 'linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)',
          color: '#0B0E11',
          _hover: {
            bg: 'linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)',
            boxShadow: '0 4px 15px rgba(240, 185, 11, 0.3)',
          },
          _active: {
            bg: 'linear-gradient(135deg, #E6C200 0%, #F0B90B 100%)',
            transform: 'scale(0.98)',
          },
        };
      case 'secondary':
        return {
          bg: 'rgba(2, 192, 118, 0.1)',
          color: '#02C076',
          borderWidth: '1px',
          borderColor: '#02C076',
          _hover: {
            bg: 'rgba(2, 192, 118, 0.2)',
            boxShadow: '0 4px 15px rgba(2, 192, 118, 0.2)',
          },
          _active: {
            bg: 'rgba(2, 192, 118, 0.3)',
            transform: 'scale(0.98)',
          },
        };
      case 'outline':
        return {
          variant: 'outline',
          borderColor: borderColor,
          color: textColor,
          _hover: {
            borderColor: accentColor,
            color: accentColor,
          },
        };
      default:
        return {};
    }
  };

  return (
    <MotionBox
      bg={variantStyle.bg}
      borderWidth={variantStyle.borderWidth}
      borderColor={variantStyle.borderColor}
      borderRadius={config.borderRadius}
      p={config.padding}
      boxShadow={variantStyle.shadow}
      position="relative"
      overflow="hidden"
      cursor={onClick ? 'pointer' : 'default'}
      transition="all 0.3s ease"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={
        isHoverable
          ? {
              y: -4,
              boxShadow: variantStyle.hoverShadow,
              borderColor: accentColor,
            }
          : {}
      }
      onClick={onClick}
      _before={{
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '3px',
        bg: `linear-gradient(90deg, ${accentColor} 0%, ${accentColor}80 100%)`,
        borderTopRadius: config.borderRadius,
      }}
    >
      {/* Header Section */}
      <Flex
        direction={orientation === 'horizontal' ? 'row' : 'column'}
        align={orientation === 'horizontal' ? 'center' : 'flex-start'}
        justify="space-between"
        mb={config.spacing}
      >
        <HStack spacing={3} flex={1} minW={0}>
          {icon && (
            <Flex
              bg={`${iconColor}20`}
              p={3}
              borderRadius="lg"
              align="center"
              justify="center"
              flexShrink={0}
            >
              <Icon as={icon} color={iconColor} boxSize={6} />
            </Flex>
          )}
          
          <VStack align="start" spacing={1} flex={1} minW={0}>
            <Text
              color={textColor}
              fontSize={config.titleSize}
              fontWeight="bold"
              lineHeight="1.2"
              noOfLines={1}
            >
              {title}
            </Text>
            {subtitle && (
              <Text
                color={secondaryTextColor}
                fontSize={config.subtitleSize}
                lineHeight="1.2"
                noOfLines={1}
              >
                {subtitle}
              </Text>
            )}
          </VStack>
        </HStack>

        {status && (
          <Badge
            colorScheme={status.color}
            variant={status.variant || 'solid'}
            borderRadius="md"
            px={3}
            py={1}
            fontSize="xs"
            fontWeight="600"
            flexShrink={0}
            ml={orientation === 'horizontal' ? 3 : 0}
            mt={orientation === 'vertical' ? 2 : 0}
          >
            {status.label}
          </Badge>
        )}
      </Flex>

      {/* Primary and Secondary Values */}
      {(primaryValue || secondaryValue) && (
        <Flex
          direction={orientation === 'horizontal' ? 'row' : 'column'}
          gap={config.spacing}
          mb={config.spacing}
        >
          {primaryValue && (
            <VStack align="start" spacing={1} flex={1}>
              <Text
                color={secondaryTextColor}
                fontSize="sm"
                fontWeight="500"
                lineHeight="1.2"
              >
                {primaryValue.label}
              </Text>
              <Text
                color={primaryValue.color || textColor}
                fontSize="xl"
                fontWeight="bold"
                lineHeight="1.2"
                noOfLines={1}
              >
                {primaryValue.value}
              </Text>
            </VStack>
          )}

          {secondaryValue && (
            <VStack align="start" spacing={1} flex={1}>
              <Text
                color={secondaryTextColor}
                fontSize="sm"
                fontWeight="500"
                lineHeight="1.2"
              >
                {secondaryValue.label}
              </Text>
              <Text
                color={secondaryValue.color || textColor}
                fontSize="lg"
                fontWeight="bold"
                lineHeight="1.2"
                noOfLines={1}
              >
                {secondaryValue.value}
              </Text>
            </VStack>
          )}
        </Flex>
      )}

      {/* Stats Section */}
      {stats.length > 0 && (
        <>
          <Divider borderColor={borderColor} mb={config.spacing} />
          <VStack spacing={2} align="stretch" mb={config.spacing}>
            {stats.map((stat, index) => (
              <Flex key={index} justify="space-between" align="center">
                <HStack spacing={2}>
                  {stat.icon && (
                    <Icon as={stat.icon} color={accentColor} boxSize={4} />
                  )}
                  <Text
                    color={secondaryTextColor}
                    fontSize="sm"
                    fontWeight="500"
                  >
                    {stat.label}
                  </Text>
                </HStack>
                <Text
                  color={stat.color || textColor}
                  fontSize="sm"
                  fontWeight="bold"
                >
                  {stat.value}
                </Text>
              </Flex>
            ))}
          </VStack>
        </>
      )}

      {/* Actions Section */}
      {actions.length > 0 && (
        <Flex
          direction={actions.length > 2 ? 'column' : 'row'}
          gap={3}
          mt={config.spacing}
        >
          {actions.map((action, index) => {
            const buttonContent = (
              <Button
                key={index}
                leftIcon={action.icon ? <Icon as={action.icon} boxSize={4} /> : undefined}
                size="sm"
                flex={actions.length <= 2 ? 1 : undefined}
                w={actions.length > 2 ? 'full' : undefined}
                minH="44px"
                fontSize="sm"
                fontWeight="600"
                borderRadius="lg"
                onClick={action.onClick}
                isDisabled={action.isDisabled}
                transition="all 0.3s ease"
                {...getButtonStyle(action.variant)}
                sx={{
                  '@media (max-width: 767px)': {
                    touchAction: 'manipulation',
                    WebkitTapHighlightColor: 'transparent',
                    WebkitTouchCallout: 'none',
                    cursor: 'pointer',
                    minHeight: '44px',
                    minWidth: '44px',
                  },
                }}
              >
                {action.label}
              </Button>
            );

            return action.tooltip ? (
              <Tooltip key={index} label={action.tooltip} placement="top">
                {buttonContent}
              </Tooltip>
            ) : (
              buttonContent
            );
          })}
        </Flex>
      )}
    </MotionBox>
  );
};

export default ProfessionalCard;
