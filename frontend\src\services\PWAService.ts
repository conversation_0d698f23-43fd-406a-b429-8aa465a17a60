/**
 * Progressive Web App Service
 * Provides PWA functionality, offline support, and mobile optimization
 */

export interface PWAInstallPrompt {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export interface PWACapabilities {
  isInstallable: boolean;
  isInstalled: boolean;
  isOnline: boolean;
  hasNotificationPermission: boolean;
  hasLocationPermission: boolean;
  supportsPushNotifications: boolean;
  supportsBackgroundSync: boolean;
  supportsOfflineMode: boolean;
}

export interface OfflineData {
  userProfile: any;
  walletBalances: any;
  recentTransactions: any[];
  investmentPackages: any[];
  lastSyncTime: number;
}

export class PWAService {
  private static instance: PWAService;
  private installPrompt: PWAInstallPrompt | null = null;
  private serviceWorker: ServiceWorkerRegistration | null = null;
  private isOnline: boolean = navigator.onLine;
  private offlineData: OfflineData | null = null;

  public static getInstance(): PWAService {
    if (!PWAService.instance) {
      PWAService.instance = new PWAService();
    }
    return PWAService.instance;
  }

  constructor() {
    this.initializePWA();
  }

  /**
   * Initialize PWA functionality
   */
  private async initializePWA(): Promise<void> {
    await this.registerServiceWorker();
    this.setupInstallPrompt();
    this.setupOnlineOfflineHandlers();
    this.setupNotificationHandlers();
    this.loadOfflineData();
  }

  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      console.warn('Service Worker not supported');
      return;
    }

    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      this.serviceWorker = registration;

      // Handle service worker updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              this.notifyAppUpdate();
            }
          });
        }
      });

      console.log('Service Worker registered successfully');
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }

  /**
   * Setup install prompt handling
   */
  private setupInstallPrompt(): void {
    window.addEventListener('beforeinstallprompt', (event) => {
      event.preventDefault();
      this.installPrompt = event as any;
      this.notifyInstallAvailable();
    });

    window.addEventListener('appinstalled', () => {
      this.installPrompt = null;
      this.notifyAppInstalled();
    });
  }

  /**
   * Setup online/offline handlers
   */
  private setupOnlineOfflineHandlers(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.handleOnline();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.handleOffline();
    });
  }

  /**
   * Setup notification handlers
   */
  private setupNotificationHandlers(): void {
    if ('Notification' in window) {
      // Request notification permission if not granted
      if (Notification.permission === 'default') {
        this.requestNotificationPermission();
      }
    }
  }

  /**
   * Install PWA
   */
  async installPWA(): Promise<boolean> {
    if (!this.installPrompt) {
      console.warn('PWA install prompt not available');
      return false;
    }

    try {
      await this.installPrompt.prompt();
      const choiceResult = await this.installPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('PWA installation accepted');
        return true;
      } else {
        console.log('PWA installation dismissed');
        return false;
      }
    } catch (error) {
      console.error('PWA installation failed:', error);
      return false;
    }
  }

  /**
   * Get PWA capabilities
   */
  getPWACapabilities(): PWACapabilities {
    return {
      isInstallable: !!this.installPrompt,
      isInstalled: window.matchMedia('(display-mode: standalone)').matches,
      isOnline: this.isOnline,
      hasNotificationPermission: Notification.permission === 'granted',
      hasLocationPermission: false, // Would need to check geolocation permission
      supportsPushNotifications: 'PushManager' in window,
      supportsBackgroundSync: 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype,
      supportsOfflineMode: 'serviceWorker' in navigator && 'caches' in window
    };
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('Notifications not supported');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    } catch (error) {
      console.error('Notification permission request failed:', error);
      return false;
    }
  }

  /**
   * Send push notification
   */
  async sendNotification(title: string, options: NotificationOptions = {}): Promise<void> {
    if (Notification.permission !== 'granted') {
      console.warn('Notification permission not granted');
      return;
    }

    try {
      const notification = new Notification(title, {
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        ...options
      });

      notification.onclick = () => {
        window.focus();
        notification.close();
      };
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  }

  /**
   * Cache critical data for offline use
   */
  async cacheOfflineData(data: Partial<OfflineData>): Promise<void> {
    const offlineData: OfflineData = {
      userProfile: null,
      walletBalances: null,
      recentTransactions: [],
      investmentPackages: [],
      lastSyncTime: Date.now(),
      ...this.offlineData,
      ...data
    };

    try {
      localStorage.setItem('cryptoyield_offline_data', JSON.stringify(offlineData));
      this.offlineData = offlineData;
    } catch (error) {
      console.error('Failed to cache offline data:', error);
    }
  }

  /**
   * Load offline data
   */
  private loadOfflineData(): void {
    try {
      const cached = localStorage.getItem('cryptoyield_offline_data');
      if (cached) {
        this.offlineData = JSON.parse(cached);
      }
    } catch (error) {
      console.error('Failed to load offline data:', error);
    }
  }

  /**
   * Get offline data
   */
  getOfflineData(): OfflineData | null {
    return this.offlineData;
  }

  /**
   * Handle online event
   */
  private async handleOnline(): Promise<void> {
    console.log('App is online');
    
    // Sync offline data
    await this.syncOfflineData();
    
    // Notify user
    this.sendNotification('Connection Restored', {
      body: 'Your data is being synchronized...',
      tag: 'connection-restored'
    });
  }

  /**
   * Handle offline event
   */
  private handleOffline(): void {
    console.log('App is offline');
    
    // Notify user
    this.sendNotification('Offline Mode', {
      body: 'You can still view your cached data',
      tag: 'offline-mode'
    });
  }

  /**
   * Sync offline data when back online
   */
  private async syncOfflineData(): Promise<void> {
    if (!this.isOnline || !this.offlineData) return;

    try {
      // Here you would implement actual sync logic
      // For now, just update the sync time
      await this.cacheOfflineData({
        lastSyncTime: Date.now()
      });
      
      console.log('Offline data synced successfully');
    } catch (error) {
      console.error('Failed to sync offline data:', error);
    }
  }

  /**
   * Notify about app update
   */
  private notifyAppUpdate(): void {
    this.sendNotification('App Update Available', {
      body: 'A new version is available. Refresh to update.',
      tag: 'app-update',
      actions: [
        { action: 'refresh', title: 'Refresh Now' },
        { action: 'dismiss', title: 'Later' }
      ]
    });
  }

  /**
   * Notify about install availability
   */
  private notifyInstallAvailable(): void {
    // This would typically trigger a custom install banner
    console.log('PWA install prompt available');
  }

  /**
   * Notify about successful installation
   */
  private notifyAppInstalled(): void {
    this.sendNotification('App Installed', {
      body: 'CryptoYield has been installed successfully!',
      tag: 'app-installed'
    });
  }

  /**
   * Update service worker
   */
  async updateServiceWorker(): Promise<void> {
    if (!this.serviceWorker) return;

    try {
      await this.serviceWorker.update();
      console.log('Service Worker updated');
    } catch (error) {
      console.error('Service Worker update failed:', error);
    }
  }

  /**
   * Clear cache
   */
  async clearCache(): Promise<void> {
    if (!('caches' in window)) return;

    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('Cache cleared');
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }

  /**
   * Get cache size
   */
  async getCacheSize(): Promise<number> {
    if (!('caches' in window)) return 0;

    try {
      const cacheNames = await caches.keys();
      let totalSize = 0;

      for (const cacheName of cacheNames) {
        const cache = await caches.open(cacheName);
        const requests = await cache.keys();
        
        for (const request of requests) {
          const response = await cache.match(request);
          if (response) {
            const blob = await response.blob();
            totalSize += blob.size;
          }
        }
      }

      return totalSize;
    } catch (error) {
      console.error('Failed to calculate cache size:', error);
      return 0;
    }
  }

  /**
   * Check if app is running in standalone mode
   */
  isStandalone(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true;
  }

  /**
   * Get device information
   */
  getDeviceInfo(): any {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      hardwareConcurrency: navigator.hardwareConcurrency,
      maxTouchPoints: navigator.maxTouchPoints,
      standalone: this.isStandalone(),
      displayMode: window.matchMedia('(display-mode: standalone)').matches ? 'standalone' : 'browser'
    };
  }
}
