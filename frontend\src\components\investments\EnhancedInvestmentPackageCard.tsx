import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Card,
  CardBody,
  VStack,
  HStack,
  Text,
  Badge,
  Button,
  Icon,
  Progress,
  Flex,
  Divider,
  useToast,
  Tooltip,
  CircularProgress,
  CircularProgressLabel,
  Alert,
  AlertIcon
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FaCoins,
  FaClock,
  FaArrowUp,
  FaWallet,
  FaLock,
  FaUnlock,
  FaCalculator,
  FaChartLine
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { useResponsive } from '../../hooks/useResponsive';

const MotionCard = motion(Card);

interface InvestmentPackage {
  id: string;
  currency: string;
  amount: number;
  totalEarned: number;
  dailyInterest: number;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  activatedAt: Date;
  nextInterestTime: Date;
  withdrawalEligibleTime: Date;
  minimumWithdrawalUSDT: number;
  realTimeUSDTValue: number;
  activeDays: number;
  canWithdraw: boolean;
  interestRate: number;
}

interface EnhancedInvestmentPackageCardProps {
  package: InvestmentPackage;
  onWithdraw?: (packageId: string) => void;
  onViewDetails?: (packageId: string) => void;
}

const EnhancedInvestmentPackageCard: React.FC<EnhancedInvestmentPackageCardProps> = ({
  package: pkg,
  onWithdraw,
  onViewDetails
}) => {
  const { t } = useTranslation();
  const { isMobile } = useResponsive();
  const toast = useToast();

  // State for countdown timer
  const [timeUntilNext, setTimeUntilNext] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  const [timeUntilWithdrawal, setTimeUntilWithdrawal] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0,
    isAllowed: false
  });

  // Colors
  const bgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const borderColor = "#2B3139";
  const successColor = "#0ECB81";
  const errorColor = "#F6465D";

  // Memoize next interest time to prevent unnecessary re-renders
  const nextInterestTime = useMemo(() => new Date(pkg.nextInterestTime), [pkg.nextInterestTime]);

  // Update countdown timers
  useEffect(() => {
    if (!pkg.nextInterestTime) return;

    const updateTimers = () => {
      const now = new Date();

      // Calculate time until next interest
      const diffInterest = nextInterestTime.getTime() - now.getTime();

      if (diffInterest > 0) {
        const hours = Math.floor(diffInterest / (1000 * 60 * 60));
        const minutes = Math.floor((diffInterest % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diffInterest % (1000 * 60)) / 1000);

        setTimeUntilNext({ hours, minutes, seconds });
      } else {
        setTimeUntilNext({ hours: 0, minutes: 0, seconds: 0 });
      }

      // Calculate time until withdrawal allowed
      const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000)); // UTC+3
      const today3AM = new Date(turkeyTime);
      today3AM.setHours(3, 0, 0, 0);

      if (turkeyTime.getHours() < 3) {
        // Before 03:00, show time until 03:00 today
        const diffWithdrawal = today3AM.getTime() - turkeyTime.getTime();
        if (diffWithdrawal > 0) {
          const hours = Math.floor(diffWithdrawal / (1000 * 60 * 60));
          const minutes = Math.floor((diffWithdrawal % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((diffWithdrawal % (1000 * 60)) / 1000);

          setTimeUntilWithdrawal({ hours, minutes, seconds, isAllowed: false });
        } else {
          setTimeUntilWithdrawal({ hours: 0, minutes: 0, seconds: 0, isAllowed: true });
        }
      } else {
        // After 03:00, withdrawal is allowed
        setTimeUntilWithdrawal({ hours: 0, minutes: 0, seconds: 0, isAllowed: true });
      }
    };

    updateTimers();
    const interval = setInterval(updateTimers, 1000);

    return () => clearInterval(interval);
  }, [nextInterestTime]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return successColor;
      case 'pending': return primaryColor;
      case 'completed': return '#3375BB';
      case 'withdrawn': return '#848E9C';
      default: return textColor;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Active';
      case 'pending': return 'Pending';
      case 'completed': return 'Completed';
      case 'withdrawn': return 'Withdrawn';
      default: return status;
    }
  };

  const formatTime = (time: { hours: number; minutes: number; seconds: number }) => {
    if (time.hours > 0) {
      return `${time.hours}h ${time.minutes}m`;
    } else if (time.minutes > 0) {
      return `${time.minutes}m ${time.seconds}s`;
    } else {
      return `${time.seconds}s`;
    }
  };

  const calculateProgress = () => {
    const totalTime = 24 * 60 * 60; // 24 hours in seconds
    const remainingTime = timeUntilNext.hours * 3600 + timeUntilNext.minutes * 60 + timeUntilNext.seconds;
    return ((totalTime - remainingTime) / totalTime) * 100;
  };

  const handleWithdraw = () => {
    if (!pkg.canWithdraw) {
      toast({
        title: 'Withdrawal Not Available',
        description: 'Minimum withdrawal amount not reached or time lock active',
        status: 'warning',
        duration: 3000,
      });
      return;
    }

    if (!timeUntilWithdrawal.isAllowed) {
      toast({
        title: 'Withdrawal Time Lock',
        description: `Withdrawals are only allowed after 03:00 UTC+3. Time remaining: ${formatTime(timeUntilWithdrawal)}`,
        status: 'warning',
        duration: 5000,
      });
      return;
    }

    onWithdraw?.(pkg.id);
  };

  return (
    <MotionCard
      bg={bgColor}
      borderColor={borderColor}
      borderWidth="1px"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: `0 4px 12px rgba(240, 185, 11, 0.2)`,
        borderColor: primaryColor
      }}
    >
      <CardBody p={isMobile ? 4 : 6}>
        <VStack spacing={4} align="stretch">
          {/* Header */}
          <Flex justify="space-between" align="center">
            <HStack>
              <Icon as={FaCoins} color={primaryColor} />
              <Text fontWeight="bold" color={textColor}>
                {pkg.currency} Package
              </Text>
            </HStack>
            <Badge
              colorScheme={pkg.status === 'active' ? 'green' : pkg.status === 'pending' ? 'yellow' : 'gray'}
              variant="solid"
            >
              {getStatusText(pkg.status)}
            </Badge>
          </Flex>

          {/* Amount and Earnings */}
          <VStack spacing={2} align="stretch">
            <Flex justify="space-between">
              <Text color="#848E9C" fontSize="sm">Principal Amount:</Text>
              <Text color={textColor} fontWeight="bold">
                {pkg.amount.toFixed(6)} {pkg.currency}
              </Text>
            </Flex>
            <Flex justify="space-between">
              <Text color="#848E9C" fontSize="sm">Total Earned:</Text>
              <Text color={successColor} fontWeight="bold">
                {pkg.totalEarned.toFixed(6)} {pkg.currency}
              </Text>
            </Flex>
            <Flex justify="space-between">
              <Text color="#848E9C" fontSize="sm">USDT Value:</Text>
              <Text color={primaryColor} fontWeight="bold">
                ${pkg.realTimeUSDTValue.toFixed(2)}
              </Text>
            </Flex>
          </VStack>

          <Divider borderColor={borderColor} />

          {/* Interest Information */}
          <VStack spacing={3} align="stretch">
            <HStack justify="space-between">
              <HStack>
                <Icon as={FaCalculator} color={primaryColor} size="sm" />
                <Text color={textColor} fontSize="sm">Daily Interest:</Text>
              </HStack>
              <Text color={successColor} fontWeight="bold">
                {(pkg.interestRate * 100).toFixed(1)}%
              </Text>
            </HStack>

            <HStack justify="space-between">
              <HStack>
                <Icon as={FaChartLine} color={primaryColor} size="sm" />
                <Text color={textColor} fontSize="sm">Active Days:</Text>
              </HStack>
              <Text color={textColor} fontWeight="bold">
                {pkg.activeDays}
              </Text>
            </HStack>
          </VStack>

          {/* Next Interest Countdown */}
          {pkg.status === 'active' && (
            <Box>
              <HStack justify="space-between" mb={2}>
                <HStack>
                  <Icon as={FaClock} color={primaryColor} size="sm" />
                  <Text color={textColor} fontSize="sm">Next Interest:</Text>
                </HStack>
                <Text color={primaryColor} fontWeight="bold" fontSize="sm">
                  {formatTime(timeUntilNext)}
                </Text>
              </HStack>
              <Progress
                value={calculateProgress()}
                colorScheme="yellow"
                size="sm"
                borderRadius="md"
                bg={borderColor}
              />
            </Box>
          )}

          {/* Withdrawal Status */}
          <Box>
            {pkg.realTimeUSDTValue >= pkg.minimumWithdrawalUSDT ? (
              timeUntilWithdrawal.isAllowed ? (
                <Alert status="success" bg={`${successColor}20`} borderColor={successColor}>
                  <AlertIcon color={successColor} />
                  <Text color={textColor} fontSize="sm">
                    Withdrawal Available
                  </Text>
                </Alert>
              ) : (
                <Alert status="warning" bg={`${primaryColor}20`} borderColor={primaryColor}>
                  <AlertIcon color={primaryColor} />
                  <VStack align="start" spacing={0}>
                    <Text color={textColor} fontSize="sm">
                      Withdrawal in: {formatTime(timeUntilWithdrawal)}
                    </Text>
                    <Text color="#848E9C" fontSize="xs">
                      Available after 03:00 UTC+3
                    </Text>
                  </VStack>
                </Alert>
              )
            ) : (
              <Alert status="info" bg={`${borderColor}40`} borderColor={borderColor}>
                <AlertIcon color="#848E9C" />
                <VStack align="start" spacing={0}>
                  <Text color={textColor} fontSize="sm">
                    Minimum: ${pkg.minimumWithdrawalUSDT}
                  </Text>
                  <Text color="#848E9C" fontSize="xs">
                    Need ${(pkg.minimumWithdrawalUSDT - pkg.realTimeUSDTValue).toFixed(2)} more
                  </Text>
                </VStack>
              </Alert>
            )}
          </Box>

          {/* Action Buttons */}
          <HStack spacing={3}>
            <Button
              flex={1}
              variant="outline"
              borderColor={borderColor}
              color={textColor}
              _hover={{ borderColor: primaryColor, color: primaryColor }}
              onClick={() => onViewDetails?.(pkg.id)}
              size="sm"
            >
              Details
            </Button>
            <Button
              flex={1}
              bg={pkg.canWithdraw && timeUntilWithdrawal.isAllowed ? successColor : borderColor}
              color={pkg.canWithdraw && timeUntilWithdrawal.isAllowed ? "#0B0E11" : "#848E9C"}
              _hover={{
                bg: pkg.canWithdraw && timeUntilWithdrawal.isAllowed ? "#02A86B" : borderColor
              }}
              onClick={handleWithdraw}
              isDisabled={!pkg.canWithdraw || !timeUntilWithdrawal.isAllowed}
              leftIcon={<Icon as={pkg.canWithdraw && timeUntilWithdrawal.isAllowed ? FaUnlock : FaLock} />}
              size="sm"
            >
              Withdraw
            </Button>
          </HStack>
        </VStack>
      </CardBody>
    </MotionCard>
  );
};

export default React.memo(EnhancedInvestmentPackageCard);
