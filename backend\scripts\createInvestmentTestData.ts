import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../src/models/userModel';
import InvestmentPackage from '../src/models/investmentPackageModel';
import Transaction from '../src/models/transactionModel';
import { db } from '../src/config/database';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

interface TestInvestmentData {
  currency: string;
  amount: number;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  daysAgo: number;
  activeDaysAgo?: number;
}

// Test investment data for different currencies
const testInvestments: TestInvestmentData[] = [
  // BTC Investments
  { currency: 'BTC', amount: 0.5, status: 'active', daysAgo: 45, activeDaysAgo: 40 },
  { currency: 'BTC', amount: 0.25, status: 'active', daysAgo: 30, activeDaysAgo: 25 },
  { currency: 'BTC', amount: 1.0, status: 'completed', daysAgo: 120, activeDaysAgo: 115 },
  { currency: 'BTC', amount: 0.1, status: 'pending', daysAgo: 2 },

  // ETH Investments
  { currency: 'ETH', amount: 5.0, status: 'active', daysAgo: 60, activeDaysAgo: 55 },
  { currency: 'ETH', amount: 2.5, status: 'active', daysAgo: 20, activeDaysAgo: 15 },
  { currency: 'ETH', amount: 10.0, status: 'withdrawn', daysAgo: 90, activeDaysAgo: 85 },
  { currency: 'ETH', amount: 1.5, status: 'pending', daysAgo: 1 },

  // USDT Investments
  { currency: 'USDT', amount: 1000, status: 'active', daysAgo: 35, activeDaysAgo: 30 },
  { currency: 'USDT', amount: 500, status: 'active', daysAgo: 15, activeDaysAgo: 10 },
  { currency: 'USDT', amount: 2000, status: 'active', daysAgo: 75, activeDaysAgo: 70 },
  { currency: 'USDT', amount: 750, status: 'completed', daysAgo: 100, activeDaysAgo: 95 },
  { currency: 'USDT', amount: 300, status: 'pending', daysAgo: 3 },

  // BNB Investments
  { currency: 'BNB', amount: 20, status: 'active', daysAgo: 25, activeDaysAgo: 20 },
  { currency: 'BNB', amount: 50, status: 'active', daysAgo: 50, activeDaysAgo: 45 },
  { currency: 'BNB', amount: 15, status: 'pending', daysAgo: 1 },

  // SOL Investments
  { currency: 'SOL', amount: 100, status: 'active', daysAgo: 40, activeDaysAgo: 35 },
  { currency: 'SOL', amount: 250, status: 'withdrawn', daysAgo: 80, activeDaysAgo: 75 },
  { currency: 'SOL', amount: 75, status: 'pending', daysAgo: 2 },

  // TRX Investments
  { currency: 'TRX', amount: 10000, status: 'active', daysAgo: 55, activeDaysAgo: 50 },
  { currency: 'TRX', amount: 5000, status: 'active', daysAgo: 18, activeDaysAgo: 13 },
  { currency: 'TRX', amount: 15000, status: 'completed', daysAgo: 110, activeDaysAgo: 105 },

  // DOGE Investments
  { currency: 'DOGE', amount: 5000, status: 'active', daysAgo: 28, activeDaysAgo: 23 },
  { currency: 'DOGE', amount: 2500, status: 'pending', daysAgo: 1 },
  { currency: 'DOGE', amount: 7500, status: 'withdrawn', daysAgo: 65, activeDaysAgo: 60 }
];

// Helper function to generate package ID
const generatePackageId = (): string => {
  return `PKG-${Date.now()}-${crypto.randomBytes(4).toString('hex').toUpperCase()}`;
};

// Helper function to generate package hash
const generatePackageHash = (): string => {
  return crypto.randomBytes(32).toString('hex');
};

// Helper function to calculate earnings based on days active
const calculateEarnings = (amount: number, daysActive: number, interestRate: number = 0.01): number => {
  return amount * interestRate * daysActive;
};

// Helper function to calculate ROI
const calculateROI = (totalEarned: number, amount: number): number => {
  return (totalEarned / amount) * 100;
};

// Create test user if not exists
const createTestUser = async (): Promise<any> => {
  try {
    let testUser = await User.findOne({ email: '<EMAIL>' });

    if (!testUser) {
      testUser = await User.create({
        firstName: 'Test',
        lastName: 'Investor',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        walletAddress: '******************************************',
        isAdmin: false,
        isVerified: true
      });
      console.log('✅ Test user created');
    } else {
      console.log('✅ Test user already exists');
    }

    return testUser;
  } catch (error) {
    console.error('❌ Error creating test user:', error);
    throw error;
  }
};

// Create investment packages
const createInvestmentPackages = async (userId: string): Promise<void> => {
  try {
    // Clear existing test investment packages and transactions for this user
    await InvestmentPackage.deleteMany({ userId });
    await Transaction.deleteMany({ userId, type: 'deposit' });
    console.log('🧹 Cleared existing test investment packages and transactions');

    const packages: any[] = [];

    for (const investment of testInvestments) {
      const createdAt = new Date(Date.now() - (investment.daysAgo * 24 * 60 * 60 * 1000));
      let activatedAt: Date | null = null;
      let totalEarned = 0;
      let activeDays = 0;
      let roi = 0;

      // Create a mock transaction for this investment
      const transaction = new Transaction({
        userId,
        type: 'deposit',
        asset: investment.currency,
        amount: investment.amount,
        status: 'completed',
        createdAt,
        completedAt: createdAt
      });
      await transaction.save();

      // Set activation date and calculate earnings for active/completed/withdrawn packages
      if (investment.status !== 'pending' && investment.activeDaysAgo !== undefined) {
        activatedAt = new Date(Date.now() - (investment.activeDaysAgo * 24 * 60 * 60 * 1000));
        activeDays = investment.activeDaysAgo;
        totalEarned = calculateEarnings(investment.amount, activeDays);
        roi = calculateROI(totalEarned, investment.amount);
      }

      const packageData: any = {
        userId,
        transactionId: transaction._id, // Required field
        packageId: generatePackageId(),
        amount: investment.amount,
        currency: investment.currency,
        status: investment.status,
        createdAt,
        activatedAt,
        totalEarned,
        accumulatedInterest: totalEarned,
        activeDays,
        totalDays: 365, // Default 1 year investment period
        roi,
        interestRate: 0.01, // 1% daily interest
        dailyInterest: investment.status === 'active' ? investment.amount * 0.01 : 0,
        lastCalculatedAt: investment.status === 'active' ? new Date() : null,
        lastInterestDistribution: investment.status === 'active' ? new Date() : null,
        compoundEnabled: false,
        emergencyWithdrawFee: 0.05,
        packageHash: generatePackageHash(),
        minimumWithdrawalUSDT: 50,
        realTimeUSDTValue: totalEarned,
        lastUSDTUpdate: new Date(),
        autoCreated: true
      };

      // Set withdrawal date for withdrawn packages
      if (investment.status === 'withdrawn') {
        packageData.withdrawnAt = new Date(Date.now() - (Math.random() * 10 * 24 * 60 * 60 * 1000)); // Random date within last 10 days
      }

      packages.push(packageData);
    }

    // Insert all packages
    await InvestmentPackage.insertMany(packages);
    console.log(`✅ Created ${packages.length} test investment packages`);

    // Display summary by currency
    const summary = packages.reduce((acc, pkg) => {
      if (!acc[pkg.currency]) {
        acc[pkg.currency] = {
          totalInvested: 0,
          totalEarned: 0,
          activePackages: 0,
          totalPackages: 0
        };
      }

      acc[pkg.currency].totalInvested += pkg.amount;
      acc[pkg.currency].totalEarned += pkg.totalEarned;
      acc[pkg.currency].totalPackages += 1;

      if (pkg.status === 'active') {
        acc[pkg.currency].activePackages += 1;
      }

      return acc;
    }, {} as Record<string, any>);

    console.log('\n📊 Investment Summary by Currency:');
    console.log('=====================================');
    Object.entries(summary).forEach(([currency, data]: [string, any]) => {
      console.log(`${currency}:`);
      console.log(`  Total Invested: ${data.totalInvested} ${currency}`);
      console.log(`  Total Earned: ${data.totalEarned.toFixed(6)} ${currency}`);
      console.log(`  Active Packages: ${data.activePackages}/${data.totalPackages}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error creating investment packages:', error);
    throw error;
  }
};

// Main function
const createInvestmentTestData = async (): Promise<void> => {
  try {
    console.log('🚀 Starting Investment Test Data Creation...');
    console.log('==========================================');

    // Connect to database
    await db.connect();
    console.log('✅ Connected to MongoDB');

    // Create test user
    const testUser = await createTestUser();

    // Create investment packages
    await createInvestmentPackages(testUser._id);

    console.log('\n🎉 Investment test data creation completed successfully!');
    console.log('\nYou can now:');
    console.log('1. Login with: <EMAIL> / TestPassword123!');
    console.log('2. Navigate to the Investments page');
    console.log('3. View the Investment Summary by Currency section');
    console.log('\n💡 The test data includes:');
    console.log('- Multiple currencies (BTC, ETH, USDT, BNB, SOL, TRX, DOGE)');
    console.log('- Different investment statuses (pending, active, completed, withdrawn)');
    console.log('- Realistic earnings based on daily interest calculations');
    console.log('- Various investment amounts and time periods');

  } catch (error) {
    console.error('❌ Error creating investment test data:', error);
  } finally {
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    process.exit(0);
  }
};

// Run the script
createInvestmentTestData();
