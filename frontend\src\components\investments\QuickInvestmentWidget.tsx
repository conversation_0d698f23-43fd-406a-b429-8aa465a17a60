import React, { useState } from 'react';
import {
  Box,
  Button,
  VStack,
  HStack,
  Text,
  Badge,
  Icon,
  useColorModeValue,
  SimpleGrid,
  Card,
  CardBody,
  Flex,
  Progress,
  useToast,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import { FaRocket, FaCoins, FaFire, FaStar, FaBolt } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const MotionCard = motion(Card);

interface QuickInvestmentOption {
  id: string;
  amount: number;
  currency: string;
  dailyEarning: number;
  monthlyEarning: number;
  tier: string;
  popular?: boolean;
  recommended?: boolean;
  icon: any;
  color: string;
  gradient: string;
}

const QuickInvestmentWidget: React.FC = () => {
  const { t } = useTranslation();
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [isInvesting, setIsInvesting] = useState(false);
  const toast = useToast();

  // Theme colors
  const bgColor = useColorModeValue('#FFFFFF', '#1E2026');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const primaryColor = '#FCD535';

  // Önceden tanımlanmış basit yatırım seçenekleri
  const quickOptions: QuickInvestmentOption[] = [
    {
      id: 'starter',
      amount: 100,
      currency: 'USDT',
      dailyEarning: 0.16,
      monthlyEarning: 4.8,
      tier: 'Başlangıç',
      icon: FaCoins,
      color: '#02C076',
      gradient: 'linear(45deg, #02C076, #00D4AA)'
    },
    {
      id: 'popular',
      amount: 500,
      currency: 'USDT',
      dailyEarning: 0.95,
      monthlyEarning: 28.5,
      tier: 'Popüler',
      popular: true,
      icon: FaFire,
      color: '#F84960',
      gradient: 'linear(45deg, #F84960, #FF6B8A)'
    },
    {
      id: 'recommended',
      amount: 1000,
      currency: 'USDT',
      dailyEarning: 1.9,
      monthlyEarning: 57,
      tier: 'Tavsiye',
      recommended: true,
      icon: FaStar,
      color: '#FCD535',
      gradient: 'linear(45deg, #FCD535, #FFE066)'
    },
    {
      id: 'premium',
      amount: 5000,
      currency: 'USDT',
      dailyEarning: 10.5,
      monthlyEarning: 315,
      tier: 'Premium',
      icon: FaBolt,
      color: '#9945FF',
      gradient: 'linear(45deg, #9945FF, #B266FF)'
    }
  ];

  const handleQuickInvest = async (option: QuickInvestmentOption) => {
    setSelectedOption(option.id);
    setIsInvesting(true);

    try {
      // Simulated API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: '🎉 Yatırım Başarılı!',
        description: `${option.amount} ${option.currency} yatırımınız aktif edildi!`,
        status: 'success',
        duration: 5000,
        isClosable: true,
        position: 'top',
      });

      // Reset state
      setSelectedOption(null);
    } catch (error) {
      toast({
        title: '❌ Hata',
        description: 'Yatırım işlemi başarısız oldu',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsInvesting(false);
    }
  };

  return (
    <Box p={6}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <Box textAlign="center">
          <HStack justify="center" mb={2}>
            <Icon as={FaRocket} color={primaryColor} boxSize={6} />
            <Text fontSize="2xl" fontWeight="bold" color={textColor}>
              ⚡ Hızlı Yatırım
            </Text>
          </HStack>
          <Text color="#848E9C" fontSize="md">
            Tek tıkla yatırım yap, günlük kazanmaya başla!
          </Text>
        </Box>

        {/* Quick Investment Options */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
          {quickOptions.map((option) => (
            <MotionCard
              key={option.id}
              bg={bgColor}
              borderWidth="2px"
              borderColor={selectedOption === option.id ? option.color : 'transparent'}
              borderRadius="xl"
              overflow="hidden"
              position="relative"
              cursor="pointer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => !isInvesting && handleQuickInvest(option)}
              _hover={{
                boxShadow: `0 8px 25px ${option.color}40`,
                borderColor: option.color
              }}
            >
              {/* Badges */}
              {option.popular && (
                <Badge
                  position="absolute"
                  top={2}
                  right={2}
                  bg="#F84960"
                  color="white"
                  fontSize="xs"
                  px={2}
                  py={1}
                  borderRadius="full"
                >
                  🔥 Popular
                </Badge>
              )}
              {option.recommended && (
                <Badge
                  position="absolute"
                  top={2}
                  right={2}
                  bg="#FCD535"
                  color="black"
                  fontSize="xs"
                  px={2}
                  py={1}
                  borderRadius="full"
                >
                  ⭐ Tavsiye
                </Badge>
              )}

              <CardBody p={4}>
                <VStack spacing={3} align="center">
                  {/* Icon */}
                  <Box
                    bg={`${option.color}20`}
                    p={3}
                    borderRadius="full"
                  >
                    <Icon as={option.icon} color={option.color} boxSize={6} />
                  </Box>

                  {/* Tier */}
                  <Text fontWeight="bold" color={textColor} fontSize="lg">
                    {option.tier}
                  </Text>

                  {/* Amount */}
                  <Text fontSize="2xl" fontWeight="bold" color={option.color}>
                    ${option.amount}
                  </Text>

                  {/* Earnings */}
                  <VStack spacing={1}>
                    <Text color="#02C076" fontSize="sm" fontWeight="bold">
                      💰 Günlük: ${option.dailyEarning}
                    </Text>
                    <Text color="#02C076" fontSize="sm" fontWeight="bold">
                      📈 Aylık: ${option.monthlyEarning}
                    </Text>
                  </VStack>

                  {/* Progress for selected */}
                  {selectedOption === option.id && isInvesting && (
                    <Box width="100%">
                      <Progress
                        size="sm"
                        isIndeterminate
                        colorScheme="yellow"
                        borderRadius="full"
                      />
                      <Text fontSize="xs" color={textColor} textAlign="center" mt={1}>
                        Yatırım yapılıyor...
                      </Text>
                    </Box>
                  )}

                  {/* Action Button */}
                  {selectedOption !== option.id && !isInvesting && (
                    <Button
                      bg={option.gradient}
                      color="white"
                      size="sm"
                      width="100%"
                      fontWeight="bold"
                      _hover={{ transform: 'translateY(-2px)' }}
                    >
                      🚀 Hemen Yatır
                    </Button>
                  )}
                </VStack>
              </CardBody>
            </MotionCard>
          ))}
        </SimpleGrid>

        {/* Benefits */}
        <Box
          bg="linear-gradient(135deg, #FCD535, #F8D12F)"
          p={4}
          borderRadius="xl"
          color="black"
        >
          <HStack justify="space-around" wrap="wrap">
            <VStack spacing={1}>
              <Text fontSize="lg">⚡</Text>
              <Text fontSize="xs" fontWeight="bold">Anında Aktif</Text>
            </VStack>
            <VStack spacing={1}>
              <Text fontSize="lg">💰</Text>
              <Text fontSize="xs" fontWeight="bold">Günlük Kazanç</Text>
            </VStack>
            <VStack spacing={1}>
              <Text fontSize="lg">🔒</Text>
              <Text fontSize="xs" fontWeight="bold">%100 Güvenli</Text>
            </VStack>
            <VStack spacing={1}>
              <Text fontSize="lg">📱</Text>
              <Text fontSize="xs" fontWeight="bold">Mobil Uyumlu</Text>
            </VStack>
          </HStack>
        </Box>
      </VStack>
    </Box>
  );
};

export default QuickInvestmentWidget;
