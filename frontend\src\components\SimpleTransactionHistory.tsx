import React, { useEffect, useState } from 'react';
import {
  Box,
  Heading,
  Text,
  Flex,
  Badge,
  Button,
  HStack,
  VStack,
  Icon,
  useColorModeValue,
  Divider,
  Spinner,
  Center,
  Tooltip,
  ScaleFade
} from '@chakra-ui/react';
import { FaArrowUp, FaArrowDown, FaExternalLinkAlt, FaSync, FaExclamationTriangle } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { transactionService } from '../services/api';
import useAuth from '../hooks/useAuth';

// Transaction type definition
export interface Transaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'interest' | 'commission';
  amount: number;
  currency: string;
  date: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'failed';
  txHash?: string;
  walletAddress?: string;
  description?: string;
  investmentId?: string;
}

interface SimpleTransactionHistoryProps {
  filter?: 'all' | 'deposit' | 'withdrawal';
  limit?: number;
}

const SimpleTransactionHistory: React.FC<SimpleTransactionHistoryProps> = ({
  filter = 'all',
  limit
}) => {
  const { t } = useTranslation();
  const { user } = useAuth();

  // State variables
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Colors
  const bgColor = useColorModeValue("#1E2329", "#1E2329");
  const borderColor = useColorModeValue("#2B3139", "#2B3139");
  const textColor = useColorModeValue("#EAECEF", "#EAECEF");
  const secondaryTextColor = useColorModeValue("#848E9C", "#848E9C");
  const depositColor = useColorModeValue("#0ECB81", "#0ECB81");
  const withdrawalColor = useColorModeValue("#F6465D", "#F6465D");

  // Function to fetch transactions from API
  const fetchTransactions = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params: any = { limit: limit || 20 };
      if (filter !== 'all') {
        params.type = filter;
      }

      // Add sorting parameter to ensure newest first
      params.sort = '-createdAt';

      // Fetch transactions from API
      const response = await transactionService.getAll(params);

      if (response.data && response.data.transactions) {
        // Map API response to our Transaction interface
        const apiTransactions = response.data.transactions.map((tx: any) => ({
          id: tx._id,
          type: tx.type,
          amount: tx.amount,
          currency: tx.asset,
          date: tx.createdAt,
          status: tx.status,
          txHash: tx.txHash,
          walletAddress: tx.walletAddress
        }));

        // Update state with new transactions
        setTransactions(apiTransactions);
        setLastUpdate(new Date());
      } else {
        setTransactions([]);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setError('Failed to load transactions. Please try again.');

      // Fallback to localStorage if API fails
      try {
        const storedTransactions = localStorage.getItem('transactions');
        if (storedTransactions) {
          const parsedTransactions = JSON.parse(storedTransactions) as Transaction[];

          // Filter transactions based on the filter prop
          let filteredTransactions = parsedTransactions;
          if (filter === 'deposit') {
            filteredTransactions = parsedTransactions.filter(tx => tx.type === 'deposit');
          } else if (filter === 'withdrawal') {
            filteredTransactions = parsedTransactions.filter(tx => tx.type === 'withdrawal');
          }

          // Sort transactions by date (newest first)
          filteredTransactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

          // Apply limit if provided
          if (limit && limit > 0) {
            filteredTransactions = filteredTransactions.slice(0, limit);
          }

          setTransactions(filteredTransactions);
        }
      } catch (localStorageError) {
        console.error('Error loading from localStorage:', localStorageError);
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch transactions on component mount and when filter or limit changes
  useEffect(() => {
    fetchTransactions();
  }, [user, filter, limit]);

  // Show loading state
  if (loading) {
    return (
      <Box
        bg={bgColor}
        p={5}
        borderRadius="md"
        borderWidth="1px"
        borderColor={borderColor}
        textAlign="center"
      >
        <Center py={4}>
          <Spinner color={depositColor} size="md" mr={3} />
          <Text color={secondaryTextColor}>
            {t('transactions.loading', 'Loading transactions...')}
          </Text>
        </Center>
      </Box>
    );
  }

  // Show error state
  if (error) {
    return (
      <Box
        bg={bgColor}
        p={5}
        borderRadius="md"
        borderWidth="1px"
        borderColor={borderColor}
        textAlign="center"
      >
        <Text color={withdrawalColor} mb={3}>
          {error}
        </Text>
        <Button
          leftIcon={<FaSync />}
          colorScheme="yellow"
          size="sm"
          onClick={fetchTransactions}
        >
          {t('transactions.retry', 'Retry')}
        </Button>
      </Box>
    );
  }

  // Show empty state
  if (transactions.length === 0) {
    return (
      <Box
        bg={bgColor}
        p={5}
        borderRadius="md"
        borderWidth="1px"
        borderColor={borderColor}
        textAlign="center"
      >
        <Text color={secondaryTextColor}>
          {t('transactions.noTransactions', 'No transactions found.')}
        </Text>
      </Box>
    );
  }

  return (
    <Box position="relative">
      {/* Last updated info */}
      {lastUpdate && (
        <Text fontSize="xs" color={secondaryTextColor} textAlign="right" mb={2}>
          Last updated: {lastUpdate.toLocaleTimeString()}
        </Text>
      )}

      <VStack spacing={4} align="stretch">
        {transactions.map((transaction) => (
          <ScaleFade key={transaction.id} in={true} initialScale={0.9}>
            <Box
              bg={bgColor}
              p={4}
              borderRadius="md"
              borderWidth="1px"
              borderColor={borderColor}
              _hover={{ borderColor: transaction.type === 'deposit' ? depositColor : withdrawalColor }}
              transition="all 0.3s ease"
              position="relative"
              overflow="hidden"
            >
              <Flex justify="space-between" align="center">
                <HStack spacing={3}>
                  <Box
                    bg={transaction.type === 'deposit' ? `${depositColor}22` : `${withdrawalColor}22`}
                    color={transaction.type === 'deposit' ? depositColor : withdrawalColor}
                    borderRadius="full"
                    p={2}
                    boxSize="40px"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <Icon
                      as={transaction.type === 'deposit' ? FaArrowDown : FaArrowUp}
                      boxSize={4}
                    />
                  </Box>

                  <VStack align="start" spacing={0}>
                    <Text color={textColor} fontWeight="bold">
                      {transaction.type === 'deposit'
                        ? t('transactions.deposit', 'Deposit')
                        : t('transactions.withdrawal', 'Withdrawal')}
                    </Text>
                    <Text color={secondaryTextColor} fontSize="sm">
                      {new Date(transaction.date).toLocaleString()}
                    </Text>
                  </VStack>
                </HStack>

                <VStack align="end" spacing={0}>
                  <Text
                    color={transaction.type === 'deposit' ? depositColor : withdrawalColor}
                    fontWeight="bold"
                  >
                    {transaction.type === 'deposit' ? '+' : '-'}{transaction.amount} {transaction.currency}
                  </Text>
                  <Tooltip
                    label={
                      transaction.status === 'pending' ? 'Waiting for approval' :
                      transaction.status === 'approved' ? 'Transaction approved' :
                      'Transaction rejected'
                    }
                    placement="top"
                  >
                    <Badge
                      colorScheme={
                        transaction.status === 'approved' ? 'green' :
                        transaction.status === 'pending' ? 'yellow' : 'red'
                      }
                      borderRadius="full"
                      px={2}
                      fontSize="xs"
                    >
                      {transaction.status}
                    </Badge>
                  </Tooltip>
                </VStack>
              </Flex>

              {transaction.txHash && (
                <>
                  <Divider my={2} borderColor={borderColor} />
                  <Flex justify="space-between" align="center">
                    <Text color={secondaryTextColor} fontSize="xs" isTruncated maxW="70%">
                      {t('transactions.txHash', 'TX Hash')}: {transaction.txHash.substring(0, 16)}...
                    </Text>
                    <Button
                      size="xs"
                      variant="ghost"
                      color={secondaryTextColor}
                      _hover={{ color: textColor }}
                      leftIcon={<FaExternalLinkAlt />}
                      onClick={() => window.open(`https://etherscan.io/tx/${transaction.txHash}`, '_blank')}
                    >
                      {t('transactions.view', 'View')}
                    </Button>
                  </Flex>
                </>
              )}
            </Box>
          </ScaleFade>
        ))}

        {/* Refresh button */}
        <Button
          size="sm"
          variant="outline"
          colorScheme="yellow"
          leftIcon={<FaSync />}
          onClick={fetchTransactions}
          alignSelf="center"
          mt={2}
        >
          {t('transactions.refresh', 'Refresh')}
        </Button>
      </VStack>
    </Box>
  );
};

export default SimpleTransactionHistory;
