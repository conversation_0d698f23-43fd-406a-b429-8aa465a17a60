import React, { useRef, useEffect } from 'react';
import {
  Box,
  IconButton,
  useColorModeValue,
  Tooltip,
  Flex,
  Text,
  Switch,
  FormControl,
  FormLabel,
  useBreakpointValue,
} from '@chakra-ui/react';
import { FaSun, FaMoon } from 'react-icons/fa';
import { useTheme } from '../../context/ThemeContext';
import { motion, AnimatePresence } from 'framer-motion';

// Define the props for the theme switcher
interface ThemeSwitcherProps {
  variant?: 'icon' | 'switch' | 'full';
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * ThemeSwitcher Component
 * 
 * A component that allows users to toggle between light and dark themes.
 * Features include:
 * - Multiple variants (icon, switch, full)
 * - Smooth transition animations
 * - Accessibility features
 * - Responsive design
 */
const ThemeSwitcher: React.FC<ThemeSwitcherProps> = ({
  variant = 'icon',
  showLabel = true,
  size = 'md',
  className,
}) => {
  // Get theme context
  const { isDarkMode, toggleTheme, themeTransition } = useTheme();
  
  // Ref for the switch input
  const switchRef = useRef<HTMLInputElement>(null);
  
  // Colors
  const bgColor = useColorModeValue('#1E2329', '#1E2329');
  const textColor = useColorModeValue('#EAECEF', '#EAECEF');
  const iconColor = useColorModeValue('#F0B90B', '#F0B90B');
  
  // Responsive label
  const displayLabel = useBreakpointValue({ base: false, md: showLabel });
  
  // Focus the switch when using keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'l' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        if (switchRef.current) {
          switchRef.current.focus();
        }
        toggleTheme();
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [toggleTheme]);
  
  // Render icon variant
  if (variant === 'icon') {
    return (
      <Tooltip
        label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
        aria-label="Theme toggle tooltip"
        hasArrow
      >
        <Box className={className} position="relative" width="40px" height="40px">
          <AnimatePresence mode="wait">
            <motion.div
              key={isDarkMode ? 'dark' : 'light'}
              initial={{ opacity: 0, rotate: -180 }}
              animate={{ opacity: 1, rotate: 0 }}
              exit={{ opacity: 0, rotate: 180 }}
              transition={{ duration: 0.3 }}
              style={{ position: 'absolute', width: '100%', height: '100%' }}
            >
              <IconButton
                aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
                icon={isDarkMode ? <FaSun /> : <FaMoon />}
                onClick={toggleTheme}
                variant="ghost"
                color={iconColor}
                size={size}
                style={{ transition: themeTransition }}
                _hover={{ transform: 'rotate(30deg)' }}
              />
            </motion.div>
          </AnimatePresence>
        </Box>
      </Tooltip>
    );
  }
  
  // Render switch variant
  if (variant === 'switch') {
    return (
      <FormControl
        display="flex"
        alignItems="center"
        className={className}
        width="fit-content"
      >
        {displayLabel && (
          <FormLabel htmlFor="theme-switch" mb="0" mr={2} color={textColor}>
            {isDarkMode ? 'Dark' : 'Light'}
          </FormLabel>
        )}
        <Switch
          id="theme-switch"
          ref={switchRef}
          isChecked={isDarkMode}
          onChange={toggleTheme}
          colorScheme="yellow"
          size={size}
          style={{ transition: themeTransition }}
        />
      </FormControl>
    );
  }
  
  // Render full variant
  return (
    <Box
      className={className}
      bg={bgColor}
      borderRadius="full"
      p={2}
      boxShadow="0 2px 10px rgba(0, 0, 0, 0.2)"
      style={{ transition: themeTransition }}
    >
      <Flex align="center" justify="space-between">
        <Flex
          align="center"
          justify="center"
          borderRadius="full"
          bg={isDarkMode ? 'transparent' : `${iconColor}22`}
          color={isDarkMode ? textColor : iconColor}
          p={2}
          cursor="pointer"
          onClick={() => !isDarkMode && toggleTheme()}
          _hover={{ bg: !isDarkMode ? 'transparent' : `${iconColor}22` }}
          transition={themeTransition}
        >
          <FaSun />
          {displayLabel && <Text ml={2}>Light</Text>}
        </Flex>
        
        <Box mx={2}>
          <Switch
            id="theme-switch-full"
            ref={switchRef}
            isChecked={isDarkMode}
            onChange={toggleTheme}
            colorScheme="yellow"
            size={size}
            style={{ transition: themeTransition }}
          />
        </Box>
        
        <Flex
          align="center"
          justify="center"
          borderRadius="full"
          bg={isDarkMode ? `${iconColor}22` : 'transparent'}
          color={isDarkMode ? iconColor : textColor}
          p={2}
          cursor="pointer"
          onClick={() => isDarkMode && toggleTheme()}
          _hover={{ bg: isDarkMode ? 'transparent' : `${iconColor}22` }}
          transition={themeTransition}
        >
          <FaMoon />
          {displayLabel && <Text ml={2}>Dark</Text>}
        </Flex>
      </Flex>
    </Box>
  );
};

export default React.memo(ThemeSwitcher);
