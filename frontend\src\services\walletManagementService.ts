import api from './api';
import {
  validateAddressFormat,
  getAddressFormatHint,
  getNetworkOptions,
  getNetworkDisplayName
} from '../utils/cryptoUtils';
// Production service - no mock imports

export interface WithdrawalAddress {
  _id: string;
  currency: string;
  address: string;
  formattedAddress: string;
  label: string;
  network: string;
  isDefault: boolean;
  isActive: boolean;
  isVerified: boolean;
  lastUsed?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface SupportedCurrency {
  currency: string;
  name: string;
  networks: string[];
  addressFormat: string;
}

export interface CreateAddressRequest {
  currency: string;
  address: string;
  label: string;
  network?: string;
}

export interface UpdateAddressRequest {
  label?: string;
  isDefault?: boolean;
}

export interface VerifyAddressRequest {
  verificationCode: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  count?: number;
}

export const walletManagementService = {
  // Get all withdrawal addresses for user
  async getUserAddresses(currency?: string): Promise<ApiResponse<WithdrawalAddress[]>> {
    const params = currency ? { currency } : {};
    const response = await api.get('/wallet-management/addresses', { params });
    return response.data;
  },

  // Get withdrawal address by ID
  async getAddressById(id: string): Promise<ApiResponse<WithdrawalAddress>> {
    const response = await api.get(`/wallet-management/addresses/${id}`);
    return response.data;
  },

  // Add new withdrawal address
  async addAddress(data: CreateAddressRequest): Promise<ApiResponse<WithdrawalAddress & { verificationRequired: boolean }>> {
    const response = await api.post('/wallet-management/addresses', data);
    return response.data;
  },

  // Update withdrawal address
  async updateAddress(id: string, data: UpdateAddressRequest): Promise<ApiResponse<WithdrawalAddress>> {
    const response = await api.put(`/wallet-management/addresses/${id}`, data);
    return response.data;
  },

  // Delete withdrawal address
  async deleteAddress(id: string): Promise<ApiResponse<void>> {
    const response = await api.delete(`/wallet-management/addresses/${id}`);
    return response.data;
  },

  // Verify withdrawal address
  async verifyAddress(id: string, data: VerifyAddressRequest): Promise<ApiResponse<{ id: string; isVerified: boolean }>> {
    const response = await api.post(`/wallet-management/addresses/${id}/verify`, data);
    return response.data;
  },

  // Set default withdrawal address
  async setDefaultAddress(id: string): Promise<ApiResponse<void>> {
    const response = await api.post(`/wallet-management/addresses/${id}/set-default`);
    return response.data;
  },

  // Resend verification code
  async resendVerificationCode(id: string): Promise<ApiResponse<void>> {
    const response = await api.post(`/wallet-management/addresses/${id}/resend-verification`);
    return response.data;
  },

  // Get supported currencies and networks
  async getSupportedCurrencies(): Promise<ApiResponse<SupportedCurrency[]>> {
    try {
      const response = await api.get('/wallet-management/supported-currencies');
      return response.data;
    } catch (error) {
      // Fallback to system config if wallet-management endpoint fails
      console.warn('Wallet management endpoint failed, trying system config:', error);
      try {
        const systemConfigService = await import('./systemConfigService');
        const systemCurrencies = await systemConfigService.default.getSupportedCurrencies();

        const convertedCurrencies: SupportedCurrency[] = systemCurrencies.map(currency => ({
          currency: currency.symbol,
          name: currency.name,
          networks: currency.networks,
          addressFormat: currency.addressFormat
        }));

        return {
          success: true,
          data: convertedCurrencies
        };
      } catch (systemError) {
        console.error('Both wallet-management and system config failed:', systemError);
        throw error; // Re-throw original error
      }
    }
  },

  // Use imported utility functions
  validateAddressFormat,
  getAddressFormatHint,
  getNetworkOptions,
  getNetworkDisplayName
};

// Enhanced withdrawal service
export interface WithdrawalRequest {
  addressId: string;
  amount: number;
  currency: string;
  twoFactorCode?: string;
}

export interface WithdrawalResponse {
  transactionId: string;
  amount: number;
  originalAmount: number;
  withdrawalFee: number;
  currency: string;
  address: string;
  addressLabel: string;
  network: string;
  status: string;
  estimatedProcessingTime: string;
}

export interface WithdrawalHistory {
  id: string;
  amount: number;
  currency: string;
  address: string;
  status: string;
  txHash?: string;
  network: string;
  description: string;
  metadata: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface WithdrawalLimits {
  minimum: number;
  maximum: number;
  dailyLimit: number;
  feePercentage: number;
  minimumFee: number;
  estimatedTime: string;
}

export interface FeeEstimation {
  originalAmount: number;
  withdrawalFee: number;
  netAmount: number;
  currency: string;
  feePercentage: number;
  limits: WithdrawalLimits;
}

export const enhancedWithdrawalService = {
  // Create withdrawal request
  async createWithdrawal(data: WithdrawalRequest): Promise<ApiResponse<WithdrawalResponse>> {
    const response = await api.post('/enhanced-withdrawals/create', data);
    return response.data;
  },

  // Get withdrawal history
  async getWithdrawalHistory(params?: {
    page?: number;
    limit?: number;
    currency?: string;
    status?: string;
  }): Promise<ApiResponse<WithdrawalHistory[]> & { pagination: any }> {
    const response = await api.get('/enhanced-withdrawals/history', { params });
    return response.data;
  },

  // Get withdrawal limits
  async getWithdrawalLimits(currency?: string): Promise<ApiResponse<WithdrawalLimits | Record<string, WithdrawalLimits>>> {
    const params = currency ? { currency } : {};
    const response = await api.get('/enhanced-withdrawals/limits', { params });
    return response.data;
  },

  // Estimate withdrawal fee
  async estimateWithdrawalFee(amount: number, currency: string): Promise<ApiResponse<FeeEstimation>> {
    const response = await api.post('/enhanced-withdrawals/estimate-fee', { amount, currency });
    return response.data;
  },

  // Cancel withdrawal
  async cancelWithdrawal(id: string): Promise<ApiResponse<{ transactionId: string; refundedAmount: number; currency: string }>> {
    const response = await api.post(`/enhanced-withdrawals/${id}/cancel`);
    return response.data;
  }
};
