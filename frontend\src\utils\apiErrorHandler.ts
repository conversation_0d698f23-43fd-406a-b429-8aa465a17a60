import { logger } from './logger';

export interface APIError {
  message: string;
  errors?: Record<string, string>;
  field?: string;
  details?: string;
}

export class APIErrorHandler {
  static handle(error: any): APIError {
    let errorResponse: APIError = {
      message: 'Bir hata oluştu. Lütfen daha sonra tekrar deneyin.'
    };

    if (error.response) {
      const { data, status } = error.response;

      // Log error details
      logger.error('API Error', {
        status,
        data,
        url: error.config?.url,
        method: error.config?.method
      });

      // Handle different status codes
      switch (status) {
        case 400:
          errorResponse = {
            message: data.message || 'Geçersiz istek',
            errors: data.errors,
            field: data.field
          };
          break;

        case 401:
          errorResponse = {
            message: 'Oturum süreniz doldu, lütfen tekrar giriş yapın'
          };
          // Clear auth state and redirect to login
          localStorage.removeItem('token');
          window.location.href = '/login';
          break;

        case 403:
          errorResponse = {
            message: 'Bu işlem için yetkiniz bulunmuyor'
          };
          break;

        case 404:
          errorResponse = {
            message: data.message || 'İstenen kaynak bulunamadı'
          };
          break;

        case 422:
          errorResponse = {
            message: 'Validasyon hatası',
            errors: data.errors
          };
          break;

        case 429:
          errorResponse = {
            message: 'Çok fazla istek gönderdiniz. Lütfen bir süre bekleyin.'
          };
          break;

        case 500:
          errorResponse = {
            message: 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.',
            details: process.env.NODE_ENV === 'development' ? data.details : undefined
          };
          break;

        default:
          if (status >= 500) {
            errorResponse = {
              message: 'Sunucu hatası. Lütfen daha sonra tekrar deneyin.'
            };
          }
      }
    } else if (error.request) {
      // Network error
      logger.error('Network Error', {
        error: error.message,
        request: error.request
      });

      errorResponse = {
        message: 'Sunucuya bağlanılamıyor. Lütfen internet bağlantınızı kontrol edin.'
      };
    }

    return errorResponse;
  }

  static isValidationError(error: APIError): boolean {
    return !!error.errors;
  }

  static getFieldError(error: APIError, field: string): string | undefined {
    return error.errors?.[field];
  }

  static isNetworkError(error: any): boolean {
    return !error.response && error.request;
  }

  static isServerError(error: any): boolean {
    return error.response && error.response.status >= 500;
  }

  static isAuthenticationError(error: any): boolean {
    return error.response && error.response.status === 401;
  }
}