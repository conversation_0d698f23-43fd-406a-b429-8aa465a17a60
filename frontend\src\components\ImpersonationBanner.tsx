import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Text,
  Button,
  Alert,
  AlertIcon,
  AlertDescription,
  useToast,
  HStack,
  Icon,
  IconButton,
  Collapse,
  useDisclosure
} from '@chakra-ui/react';
import { motion, useDragControls } from 'framer-motion';
import { FaUserShield, FaSignOutAlt, FaTimes, FaChevronDown, FaChevronUp, FaGripVertical } from 'react-icons/fa';

const MotionBox = motion(Box);
import { useNavigate } from 'react-router-dom';
import { adminApiService } from '../services/adminApi';
import useAuth from '../hooks/useAuth';
import { useImpersonation } from '../hooks/useImpersonation';

interface ImpersonationBannerProps {
  isVisible?: boolean;
}

const ImpersonationBanner: React.FC<ImpersonationBannerProps> = ({ isVisible = true }) => {
  const [isReturning, setIsReturning] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: true });
  const { isImpersonating, isHidden, adminInfo, refreshStatus } = useImpersonation();
  const toast = useToast();
  const navigate = useNavigate();
  const { login } = useAuth();
  const dragControls = useDragControls();

  // Refresh status when component mounts (only once)
  useEffect(() => {
    refreshStatus();
  }, []); // Empty dependency array to run only once

  const handleReturnToAdmin = async () => {
    setIsReturning(true);
    
    try {
      const response = await adminApiService.returnToAdmin();
      
      if (response.data && response.data.data) {
        const adminData = response.data.data;
        
        // Update auth context with admin data
        await login(adminData.email, '', adminData);
        
        // Clear impersonation state
        localStorage.removeItem('impersonationAdminInfo');
        localStorage.removeItem('impersonationBannerHidden');
        refreshStatus();
        
        toast({
          title: 'Success',
          description: response.data.message || 'Successfully returned to admin panel',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Redirect to admin dashboard
        navigate('/admin');
      }
    } catch (error: any) {
      console.error('Return to admin error:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to return to admin panel',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsReturning(false);
    }
  };

  const handleHideBanner = () => {
    localStorage.setItem('impersonationBannerHidden', 'true');
    refreshStatus();
  };

  const handleShowBanner = () => {
    localStorage.removeItem('impersonationBannerHidden');
    refreshStatus();
  };

  const handleToggleCollapse = () => {
    onToggle();
  };

  if (!isVisible || !isImpersonating) {
    return null;
  }

  // If banner is hidden, show a draggable chat bubble
  if (isHidden) {
    return (
      <MotionBox
        position="fixed"
        top={4}
        right={4}
        zIndex={9999}
        drag
        dragControls={dragControls}
        dragMomentum={false}
        dragElastic={0.1}
        whileDrag={{ scale: 1.1 }}
        onDrag={(event, info) => {
          setPosition({ x: info.offset.x, y: info.offset.y });
        }}
        style={{ x: position.x, y: position.y }}
      >
        <Box
          bg="linear-gradient(135deg, #4A90E2, #357ABD)"
          color="white"
          borderRadius="20px"
          boxShadow="0 8px 25px rgba(74, 144, 226, 0.4)"
          cursor="grab"
          _active={{ cursor: "grabbing" }}
          position="relative"
          overflow="hidden"
          border="1px solid rgba(255, 255, 255, 0.2)"
        >
          {/* Chat bubble tail */}
          <Box
            position="absolute"
            bottom="-8px"
            right="20px"
            width="0"
            height="0"
            borderLeft="8px solid transparent"
            borderRight="8px solid transparent"
            borderTop="8px solid #4A90E2"
          />

          {/* Drag handle */}
          <Box
            position="absolute"
            top="8px"
            left="8px"
            onPointerDown={(e) => dragControls.start(e)}
            cursor="grab"
            _active={{ cursor: "grabbing" }}
            opacity={0.7}
            _hover={{ opacity: 1 }}
            transition="opacity 0.2s"
            color="rgba(255, 255, 255, 0.8)"
          >
            <Icon as={FaGripVertical} boxSize={3} />
          </Box>

          {/* Content */}
          <Flex
            align="center"
            px={6}
            py={3}
            pl={8}
            onClick={handleShowBanner}
            cursor="pointer"
            _hover={{ bg: "rgba(255,255,255,0.15)" }}
            transition="all 0.2s ease"
          >
            <Icon as={FaUserShield} boxSize={4} mr={2} color="rgba(255, 255, 255, 0.9)" />
            <Text fontWeight="600" fontSize="sm" color="white">
              Admin Mode
            </Text>
          </Flex>
        </Box>
      </MotionBox>
    );
  }

  return (
    <Box
      position="relative"
      width="100%"
      bg="linear-gradient(135deg, #4A90E2, #357ABD)"
      color="white"
      boxShadow="0 2px 8px rgba(74, 144, 226, 0.3)"
      borderBottom="1px solid rgba(255, 255, 255, 0.2)"
    >
      {/* Collapsed header bar */}
      <Flex
        align="center"
        justify="space-between"
        px={4}
        py={3}
        cursor="pointer"
        onClick={handleToggleCollapse}
        _hover={{ bg: "rgba(255, 255, 255, 0.1)" }}
        transition="all 0.2s ease"
        borderRadius="md"
        mx={2}
        my={1}
      >
        <HStack spacing={3}>
          <Icon as={FaUserShield} boxSize={5} color="rgba(255, 255, 255, 0.9)" />
          <Text fontWeight="600" fontSize="md" color="white">
            Admin Impersonation Mode
          </Text>
          <Icon as={isOpen ? FaChevronUp : FaChevronDown} boxSize={4} color="rgba(255, 255, 255, 0.7)" />
        </HStack>

        <HStack spacing={2}>
          <IconButton
            aria-label="Hide banner"
            icon={<FaTimes />}
            size="sm"
            variant="ghost"
            color="rgba(255, 255, 255, 0.8)"
            onClick={(e) => {
              e.stopPropagation();
              handleHideBanner();
            }}
            _hover={{
              bg: "rgba(255,255,255,0.2)",
              color: "white"
            }}
            borderRadius="md"
          />
        </HStack>
      </Flex>

      {/* Collapsible content */}
      <Collapse in={isOpen} animateOpacity>
        <Box px={6} pb={4} pt={2}>
          <Flex
            maxW="1200px"
            mx="auto"
            align="center"
            justify="space-between"
            flexDir={{ base: "column", md: "row" }}
            gap={{ base: 3, md: 0 }}
          >
            <HStack spacing={3} flex={1}>
              {adminInfo && (
                <Text fontSize="md" color="rgba(255, 255, 255, 0.95)" fontWeight="500">
                  Logged in as user by admin: {adminInfo.adminName || adminInfo.adminEmail}
                </Text>
              )}
            </HStack>

            <Button
              size="md"
              variant="solid"
              bg="rgba(255, 255, 255, 0.15)"
              color="white"
              borderColor="rgba(255, 255, 255, 0.3)"
              border="1px solid"
              leftIcon={<FaSignOutAlt />}
              onClick={handleReturnToAdmin}
              isLoading={isReturning}
              loadingText="Returning..."
              _hover={{
                bg: "rgba(255,255,255,0.25)",
                borderColor: "rgba(255, 255, 255, 0.5)",
                transform: "translateY(-1px)"
              }}
              _active={{
                bg: "rgba(255,255,255,0.3)"
              }}
              transition="all 0.2s ease"
              fontWeight="600"
            >
              Return to Admin
            </Button>
          </Flex>
        </Box>
      </Collapse>
    </Box>
  );
};

export default React.memo(ImpersonationBanner);
