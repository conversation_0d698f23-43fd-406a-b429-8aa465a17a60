import React from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  Flex,
  Button,
  SimpleGrid,
  Badge,
  Divider,
  List,
  ListItem,
  ListIcon,
  useColorModeValue
} from '@chakra-ui/react';
import { CheckIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import { 
  FaBriefcase, 
  FaUsers, 
  FaChartBar, 
  FaShieldAlt, 
  FaRegFileAlt,
  FaRegCreditCard,
  FaRegChartBar,
  FaRegClock
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

const CorporateSolutions = () => {
  const { t } = useTranslation();
  
  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";
  
  // Corporate solution packages
  const packages = [
    {
      id: 'enterprise',
      name: t('corporate.packages.enterprise', 'Enterprise'),
      description: t('corporate.packages.enterpriseDesc', 'For large organizations with complex investment needs'),
      price: '$5,000',
      period: t('corporate.monthly', 'monthly'),
      features: [
        t('corporate.features.multiUser', 'Up to 50 user accounts'),
        t('corporate.features.roleBasedAccess', 'Role-based access control'),
        t('corporate.features.dedicatedAccount', 'Dedicated account manager'),
        t('corporate.features.customReporting', 'Custom reporting tools'),
        t('corporate.features.apiAccess', 'Full API access'),
        t('corporate.features.bulkOperations', 'Bulk operations'),
        t('corporate.features.prioritySupport', '24/7 priority support'),
        t('corporate.features.customContracts', 'Custom contract terms')
      ],
      popular: false
    },
    {
      id: 'business',
      name: t('corporate.packages.business', 'Business'),
      description: t('corporate.packages.businessDesc', 'For medium-sized businesses looking to optimize investments'),
      price: '$1,000',
      period: t('corporate.monthly', 'monthly'),
      features: [
        t('corporate.features.multiUser', 'Up to 10 user accounts'),
        t('corporate.features.roleBasedAccess', 'Role-based access control'),
        t('corporate.features.dedicatedAccount', 'Dedicated account manager'),
        t('corporate.features.standardReporting', 'Standard reporting tools'),
        t('corporate.features.apiAccess', 'Basic API access'),
        t('corporate.features.bulkOperations', 'Limited bulk operations'),
        t('corporate.features.prioritySupport', 'Business hours support'),
        t('corporate.features.standardContracts', 'Standard contract terms')
      ],
      popular: true
    },
    {
      id: 'startup',
      name: t('corporate.packages.startup', 'Startup'),
      description: t('corporate.packages.startupDesc', 'For small businesses and startups entering the market'),
      price: '$250',
      period: t('corporate.monthly', 'monthly'),
      features: [
        t('corporate.features.multiUser', 'Up to 3 user accounts'),
        t('corporate.features.basicAccess', 'Basic access control'),
        t('corporate.features.sharedAccount', 'Shared account manager'),
        t('corporate.features.basicReporting', 'Basic reporting tools'),
        t('corporate.features.noApi', 'No API access'),
        t('corporate.features.manualOperations', 'Manual operations only'),
        t('corporate.features.emailSupport', 'Email support'),
        t('corporate.features.standardContracts', 'Standard contract terms')
      ],
      popular: false
    }
  ];
  
  return (
    <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
      <Flex align="center" mb={6}>
        <Icon as={FaBriefcase} color={primaryColor} boxSize={5} mr={2} />
        <Heading size="md" color={textColor}>{t('corporate.title', 'Corporate Solutions')}</Heading>
      </Flex>
      
      <Text color={secondaryTextColor} mb={8}>
        {t('corporate.description', 'Tailored investment solutions for businesses of all sizes. Our corporate packages offer enhanced features, dedicated support, and customized reporting to meet your organization\'s specific needs.')}
      </Text>
      
      <SimpleGrid columns={{ base: 1, lg: 3 }} spacing={6} mb={10}>
        {packages.map((pkg) => (
          <Box
            key={pkg.id}
            bg={cardBgColor}
            borderRadius="md"
            borderWidth="1px"
            borderColor={pkg.popular ? primaryColor : borderColor}
            position="relative"
            overflow="hidden"
          >
            {pkg.popular && (
              <Box
                position="absolute"
                top={0}
                right={0}
                bg={primaryColor}
                color="#000"
                fontWeight="bold"
                px={3}
                py={1}
                fontSize="sm"
                transform="rotate(45deg) translateX(20px) translateY(-15px)"
                width="150px"
                textAlign="center"
              >
                {t('corporate.mostPopular', 'Most Popular')}
              </Box>
            )}
            
            <Box p={6}>
              <Heading size="md" color={textColor} mb={2}>{pkg.name}</Heading>
              <Text color={secondaryTextColor} fontSize="sm" mb={4} height="40px">
                {pkg.description}
              </Text>
              
              <HStack mb={6}>
                <Text color={textColor} fontSize="3xl" fontWeight="bold">{pkg.price}</Text>
                <Text color={secondaryTextColor}>{pkg.period}</Text>
              </HStack>
              
              <Divider borderColor={borderColor} mb={6} />
              
              <List spacing={3} mb={6}>
                {pkg.features.map((feature, index) => (
                  <ListItem key={index} color={textColor} fontSize="sm">
                    <ListIcon as={CheckIcon} color={primaryColor} />
                    {feature}
                  </ListItem>
                ))}
              </List>
              
              <Button
                colorScheme="yellow"
                size="lg"
                width="full"
                variant={pkg.popular ? "solid" : "outline"}
              >
                {t('corporate.contactSales', 'Contact Sales')}
              </Button>
            </Box>
          </Box>
        ))}
      </SimpleGrid>
      
      <Box bg={cardBgColor} p={6} borderRadius="md" borderWidth="1px" borderColor={borderColor} mb={10}>
        <Heading size="md" color={textColor} mb={4}>{t('corporate.keyFeatures', 'Key Corporate Features')}</Heading>
        
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
          <VStack align="start" spacing={3}>
            <Icon as={FaUsers} color={primaryColor} boxSize={8} />
            <Heading size="sm" color={textColor}>{t('corporate.features.multiUserManagement', 'Multi-User Management')}</Heading>
            <Text color={secondaryTextColor} fontSize="sm">
              {t('corporate.features.multiUserDesc', 'Create and manage multiple user accounts with customized permissions and access levels.')}
            </Text>
          </VStack>
          
          <VStack align="start" spacing={3}>
            <Icon as={FaChartBar} color={primaryColor} boxSize={8} />
            <Heading size="sm" color={textColor}>{t('corporate.features.advancedReporting', 'Advanced Reporting')}</Heading>
            <Text color={secondaryTextColor} fontSize="sm">
              {t('corporate.features.reportingDesc', 'Generate detailed reports on investments, returns, and performance metrics tailored to your needs.')}
            </Text>
          </VStack>
          
          <VStack align="start" spacing={3}>
            <Icon as={FaRegFileAlt} color={primaryColor} boxSize={8} />
            <Heading size="sm" color={textColor}>{t('corporate.features.bulkOperations', 'Bulk Operations')}</Heading>
            <Text color={secondaryTextColor} fontSize="sm">
              {t('corporate.features.bulkDesc', 'Perform multiple investment operations simultaneously, saving time and reducing errors.')}
            </Text>
          </VStack>
          
          <VStack align="start" spacing={3}>
            <Icon as={FaShieldAlt} color={primaryColor} boxSize={8} />
            <Heading size="sm" color={textColor}>{t('corporate.features.enhancedSecurity', 'Enhanced Security')}</Heading>
            <Text color={secondaryTextColor} fontSize="sm">
              {t('corporate.features.securityDesc', 'Additional security features including IP whitelisting, advanced 2FA, and audit logs.')}
            </Text>
          </VStack>
        </SimpleGrid>
      </Box>
      
      <Box bg={cardBgColor} p={6} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
        <Heading size="md" color={textColor} mb={4}>{t('corporate.customSolutions', 'Need a Custom Solution?')}</Heading>
        <Text color={secondaryTextColor} mb={6}>
          {t('corporate.customDesc', 'We understand that every business has unique requirements. Our team can create a tailored solution specifically for your organization\'s needs.')}
        </Text>
        
        <HStack spacing={4}>
          <Button colorScheme="yellow" leftIcon={<Icon as={FaRegClock} />}>
            {t('corporate.scheduleDemo', 'Schedule a Demo')}
          </Button>
          <Button variant="outline" borderColor={borderColor} color={textColor} _hover={{ bg: "#2B3139" }}>
            {t('corporate.learnMore', 'Learn More')}
          </Button>
        </HStack>
      </Box>
    </Box>
  );
};

export default CorporateSolutions;
