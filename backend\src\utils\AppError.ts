/**
 * Custom error class for application errors
 * Extends the built-in Error class with additional properties
 */
export class AppError extends Error {
  statusCode: number;
  status: string;
  isOperational: boolean;
  
  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Factory function to create a not found error
 * @param resource - The resource that was not found
 * @returns AppError instance
 */
export const createNotFoundError = (resource: string): AppError => {
  return new AppError(`${resource} not found`, 404);
};

/**
 * Factory function to create a validation error
 * @param message - The validation error message
 * @returns AppError instance
 */
export const createValidationError = (message: string): AppError => {
  return new AppError(message, 400);
};

/**
 * Factory function to create an unauthorized error
 * @param message - The unauthorized error message
 * @returns AppError instance
 */
export const createUnauthorizedError = (message = 'Unauthorized'): AppError => {
  return new AppError(message, 401);
};

/**
 * Factory function to create a forbidden error
 * @param message - The forbidden error message
 * @returns AppError instance
 */
export const createForbiddenError = (message = 'Forbidden'): AppError => {
  return new AppError(message, 403);
};

/**
 * Factory function to create a conflict error
 * @param resource - The resource that has a conflict
 * @returns AppError instance
 */
export const createConflictError = (resource: string): AppError => {
  return new AppError(`${resource} already exists`, 409);
};
