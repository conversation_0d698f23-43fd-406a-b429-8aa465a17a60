import React from 'react';
import {
  Box,
  VStack,
  Text,
  Heading,
  Icon,
  Center
} from '@chakra-ui/react';
import { FaClock } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

interface InterestDistribution {
  id: string;
  packageId: string;
  cryptocurrency: string;
  amount: number;
  usdValue: number;
  distributionDate: string;
  type: 'daily' | 'bonus' | 'completion';
  status: 'completed' | 'pending' | 'failed';
  transactionId: string;
}

interface InterestDistributionHistoryProps {
  interestDistributions?: InterestDistribution[];
  formatCryptocurrencyAmount?: (amount: number, currency: string) => string;
  formatUSDValue?: (amount: number) => string;
  handleCopyToClipboard?: (text: string) => void;
}

const InterestDistributionHistory: React.FC<InterestDistributionHistoryProps> = () => {
  const { t } = useTranslation();

  return (
    <Box
      bg="#1E2026"
      p={6}
      borderRadius="lg"
      borderWidth="1px"
      borderColor="#2B3139"
      mb={6}
    >
      <Heading size="md" color="#F0B90B" mb={6}>
        {t('investments.distributionHistory', 'Interest Distribution History')}
      </Heading>

      <Center py={8}>
        <VStack spacing={4}>
          <Icon as={FaClock} color="#848E9C" boxSize={12} />
          <Text color="#EAECEF" fontSize="lg" fontWeight="600">
            {t('investments.noDistributions', 'No interest distributions yet')}
          </Text>
          <Text color="#848E9C" fontSize="sm" textAlign="center" maxW="500px">
            Interest distribution history will be displayed here when connected to the unified transaction system.
            This will show daily interest payments, bonus distributions, and completion rewards.
          </Text>
        </VStack>
      </Center>
    </Box>
  );
};

export default InterestDistributionHistory;
