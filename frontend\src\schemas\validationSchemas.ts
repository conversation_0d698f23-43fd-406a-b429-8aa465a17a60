import { z } from 'zod';

/**
 * Comprehensive validation schemas for CryptoYield platform
 * Using Zod for type-safe validation with i18n support
 */

// Common validation patterns
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;
const phoneRegex = /^\+?[1-9]\d{1,14}$/;

// Crypto address validation patterns
const bitcoinAddressRegex = /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$/;
const ethereumAddressRegex = /^0x[a-fA-F0-9]{40}$/;
const solanaAddressRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;

// Supported currencies
const supportedCurrencies = ['BTC', 'ETH', 'USDT', 'BNB', 'TRX', 'SOL'] as const;
const supportedLanguages = ['tr', 'en'] as const;

/**
 * Authentication Schemas
 */
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email is too long'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password is too long'),
  rememberMe: z.boolean().optional(),
  captcha: z.string().optional()
});

export const registerSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name is too long')
    .regex(/^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$/, 'First name contains invalid characters'),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name is too long')
    .regex(/^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$/, 'Last name contains invalid characters'),
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email is too long'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password is too long')
    .regex(passwordRegex, 'Password must contain uppercase, lowercase, number and special character'),
  confirmPassword: z
    .string()
    .min(1, 'Please confirm your password'),
  phone: z
    .string()
    .optional()
    .refine((val) => !val || phoneRegex.test(val), 'Please enter a valid phone number'),
  termsAccepted: z
    .boolean()
    .refine((val) => val === true, 'You must accept the terms and conditions'),
  privacyAccepted: z
    .boolean()
    .refine((val) => val === true, 'You must accept the privacy policy'),
  marketingAccepted: z.boolean().optional()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

export const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email is too long')
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password is too long')
    .regex(passwordRegex, 'Password must contain uppercase, lowercase, number and special character'),
  confirmPassword: z
    .string()
    .min(1, 'Please confirm your password')
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

/**
 * Investment Schemas
 */
export const investmentSchema = z.object({
  packageId: z
    .string()
    .min(1, 'Investment package is required')
    .uuid('Invalid package ID'),
  amount: z
    .number()
    .min(50, 'Minimum investment amount is 50 USDT')
    .max(100000, 'Maximum investment amount is 100,000 USDT')
    .multipleOf(0.01, 'Amount must have at most 2 decimal places'),
  currency: z
    .enum(supportedCurrencies, { errorMap: () => ({ message: 'Invalid currency' }) }),
  termsAccepted: z
    .boolean()
    .refine((val) => val === true, 'You must accept the investment terms'),
  riskAccepted: z
    .boolean()
    .refine((val) => val === true, 'You must acknowledge the risk disclosure')
});

export const withdrawalSchema = z.object({
  amount: z
    .number()
    .min(50, 'Minimum withdrawal amount is 50 USDT')
    .max(1000000, 'Maximum withdrawal amount is 1,000,000 USDT')
    .multipleOf(0.01, 'Amount must have at most 2 decimal places'),
  currency: z
    .enum(supportedCurrencies, { errorMap: () => ({ message: 'Invalid currency' }) }),
  address: z
    .string()
    .min(1, 'Withdrawal address is required')
    .max(100, 'Address is too long')
    .refine((address) => {
      // Basic address validation - more specific validation on backend
      return address.length >= 20 && address.length <= 100;
    }, 'Invalid address format'),
  twoFactorCode: z
    .string()
    .optional()
    .refine((code) => !code || /^\d{6}$/.test(code), 'Two-factor code must be 6 digits'),
  withdrawalPassword: z
    .string()
    .min(1, 'Withdrawal password is required')
    .max(128, 'Password is too long')
});

/**
 * Crypto Address Schemas
 */
export const cryptoAddressSchema = z.object({
  currency: z
    .enum(supportedCurrencies, { errorMap: () => ({ message: 'Invalid currency' }) }),
  address: z
    .string()
    .min(1, 'Address is required')
    .max(100, 'Address is too long')
    .refine((address) => {
      // Basic validation - specific validation on backend
      return address.length >= 20;
    }, 'Invalid address format')
});

export const addressValidationSchema = z.object({
  address: z
    .string()
    .min(1, 'Address is required')
    .max(100, 'Address is too long'),
  currency: z
    .enum(supportedCurrencies, { errorMap: () => ({ message: 'Invalid currency' }) })
});

/**
 * Profile & Settings Schemas
 */
export const profileUpdateSchema = z.object({
  firstName: z
    .string()
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name is too long')
    .regex(/^[a-zA-Z\s]+$/, 'First name contains invalid characters'),
  lastName: z
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name is too long')
    .regex(/^[a-zA-Z\s]+$/, 'Last name contains invalid characters'),
  phone: z
    .string()
    .optional()
    .refine((val) => !val || phoneRegex.test(val), 'Please enter a valid phone number'),
  language: z
    .enum(supportedLanguages, { errorMap: () => ({ message: 'Invalid language' }) })
    .optional(),
  timezone: z.string().optional(),
  notifications: z.object({
    email: z.boolean(),
    sms: z.boolean(),
    push: z.boolean()
  }).optional()
});

export const changePasswordSchema = z.object({
  currentPassword: z
    .string()
    .min(1, 'Current password is required'),
  newPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password is too long')
    .regex(passwordRegex, 'Password must contain uppercase, lowercase, number and special character'),
  confirmPassword: z
    .string()
    .min(1, 'Please confirm your new password')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
}).refine((data) => data.currentPassword !== data.newPassword, {
  message: "New password must be different from current password",
  path: ["newPassword"]
});

/**
 * Two-Factor Authentication Schemas
 */
export const twoFactorSetupSchema = z.object({
  secret: z.string().min(1, 'Secret is required'),
  code: z
    .string()
    .min(6, 'Code must be 6 digits')
    .max(6, 'Code must be 6 digits')
    .regex(/^\d{6}$/, 'Code must contain only numbers')
});

export const twoFactorVerifySchema = z.object({
  code: z
    .string()
    .min(6, 'Code must be 6 digits')
    .max(6, 'Code must be 6 digits')
    .regex(/^\d{6}$/, 'Code must contain only numbers')
});

/**
 * Search & Filter Schemas
 */
export const searchSchema = z.object({
  query: z
    .string()
    .max(100, 'Search query is too long')
    .regex(/^[a-zA-Z0-9\s\-_.@]+$/, 'Search query contains invalid characters')
    .optional(),
  category: z.string().optional(),
  sortBy: z.enum(['date', 'amount', 'status', 'name']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  page: z.number().min(1).max(1000).optional(),
  limit: z.number().min(1).max(100).optional()
});

/**
 * Type exports for TypeScript
 */
export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
export type InvestmentFormData = z.infer<typeof investmentSchema>;
export type WithdrawalFormData = z.infer<typeof withdrawalSchema>;
export type CryptoAddressFormData = z.infer<typeof cryptoAddressSchema>;
export type ProfileUpdateFormData = z.infer<typeof profileUpdateSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
export type TwoFactorSetupFormData = z.infer<typeof twoFactorSetupSchema>;
export type TwoFactorVerifyFormData = z.infer<typeof twoFactorVerifySchema>;
export type SearchFormData = z.infer<typeof searchSchema>;
