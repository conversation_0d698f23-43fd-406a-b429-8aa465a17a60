import { Request, Response, NextFunction } from 'express';
import { logger, logPerformance } from '../utils/logger';

export const performanceMonitor = (req: Request, res: Response, next: NextFunction) => {
  const start = process.hrtime();

  // Response bittiğinde süreyi hesapla
  res.on('finish', () => {
    const diff = process.hrtime(start);
    const time = diff[0] * 1e3 + diff[1] * 1e-6; // Milisaniyeye çevir
    
    // Performans metriklerini kaydet
    logPerformance(req.path, time, {
      method: req.method,
      statusCode: res.statusCode,
      userAgent: req.headers['user-agent'],
      ip: req.ip
    });

    // Yavaş istekleri tespit et ve logla
    if (time > 1000) { // 1 saniyeden uzun süren istekler
      logger.warn('Yavaş istek tespit edildi:', {
        path: req.path,
        method: req.method,
        duration: time,
        statusCode: res.statusCode
      });
    }
  });

  next();
};