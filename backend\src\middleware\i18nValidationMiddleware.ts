import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';
import { detectLanguage, getTranslation } from '../i18n';
import DOMPurify from 'isomorphic-dompurify';

/**
 * Sanitization helper function
 */
export const sanitizeInput = (value: string): string => {
  if (typeof value !== 'string') return value;
  
  // Remove HTML tags and potentially dangerous content
  const cleaned = DOMPurify.sanitize(value, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
  
  // Trim whitespace
  return cleaned.trim();
};

/**
 * Get validation messages with i18n support
 */
const getValidationMessages = (req: Request) => {
  const userLanguage = detectLanguage(req);
  const t = (key: string, options?: any) => getTranslation(userLanguage, key, options);

  return {
    emailRequired: t('auth:validation.emailRequired'),
    emailInvalid: t('auth:validation.validEmailRequired'),
    passwordRequired: t('auth:validation.passwordRequired'),
    passwordLength: t('auth:validation.passwordLength'),
    passwordStrength: t('auth:validation.passwordStrength'),
    firstNameRequired: t('auth:validation.firstNameRequired'),
    firstNameLength: t('auth:validation.firstNameLength'),
    firstNameFormat: t('auth:validation.firstNameFormat'),
    lastNameRequired: t('auth:validation.lastNameRequired'),
    lastNameLength: t('auth:validation.lastNameLength'),
    lastNameFormat: t('auth:validation.lastNameFormat'),
    usernameLength: t('auth:validation.usernameLength'),
    usernameFormat: t('auth:validation.usernameFormat'),
    birthDateInvalid: t('auth:validation.birthDateInvalid'),
    ageRestriction: t('auth:validation.ageRestriction'),
    phoneInvalid: t('auth:validation.phoneInvalid'),
    countryLength: t('auth:validation.countryLength'),
    cityLength: t('auth:validation.cityLength'),
    referralCodeLength: t('auth:validation.referralCodeLength'),
    referralCodeFormat: t('auth:validation.referralCodeFormat'),
    marketingConsentBoolean: t('auth:validation.marketingConsentBoolean')
  };
};

/**
 * Custom validation error handler with i18n support
 */
export const handleI18nValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    // Detect user's language
    const userLanguage = detectLanguage(req);
    const t = (key: string, options?: any) => getTranslation(userLanguage, key, options);
    const messages = getValidationMessages(req);

    // Map validation errors to i18n messages
    const translatedErrors = errors.array().map(error => {
      let translatedMessage = error.msg;
      
      // Map common validation messages to i18n keys
      switch (error.msg) {
        case 'Please provide a valid email address':
          translatedMessage = messages.emailInvalid;
          break;
        case 'Password must be between 8 and 128 characters':
          translatedMessage = messages.passwordLength;
          break;
        case 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character':
          translatedMessage = messages.passwordStrength;
          break;
        case 'First name must be between 1 and 50 characters':
          translatedMessage = messages.firstNameLength;
          break;
        case 'First name can only contain letters, spaces, hyphens, and apostrophes':
          translatedMessage = messages.firstNameFormat;
          break;
        case 'Last name must be between 1 and 50 characters':
          translatedMessage = messages.lastNameLength;
          break;
        case 'Last name can only contain letters, spaces, hyphens, and apostrophes':
          translatedMessage = messages.lastNameFormat;
          break;
        case 'Username must be between 3 and 20 characters':
          translatedMessage = messages.usernameLength;
          break;
        case 'Username can only contain letters, numbers, and underscores':
          translatedMessage = messages.usernameFormat;
          break;
        case 'Please provide a valid birth date':
          translatedMessage = messages.birthDateInvalid;
          break;
        case 'You must be at least 18 years old':
          translatedMessage = messages.ageRestriction;
          break;
        case 'Please provide a valid phone number':
          translatedMessage = messages.phoneInvalid;
          break;
        case 'Country name cannot exceed 100 characters':
          translatedMessage = messages.countryLength;
          break;
        case 'City name cannot exceed 100 characters':
          translatedMessage = messages.cityLength;
          break;
        case 'Referral code must be between 3 and 20 characters':
          translatedMessage = messages.referralCodeLength;
          break;
        case 'Referral code can only contain letters and numbers':
          translatedMessage = messages.referralCodeFormat;
          break;
        case 'Marketing consent must be a boolean value':
          translatedMessage = messages.marketingConsentBoolean;
          break;
        case 'Password is required':
          translatedMessage = messages.passwordRequired;
          break;
        default:
          // Keep original message if no translation found
          break;
      }

      return {
        field: (error as any).param || (error as any).path,
        message: translatedMessage,
        value: (error as any).value
      };
    });

    res.status(400).json({
      status: 'error',
      message: t('auth:errors.validationError'),
      errors: translatedErrors
    });
    return;
  }
  next();
};

/**
 * User registration validation with i18n support
 */
export const validateUserRegistrationI18n = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
    .customSanitizer(sanitizeInput),

  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),

  body('firstName')
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters')
    .matches(/^[a-zA-ZÀ-ÿğüşıöçĞÜŞİÖÇ\s'-]+$/)
    .withMessage('First name can only contain letters, spaces, hyphens, and apostrophes')
    .customSanitizer(sanitizeInput),

  body('lastName')
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters')
    .matches(/^[a-zA-ZÀ-ÿğüşıöçĞÜŞİÖÇ\s'-]+$/)
    .withMessage('Last name can only contain letters, spaces, hyphens, and apostrophes')
    .customSanitizer(sanitizeInput),

  body('username')
    .optional()
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores')
    .customSanitizer(sanitizeInput),

  body('birthDate')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid birth date')
    .custom((value) => {
      if (value) {
        const birthDate = new Date(value);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        if (age < 18) {
          throw new Error('You must be at least 18 years old');
        }
      }
      return true;
    }),

  body('phoneNumber')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number')
    .customSanitizer(sanitizeInput),

  body('country')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Country name cannot exceed 100 characters')
    .customSanitizer(sanitizeInput),

  body('city')
    .optional()
    .isLength({ max: 100 })
    .withMessage('City name cannot exceed 100 characters')
    .customSanitizer(sanitizeInput),

  body('referralCode')
    .optional()
    .isLength({ min: 3, max: 20 })
    .withMessage('Referral code must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9]+$/)
    .withMessage('Referral code can only contain letters and numbers')
    .customSanitizer(sanitizeInput),

  body('marketingConsent')
    .optional()
    .isBoolean()
    .withMessage('Marketing consent must be a boolean value'),

  handleI18nValidationErrors
];

/**
 * User login validation with i18n support
 */
export const validateUserLoginI18n = [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail()
    .customSanitizer(sanitizeInput),

  body('password')
    .isLength({ min: 1, max: 128 })
    .withMessage('Password is required')
    .customSanitizer(sanitizeInput),

  handleI18nValidationErrors
];
