import mongoose from 'mongoose';
import { getEnhancedSocketService } from './enhancedSocketService';
import logger from '../utils/logger';

/**
 * Enhanced Wallet Service for Real-Time Balance Management
 * Provides comprehensive wallet operations with instant synchronization
 */
export class EnhancedWalletService {
  
  /**
   * Update wallet balance with real-time synchronization
   */
  async updateBalance(
    userId: string,
    cryptocurrency: string,
    amount: number,
    operation: 'credit' | 'debit',
    session?: any,
    metadata?: any
  ): Promise<boolean> {
    try {
      // Mock wallet update - in real implementation, this would update the actual wallet model
      const balanceChange = operation === 'credit' ? amount : -amount;
      
      logger.info(`Wallet balance ${operation}:`, {
        userId,
        cryptocurrency,
        amount,
        balanceChange
      });

      // Emit real-time wallet update
      const socketService = getEnhancedSocketService();
      socketService.notifyWalletBalanceUpdated(userId, {
        cryptocurrency,
        balanceChange,
        changeType: operation,
        amount,
        timestamp: new Date().toISOString(),
        metadata
      });

      return true;
    } catch (error) {
      logger.error('Error updating wallet balance:', error);
      throw error;
    }
  }

  /**
   * Check if user has sufficient balance
   */
  async checkBalance(
    userId: string,
    cryptocurrency: string,
    requiredAmount: number
  ): Promise<boolean> {
    try {
      // Mock balance check - in real implementation, this would check actual wallet balance
      logger.info(`Balance check for user ${userId}:`, {
        cryptocurrency,
        requiredAmount
      });

      // For demo purposes, assume user has sufficient balance
      return true;
    } catch (error) {
      logger.error('Error checking wallet balance:', error);
      return false;
    }
  }

  /**
   * Get user wallet balances
   */
  async getUserBalances(userId: string): Promise<any> {
    try {
      // Mock wallet balances - in real implementation, this would fetch from wallet model
      const mockBalances = {
        BTC: { balance: 0.5, interestBalance: 0.01, commissionBalance: 0.005 },
        ETH: { balance: 2.5, interestBalance: 0.1, commissionBalance: 0.05 },
        USDT: { balance: 1000, interestBalance: 50, commissionBalance: 25 },
        TRX: { balance: 50000, interestBalance: 2500, commissionBalance: 1000 }
      };

      logger.info(`Retrieved wallet balances for user: ${userId}`);
      return mockBalances;
    } catch (error) {
      logger.error('Error getting user balances:', error);
      throw error;
    }
  }

  /**
   * Get withdrawable balance for specific cryptocurrency
   */
  async getWithdrawableBalance(
    userId: string,
    cryptocurrency: string,
    withdrawalType: 'balance' | 'interest' | 'commission'
  ): Promise<number> {
    try {
      const balances = await this.getUserBalances(userId);
      const cryptoBalance = balances[cryptocurrency];

      if (!cryptoBalance) {
        return 0;
      }

      switch (withdrawalType) {
        case 'balance':
          return cryptoBalance.balance || 0;
        case 'interest':
          return cryptoBalance.interestBalance || 0;
        case 'commission':
          return cryptoBalance.commissionBalance || 0;
        default:
          return 0;
      }
    } catch (error) {
      logger.error('Error getting withdrawable balance:', error);
      return 0;
    }
  }

  /**
   * Create or update wallet for user
   */
  async createOrUpdateWallet(
    userId: string,
    cryptocurrency: string,
    initialBalance: number = 0
  ): Promise<any> {
    try {
      // Mock wallet creation - in real implementation, this would create/update wallet model
      const wallet = {
        _id: new mongoose.Types.ObjectId(),
        userId: new mongoose.Types.ObjectId(userId),
        cryptocurrency,
        balance: initialBalance,
        interestBalance: 0,
        commissionBalance: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      logger.info(`Wallet created/updated for user ${userId}:`, {
        cryptocurrency,
        initialBalance
      });

      return wallet;
    } catch (error) {
      logger.error('Error creating/updating wallet:', error);
      throw error;
    }
  }

  /**
   * Process daily interest distribution
   */
  async distributeInterest(
    userId: string,
    cryptocurrency: string,
    interestAmount: number,
    investmentPackageId?: string
  ): Promise<boolean> {
    try {
      // Update interest balance
      await this.updateBalance(
        userId,
        cryptocurrency,
        interestAmount,
        'credit',
        undefined,
        {
          type: 'interest_distribution',
          investmentPackageId,
          dailyRate: 0.01 // 1% daily
        }
      );

      logger.info(`Interest distributed:`, {
        userId,
        cryptocurrency,
        interestAmount,
        investmentPackageId
      });

      return true;
    } catch (error) {
      logger.error('Error distributing interest:', error);
      return false;
    }
  }

  /**
   * Process commission distribution
   */
  async distributeCommission(
    userId: string,
    cryptocurrency: string,
    commissionAmount: number,
    referralUserId: string,
    level: number
  ): Promise<boolean> {
    try {
      // Update commission balance
      await this.updateBalance(
        userId,
        cryptocurrency,
        commissionAmount,
        'credit',
        undefined,
        {
          type: 'commission_distribution',
          referralUserId,
          level,
          commissionRate: level === 1 ? 0.05 : 0.02 // 5% for level 1, 2% for level 2
        }
      );

      logger.info(`Commission distributed:`, {
        userId,
        cryptocurrency,
        commissionAmount,
        referralUserId,
        level
      });

      return true;
    } catch (error) {
      logger.error('Error distributing commission:', error);
      return false;
    }
  }

  /**
   * Get wallet transaction history
   */
  async getWalletHistory(
    userId: string,
    cryptocurrency?: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    transactions: any[];
    pagination: any;
  }> {
    try {
      // Mock transaction history - in real implementation, this would fetch from transaction model
      const mockTransactions = [
        {
          id: '1',
          type: 'deposit',
          cryptocurrency: 'BTC',
          amount: 0.1,
          status: 'completed',
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
        },
        {
          id: '2',
          type: 'interest',
          cryptocurrency: 'BTC',
          amount: 0.001,
          status: 'completed',
          createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000)
        },
        {
          id: '3',
          type: 'withdrawal',
          cryptocurrency: 'USDT',
          amount: 100,
          status: 'pending',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000)
        }
      ];

      const filteredTransactions = cryptocurrency 
        ? mockTransactions.filter(tx => tx.cryptocurrency === cryptocurrency)
        : mockTransactions;

      const total = filteredTransactions.length;
      const skip = (page - 1) * limit;
      const paginatedTransactions = filteredTransactions.slice(skip, skip + limit);

      return {
        transactions: paginatedTransactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting wallet history:', error);
      throw error;
    }
  }

  /**
   * Get wallet statistics
   */
  async getWalletStats(userId: string): Promise<any> {
    try {
      const balances = await this.getUserBalances(userId);
      
      // Calculate total USD value
      const mockPrices: { [key: string]: number } = {
        'BTC': 50000,
        'ETH': 3000,
        'USDT': 1,
        'TRX': 0.1
      };

      let totalUSDValue = 0;
      let totalInterestUSD = 0;
      let totalCommissionUSD = 0;

      Object.keys(balances).forEach(crypto => {
        const price = mockPrices[crypto] || 1;
        const balance = balances[crypto];
        
        totalUSDValue += (balance.balance || 0) * price;
        totalInterestUSD += (balance.interestBalance || 0) * price;
        totalCommissionUSD += (balance.commissionBalance || 0) * price;
      });

      const stats = {
        totalUSDValue,
        totalInterestUSD,
        totalCommissionUSD,
        totalEarnings: totalInterestUSD + totalCommissionUSD,
        activeCryptocurrencies: Object.keys(balances).length,
        lastUpdated: new Date().toISOString()
      };

      logger.info(`Wallet stats calculated for user: ${userId}`, stats);
      return stats;
    } catch (error) {
      logger.error('Error calculating wallet stats:', error);
      throw error;
    }
  }

  /**
   * Sync wallet data across all components
   */
  async syncWalletData(userId: string): Promise<boolean> {
    try {
      const balances = await this.getUserBalances(userId);
      const stats = await this.getWalletStats(userId);

      // Emit comprehensive wallet sync event
      const socketService = getEnhancedSocketService();
      socketService.notifyWalletBalanceUpdated(userId, {
        type: 'full_sync',
        balances,
        stats,
        timestamp: new Date().toISOString()
      });

      logger.info(`Wallet data synced for user: ${userId}`);
      return true;
    } catch (error) {
      logger.error('Error syncing wallet data:', error);
      return false;
    }
  }

  /**
   * Validate wallet address format
   */
  validateWalletAddress(address: string, cryptocurrency: string, network: string): boolean {
    try {
      // Basic validation - in real implementation, this would use proper address validation libraries
      if (!address || address.length < 10) {
        return false;
      }

      // Cryptocurrency-specific validation
      switch (cryptocurrency.toUpperCase()) {
        case 'BTC':
          return address.match(/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/) !== null ||
                 address.match(/^bc1[a-z0-9]{39,59}$/) !== null;
        case 'ETH':
        case 'USDT':
        case 'BNB':
          return address.match(/^0x[a-fA-F0-9]{40}$/) !== null;
        case 'TRX':
          return address.match(/^T[A-Za-z1-9]{33}$/) !== null;
        case 'SOL':
          return address.length >= 32 && address.length <= 44;
        case 'DOGE':
          return address.match(/^D{1}[5-9A-HJ-NP-U]{1}[1-9A-HJ-NP-Za-km-z]{32}$/) !== null;
        default:
          return true; // Allow unknown cryptocurrencies
      }
    } catch (error) {
      logger.error('Error validating wallet address:', error);
      return false;
    }
  }
}

export default EnhancedWalletService;
