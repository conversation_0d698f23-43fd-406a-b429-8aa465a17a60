const mongoose = require('mongoose');

// MongoDB connection
const MONGO_URI = '*******************************************************************************************';

async function checkInvestmentPackage() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Check the investment package that was created
    const investmentPackageId = '6836d083cf1c66c999689e72';
    const depositId = '6836d0664fb2594881c0fd77';

    // Get the investment package collection
    const db = mongoose.connection.db;
    const investmentPackages = db.collection('investmentpackages');
    const transactions = db.collection('transactions');

    // Find the investment package
    const investmentPackage = await investmentPackages.findOne({ _id: new mongoose.Types.ObjectId(investmentPackageId) });
    console.log('\n📦 Investment Package:');
    console.log(JSON.stringify(investmentPackage, null, 2));

    // Find the updated deposit
    const deposit = await transactions.findOne({ _id: new mongoose.Types.ObjectId(depositId) });
    console.log('\n💰 Updated Deposit:');
    console.log(JSON.stringify(deposit, null, 2));

    // Check if the deposit now has the investmentId
    if (deposit && deposit.investmentId) {
      console.log(`\n✅ SUCCESS: Deposit ${depositId} now has investmentId: ${deposit.investmentId}`);
    } else {
      console.log(`\n❌ ISSUE: Deposit ${depositId} does not have investmentId set`);
    }

    // Find all investment packages for the test user
    const testUserId = '6835c5e6fa02cacf367d5b99';
    const userInvestmentPackages = await investmentPackages.find({ userId: new mongoose.Types.ObjectId(testUserId) }).toArray();
    console.log(`\n📊 All Investment Packages for Test User (${testUserId}):`);
    console.log(`Found ${userInvestmentPackages.length} investment packages`);
    userInvestmentPackages.forEach((pkg, index) => {
      console.log(`${index + 1}. ID: ${pkg._id}, Amount: ${pkg.amount}, Asset: ${pkg.asset}, Status: ${pkg.status}, Created: ${pkg.createdAt}`);
    });

  } catch (error) {
    console.error('❌ Error checking investment package:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  checkInvestmentPackage()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { checkInvestmentPackage };
