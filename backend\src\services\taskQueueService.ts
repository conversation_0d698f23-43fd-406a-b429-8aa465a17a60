import Bull, { Job, Queue, QueueOptions } from 'bull';
import { logger } from '../utils/logger';
import { initializeSocketService } from './socketService';

interface TaskConfig {
  name: string;
  concurrency?: number;
  attempts?: number;
  backoff?: {
    type: 'fixed' | 'exponential';
    delay: number;
  };
}

interface TaskData {
  userId: string;
  type: string;
  payload: any;
}

class TaskQueueService {
  private static instance: TaskQueueService;
  private queues: Map<string, Queue> = new Map();
  private readonly defaultConfig: QueueOptions = {
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
    },
    defaultJobOptions: {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 1000
      },
      removeOnComplete: 100,
      removeOnFail: 1000
    }
  } as QueueOptions;

  private constructor() {}

  public static getInstance(): TaskQueueService {
    if (!TaskQueueService.instance) {
      TaskQueueService.instance = new TaskQueueService();
    }
    return TaskQueueService.instance;
  }

  public createQueue(config: TaskConfig): Queue {
    if (this.queues.has(config.name)) {
      return this.queues.get(config.name)!;
    }

    try {
      // Check if we're in development mode and Redis is not available
      if (process.env.NODE_ENV === 'development' && process.env.USE_MOCK_QUEUE === 'true') {
        logger.info(`Creating mock queue for ${config.name} in development mode`);
        // Create a mock queue for development
        return this.createMockQueue(config);
      }

      const queue = new Bull(config.name, {
        ...this.defaultConfig,
        defaultJobOptions: {
          ...this.defaultConfig.defaultJobOptions,
          attempts: config.attempts || 3,
          backoff: config.backoff || {
            type: 'exponential',
            delay: 1000
          }
        }
      });

      this.setupQueueEvents(queue);
      this.queues.set(config.name, queue);

      return queue;
    } catch (error) {
      logger.error(`Error creating queue ${config.name}:`, error);

      // In development, fall back to mock queue if real queue creation fails
      if (process.env.NODE_ENV === 'development') {
        logger.info(`Falling back to mock queue for ${config.name} in development mode`);
        return this.createMockQueue(config);
      }

      throw error;
    }
  }

  private createMockQueue(config: TaskConfig): Queue {
    // Create a minimal mock implementation that satisfies the Queue interface
    // This is just for development when Redis might not be available
    logger.warn(`Using mock queue for ${config.name} - limited functionality available`);

    // Create a mock Job class that implements the minimum required interface
    class MockJob {
      id: string;
      data: any;
      opts: any;
      queue: any;
      timestamp: number;
      stacktrace: string[];
      returnvalue: any;
      attemptsMade: number;
      finishedOn?: number;
      processedOn?: number;
      failedReason?: string;

      constructor(id: string, data: any, opts: any = {}) {
        this.id = id;
        this.data = data;
        this.opts = opts;
        this.queue = { name: config.name };
        this.timestamp = Date.now();
        this.stacktrace = [];
        this.returnvalue = null;
        this.attemptsMade = 0;
      }

      async remove(): Promise<void> {
        logger.info(`[MOCK] Removed job ${this.id}`);
        return Promise.resolve();
      }

      async retry(): Promise<void> {
        logger.info(`[MOCK] Retried job ${this.id}`);
        return Promise.resolve();
      }

      async discard(): Promise<void> {
        logger.info(`[MOCK] Discarded job ${this.id}`);
        return Promise.resolve();
      }

      async finished(): Promise<any> {
        return this.returnvalue;
      }

      async moveToCompleted(): Promise<any> {
        this.finishedOn = Date.now();
        return this;
      }

      async moveToFailed(err: Error): Promise<any> {
        this.failedReason = err.message;
        return this;
      }

      toJSON(): any {
        return {
          id: this.id,
          data: this.data,
          opts: this.opts,
          timestamp: this.timestamp,
          attemptsMade: this.attemptsMade,
          finishedOn: this.finishedOn,
          processedOn: this.processedOn,
          failedReason: this.failedReason,
        };
      }
    }

    // Create the mock queue
    const mockQueue: Partial<Queue> = {
      name: config.name,

      // Implement add method that returns a Job
      add: async (data: any, opts?: any) => {
        logger.info(`[MOCK] Added job to ${config.name}:`, { data, opts });
        const jobId = `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        return new MockJob(jobId, data, opts) as any;
      },

      // Implement process method with proper signature
      process: function(concurrencyOrCallback: any, maybeCallback?: any): Promise<void> {
        // Handle both signatures: process(callback) and process(concurrency, callback)
        const callback = typeof concurrencyOrCallback === 'function'
          ? concurrencyOrCallback
          : maybeCallback;

        const concurrency = typeof concurrencyOrCallback === 'function'
          ? 1
          : concurrencyOrCallback;

        logger.info(`[MOCK] Registered processor for ${config.name} with concurrency ${concurrency}`);
        return Promise.resolve();
      },

      getJob: async (jobId: string) => {
        logger.info(`[MOCK] Getting job ${jobId} from ${config.name}`);
        return null;
      },

      getWaitingCount: async () => 0,
      getActiveCount: async () => 0,
      getCompletedCount: async () => 0,
      getFailedCount: async () => 0,
      getDelayedCount: async () => 0,

      pause: async () => {
        logger.info(`[MOCK] Paused queue ${config.name}`);
        return Promise.resolve();
      },

      resume: async () => {
        logger.info(`[MOCK] Resumed queue ${config.name}`);
        return Promise.resolve();
      },

      // Fix clean method signature
      clean: async (grace: number, status?: string, limit?: number) => {
        logger.info(`[MOCK] Cleaned queue ${config.name} with status: ${status || 'all'}`);
        return [] as any[];
      },

      close: async () => {
        logger.info(`[MOCK] Closed queue ${config.name}`);
        return Promise.resolve();
      },

      on: function(event: string, callback: Function): any {
        logger.info(`[MOCK] Registered event listener for ${event} on ${config.name}`);
        return this;
      }
    };

    // Store the mock queue
    this.queues.set(config.name, mockQueue as Queue);

    return mockQueue as Queue;
  }

  private setupQueueEvents(queue: Queue): void {
    queue.on('error', (error) => {
      logger.error(`Queue error in ${queue.name}:`, error);
    });

    queue.on('failed', (job, error) => {
      logger.error(`Job failed in ${queue.name}:`, {
        jobId: job.id,
        error: error.message,
        stack: error.stack
      });

      this.notifyUser(job.data.userId, {
        type: 'task_failed',
        payload: {
          taskId: job.id,
          taskType: job.data.type,
          error: error.message
        }
      });
    });

    queue.on('completed', (job) => {
      logger.info(`Job completed in ${queue.name}:`, {
        jobId: job.id,
        type: job.data.type
      });

      this.notifyUser(job.data.userId, {
        type: 'task_completed',
        payload: {
          taskId: job.id,
          taskType: job.data.type,
          result: job.returnvalue
        }
      });
    });

    queue.on('stalled', (job) => {
      logger.warn(`Job stalled in ${queue.name}:`, {
        jobId: job.id,
        type: job.data.type
      });
    });
  }

  private notifyUser(userId: string, message: any): void {
    try {
      const socketService = initializeSocketService(null);
      socketService.broadcastToUser(userId, message);
    } catch (error) {
      logger.error('Failed to notify user:', error);
    }
  }

  public async addTask(
    queueName: string,
    data: TaskData,
    opts?: Bull.JobOptions
  ): Promise<Job> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    try {
      const job = await queue.add(data, {
        ...opts,
        jobId: `${data.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      });

      logger.info(`Added job to ${queueName}:`, {
        jobId: job.id,
        type: data.type
      });

      return job;
    } catch (error) {
      logger.error(`Failed to add job to ${queueName}:`, error);
      throw error;
    }
  }

  public async processQueue(
    queueName: string,
    processor: (job: Job) => Promise<any>,
    concurrency: number = 1
  ): Promise<void> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    queue.process(concurrency, async (job: Job) => {
      try {
        logger.info(`Processing job in ${queueName}:`, {
          jobId: job.id,
          type: job.data.type
        });

        const result = await processor(job);

        logger.info(`Job processed successfully in ${queueName}:`, {
          jobId: job.id,
          type: job.data.type
        });

        return result;
      } catch (error) {
        logger.error(`Error processing job in ${queueName}:`, {
          jobId: job.id,
          error
        });
        throw error;
      }
    });
  }

  public async getJobStatus(queueName: string, jobId: string): Promise<any> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    const job = await queue.getJob(jobId);
    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }

    const state = await job.getState();
    const progress = await job.progress();

    return {
      id: job.id,
      state,
      progress,
      data: job.data,
      returnvalue: job.returnvalue,
      failedReason: job.failedReason,
      stacktrace: job.stacktrace,
      timestamp: job.timestamp
    };
  }

  public async pauseQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    await queue.pause();
    logger.info(`Queue ${queueName} paused`);
  }

  public async resumeQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    await queue.resume();
    logger.info(`Queue ${queueName} resumed`);
  }

  public async cleanQueue(queueName: string): Promise<void> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    await queue.clean(0, 'completed');
    await queue.clean(0, 'failed');
    logger.info(`Queue ${queueName} cleaned`);
  }

  public async shutdown(): Promise<void> {
    const closePromises = Array.from(this.queues.values()).map(queue => queue.close());
    await Promise.all(closePromises);
    this.queues.clear();
    logger.info('All queues closed');
  }

  public getQueueMetrics(queueName: string): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  }> {
    const queue = this.queues.get(queueName);
    if (!queue) {
      throw new Error(`Queue ${queueName} not found`);
    }

    return Promise.all([
      queue.getWaitingCount(),
      queue.getActiveCount(),
      queue.getCompletedCount(),
      queue.getFailedCount(),
      queue.getDelayedCount()
    ]).then(([waiting, active, completed, failed, delayed]) => ({
      waiting,
      active,
      completed,
      failed,
      delayed
    }));
  }
}

export const taskQueueService = TaskQueueService.getInstance();