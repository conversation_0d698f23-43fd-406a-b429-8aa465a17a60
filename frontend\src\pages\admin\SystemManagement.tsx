import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Grid,
  GridItem,
  FormControl,
  FormLabel,
  Input,
  Button,
  Text,
  Flex,
  VStack,
  useToast,
  Divider,
  Switch,
  Card,
  CardBody,
  CardHeader,
  Icon
} from '@chakra-ui/react';
import { FaServer } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { API_URL } from "../../config";
import axios from 'axios';
import useAuth from '../../hooks/useAuth';

// Define system config interface
interface SystemConfig {
  siteName: string;
  siteDescription: string;
  maintenanceMode: boolean;
  commissionRate: number;
  referralRate: number;
  minimumDeposit: number;
  minimumWithdrawal: number;
  withdrawalsEnabled: boolean;
  depositsEnabled: boolean;
}

const SystemManagement = () => {
  const { t } = useTranslation('translation');
  const toast = useToast();
  const { user, token } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // System config state
  const [systemConfig, setSystemConfig] = useState<SystemConfig>({
    siteName: 'Shipping Finance',
    siteDescription: 'Secure Crypto Investment Platform',
    maintenanceMode: false,
    commissionRate: 1.0,
    referralRate: 3.0,
    minimumDeposit: 100,
    minimumWithdrawal: 50,
    withdrawalsEnabled: true,
    depositsEnabled: true,
  });

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  // Fetch system config on component mount
  useEffect(() => {
    fetchSystemConfig();
  }, []);

  // Fetch system configuration from API
  const fetchSystemConfig = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`${API_URL}/admin/system/config`, {
        withCredentials: true
      });

      if (response.data.success) {
        setSystemConfig(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching system config:', error);
      toast({
        title: t('admin.system.error'),
        description: t('admin.system.errorFetchingConfig'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Save system configuration
  const saveSystemConfig = async () => {
    try {
      setIsSaving(true);
      const response = await axios.put(
        `${API_URL}/admin/system/config`,
        systemConfig,
        { withCredentials: true }
      );

      if (response.data.success) {
        toast({
          title: t('admin.system.success'),
          description: t('admin.system.configSaved'),
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error saving system config:', error);
      toast({
        title: t('admin.system.error'),
        description: t('admin.system.errorSavingConfig'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSaving(false);
    }
  };



  return (
    <Box p={4}>
      <Heading size="lg" mb={6} color={textColor}>{t('admin.system.title')}</Heading>

      {/* General Settings Section - No longer using tabs since crypto addresses moved to separate page */}
      <Box bg={bgColor} p={6} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
        <Heading size="md" mb={6} color={textColor}>
          <Icon as={FaServer} mr={2} />
          {t('admin.system.generalSettings')}
        </Heading>
        <Grid templateColumns={{ base: "1fr", lg: "repeat(2, 1fr)" }} gap={6}>
              {/* Site Settings */}
              <GridItem>
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardHeader>
                    <Heading size="md" color={textColor}>{t('admin.system.siteName')}</Heading>
                  </CardHeader>
                  <Divider borderColor={borderColor} />
                  <CardBody>
                    <VStack spacing={6} align="stretch">
                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.siteName')}</FormLabel>
                        <Input
                          value={systemConfig.siteName}
                          onChange={(e) => setSystemConfig({...systemConfig, siteName: e.target.value})}
                          bg={cardBgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.siteDescription')}</FormLabel>
                        <Input
                          value={systemConfig.siteDescription}
                          onChange={(e) => setSystemConfig({...systemConfig, siteDescription: e.target.value})}
                          bg={cardBgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>

                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="maintenance-mode" mb="0" color={secondaryTextColor}>
                          {t('admin.system.maintenanceMode')}
                        </FormLabel>
                        <Switch
                          id="maintenance-mode"
                          isChecked={systemConfig.maintenanceMode}
                          onChange={(e) => setSystemConfig({...systemConfig, maintenanceMode: e.target.checked})}
                          colorScheme="yellow"
                        />
                      </FormControl>
                    </VStack>
                  </CardBody>
                </Card>
              </GridItem>

              {/* Financial Settings */}
              <GridItem>
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardHeader>
                    <Heading size="md" color={textColor}>{t('admin.system.financialSettings')}</Heading>
                  </CardHeader>
                  <Divider borderColor={borderColor} />
                  <CardBody>
                    <VStack spacing={6} align="stretch">
                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.commissionRate')}</FormLabel>
                        <Flex align="center">
                          <Input
                            type="number"
                            value={systemConfig.commissionRate}
                            onChange={(e) => setSystemConfig({...systemConfig, commissionRate: parseFloat(e.target.value)})}
                            bg={cardBgColor}
                            borderColor={borderColor}
                            color={textColor}
                            maxW="100px"
                          />
                          <Text ml={2} color={secondaryTextColor}>%</Text>
                        </Flex>
                      </FormControl>

                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.referralRate')}</FormLabel>
                        <Flex align="center">
                          <Input
                            type="number"
                            value={systemConfig.referralRate}
                            onChange={(e) => setSystemConfig({...systemConfig, referralRate: parseFloat(e.target.value)})}
                            bg={cardBgColor}
                            borderColor={borderColor}
                            color={textColor}
                            maxW="100px"
                          />
                          <Text ml={2} color={secondaryTextColor}>%</Text>
                        </Flex>
                      </FormControl>

                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.minimumDeposit')}</FormLabel>
                        <Input
                          type="number"
                          value={systemConfig.minimumDeposit}
                          onChange={(e) => setSystemConfig({...systemConfig, minimumDeposit: parseFloat(e.target.value)})}
                          bg={cardBgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.minimumWithdrawal')}</FormLabel>
                        <Input
                          type="number"
                          value={systemConfig.minimumWithdrawal}
                          onChange={(e) => setSystemConfig({...systemConfig, minimumWithdrawal: parseFloat(e.target.value)})}
                          bg={cardBgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>

                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="deposits-enabled" mb="0" color={secondaryTextColor}>
                          {t('admin.system.depositsEnabled')}
                        </FormLabel>
                        <Switch
                          id="deposits-enabled"
                          isChecked={systemConfig.depositsEnabled}
                          onChange={(e) => setSystemConfig({...systemConfig, depositsEnabled: e.target.checked})}
                          colorScheme="yellow"
                        />
                      </FormControl>

                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="withdrawals-enabled" mb="0" color={secondaryTextColor}>
                          {t('admin.system.withdrawalsEnabled')}
                        </FormLabel>
                        <Switch
                          id="withdrawals-enabled"
                          isChecked={systemConfig.withdrawalsEnabled}
                          onChange={(e) => setSystemConfig({...systemConfig, withdrawalsEnabled: e.target.checked})}
                          colorScheme="yellow"
                        />
                      </FormControl>
                    </VStack>
                  </CardBody>
                </Card>
              </GridItem>
            </Grid>

        <Flex justify="flex-end" mt={8}>
          <Button
            colorScheme="yellow"
            size="lg"
            onClick={saveSystemConfig}
            isLoading={isSaving}
            loadingText={t('admin.system.saving')}
          >
            {t('admin.system.saveSettings')}
          </Button>
        </Flex>
      </Box>
    </Box>
  );
};

export default SystemManagement;
