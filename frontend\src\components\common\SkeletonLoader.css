/* Base skeleton animation */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Common skeleton styles */
.skeleton-line,
.skeleton-button,
.skeleton-header,
.skeleton-avatar,
.skeleton-input,
.skeleton-table-cell,
.skeleton-stat-card,
.skeleton-dashboard-chart {
  background-color: #eee;
  background-image: linear-gradient(90deg, #eee, #f5f5f5, #eee);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  border-radius: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .skeleton-line,
  .skeleton-button,
  .skeleton-header,
  .skeleton-avatar,
  .skeleton-input,
  .skeleton-table-cell,
  .skeleton-stat-card,
  .skeleton-dashboard-chart {
    background-color: #333;
    background-image: linear-gradient(90deg, #333, #3a3a3a, #333);
  }
}

/* Card skeleton */
.skeleton-card {
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  margin-bottom: 16px;
}

.skeleton-header {
  height: 24px;
  margin-bottom: 16px;
}

.skeleton-content {
  margin-bottom: 16px;
}

.skeleton-line {
  height: 16px;
  margin-bottom: 12px;
  width: 100%;
}

.skeleton-line-short {
  width: 60%;
}

.skeleton-line-medium {
  width: 80%;
}

.skeleton-footer {
  display: flex;
  justify-content: flex-end;
}

.skeleton-button {
  height: 36px;
  width: 120px;
  border-radius: 4px;
}

.skeleton-button-large {
  height: 48px;
  width: 100%;
  margin-top: 16px;
}

/* Table skeleton */
.skeleton-table {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.skeleton-table-header {
  display: flex;
  padding: 16px;
  background-color: #f9f9f9;
}

.skeleton-table-body {
  padding: 0 16px;
}

.skeleton-table-row {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.skeleton-table-row:last-child {
  border-bottom: none;
}

.skeleton-table-cell {
  flex: 1;
  height: 16px;
  margin-right: 16px;
}

.skeleton-table-cell:last-child {
  margin-right: 0;
}

/* Profile skeleton */
.skeleton-profile {
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.skeleton-profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.skeleton-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-right: 24px;
}

.skeleton-profile-info {
  flex: 1;
}

.skeleton-profile-body {
  padding-top: 16px;
  border-top: 1px solid #eee;
}

/* Dashboard skeleton */
.skeleton-dashboard {
  width: 100%;
}

.skeleton-dashboard-header {
  margin-bottom: 24px;
}

.skeleton-dashboard-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.skeleton-stat-card {
  flex: 1;
  height: 100px;
  border-radius: 8px;
}

.skeleton-dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.skeleton-dashboard-chart {
  height: 300px;
  border-radius: 8px;
}

/* Form skeleton */
.skeleton-form {
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.skeleton-form-field {
  margin-bottom: 16px;
}

.skeleton-input {
  height: 40px;
  width: 100%;
  border-radius: 4px;
  margin-top: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .skeleton-dashboard-stats {
    flex-direction: column;
  }
  
  .skeleton-stat-card {
    height: 80px;
  }
  
  .skeleton-dashboard-chart {
    height: 200px;
  }
  
  .skeleton-avatar {
    width: 60px;
    height: 60px;
  }
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  .skeleton-card,
  .skeleton-table,
  .skeleton-profile,
  .skeleton-form {
    background-color: #1E2329;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  .skeleton-table-header {
    background-color: #2B3139;
  }
  
  .skeleton-table-row {
    border-bottom-color: #2B3139;
  }
  
  .skeleton-profile-body {
    border-top-color: #2B3139;
  }
}
