import React, { useState } from 'react';
import {
  Box,
  Flex,
  Text,
  Icon,
  useColorModeValue
} from '@chakra-ui/react';
import { motion, useDragControls } from 'framer-motion';
import { FaUserShield, FaGripVertical } from 'react-icons/fa';

const MotionBox = motion(Box);

interface DraggableChatBubbleProps {
  onClick?: () => void;
  children?: React.ReactNode;
  icon?: React.ElementType;
  text?: string;
  colorScheme?: 'orange' | 'blue' | 'green' | 'purple';
}

const DraggableChatBubble: React.FC<DraggableChatBubbleProps> = ({
  onClick,
  children,
  icon = FaUserShield,
  text = "Admin Mode",
  colorScheme = 'orange'
}) => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const dragControls = useDragControls();

  const colorSchemes = {
    orange: {
      bg: 'linear-gradient(135deg, #FF6B35, #F7931E)',
      shadow: 'rgba(255, 107, 53, 0.4)',
      tail: '#FF6B35'
    },
    blue: {
      bg: 'linear-gradient(135deg, #4299E1, #3182CE)',
      shadow: 'rgba(66, 153, 225, 0.4)',
      tail: '#4299E1'
    },
    green: {
      bg: 'linear-gradient(135deg, #48BB78, #38A169)',
      shadow: 'rgba(72, 187, 120, 0.4)',
      tail: '#48BB78'
    },
    purple: {
      bg: 'linear-gradient(135deg, #9F7AEA, #805AD5)',
      shadow: 'rgba(159, 122, 234, 0.4)',
      tail: '#9F7AEA'
    }
  };

  const colors = colorSchemes[colorScheme];

  return (
    <MotionBox
      position="fixed"
      top={4}
      right={4}
      zIndex={9999}
      drag
      dragControls={dragControls}
      dragMomentum={false}
      dragElastic={0.1}
      whileDrag={{ scale: 1.1 }}
      whileHover={{ scale: 1.05 }}
      onDrag={(event, info) => {
        setPosition({ x: info.offset.x, y: info.offset.y });
      }}
      style={{ x: position.x, y: position.y }}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Box
        bg={colors.bg}
        color="white"
        borderRadius="20px"
        boxShadow={`0 8px 25px ${colors.shadow}`}
        cursor="grab"
        _active={{ cursor: "grabbing" }}
        position="relative"
        overflow="hidden"
        minW="120px"
      >
        {/* Chat bubble tail */}
        <Box
          position="absolute"
          bottom="-8px"
          right="20px"
          width="0"
          height="0"
          borderLeft="8px solid transparent"
          borderRight="8px solid transparent"
          borderTop={`8px solid ${colors.tail}`}
        />
        
        {/* Drag handle */}
        <Box
          position="absolute"
          top="8px"
          left="8px"
          onPointerDown={(e) => dragControls.start(e)}
          cursor="grab"
          _active={{ cursor: "grabbing" }}
          opacity={0.6}
          _hover={{ opacity: 1 }}
          transition="opacity 0.2s"
        >
          <Icon as={FaGripVertical} boxSize={3} />
        </Box>

        {/* Content */}
        <Flex
          align="center"
          px={6}
          py={3}
          pl={8}
          onClick={onClick}
          cursor="pointer"
          _hover={{ bg: "rgba(255,255,255,0.1)" }}
          transition="background-color 0.2s"
        >
          {children || (
            <>
              <Icon as={icon} boxSize={4} mr={2} />
              <Text fontWeight="bold" fontSize="sm">
                {text}
              </Text>
            </>
          )}
        </Flex>

        {/* Pulse animation for attention */}
        <Box
          position="absolute"
          top="0"
          left="0"
          right="0"
          bottom="0"
          borderRadius="20px"
          border="2px solid rgba(255,255,255,0.3)"
          animation="pulse 2s infinite"
          pointerEvents="none"
        />
      </Box>

      <style>
        {`
          @keyframes pulse {
            0% {
              transform: scale(1);
              opacity: 1;
            }
            50% {
              transform: scale(1.05);
              opacity: 0.7;
            }
            100% {
              transform: scale(1);
              opacity: 1;
            }
          }
        `}
      </style>
    </MotionBox>
  );
};

export default DraggableChatBubble;
