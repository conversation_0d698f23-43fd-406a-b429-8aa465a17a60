import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  Icon,
  Flex,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Select,
  Input,
  InputGroup,
  InputLeftElement,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  Skeleton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton
} from '@chakra-ui/react';
import {
  FaSearch,
  FaFilter,
  FaDownload,
  FaEye,
  FaArrowUp,
  FaArrowDown,
  FaClock,
  FaCheck,
  FaTimes,
  FaExclamationTriangle
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { transactionHistoryService, Transaction, TransactionFilter } from '../../services/transactionHistoryService';

interface RealTimeTransactionHistoryProps {
  showFilters?: boolean;
  compactMode?: boolean;
  maxHeight?: string;
  onTransactionClick?: (transaction: Transaction) => void;
}

/**
 * Real-Time Transaction History Component
 * 
 * Features:
 * - Real-time transaction updates via WebSocket
 * - Advanced filtering and search
 * - Export functionality
 * - Mobile-responsive design
 * - Integration with Enhanced Withdrawal System
 */
const RealTimeTransactionHistory: React.FC<RealTimeTransactionHistoryProps> = ({
  showFilters = true,
  compactMode = false,
  maxHeight = "600px",
  onTransactionClick
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { isOpen: isDetailOpen, onOpen: onDetailOpen, onClose: onDetailClose } = useDisclosure();

  // State management
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  
  // Filter state
  const [filter, setFilter] = useState<TransactionFilter>({
    page: 1,
    limit: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [searchTerm, setSearchTerm] = useState('');

  // Get transaction status color and icon
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'completed':
        return { color: '#0ECB81', icon: FaCheck, text: t('transaction.status.completed', 'Completed') };
      case 'pending':
        return { color: '#F0B90B', icon: FaClock, text: t('transaction.status.pending', 'Pending') };
      case 'processing':
        return { color: '#F0B90B', icon: FaClock, text: t('transaction.status.processing', 'Processing') };
      case 'failed':
        return { color: '#F84960', icon: FaTimes, text: t('transaction.status.failed', 'Failed') };
      case 'cancelled':
        return { color: '#848E9C', icon: FaTimes, text: t('transaction.status.cancelled', 'Cancelled') };
      default:
        return { color: '#848E9C', icon: FaExclamationTriangle, text: status };
    }
  };

  // Get transaction type display
  const getTypeDisplay = (type: string) => {
    switch (type) {
      case 'deposit':
        return { color: '#0ECB81', icon: FaArrowDown, text: t('transaction.type.deposit', 'Deposit') };
      case 'withdrawal':
        return { color: '#F84960', icon: FaArrowUp, text: t('transaction.type.withdrawal', 'Withdrawal') };
      case 'interest':
        return { color: '#F0B90B', icon: FaArrowDown, text: t('transaction.type.interest', 'Interest') };
      case 'commission':
        return { color: '#F0B90B', icon: FaArrowDown, text: t('transaction.type.commission', 'Commission') };
      case 'referral':
        return { color: '#F0B90B', icon: FaArrowDown, text: t('transaction.type.referral', 'Referral') };
      case 'investment':
        return { color: '#0ECB81', icon: FaArrowUp, text: t('transaction.type.investment', 'Investment') };
      default:
        return { color: '#848E9C', icon: FaExclamationTriangle, text: type };
    }
  };

  // Load transaction history
  const loadTransactions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await transactionHistoryService.getTransactionHistory(filter);
      setTransactions(result.transactions);

    } catch (error) {
      console.error('Error loading transaction history:', error);
      setError('Failed to load transaction history');
      toast({
        title: t('error.loadingFailed', 'Loading Failed'),
        description: t('error.transactionHistoryFailed', 'Failed to load transaction history'),
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      setLoading(false);
    }
  }, [filter, toast, t]);

  // Setup real-time updates
  useEffect(() => {
    // Load initial data
    loadTransactions();

    // Setup real-time transaction updates
    const unsubscribeUpdate = transactionHistoryService.onTransactionUpdate((updatedTransaction) => {
      console.log('📝 Real-time transaction update received:', updatedTransaction);
      setTransactions(prev => {
        const index = prev.findIndex(t => t.id === updatedTransaction.id);
        if (index >= 0) {
          const updated = [...prev];
          updated[index] = updatedTransaction;
          return updated;
        } else {
          return [updatedTransaction, ...prev];
        }
      });
      setConnectionStatus('connected');

      // Show toast for important updates
      if (updatedTransaction.status === 'completed') {
        toast({
          title: t('transaction.completed', 'Transaction Completed'),
          description: `${updatedTransaction.type} of ${updatedTransaction.amount} ${updatedTransaction.cryptocurrency}`,
          status: 'success',
          duration: 5000,
          isClosable: true
        });
      }
    });

    // Setup real-time list updates
    const unsubscribeList = transactionHistoryService.onTransactionListUpdate((updatedTransactions) => {
      console.log('📋 Real-time transaction list update received');
      setTransactions(updatedTransactions);
    });

    // Initialize WebSocket connection
    setConnectionStatus('connecting');
    transactionHistoryService.initializeRealTimeUpdates();

    // Check connection status
    const connectionInterval = setInterval(() => {
      const isConnected = transactionHistoryService.isWebSocketConnected();
      setConnectionStatus(isConnected ? 'connected' : 'disconnected');
    }, 5000);

    // Cleanup
    return () => {
      unsubscribeUpdate();
      unsubscribeList();
      clearInterval(connectionInterval);
    };
  }, [loadTransactions, toast, t]);

  // Handle transaction detail view
  const handleTransactionClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    if (onTransactionClick) {
      onTransactionClick(transaction);
    } else {
      onDetailOpen();
    }
  };

  // Handle filter changes
  const handleFilterChange = (newFilter: Partial<TransactionFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter, page: 1 }));
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    // Implement search logic here
  };

  // Export transactions
  const handleExport = async (format: 'csv' | 'xlsx' | 'pdf') => {
    try {
      const blob = await transactionHistoryService.exportTransactionHistory(format, filter);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `transactions.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: t('export.success', 'Export Successful'),
        description: t('export.downloadStarted', 'Download started'),
        status: 'success',
        duration: 3000,
        isClosable: true
      });
    } catch (error) {
      toast({
        title: t('export.failed', 'Export Failed'),
        description: t('export.error', 'Failed to export transactions'),
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  };

  // Render loading state
  if (loading && transactions.length === 0) {
    return (
      <VStack spacing={4}>
        {[1, 2, 3, 4, 5].map((index) => (
          <Skeleton key={index} height="60px" borderRadius="md" />
        ))}
      </VStack>
    );
  }

  // Render error state
  if (error) {
    return (
      <Alert status="error" borderRadius="md">
        <AlertIcon />
        <VStack align="start" spacing={2}>
          <Text fontWeight="bold">{t('error.title', 'Error')}</Text>
          <Text fontSize="sm">{error}</Text>
          <Button size="sm" onClick={loadTransactions} colorScheme="red" variant="outline">
            {t('common.retry', 'Retry')}
          </Button>
        </VStack>
      </Alert>
    );
  }

  return (
    <>
      <VStack spacing={4} w="full">
        {/* Header with Connection Status */}
        <HStack spacing={4} w="full" justify="space-between">
          <HStack spacing={2}>
            <Text color="#EAECEF" fontSize="lg" fontWeight="bold">
              {t('transaction.history', 'Transaction History')}
            </Text>
            <Box
              w={2}
              h={2}
              borderRadius="full"
              bg={connectionStatus === 'connected' ? '#0ECB81' : connectionStatus === 'connecting' ? '#F0B90B' : '#F84960'}
            />
            {connectionStatus === 'connecting' && <Spinner size="xs" color="#F0B90B" />}
          </HStack>

          <HStack spacing={2}>
            <Menu>
              <MenuButton as={Button} size="sm" leftIcon={<FaDownload />} variant="outline">
                {t('common.export', 'Export')}
              </MenuButton>
              <MenuList bg="#1E2329" borderColor="#2B3139">
                <MenuItem onClick={() => handleExport('csv')} bg="#1E2329" _hover={{ bg: "#2B3139" }}>
                  CSV
                </MenuItem>
                <MenuItem onClick={() => handleExport('xlsx')} bg="#1E2329" _hover={{ bg: "#2B3139" }}>
                  Excel
                </MenuItem>
                <MenuItem onClick={() => handleExport('pdf')} bg="#1E2329" _hover={{ bg: "#2B3139" }}>
                  PDF
                </MenuItem>
              </MenuList>
            </Menu>
          </HStack>
        </HStack>

        {/* Filters */}
        {showFilters && (
          <HStack spacing={4} w="full" wrap="wrap">
            <InputGroup maxW="300px">
              <InputLeftElement>
                <Icon as={FaSearch} color="#848E9C" />
              </InputLeftElement>
              <Input
                placeholder={t('transaction.search', 'Search transactions...')}
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                bg="#0B0E11"
                borderColor="#2B3139"
                color="#EAECEF"
                _hover={{ borderColor: "#F0B90B" }}
              />
            </InputGroup>

            <Select
              maxW="150px"
              value={filter.type?.[0] || ''}
              onChange={(e) => handleFilterChange({ type: e.target.value ? [e.target.value] : undefined })}
              bg="#0B0E11"
              borderColor="#2B3139"
              color="#EAECEF"
            >
              <option value="">{t('transaction.allTypes', 'All Types')}</option>
              <option value="deposit">{t('transaction.type.deposit', 'Deposit')}</option>
              <option value="withdrawal">{t('transaction.type.withdrawal', 'Withdrawal')}</option>
              <option value="interest">{t('transaction.type.interest', 'Interest')}</option>
              <option value="commission">{t('transaction.type.commission', 'Commission')}</option>
              <option value="referral">{t('transaction.type.referral', 'Referral')}</option>
            </Select>

            <Select
              maxW="150px"
              value={filter.status?.[0] || ''}
              onChange={(e) => handleFilterChange({ status: e.target.value ? [e.target.value] : undefined })}
              bg="#0B0E11"
              borderColor="#2B3139"
              color="#EAECEF"
            >
              <option value="">{t('transaction.allStatuses', 'All Statuses')}</option>
              <option value="pending">{t('transaction.status.pending', 'Pending')}</option>
              <option value="completed">{t('transaction.status.completed', 'Completed')}</option>
              <option value="failed">{t('transaction.status.failed', 'Failed')}</option>
            </Select>
          </HStack>
        )}

        {/* Transaction Table */}
        <Box
          w="full"
          bg="#1E2329"
          borderRadius="md"
          borderWidth="1px"
          borderColor="#2B3139"
          maxH={maxHeight}
          overflowY="auto"
        >
          <Table variant="simple" size={compactMode ? "sm" : "md"}>
            <Thead>
              <Tr>
                <Th color="#848E9C" borderColor="#2B3139">{t('transaction.type', 'Type')}</Th>
                <Th color="#848E9C" borderColor="#2B3139">{t('transaction.amount', 'Amount')}</Th>
                <Th color="#848E9C" borderColor="#2B3139">{t('transaction.status', 'Status')}</Th>
                <Th color="#848E9C" borderColor="#2B3139">{t('transaction.date', 'Date')}</Th>
                <Th color="#848E9C" borderColor="#2B3139">{t('common.actions', 'Actions')}</Th>
              </Tr>
            </Thead>
            <Tbody>
              {transactions.map((transaction) => {
                const typeDisplay = getTypeDisplay(transaction.type);
                const statusDisplay = getStatusDisplay(transaction.status);

                return (
                  <Tr
                    key={transaction.id}
                    _hover={{ bg: "#2B3139" }}
                    cursor="pointer"
                    onClick={() => handleTransactionClick(transaction)}
                  >
                    <Td borderColor="#2B3139">
                      <HStack spacing={2}>
                        <Icon as={typeDisplay.icon} color={typeDisplay.color} />
                        <Text color="#EAECEF" fontSize="sm">
                          {typeDisplay.text}
                        </Text>
                      </HStack>
                    </Td>
                    <Td borderColor="#2B3139">
                      <VStack align="start" spacing={0}>
                        <Text color="#EAECEF" fontWeight="bold">
                          {transaction.amount.toFixed(8)} {transaction.cryptocurrency}
                        </Text>
                        <Text color="#848E9C" fontSize="xs">
                          ≈ ${transaction.usdValue.toFixed(2)}
                        </Text>
                      </VStack>
                    </Td>
                    <Td borderColor="#2B3139">
                      <Badge
                        colorScheme={statusDisplay.color === '#0ECB81' ? 'green' : 
                                   statusDisplay.color === '#F0B90B' ? 'yellow' : 
                                   statusDisplay.color === '#F84960' ? 'red' : 'gray'}
                        variant="subtle"
                      >
                        {statusDisplay.text}
                      </Badge>
                    </Td>
                    <Td borderColor="#2B3139">
                      <Text color="#EAECEF" fontSize="sm">
                        {new Date(transaction.createdAt).toLocaleDateString()}
                      </Text>
                      <Text color="#848E9C" fontSize="xs">
                        {new Date(transaction.createdAt).toLocaleTimeString()}
                      </Text>
                    </Td>
                    <Td borderColor="#2B3139">
                      <Button
                        size="sm"
                        variant="ghost"
                        leftIcon={<FaEye />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTransactionClick(transaction);
                        }}
                      >
                        {t('common.view', 'View')}
                      </Button>
                    </Td>
                  </Tr>
                );
              })}
            </Tbody>
          </Table>

          {transactions.length === 0 && !loading && (
            <Box p={8} textAlign="center">
              <Text color="#848E9C">
                {t('transaction.noTransactions', 'No transactions found')}
              </Text>
            </Box>
          )}
        </Box>
      </VStack>

      {/* Transaction Detail Modal */}
      <Modal isOpen={isDetailOpen} onClose={onDetailClose} size="lg">
        <ModalOverlay />
        <ModalContent bg="#1E2329" borderColor="#2B3139">
          <ModalHeader color="#EAECEF">
            {t('transaction.details', 'Transaction Details')}
          </ModalHeader>
          <ModalCloseButton color="#848E9C" />
          <ModalBody pb={6}>
            {selectedTransaction && (
              <VStack spacing={4} align="stretch">
                {/* Transaction details content would go here */}
                <Text color="#EAECEF">
                  Transaction ID: {selectedTransaction.id}
                </Text>
                <Text color="#EAECEF">
                  Amount: {selectedTransaction.amount} {selectedTransaction.cryptocurrency}
                </Text>
                <Text color="#EAECEF">
                  Status: {selectedTransaction.status}
                </Text>
                {/* Add more transaction details as needed */}
              </VStack>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export default RealTimeTransactionHistory;
