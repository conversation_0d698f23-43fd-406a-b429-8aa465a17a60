import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Switch,
  useColorModeValue,
  useToast,
  Spinner,
  Text,
  IconButton,
  Tooltip,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useDisclosure,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  StatGroup,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  VStack,
  HStack,
  Badge,
  Select,
  Textarea,
  Grid,
  GridItem,
  InputGroup,
  InputRightAddon,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { AddIcon, EditIcon, DeleteIcon, CheckIcon, CloseIcon, InfoIcon } from '@chakra-ui/icons';
import { FaPlus, FaEdit, FaTrash, FaSave, FaTimes, FaCoins, FaUserFriends, FaPercentage } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { apiClient } from '../../utils/apiClient';

interface CommissionConfig {
  _id: string;
  level: number;
  commissionRate: number;
  minInvestmentAmount: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CommissionRate {
  id: string;
  name: string;
  label: string;
  type: string;
  currency: string;
  rate: number;
  minAmount: number;
  maxAmount: number;
  status: string;
  description: string;
}

interface ReferralRate {
  id: string;
  level: number;
  rate: number;
  description: string;
  status: string;
}

interface ReferralStats {
  totalReferrals: number;
  totalCommissions: number;
  activeReferrers: number;
  commissionsByAsset: {
    currency: string;
    amount: number;
  }[];
}

const CommissionSettings: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const bgColor = useColorModeValue('white', 'gray.800');
  const cardBgColor = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');
  const accentColor = useColorModeValue('yellow.500', 'yellow.300');

  const [configs, setConfigs] = useState<CommissionConfig[]>([]);
  const [commissionRates, setCommissionRates] = useState<CommissionRate[]>([]);
  const [referralRates, setReferralRates] = useState<ReferralRate[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState<boolean>(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [configToDelete, setConfigToDelete] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [stats, setStats] = useState<ReferralStats | null>(null);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const cancelRef = React.useRef<HTMLButtonElement>(null);

  // Form state for commission rates
  const [commName, setCommName] = useState('');
  const [commLabel, setCommLabel] = useState('');
  const [commType, setCommType] = useState('deposit');
  const [commCurrency, setCommCurrency] = useState('BTC');
  const [commRate, setCommRate] = useState(1.0);
  const [commMinAmount, setCommMinAmount] = useState(0);
  const [commMaxAmount, setCommMaxAmount] = useState(1000000);
  const [commDescription, setCommDescription] = useState('');
  const [commStatus, setCommStatus] = useState('active');

  // Form state for referral configs
  const [formData, setFormData] = useState({
    level: 1,
    commissionRate: 5,
    minInvestmentAmount: 100,
    isActive: true,
  });

  const { isOpen, onOpen, onClose } = useDisclosure();

  // Options for dropdowns
  const transactionTypeOptions = [
    { value: 'deposit', label: 'Yatırma' },
    { value: 'withdraw', label: 'Çekme' },
    { value: 'investment', label: 'Yatırım' },
  ];

  const currencyOptions = [
    { value: 'BTC', label: 'Bitcoin (BTC)' },
    { value: 'ETH', label: 'Ethereum (ETH)' },
    { value: 'USDT', label: 'Tether (USDT)' },
    { value: 'BNB', label: 'Binance Coin (BNB)' },
    { value: 'DOGE', label: 'Dogecoin (DOGE)' },
    { value: 'XRP', label: 'Ripple (XRP)' },
  ];

  // Fetch commission configs
  const fetchConfigs = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get('/api/commissions/admin/config');
      if (response.data && response.data.configs) {
        // Sort by level
        const sortedConfigs = response.data.configs.sort((a: CommissionConfig, b: CommissionConfig) => a.level - b.level);
        setConfigs(sortedConfigs);
      }
    } catch (err: any) {
      console.error('Error fetching commission configs:', err);
      setError(err.response?.data?.message || 'Failed to fetch commission configurations');
      toast({
        title: 'Error',
        description: err.response?.data?.message || 'Failed to fetch commission configurations',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch referral stats
  const fetchReferralStats = async () => {
    try {
      setLoading(true);
      // Mock stats for now - would be replaced with actual API call
      setStats({
        totalReferrals: 156,
        totalCommissions: 2345.67,
        activeReferrers: 42,
        commissionsByAsset: [
          { currency: 'BTC', amount: 0.12 },
          { currency: 'ETH', amount: 1.5 },
          { currency: 'USDT', amount: 1200 }
        ]
      });
    } catch (err: any) {
      console.error('Error fetching referral stats:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConfigs();
    fetchReferralStats();
    fetchCommissionRates();
    fetchReferralRates();
  }, []);

  // Fetch commission rates
  const fetchCommissionRates = async () => {
    try {
      // Mock data for now - replace with actual API call
      setCommissionRates([
        {
          id: '1',
          name: 'deposit_btc',
          label: 'Bitcoin Yatırma',
          type: 'deposit',
          currency: 'BTC',
          rate: 1.5,
          minAmount: 0.001,
          maxAmount: 10,
          status: 'active',
          description: 'Bitcoin yatırma işlemleri için komisyon'
        },
        {
          id: '2',
          name: 'withdraw_btc',
          label: 'Bitcoin Çekme',
          type: 'withdraw',
          currency: 'BTC',
          rate: 2.0,
          minAmount: 0.001,
          maxAmount: 10,
          status: 'active',
          description: 'Bitcoin çekme işlemleri için komisyon'
        }
      ]);
    } catch (err: any) {
      console.error('Error fetching commission rates:', err);
    }
  };

  // Fetch referral rates
  const fetchReferralRates = async () => {
    try {
      // Mock data for now - replace with actual API call
      setReferralRates([
        {
          id: '1',
          level: 1,
          rate: 3.0,
          description: 'Birinci seviye referans komisyonu',
          status: 'active'
        },
        {
          id: '2',
          level: 2,
          rate: 1.5,
          description: 'İkinci seviye referans komisyonu',
          status: 'active'
        }
      ]);
    } catch (err: any) {
      console.error('Error fetching referral rates:', err);
    }
  };

  // Commission rate handlers
  const handleAddCommissionRate = () => {
    setSelectedItem({ currency: 'BTC' });
    setIsEditing(false);
    resetCommissionForm();
    onOpen();
  };

  const handleEditCommissionRate = (rate: CommissionRate) => {
    setSelectedItem(rate);
    setIsEditing(true);
    setCommName(rate.name);
    setCommLabel(rate.label);
    setCommType(rate.type);
    setCommCurrency(rate.currency);
    setCommRate(rate.rate);
    setCommMinAmount(rate.minAmount);
    setCommMaxAmount(rate.maxAmount);
    setCommDescription(rate.description);
    setCommStatus(rate.status);
    onOpen();
  };

  const handleDeleteCommissionRate = (rate: CommissionRate) => {
    setConfigToDelete(rate.id);
    setIsDeleteDialogOpen(true);
  };

  const handleSaveCommissionRate = async () => {
    try {
      setLoading(true);
      // Mock save - replace with actual API call
      const newRate: CommissionRate = {
        id: isEditing ? selectedItem.id : Date.now().toString(),
        name: commName,
        label: commLabel,
        type: commType,
        currency: commCurrency,
        rate: commRate,
        minAmount: commMinAmount,
        maxAmount: commMaxAmount,
        description: commDescription,
        status: commStatus
      };

      if (isEditing) {
        setCommissionRates(prev => prev.map(r => r.id === selectedItem.id ? newRate : r));
      } else {
        setCommissionRates(prev => [...prev, newRate]);
      }

      toast({
        title: 'Başarılı',
        description: isEditing ? 'Komisyon oranı güncellendi' : 'Yeni komisyon oranı eklendi',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      onClose();
      resetCommissionForm();
    } catch (err: any) {
      console.error('Error saving commission rate:', err);
      toast({
        title: 'Hata',
        description: 'Komisyon oranı kaydedilemedi',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Referral rate handlers
  const handleAddReferralRate = () => {
    setSelectedItem({ level: referralRates.length + 1 });
    setIsEditing(false);
    onOpen();
  };

  const handleEditReferralRate = (rate: ReferralRate) => {
    setSelectedItem(rate);
    setIsEditing(true);
    onOpen();
  };

  const handleDeleteReferralRate = (rate: ReferralRate) => {
    setConfigToDelete(rate.id);
    setIsDeleteDialogOpen(true);
  };

  // Reset form
  const resetCommissionForm = () => {
    setCommName('');
    setCommLabel('');
    setCommType('deposit');
    setCommCurrency('BTC');
    setCommRate(1.0);
    setCommMinAmount(0);
    setCommMaxAmount(1000000);
    setCommDescription('');
    setCommStatus('active');
  };

  // Handle tab change
  const handleTabChange = (index: number) => {
    setActiveTab(index);
  };

  // Handle form input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
    });
  };

  // Start editing a config
  const handleEdit = (config: CommissionConfig) => {
    setEditingId(config._id);
    setFormData({
      level: config.level,
      commissionRate: config.commissionRate,
      minInvestmentAmount: config.minInvestmentAmount,
      isActive: config.isActive,
    });
    setIsAdding(true);
  };

  // Start adding a new config
  const handleAdd = () => {
    setEditingId(null);
    setFormData({
      level: configs.length > 0 ? Math.max(...configs.map(c => c.level)) + 1 : 1,
      commissionRate: 5,
      minInvestmentAmount: 100,
      isActive: true,
    });
    setIsAdding(true);
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingId(null);
    setIsAdding(false);
  };

  // Save a config (create or update)
  const handleSave = async () => {
    try {
      setLoading(true);
      const response = await apiClient.post('/api/commissions/admin/config', formData);

      if (response.data && response.data.config) {
        toast({
          title: 'Success',
          description: editingId
            ? t('admin.commissionUpdated', 'Commission configuration updated successfully')
            : t('admin.commissionCreated', 'Commission configuration created successfully'),
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Refresh the list
        await fetchConfigs();

        // Reset form
        handleCancelEdit();
      }
    } catch (err: any) {
      console.error('Error saving commission config:', err);
      setError(err.response?.data?.message || 'Failed to save commission configuration');
      toast({
        title: 'Error',
        description: err.response?.data?.message || 'Failed to save commission configuration',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (id: string) => {
    setConfigToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  // Delete a config
  const handleDelete = async () => {
    if (!configToDelete) return;

    try {
      setLoading(true);
      await apiClient.delete(`/api/commissions/admin/config/${configToDelete}`);

      toast({
        title: 'Success',
        description: t('admin.commissionDeleted', 'Commission configuration deleted successfully'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Refresh the list
      await fetchConfigs();
    } catch (err: any) {
      console.error('Error deleting commission config:', err);
      setError(err.response?.data?.message || 'Failed to delete commission configuration');
      toast({
        title: 'Error',
        description: err.response?.data?.message || 'Failed to delete commission configuration',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
      setIsDeleteDialogOpen(false);
      setConfigToDelete(null);
    }
  };

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>Komisyon Ayarları</Heading>

      <Tabs variant="enclosed" colorScheme="yellow">
        <TabList borderColor={borderColor}>
          <Tab color={textColor} _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>İşlem Komisyonları</Tab>
          <Tab color={textColor} _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>Referans Komisyonları</Tab>
        </TabList>

        <TabPanels>
          {/* Commission Rates Tab */}
          <TabPanel p={0} pt={4}>
            <Flex justify="space-between" mb={4}>
              <StatGroup width="100%" mb={4}>
                <Stat bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                  <StatLabel color={secondaryTextColor}>Ortalama Yatırma Komisyonu</StatLabel>
                  <StatNumber color="#F0B90B">
                    {(commissionRates
                      .filter(rate => rate.type === 'deposit' && rate.status === 'active')
                      .reduce((acc, curr) => acc + curr.rate, 0) /
                      commissionRates.filter(rate => rate.type === 'deposit' && rate.status === 'active').length || 0
                    ).toFixed(2)}%
                  </StatNumber>
                  <StatHelpText>
                    <StatArrow type="decrease" />
                    0.1% son 30 günde
                  </StatHelpText>
                </Stat>

                <Stat bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                  <StatLabel color={secondaryTextColor}>Ortalama Çekme Komisyonu</StatLabel>
                  <StatNumber color="#F0B90B">
                    {(commissionRates
                      .filter(rate => rate.type === 'withdraw' && rate.status === 'active')
                      .reduce((acc, curr) => acc + curr.rate, 0) /
                      commissionRates.filter(rate => rate.type === 'withdraw' && rate.status === 'active').length || 0
                    ).toFixed(2)}%
                  </StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    0.2% son 30 günde
                  </StatHelpText>
                </Stat>

                <Stat bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                  <StatLabel color={secondaryTextColor}>Aktif Komisyon Sayısı</StatLabel>
                  <StatNumber color="#F0B90B">
                    {commissionRates.filter(rate => rate.status === 'active').length}
                  </StatNumber>
                  <StatHelpText>
                    Toplam {commissionRates.length} komisyon
                  </StatHelpText>
                </Stat>
              </StatGroup>
            </Flex>

            <Flex justify="flex-end" mb={4}>
              <Button
                leftIcon={<AddIcon />}
                colorScheme="green"
                onClick={handleAddCommissionRate}
              >
                Yeni Komisyon Oranı Ekle
              </Button>
            </Flex>

            <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Box overflowX="auto">
                <Table variant="simple" size="md">
                  <Thead>
                    <Tr>
                      <Th color={secondaryTextColor} borderColor={borderColor}>İşlem Tipi</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Para Birimi</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Etiket</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Oran (%)</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Min. Tutar</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Maks. Tutar</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Durum</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>İşlemler</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {commissionRates.map((rate) => (
                      <Tr key={rate.id}>
                        <Td color={textColor} borderColor={borderColor}>
                          <Badge
                            colorScheme={rate.type === 'deposit' ? 'green' : rate.type === 'withdraw' ? 'red' : 'blue'}
                            borderRadius="full"
                            px={2}
                          >
                            {transactionTypeOptions.find(t => t.value === rate.type)?.label || rate.type}
                          </Badge>
                        </Td>
                        <Td color={textColor} borderColor={borderColor}>{rate.currency}</Td>
                        <Td color={textColor} borderColor={borderColor}>{rate.label}</Td>
                        <Td color={textColor} borderColor={borderColor}>{rate.rate}%</Td>
                        <Td color={textColor} borderColor={borderColor}>{rate.minAmount} {rate.currency}</Td>
                        <Td color={textColor} borderColor={borderColor}>{rate.maxAmount} {rate.currency}</Td>
                        <Td borderColor={borderColor}>
                          <Badge
                            colorScheme={rate.status === 'active' ? 'green' : 'gray'}
                            borderRadius="full"
                            px={2}
                          >
                            {rate.status === 'active' ? 'Aktif' : 'Pasif'}
                          </Badge>
                        </Td>
                        <Td borderColor={borderColor}>
                          <HStack spacing={2}>
                            <Tooltip label={rate.description} hasArrow placement="top">
                              <IconButton
                                aria-label="Bilgi"
                                icon={<InfoIcon />}
                                size="sm"
                                colorScheme="teal"
                              />
                            </Tooltip>
                            <IconButton
                              aria-label="Düzenle"
                              icon={<EditIcon />}
                              size="sm"
                              colorScheme="blue"
                              onClick={() => handleEditCommissionRate(rate)}
                            />
                            <IconButton
                              aria-label="Sil"
                              icon={<DeleteIcon />}
                              size="sm"
                              colorScheme="red"
                              onClick={() => handleDeleteCommissionRate(rate)}
                            />
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            </Box>
          </TabPanel>

          {/* Referral Rates Tab */}
          <TabPanel p={0} pt={4}>
            <Flex justify="space-between" mb={4}>
              <StatGroup width="100%" mb={4}>
                <Stat bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                  <StatLabel color={secondaryTextColor}>Toplam Referans Seviyesi</StatLabel>
                  <StatNumber color="#F0B90B">
                    {referralRates.filter(rate => rate.status === 'active').length}
                  </StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    1 seviye son 30 günde
                  </StatHelpText>
                </Stat>

                <Stat bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                  <StatLabel color={secondaryTextColor}>Ortalama Referans Komisyonu</StatLabel>
                  <StatNumber color="#F0B90B">
                    {(referralRates
                      .filter(rate => rate.status === 'active')
                      .reduce((acc, curr) => acc + curr.rate, 0) /
                      referralRates.filter(rate => rate.status === 'active').length || 0
                    ).toFixed(2)}%
                  </StatNumber>
                  <StatHelpText>
                    <StatArrow type="decrease" />
                    0.5% son 30 günde
                  </StatHelpText>
                </Stat>

                <Stat bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                  <StatLabel color={secondaryTextColor}>Toplam Referans Kazancı</StatLabel>
                  <StatNumber color="#F0B90B">
                    12,450 USDT
                  </StatNumber>
                  <StatHelpText>
                    <StatArrow type="increase" />
                    2,340 USDT son 30 günde
                  </StatHelpText>
                </Stat>
              </StatGroup>
            </Flex>

            <Flex justify="flex-end" mb={4}>
              <Button
                leftIcon={<AddIcon />}
                colorScheme="green"
                onClick={handleAddReferralRate}
              >
                Yeni Referans Seviyesi Ekle
              </Button>
            </Flex>

            <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Alert status="info" mb={4} bg="blue.800" color="white" borderRadius="md">
                <AlertIcon color="blue.200" />
                <Box>
                  <AlertTitle>Referans Sistemi Bilgisi</AlertTitle>
                  <AlertDescription>
                    Referans komisyonları, kullanıcıların davet ettikleri kişilerin işlemlerinden kazandıkları yüzdelik oranları belirtir.
                    Seviye 1, doğrudan davet edilen kullanıcıları; Seviye 2, onların davet ettiklerini temsil eder.
                  </AlertDescription>
                </Box>
              </Alert>

              <Box overflowX="auto">
                <Table variant="simple" size="md">
                  <Thead>
                    <Tr>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Seviye</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Oran (%)</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Açıklama</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>Durum</Th>
                      <Th color={secondaryTextColor} borderColor={borderColor}>İşlemler</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {referralRates.sort((a, b) => a.level - b.level).map((rate) => (
                      <Tr key={rate.id}>
                        <Td color={textColor} borderColor={borderColor}>{rate.level}</Td>
                        <Td color={textColor} borderColor={borderColor}>{rate.rate}%</Td>
                        <Td color={textColor} borderColor={borderColor}>{rate.description}</Td>
                        <Td borderColor={borderColor}>
                          <Badge
                            colorScheme={rate.status === 'active' ? 'green' : 'gray'}
                            borderRadius="full"
                            px={2}
                          >
                            {rate.status === 'active' ? 'Aktif' : 'Pasif'}
                          </Badge>
                        </Td>
                        <Td borderColor={borderColor}>
                          <HStack spacing={2}>
                            <IconButton
                              aria-label="Düzenle"
                              icon={<EditIcon />}
                              size="sm"
                              colorScheme="blue"
                              onClick={() => handleEditReferralRate(rate)}
                            />
                            <IconButton
                              aria-label="Sil"
                              icon={<DeleteIcon />}
                              size="sm"
                              colorScheme="red"
                              onClick={() => handleDeleteReferralRate(rate)}
                            />
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Commission Rate Edit Modal */}
      <Modal isOpen={isOpen && selectedItem?.currency !== undefined} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor}>
          <ModalHeader>
            {isEditing
              ? `${selectedItem?.label} Düzenle`
              : 'Yeni Komisyon Oranı Ekle'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl isRequired>
                <FormLabel>Sistem Adı</FormLabel>
                <Input
                  value={commName}
                  onChange={(e) => setCommName(e.target.value)}
                  placeholder="Sistem adı giriniz (örn: deposit_btc)"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Etiket</FormLabel>
                <Input
                  value={commLabel}
                  onChange={(e) => setCommLabel(e.target.value)}
                  placeholder="Etiket giriniz (örn: Bitcoin Yatırma)"
                  bg={bgColor}
                  borderColor={borderColor}
                />
              </FormControl>

              <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                <GridItem>
                  <FormControl isRequired>
                    <FormLabel>İşlem Tipi</FormLabel>
                    <Select
                      value={commType}
                      onChange={(e) => setCommType(e.target.value)}
                      bg={bgColor}
                      borderColor={borderColor}
                    >
                      {transactionTypeOptions.map(type => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </Select>
                  </FormControl>
                </GridItem>

                <GridItem>
                  <FormControl isRequired>
                    <FormLabel>Para Birimi</FormLabel>
                    <Select
                      value={commCurrency}
                      onChange={(e) => setCommCurrency(e.target.value)}
                      bg={bgColor}
                      borderColor={borderColor}
                    >
                      {currencyOptions.map(currency => (
                        <option key={currency.value} value={currency.value}>{currency.label}</option>
                      ))}
                    </Select>
                  </FormControl>
                </GridItem>
              </Grid>

              <FormControl isRequired>
                <FormLabel>Komisyon Oranı (%)</FormLabel>
                <Flex>
                  <NumberInput
                    value={commRate}
                    onChange={(valueString) => setCommRate(parseFloat(valueString))}
                    step={0.1}
                    min={0}
                    max={100}
                    precision={2}
                    flex="1"
                    mr={4}
                  >
                    <NumberInputField bg={bgColor} borderColor={borderColor} />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                  <Slider
                    flex="2"
                    colorScheme="yellow"
                    value={commRate}
                    onChange={(v) => setCommRate(v)}
                    min={0}
                    max={10}
                    step={0.1}
                  >
                    <SliderTrack>
                      <SliderFilledTrack />
                    </SliderTrack>
                    <SliderThumb boxSize={6}>
                      <Box color="yellow.500" as={FaPercentage} />
                    </SliderThumb>
                  </Slider>
                </Flex>
              </FormControl>

              <Grid templateColumns="repeat(2, 1fr)" gap={4}>
                <GridItem>
                  <FormControl isRequired>
                    <FormLabel>Minimum Tutar</FormLabel>
                    <InputGroup>
                      <NumberInput
                        value={commMinAmount}
                        onChange={(valueString) => setCommMinAmount(parseFloat(valueString))}
                        min={0}
                        precision={commCurrency === 'BTC' || commCurrency === 'ETH' ? 6 : 2}
                        width="100%"
                      >
                        <NumberInputField bg={bgColor} borderColor={borderColor} />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                      <InputRightAddon children={commCurrency} />
                    </InputGroup>
                  </FormControl>
                </GridItem>

                <GridItem>
                  <FormControl isRequired>
                    <FormLabel>Maksimum Tutar</FormLabel>
                    <InputGroup>
                      <NumberInput
                        value={commMaxAmount}
                        onChange={(valueString) => setCommMaxAmount(parseFloat(valueString))}
                        min={commMinAmount}
                        precision={commCurrency === 'BTC' || commCurrency === 'ETH' ? 6 : 2}
                        width="100%"
                      >
                        <NumberInputField bg={bgColor} borderColor={borderColor} />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                      <InputRightAddon children={commCurrency} />
                    </InputGroup>
                  </FormControl>
                </GridItem>
              </Grid>

              <FormControl isRequired>
                <FormLabel>Açıklama</FormLabel>
                <Textarea
                  value={commDescription}
                  onChange={(e) => setCommDescription(e.target.value)}
                  placeholder="Komisyon açıklaması giriniz"
                  bg={bgColor}
                  borderColor={borderColor}
                  rows={3}
                />
              </FormControl>

              <FormControl display="flex" alignItems="center">
                <FormLabel mb="0">Durum</FormLabel>
                <Switch
                  colorScheme="green"
                  isChecked={commStatus === 'active'}
                  onChange={(e) => setCommStatus(e.target.checked ? 'active' : 'inactive')}
                />
                <Text ml={2}>{commStatus === 'active' ? 'Aktif' : 'Pasif'}</Text>
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              İptal
            </Button>
            <Button
              colorScheme="yellow"
              onClick={handleSaveCommissionRate}
            >
              Kaydet
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Referral Rate Edit Modal */}
      <Modal isOpen={isOpen && selectedItem?.level !== undefined} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor}>
          <ModalHeader>
            {isEditing
              ? `Seviye ${selectedItem?.level} Düzenle`
              : 'Yeni Referans Seviyesi Ekle'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl isRequired>
                <FormLabel>Seviye</FormLabel>
                <NumberInput
                  value={refLevel}
                  onChange={(valueString) => setRefLevel(parseInt(valueString))}
                  min={1}
                  max={10}
                  isReadOnly={isEditing}
                >
                  <NumberInputField bg={bgColor} borderColor={borderColor} />
                  <NumberInputStepper>
                    <NumberIncrementStepper />
                    <NumberDecrementStepper />
                  </NumberInputStepper>
                </NumberInput>
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Komisyon Oranı (%)</FormLabel>
                <Flex>
                  <NumberInput
                    value={refRate}
                    onChange={(valueString) => setRefRate(parseFloat(valueString))}
                    step={0.1}
                    min={0}
                    max={100}
                    precision={2}
                    flex="1"
                    mr={4}
                  >
                    <NumberInputField bg={bgColor} borderColor={borderColor} />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                  <Slider
                    flex="2"
                    colorScheme="yellow"
                    value={refRate}
                    onChange={(v) => setRefRate(v)}
                    min={0}
                    max={10}
                    step={0.1}
                  >
                    <SliderTrack>
                      <SliderFilledTrack />
                    </SliderTrack>
                    <SliderThumb boxSize={6}>
                      <Box color="yellow.500" as={FaPercentage} />
                    </SliderThumb>
                  </Slider>
                </Flex>
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Açıklama</FormLabel>
                <Textarea
                  value={refDescription}
                  onChange={(e) => setRefDescription(e.target.value)}
                  placeholder="Referans seviyesi açıklaması giriniz"
                  bg={bgColor}
                  borderColor={borderColor}
                  rows={3}
                />
              </FormControl>

              <FormControl display="flex" alignItems="center">
                <FormLabel mb="0">Durum</FormLabel>
                <Switch
                  colorScheme="green"
                  isChecked={refStatus === 'active'}
                  onChange={(e) => setRefStatus(e.target.checked ? 'active' : 'inactive')}
                />
                <Text ml={2}>{refStatus === 'active' ? 'Aktif' : 'Pasif'}</Text>
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              İptal
            </Button>
            <Button
              colorScheme="yellow"
              onClick={handleSaveReferralRate}
            >
              Kaydet
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={isDeleteOpen} onClose={onDeleteClose} isCentered size="md">
        <ModalOverlay />
        <ModalContent bg={cardBgColor} color={textColor} borderColor={borderColor}>
          <ModalHeader>Silme Onayı</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text>
              {selectedItem?.label
                ? `"${selectedItem.label}" komisyon oranını silmek istediğinizden emin misiniz?`
                : `Seviye ${selectedItem?.level} referans oranını silmek istediğinizden emin misiniz?`
              }
              Bu işlem geri alınamaz.
            </Text>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onDeleteClose}>
              İptal
            </Button>
            <Button
              colorScheme="red"
              onClick={() => handleDelete(selectedItem?.currency !== undefined ? 0 : 1)}
              leftIcon={<DeleteIcon />}
            >
              Sil
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default CommissionSettings;
