import { logger } from '../utils/logger';
import cryptoApiService from './cryptoApiService';
import { cryptoCurrencyService } from './cryptoCurrencyService';
import CryptoPriceHistory from '../models/cryptoPriceHistoryModel';
import NodeCache from 'node-cache';

// Cache cho dữ liệu cryptocurrency với TTL 1 giờ
const cryptoDataCache = new NodeCache({ stdTTL: 3600 });

interface CryptoPriceData {
  symbol: string;
  name: string;
  price: number;
  volume24h?: number;
  marketCap?: number;
  change24h?: number;
  changePercent24h?: number;
  lastUpdated: Date;
  source: 'binance' | 'coingecko' | 'internal';
}

interface CollectionStats {
  totalSymbols: number;
  successfulCollections: number;
  failedCollections: number;
  savedToDatabase: number;
  lastCollectionTime: Date;
  errors: string[];
}

/**
 * Service để thu thập và quản lý dữ liệu cryptocurrency
 */
class CryptoDataCollectionService {
  private readonly supportedSymbols = ['BTC', 'ETH', 'USDT', 'BNB', 'SOL', 'DOGE', 'TRX', 'ADA', 'DOT', 'LINK'];
  private collectionStats: CollectionStats = {
    totalSymbols: 0,
    successfulCollections: 0,
    failedCollections: 0,
    savedToDatabase: 0,
    lastCollectionTime: new Date(),
    errors: []
  };

  /**
   * Thu thập dữ liệu giá cho tất cả cryptocurrency được hỗ trợ
   */
  async collectAllPriceData(): Promise<CryptoPriceData[]> {
    logger.info('Bắt đầu thu thập dữ liệu cryptocurrency...');

    const results: CryptoPriceData[] = [];
    const errors: string[] = [];
    let successCount = 0;
    let failCount = 0;
    let savedCount = 0;

    // Lấy danh sách cryptocurrency từ database
    const enabledCurrencies = await cryptoCurrencyService.getAllCurrencies(false);
    const symbolsToCollect = enabledCurrencies
      .map(currency => currency.symbol)
      .filter(symbol => this.supportedSymbols.includes(symbol));

    logger.info(`Thu thập dữ liệu cho ${symbolsToCollect.length} cryptocurrency: ${symbolsToCollect.join(', ')}`);

    for (const symbol of symbolsToCollect) {
      try {
        const priceData = await this.collectPriceDataForSymbol(symbol);
        if (priceData) {
          results.push(priceData);
          successCount++;

          // Cache dữ liệu
          cryptoDataCache.set(`price_${symbol}`, priceData);

          // Lưu vào database
          try {
            await CryptoPriceHistory.savePriceData({
              symbol: priceData.symbol,
              name: priceData.name,
              price: priceData.price,
              volume24h: priceData.volume24h,
              marketCap: priceData.marketCap,
              change24h: priceData.change24h,
              changePercent24h: priceData.changePercent24h,
              source: priceData.source,
              timestamp: priceData.lastUpdated
            });
            savedCount++;
            logger.debug(`Thu thập và lưu thành công dữ liệu cho ${symbol}: $${priceData.price}`);
          } catch (dbError: any) {
            logger.warn(`Lưu database thất bại cho ${symbol}: ${dbError.message}`);
          }
        }
      } catch (error: any) {
        failCount++;
        const errorMsg = `Lỗi thu thập dữ liệu cho ${symbol}: ${error.message}`;
        errors.push(errorMsg);
        logger.error(errorMsg);
      }
    }

    // Cập nhật thống kê
    this.collectionStats = {
      totalSymbols: symbolsToCollect.length,
      successfulCollections: successCount,
      failedCollections: failCount,
      savedToDatabase: savedCount,
      lastCollectionTime: new Date(),
      errors: errors.slice(-10) // Chỉ giữ 10 lỗi gần nhất
    };

    logger.info(`Hoàn thành thu thập dữ liệu: ${successCount}/${symbolsToCollect.length} thành công, ${savedCount} lưu database`);

    // Cache toàn bộ kết quả
    cryptoDataCache.set('all_prices', results);

    return results;
  }

  /**
   * Thu thập dữ liệu giá cho một cryptocurrency cụ thể
   */
  async collectPriceDataForSymbol(symbol: string): Promise<CryptoPriceData | null> {
    try {
      // Lấy thông tin currency từ database
      const currency = await cryptoCurrencyService.getCurrencyBySymbol(symbol);
      if (!currency) {
        throw new Error(`Không tìm thấy cryptocurrency ${symbol} trong database`);
      }

      // Thu thập dữ liệu giá từ external API
      const exchangeRate = await cryptoApiService.getExchangeRate(symbol, 'USDT');
      
      const priceData: CryptoPriceData = {
        symbol: currency.symbol,
        name: currency.name,
        price: exchangeRate.rate,
        lastUpdated: new Date(),
        source: exchangeRate.source
      };

      // Thử lấy thêm dữ liệu từ CoinGecko nếu có thể
      try {
        if (symbol !== 'USDT') {
          const coinGeckoPrice = await cryptoApiService.getCoinGeckoPrice(symbol);
          if (coinGeckoPrice) {
            priceData.price = coinGeckoPrice;
            priceData.source = 'coingecko';
          }
        }
      } catch (coinGeckoError) {
        // Không cần log lỗi CoinGecko, sử dụng dữ liệu từ Binance
        logger.debug(`CoinGecko không khả dụng cho ${symbol}, sử dụng dữ liệu từ ${priceData.source}`);
      }

      return priceData;
    } catch (error: any) {
      logger.error(`Lỗi thu thập dữ liệu cho ${symbol}:`, error);
      throw error;
    }
  }

  /**
   * Lấy dữ liệu giá mới nhất từ cache hoặc thu thập mới
   */
  async getLatestPrices(forceRefresh = false): Promise<CryptoPriceData[]> {
    if (!forceRefresh) {
      const cachedData = cryptoDataCache.get<CryptoPriceData[]>('all_prices');
      if (cachedData && cachedData.length > 0) {
        logger.debug('Trả về dữ liệu giá từ cache');
        return cachedData;
      }
    }

    logger.info('Thu thập dữ liệu giá mới...');
    return await this.collectAllPriceData();
  }

  /**
   * Lấy dữ liệu giá cho một cryptocurrency cụ thể
   */
  async getPriceForSymbol(symbol: string, forceRefresh = false): Promise<CryptoPriceData | null> {
    const cacheKey = `price_${symbol.toUpperCase()}`;
    
    if (!forceRefresh) {
      const cachedData = cryptoDataCache.get<CryptoPriceData>(cacheKey);
      if (cachedData) {
        logger.debug(`Trả về dữ liệu giá cho ${symbol} từ cache`);
        return cachedData;
      }
    }

    logger.info(`Thu thập dữ liệu giá mới cho ${symbol}...`);
    return await this.collectPriceDataForSymbol(symbol.toUpperCase());
  }

  /**
   * Lấy thống kê thu thập dữ liệu
   */
  getCollectionStats(): CollectionStats {
    return { ...this.collectionStats };
  }

  /**
   * Lấy thống kê cache
   */
  getCacheStats(): { keys: number; hits: number; misses: number } {
    const stats = cryptoDataCache.getStats();
    return {
      keys: cryptoDataCache.keys().length,
      hits: stats.hits,
      misses: stats.misses
    };
  }

  /**
   * Xóa cache
   */
  clearCache(): void {
    cryptoDataCache.flushAll();
    logger.info('Đã xóa cache dữ liệu cryptocurrency');
  }

  /**
   * Lấy danh sách cryptocurrency được hỗ trợ
   */
  getSupportedSymbols(): string[] {
    return [...this.supportedSymbols];
  }

  /**
   * Kiểm tra xem một symbol có được hỗ trợ không
   */
  isSymbolSupported(symbol: string): boolean {
    return this.supportedSymbols.includes(symbol.toUpperCase());
  }

  /**
   * Lấy lịch sử giá từ database
   */
  async getPriceHistory(
    symbol: string,
    startDate?: Date,
    endDate?: Date,
    limit = 100
  ): Promise<CryptoPriceData[]> {
    try {
      const historyData = await CryptoPriceHistory.getPriceHistory(
        symbol.toUpperCase(),
        startDate,
        endDate,
        limit
      );

      return historyData.map(item => ({
        symbol: item.symbol,
        name: item.name,
        price: item.price,
        volume24h: item.volume24h,
        marketCap: item.marketCap,
        change24h: item.change24h,
        changePercent24h: item.changePercent24h,
        lastUpdated: item.timestamp,
        source: item.source
      }));
    } catch (error: any) {
      logger.error(`Lỗi lấy lịch sử giá cho ${symbol}:`, error);
      return [];
    }
  }

  /**
   * Lấy giá mới nhất từ database
   */
  async getLatestPriceFromDB(symbol: string): Promise<CryptoPriceData | null> {
    try {
      const latestPrice = await CryptoPriceHistory.getLatestPrice(symbol.toUpperCase());
      if (!latestPrice) {
        return null;
      }

      return {
        symbol: latestPrice.symbol,
        name: latestPrice.name,
        price: latestPrice.price,
        volume24h: latestPrice.volume24h,
        marketCap: latestPrice.marketCap,
        change24h: latestPrice.change24h,
        changePercent24h: latestPrice.changePercent24h,
        lastUpdated: latestPrice.timestamp,
        source: latestPrice.source
      };
    } catch (error: any) {
      logger.error(`Lỗi lấy giá mới nhất từ database cho ${symbol}:`, error);
      return null;
    }
  }

  /**
   * Lấy tất cả giá mới nhất từ database
   */
  async getAllLatestPricesFromDB(): Promise<CryptoPriceData[]> {
    try {
      const latestPrices = await CryptoPriceHistory.getAllLatestPrices();

      return latestPrices.map(item => ({
        symbol: item.symbol,
        name: item.name,
        price: item.price,
        volume24h: item.volume24h,
        marketCap: item.marketCap,
        change24h: item.change24h,
        changePercent24h: item.changePercent24h,
        lastUpdated: item.timestamp,
        source: item.source
      }));
    } catch (error: any) {
      logger.error('Lỗi lấy tất cả giá mới nhất từ database:', error);
      return [];
    }
  }

  /**
   * Dọn dẹp dữ liệu cũ trong database
   */
  async cleanOldPriceData(daysToKeep = 90): Promise<number> {
    try {
      const deletedCount = await CryptoPriceHistory.cleanOldData(daysToKeep);
      logger.info(`Đã xóa ${deletedCount} bản ghi giá cũ hơn ${daysToKeep} ngày`);
      return deletedCount;
    } catch (error: any) {
      logger.error('Lỗi khi dọn dẹp dữ liệu cũ:', error);
      return 0;
    }
  }
}

export const cryptoDataCollectionService = new CryptoDataCollectionService();
