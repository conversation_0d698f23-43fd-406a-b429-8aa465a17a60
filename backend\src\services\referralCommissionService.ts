import mongoose from 'mongoose';
import ReferralCommission from '../models/referralCommission';
import ReferralCommissionConfig from '../models/referralCommissionConfig';
import User from '../models/userModel';
import Investment from '../models/investmentModel';
import logger from '../utils/logger';

/**
 * T<PERSON>h và tạo hoa hồng cho người giới thiệu khi có khoản đầu tư mới được phê duyệt
 */
export const calculateAndCreateCommission = async (investmentId: string): Promise<boolean> => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    // Lấy thông tin khoản đầu tư
    const investment = await Investment.findById(investmentId).session(session);
    if (!investment) {
      logger.error(`Investment not found: ${investmentId}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // <PERSON><PERSON>m tra trạng thái khoản đầu tư
    if (investment.status !== 'approved') {
      logger.info(`Investment ${investmentId} is not approved, skipping commission calculation`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Lấy thông tin người đầu tư
    const investor = await User.findById(investment.userId).session(session);
    if (!investor) {
      logger.error(`Investor not found for investment: ${investmentId}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Kiểm tra xem người đầu tư có người giới thiệu không
    if (!investor.referrerId) {
      logger.info(`Investor ${investor._id} has no referrer, skipping commission calculation`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Lấy thông tin người giới thiệu
    const referrer = await User.findById(investor.referrerId).session(session);
    if (!referrer) {
      logger.error(`Referrer not found for investor: ${investor._id}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Lấy cấu hình hoa hồng dựa trên level của người giới thiệu
    const referrerLevel = referrer.level || 1;
    const commissionConfig = await ReferralCommissionConfig.findOne({
      level: referrerLevel,
      isActive: true,
    }).session(session);

    if (!commissionConfig) {
      logger.error(`No active commission config found for level: ${referrerLevel}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Kiểm tra số tiền đầu tư có đủ điều kiện nhận hoa hồng không
    if (investment.amount < commissionConfig.minInvestmentAmount) {
      logger.info(
        `Investment amount ${investment.amount} is less than minimum required ${commissionConfig.minInvestmentAmount}, skipping commission`
      );
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Tính số tiền hoa hồng
    const commissionAmount = (investment.amount * commissionConfig.commissionRate) / 100;

    // Kiểm tra xem đã có hoa hồng cho khoản đầu tư này chưa
    const existingCommission = await ReferralCommission.findOne({
      investmentId: investment._id,
    }).session(session);

    if (existingCommission) {
      logger.info(`Commission already exists for investment: ${investmentId}`);
      await session.abortTransaction();
      session.endSession();
      return false;
    }

    // Tạo bản ghi hoa hồng mới
    const newCommission = new ReferralCommission({
      referrerId: referrer._id,
      referredId: investor._id,
      investmentId: investment._id,
      amount: commissionAmount,
      commissionRate: commissionConfig.commissionRate,
      level: referrerLevel,
      status: 'approved', // Tự động phê duyệt hoa hồng
      currency: investment.currency,
      description: `Referral commission for investment ${investment._id}`,
    });

    await newCommission.save({ session });

    // Cập nhật tổng hoa hồng cho người giới thiệu
    referrer.totalCommission = (referrer.totalCommission || 0) + commissionAmount;
    await referrer.save({ session });

    await session.commitTransaction();
    session.endSession();

    logger.info(
      `Successfully created commission of ${commissionAmount} ${investment.currency} for referrer ${referrer._id}`
    );
    return true;
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    logger.error('Error calculating commission:', error);
    return false;
  }
};

/**
 * Lấy danh sách hoa hồng của một người dùng
 */
export const getUserCommissions = async (userId: string) => {
  try {
    return await ReferralCommission.find({ referrerId: userId })
      .populate('referredId', 'name email')
      .populate('investmentId', 'amount currency status')
      .sort({ createdAt: -1 });
  } catch (error) {
    logger.error(`Error getting commissions for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Lấy tổng hoa hồng của một người dùng
 */
export const getUserTotalCommission = async (userId: string) => {
  try {
    const result = await ReferralCommission.aggregate([
      { $match: { referrerId: new mongoose.Types.ObjectId(userId), status: 'approved' } },
      { $group: { _id: '$currency', total: { $sum: '$amount' } } },
    ]);
    return result;
  } catch (error) {
    logger.error(`Error getting total commission for user ${userId}:`, error);
    throw error;
  }
};

export default {
  calculateAndCreateCommission,
  getUserCommissions,
  getUserTotalCommission,
};
