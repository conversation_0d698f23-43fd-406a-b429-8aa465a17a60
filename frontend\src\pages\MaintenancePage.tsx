import React, { useEffect, useState } from 'react';
import {
  Box,
  VStack,
  Heading,
  Text,
  Image,
  Container,
  Icon,
  Spinner,
  Button,
  useColorModeValue,
  Flex,
  HStack,
} from '@chakra-ui/react';
import { FiTool, FiClock, FiRefreshCw } from 'react-icons/fi';
import { useTranslation } from 'react-i18next';

interface MaintenanceData {
  maintenance: boolean;
  message?: string;
  siteName?: string;
  estimatedTime?: string;
}

const MaintenancePage: React.FC = () => {
  const { t } = useTranslation();
  const [maintenanceData, setMaintenanceData] = useState<MaintenanceData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastChecked, setLastChecked] = useState<Date>(new Date());

  // Color scheme
  const bgColor = useColorModeValue('gray.50', '#0B0E11');
  const cardBgColor = useColorModeValue('white', '#1E2329');
  const textColor = useColorModeValue('gray.800', '#EAECEF');
  const secondaryTextColor = useColorModeValue('gray.600', '#848E9C');
  const accentColor = '#F0B90B';

  // Check maintenance status
  const checkMaintenanceStatus = async () => {
    try {
      const response = await fetch('/api/system/maintenance-status');
      const data = await response.json();
      
      if (data.success) {
        setMaintenanceData(data.data);
        setLastChecked(new Date());
        
        // If maintenance is disabled, redirect to home
        if (!data.data.maintenance) {
          window.location.href = '/';
        }
      }
    } catch (error) {
      console.error('Error checking maintenance status:', error);
      // If we can't check status, assume maintenance is still active
    } finally {
      setIsLoading(false);
    }
  };

  // Check status on component mount
  useEffect(() => {
    checkMaintenanceStatus();
    
    // Set up periodic checking every 30 seconds
    const interval = setInterval(checkMaintenanceStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // Manual refresh
  const handleRefresh = () => {
    setIsLoading(true);
    checkMaintenanceStatus();
  };

  if (isLoading && !maintenanceData) {
    return (
      <Box
        minH="100vh"
        bg={bgColor}
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <VStack spacing={4}>
          <Spinner size="xl" color={accentColor} thickness="4px" />
          <Text color={textColor}>Checking system status...</Text>
        </VStack>
      </Box>
    );
  }

  return (
    <Box minH="100vh" bg={bgColor} py={8}>
      <Container maxW="md" centerContent>
        <VStack spacing={8} textAlign="center">
          {/* Logo/Icon */}
          <Box
            p={6}
            bg={cardBgColor}
            borderRadius="full"
            boxShadow="lg"
            border="2px solid"
            borderColor={accentColor}
          >
            <Icon as={FiTool} boxSize={12} color={accentColor} />
          </Box>

          {/* Main Content */}
          <VStack spacing={4}>
            <Heading
              size="xl"
              color={textColor}
              fontWeight="bold"
            >
              {maintenanceData?.siteName || 'Shipping Finance'}
            </Heading>
            
            <Heading
              size="lg"
              color={accentColor}
              fontWeight="semibold"
            >
              {t('maintenance.title', 'System Under Maintenance')}
            </Heading>

            <Text
              fontSize="lg"
              color={secondaryTextColor}
              maxW="400px"
              lineHeight="1.6"
            >
              {maintenanceData?.message || 
               t('maintenance.defaultMessage', 'We are currently performing scheduled maintenance to improve our services. Please check back soon.')}
            </Text>

            {/* Estimated Time */}
            <HStack spacing={2} color={secondaryTextColor}>
              <Icon as={FiClock} />
              <Text fontSize="sm">
                {maintenanceData?.estimatedTime || 
                 t('maintenance.estimatedTime', 'We will be back soon')}
              </Text>
            </HStack>
          </VStack>

          {/* Status Card */}
          <Box
            bg={cardBgColor}
            p={6}
            borderRadius="lg"
            boxShadow="md"
            border="1px solid"
            borderColor="gray.200"
            w="full"
            maxW="400px"
          >
            <VStack spacing={4}>
              <Text fontSize="sm" color={secondaryTextColor}>
                {t('maintenance.statusCheck', 'System Status')}
              </Text>
              
              <Flex
                justify="space-between"
                align="center"
                w="full"
                p={3}
                bg={bgColor}
                borderRadius="md"
              >
                <HStack spacing={2}>
                  <Box
                    w={3}
                    h={3}
                    borderRadius="full"
                    bg="orange.400"
                  />
                  <Text fontSize="sm" color={textColor}>
                    {t('maintenance.status', 'Under Maintenance')}
                  </Text>
                </HStack>
                
                <Button
                  size="sm"
                  variant="ghost"
                  leftIcon={<FiRefreshCw />}
                  onClick={handleRefresh}
                  isLoading={isLoading}
                  color={accentColor}
                  _hover={{ bg: 'gray.100' }}
                >
                  {t('maintenance.refresh', 'Refresh')}
                </Button>
              </Flex>

              <Text fontSize="xs" color={secondaryTextColor}>
                {t('maintenance.lastChecked', 'Last checked')}: {lastChecked.toLocaleTimeString()}
              </Text>
            </VStack>
          </Box>

          {/* Additional Info */}
          <VStack spacing={2}>
            <Text fontSize="sm" color={secondaryTextColor}>
              {t('maintenance.autoRefresh', 'This page will automatically refresh every 30 seconds')}
            </Text>
            
            <Text fontSize="xs" color={secondaryTextColor}>
              {t('maintenance.support', 'For urgent matters, please contact our support team')}
            </Text>
          </VStack>
        </VStack>
      </Container>
    </Box>
  );
};

export default MaintenancePage;
