// Admin utility functions for debugging and testing

export const forceAdminAccess = () => {
  try {
    // Set admin token in localStorage
    localStorage.setItem('adminToken', 'true');
    
    // Update user object in localStorage to include admin status
    const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      user.isAdmin = true;
      localStorage.setItem('user', JSON.stringify(user));
    }
    
    // Set admin cookie if possible
    document.cookie = 'adminToken=true; path=/; max-age=86400'; // 24 hours
    
    console.log('✅ Force admin access enabled');
    console.log('🔄 Please refresh the page or navigate to /admin');
    
    return true;
  } catch (error) {
    console.error('❌ Error enabling admin access:', error);
    return false;
  }
};

export const removeAdminAccess = () => {
  try {
    // Remove admin token from localStorage
    localStorage.removeItem('adminToken');
    
    // Update user object in localStorage to remove admin status
    const userStr = localStorage.getItem('user');
    if (userStr) {
      const user = JSON.parse(userStr);
      user.isAdmin = false;
      localStorage.setItem('user', JSON.stringify(user));
    }
    
    // Remove admin cookie
    document.cookie = 'adminToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    
    console.log('❌ Admin access removed');
    
    return true;
  } catch (error) {
    console.error('❌ Error removing admin access:', error);
    return false;
  }
};

export const checkAdminStatus = () => {
  const hasLocalStorageToken = localStorage.getItem('adminToken') === 'true';
  const hasCookie = document.cookie.includes('adminToken=true');
  
  const userStr = localStorage.getItem('user');
  let userIsAdmin = false;
  if (userStr) {
    try {
      const user = JSON.parse(userStr);
      userIsAdmin = user.isAdmin === true;
    } catch (error) {
      console.error('Error parsing user data:', error);
    }
  }
  
  const status = {
    hasLocalStorageToken,
    hasCookie,
    userIsAdmin,
    hasAnyAdminIndicator: hasLocalStorageToken || hasCookie || userIsAdmin
  };
  
  console.log('🔍 Admin Status Check:', status);
  return status;
};

// Make functions available globally for console debugging
if (typeof window !== 'undefined') {
  (window as any).forceAdminAccess = forceAdminAccess;
  (window as any).removeAdminAccess = removeAdminAccess;
  (window as any).checkAdminStatus = checkAdminStatus;
}

export default {
  forceAdminAccess,
  removeAdminAccess,
  checkAdminStatus
};
