import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dalO<PERSON>lay,
  ModalContent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Alert,
  AlertIcon,
  useToast,
  FormErrorMessage,
  Box,
  Icon,
  Switch,
  Badge,
  Divider
} from '@chakra-ui/react';
import { FaEdit, FaStar } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { walletManagementService, WithdrawalAddress } from '../../services/walletManagementService';
import { getCryptoColor } from '../../utils/cryptoUtils';
import { getCryptoIcon } from '../../utils/cryptoIconMap';

interface EditWalletModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  address: WithdrawalAddress | null;
}

const EditWalletModal: React.FC<EditWalletModalProps> = ({ 
  isOpen, 
  onClose, 
  onSuccess, 
  address 
}) => {
  const { t } = useTranslation();
  const toast = useToast();

  const [formData, setFormData] = useState({
    label: '',
    isDefault: false
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  // Initialize form data when address changes
  useEffect(() => {
    if (address) {
      setFormData({
        label: address.label,
        isDefault: address.isDefault
      });
      setErrors({});
    }
  }, [address]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setErrors({});
    }
  }, [isOpen]);

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.label.trim()) {
      newErrors.label = t('Label is required');
    } else if (formData.label.length > 50) {
      newErrors.label = t('Label must be 50 characters or less');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!address || !validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await walletManagementService.updateAddress(address._id, {
        label: formData.label.trim(),
        isDefault: formData.isDefault
      });

      toast({
        title: t('Success'),
        description: t('Wallet address updated successfully'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      onSuccess();
    } catch (error: any) {
      toast({
        title: t('Error'),
        description: error.message || t('Failed to update wallet address'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  if (!address) {
    return null;
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <HStack spacing={3}>
            <Icon as={FaEdit} color="blue.500" />
            <Text>{t('Edit Wallet Address')}</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <VStack spacing={6} align="stretch">
            {/* Address Info */}
            <Box>
              <VStack spacing={3} align="center">
                <Icon
                  as={getCryptoIcon(address.currency)}
                  color={getCryptoColor(address.currency)}
                  boxSize={12}
                />
                <VStack spacing={1}>
                  <HStack spacing={2}>
                    <Text fontSize="lg" fontWeight="bold">
                      {address.currency}
                    </Text>
                    <Badge
                      colorScheme={address.isVerified ? 'green' : 'orange'}
                      variant="subtle"
                    >
                      {address.isVerified ? t('Verified') : t('Unverified')}
                    </Badge>
                    {address.isDefault && (
                      <Badge colorScheme="blue" variant="solid">
                        <Icon as={FaStar} mr={1} />
                        {t('Default')}
                      </Badge>
                    )}
                  </HStack>
                  <Text
                    fontSize="sm"
                    color="gray.600"
                    fontFamily="mono"
                    textAlign="center"
                    wordBreak="break-all"
                  >
                    {address.formattedAddress}
                  </Text>
                  <Badge variant="outline">
                    {walletManagementService.getNetworkDisplayName(address.currency, address.network)}
                  </Badge>
                </VStack>
              </VStack>
            </Box>

            <Divider />

            {/* Label Input */}
            <FormControl isInvalid={!!errors.label}>
              <FormLabel>{t('Label')}</FormLabel>
              <Input
                placeholder={t('e.g., My Main Wallet, Exchange Wallet')}
                value={formData.label}
                onChange={(e) => handleInputChange('label', e.target.value)}
                maxLength={50}
              />
              <FormErrorMessage>{errors.label}</FormErrorMessage>
              <Text fontSize="xs" color="gray.600" mt={1}>
                {formData.label.length}/50 {t('characters')}
              </Text>
            </FormControl>

            {/* Default Address Switch */}
            {address.isVerified && (
              <FormControl>
                <HStack justify="space-between">
                  <VStack align="start" spacing={1}>
                    <FormLabel mb={0}>{t('Set as Default Address')}</FormLabel>
                    <Text fontSize="sm" color="gray.600">
                      {t('Default address will be pre-selected for withdrawals')}
                    </Text>
                  </VStack>
                  <Switch
                    isChecked={formData.isDefault}
                    onChange={(e) => handleInputChange('isDefault', e.target.checked)}
                    colorScheme="blue"
                    size="lg"
                  />
                </HStack>
              </FormControl>
            )}

            {/* Verification Notice */}
            {!address.isVerified && (
              <Alert status="warning" borderRadius="md">
                <AlertIcon />
                <VStack align="start" spacing={1} flex={1}>
                  <Text fontWeight="semibold">{t('Verification Required')}</Text>
                  <Text fontSize="sm">
                    {t('This address must be verified before it can be set as default or used for withdrawals.')}
                  </Text>
                </VStack>
              </Alert>
            )}

            {/* Address Details */}
            <Box bg="gray.50" p={4} borderRadius="md">
              <VStack spacing={2} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">{t('Created')}</Text>
                  <Text fontSize="sm">
                    {new Date(address.createdAt).toLocaleDateString()}
                  </Text>
                </HStack>
                {address.lastUsed && (
                  <HStack justify="space-between">
                    <Text fontSize="sm" color="gray.600">{t('Last Used')}</Text>
                    <Text fontSize="sm">
                      {new Date(address.lastUsed).toLocaleDateString()}
                    </Text>
                  </HStack>
                )}
                <HStack justify="space-between">
                  <Text fontSize="sm" color="gray.600">{t('Network')}</Text>
                  <Badge variant="outline">
                    {walletManagementService.getNetworkDisplayName(address.currency, address.network)}
                  </Badge>
                </HStack>
              </VStack>
            </Box>

            {/* Important Notice */}
            <Alert status="info" borderRadius="md">
              <AlertIcon />
              <VStack align="start" spacing={1} flex={1}>
                <Text fontWeight="semibold">{t('Note')}</Text>
                <Text fontSize="sm">
                  {t('You can only modify the label and default status. The address and currency cannot be changed for security reasons.')}
                </Text>
              </VStack>
            </Alert>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="ghost" onClick={onClose}>
              {t('Cancel')}
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleSubmit}
              isLoading={loading}
              loadingText={t('Updating...')}
              disabled={!formData.label.trim()}
            >
              {t('Update Address')}
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default EditWalletModal;
