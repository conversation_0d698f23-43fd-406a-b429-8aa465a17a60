import { logger } from './logger';

const DB_NAME = 'CryptoYieldDB';
const DB_VERSION = 1;

interface DBStores {
  pendingTransactions: 'pending-transactions';
  userCache: 'user-cache';
  walletCache: 'wallet-cache';
}

export const stores: DBStores = {
  pendingTransactions: 'pending-transactions',
  userCache: 'user-cache',
  walletCache: 'wallet-cache'
};

class IndexedDBService {
  private db: IDBDatabase | null = null;

  async initDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        logger.error('IndexedDB açılırken hata oluştu:', { error: request.error ? request.error.toString() : 'Unknown error' });
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        logger.info('IndexedDB başarıyla açıldı');
        resolve();
      };

      request.onupgradeneeded = (event: IDBVersionChangeEvent) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Bekleyen işlemler için store
        if (!db.objectStoreNames.contains(stores.pendingTransactions)) {
          db.createObjectStore(stores.pendingTransactions, {
            keyPath: 'id',
            autoIncrement: true
          });
        }

        // Kullanıcı cache için store
        if (!db.objectStoreNames.contains(stores.userCache)) {
          const userStore = db.createObjectStore(stores.userCache, {
            keyPath: 'id'
          });
          userStore.createIndex('email', 'email', { unique: true });
        }

        // Cüzdan cache için store
        if (!db.objectStoreNames.contains(stores.walletCache)) {
          const walletStore = db.createObjectStore(stores.walletCache, {
            keyPath: 'userId'
          });
          walletStore.createIndex('address', 'address', { unique: true });
        }
      };
    });
  }

  async addPendingTransaction(transaction: any): Promise<number> {
    try {
      await this.ensureDB();
      return await this.add(stores.pendingTransactions, {
        ...transaction,
        timestamp: new Date().toISOString(),
        status: 'pending'
      });
    } catch (error) {
      logger.error('Bekleyen işlem eklenirken hata:', { error: error instanceof Error ? error.message : 'Unknown error' });
      throw error;
    }
  }

  async getPendingTransactions(): Promise<any[]> {
    try {
      await this.ensureDB();
      return await this.getAll(stores.pendingTransactions);
    } catch (error) {
      logger.error('Bekleyen işlemler alınırken hata:', { error: error instanceof Error ? error.message : 'Unknown error' });
      throw error;
    }
  }

  async removePendingTransaction(id: number): Promise<void> {
    try {
      await this.ensureDB();
      await this.delete(stores.pendingTransactions, id);
    } catch (error) {
      logger.error('Bekleyen işlem silinirken hata:', { error: error instanceof Error ? error.message : 'Unknown error' });
      throw error;
    }
  }

  async cacheUserData(userData: any): Promise<void> {
    try {
      await this.ensureDB();
      await this.put(stores.userCache, {
        ...userData,
        lastUpdated: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Kullanıcı verisi cache\'lenirken hata:', { error: error instanceof Error ? error.message : 'Unknown error' });
      throw error;
    }
  }

  async getCachedUserData(userId: string): Promise<any | null> {
    try {
      await this.ensureDB();
      return await this.get(stores.userCache, userId);
    } catch (error) {
      logger.error('Cache\'lenmiş kullanıcı verisi alınırken hata:', { error: error instanceof Error ? error.message : 'Unknown error' });
      throw error;
    }
  }

  async cacheWalletData(walletData: any): Promise<void> {
    try {
      await this.ensureDB();
      await this.put(stores.walletCache, {
        ...walletData,
        lastUpdated: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Cüzdan verisi cache\'lenirken hata:', { error: error instanceof Error ? error.message : 'Unknown error' });
      throw error;
    }
  }

  async getCachedWalletData(userId: string): Promise<any | null> {
    try {
      await this.ensureDB();
      return await this.get(stores.walletCache, userId);
    } catch (error) {
      logger.error('Cache\'lenmiş cüzdan verisi alınırken hata:', { error: error instanceof Error ? error.message : 'Unknown error' });
      throw error;
    }
  }

  async clearCache(): Promise<void> {
    try {
      await this.ensureDB();
      await this.clear(stores.userCache);
      await this.clear(stores.walletCache);
      logger.info('Cache başarıyla temizlendi');
    } catch (error) {
      logger.error('Cache temizlenirken hata:', { error: error instanceof Error ? error.message : 'Unknown error' });
      throw error;
    }
  }

  private async ensureDB(): Promise<void> {
    if (!this.db) {
      await this.initDB();
    }
  }

  private add(storeName: string, value: any): Promise<number> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database is not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.add(value);

      request.onsuccess = () => resolve(request.result as number);
      request.onerror = () => reject(request.error);
    });
  }

  private get(storeName: string, key: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database is not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(key);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  private getAll(storeName: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database is not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  private put(storeName: string, value: any): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database is not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(value);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  private delete(storeName: string, key: any): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database is not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(key);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  private clear(storeName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database is not initialized'));
        return;
      }

      const transaction = this.db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }
}

export const indexedDBService = new IndexedDBService();