import mongoose, { Document, Schema } from 'mongoose';
import { logger } from '../utils/logger';

export interface ITransaction extends Document {
  userId: mongoose.Types.ObjectId;
  walletId: mongoose.Types.ObjectId;
  walletAddress?: string;
  type: 'deposit' | 'withdrawal' | 'commission' | 'interest';
  asset: string;
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'approved' | 'rejected';
  txHash?: string;
  blockchainNetwork?: 'ethereum' | 'bsc' | 'tron';
  investmentId?: mongoose.Types.ObjectId;
  description?: string;
  metadata?: Record<string, any>;

  // Amount correction fields for admin verification
  originalAmount?: number;
  adminVerifiedAmount?: number;
  amountModifiedBy?: mongoose.Types.ObjectId;
  amountModifiedAt?: Date;
  amountCorrectionReason?: string;

  createdAt: Date;
  updatedAt: Date;
}

const transactionSchema = new Schema<ITransaction>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    walletId: {
      type: Schema.Types.ObjectId,
      ref: 'Wallet',
      required: true,
    },
    walletAddress: {
      type: String,
      trim: true,
    },
    type: {
      type: String,
      enum: ['deposit', 'withdrawal', 'commission', 'interest'],
      required: true,
    },
    asset: {
      type: String,
      required: true,
      trim: true,
      uppercase: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'approved', 'rejected'],
      default: 'pending',
    },
    txHash: {
      type: String,
      trim: true,
    },
    blockchainNetwork: {
      type: String,
      trim:true,
      required: false,
    },
    investmentId: {
      type: Schema.Types.ObjectId,
      ref: 'Investment',
      required: false,
    },
    description: {
      type: String,
      trim: true,
    },
    metadata: {
      type: Object,
      default: {},
    },

    // Amount correction fields for admin verification
    originalAmount: {
      type: Number,
      required: false,
    },
    adminVerifiedAmount: {
      type: Number,
      required: false,
    },
    amountModifiedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
    amountModifiedAt: {
      type: Date,
      required: false,
    },
    amountCorrectionReason: {
      type: String,
      trim: true,
      maxlength: 500,
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

// Middleware to emit events after transaction is saved
transactionSchema.post('save', async function(doc) {
  try {
    // Import services dynamically to avoid circular dependencies
    const { notificationService } = require('../services/notificationService');

    logger.info(`Transaction saved: ${doc._id}`, {
      type: doc.type,
      status: doc.status,
      userId: doc.userId.toString()
    });

    // Notify the user about their transaction
    await notificationService.notifyUserAboutTransactionUpdate(
      doc.userId.toString(),
      doc
    );

    // Emit specific event based on transaction type
    if (doc.type === 'deposit') {
      // Notify about new deposit
      await notificationService.notifyNewDeposit({
        id: doc._id.toString(),
        userId: doc.userId.toString(),
        type: doc.type,
        amount: doc.amount,
        asset: doc.asset,
        status: doc.status,
        timestamp: doc.updatedAt || doc.createdAt,
        txHash: doc.txHash,
        walletAddress: doc.walletAddress,
        blockchainNetwork: doc.blockchainNetwork,
        metadata: doc.metadata
      });

    } else if (doc.type === 'withdrawal') {
      // Notify about new withdrawal
      await notificationService.notifyNewWithdrawal({
        id: doc._id.toString(),
        userId: doc.userId.toString(),
        type: doc.type,
        amount: doc.amount,
        asset: doc.asset,
        status: doc.status,
        timestamp: doc.updatedAt || doc.createdAt,
        txHash: doc.txHash,
        walletAddress: doc.walletAddress,
        blockchainNetwork: doc.blockchainNetwork,
        metadata: doc.metadata
      });
    }

    // Emit general transaction update
    await notificationService.notifyTransactionUpdate({
      id: doc._id.toString(),
      userId: doc.userId.toString(),
      type: doc.type,
      amount: doc.amount,
      asset: doc.asset,
      status: doc.status,
      timestamp: doc.updatedAt || doc.createdAt,
      metadata: doc.metadata
    });
  } catch (error) {
    logger.error('Error in transaction post-save hook:', error);
  }
});

// Middleware to emit events after transaction is updated
transactionSchema.post('findOneAndUpdate', async function(doc) {
  if (!doc) return;

  try {
    // Import services dynamically to avoid circular dependencies
    const { notificationService } = require('../services/notificationService');

    logger.info(`Transaction updated: ${doc._id}`, {
      type: doc.type,
      status: doc.status,
      userId: doc.userId.toString()
    });

    // Notify the user about their transaction update
    await notificationService.notifyUserAboutTransactionUpdate(
      doc.userId.toString(),
      doc
    );

    // Emit specific event based on transaction type
    if (doc.type === 'deposit') {
      // Prepare payload for notifications
      const depositUpdatePayload = {
        transaction: {
          id: doc._id.toString(),
          userId: doc.userId.toString(),
          status: doc.status,
          amount: doc.amount,
          asset: doc.asset,
          updatedAt: new Date(),
          txHash: doc.txHash,
          walletAddress: doc.walletAddress,
          blockchainNetwork: doc.blockchainNetwork,
          metadata: doc.metadata
        },
        message: `Your deposit status has been updated to ${doc.status}`,
        timestamp: new Date().toISOString()
      };

      // Notify about deposit update
      await notificationService.notifyDepositUpdate(depositUpdatePayload.transaction);

    } else if (doc.type === 'withdrawal') {
      // Prepare withdrawal update payload
      const withdrawalUpdatePayload = {
        id: doc._id.toString(),
        userId: doc.userId.toString(),
        status: doc.status,
        amount: doc.amount,
        asset: doc.asset,
        updatedAt: new Date(),
        txHash: doc.txHash,
        walletAddress: doc.walletAddress,
        blockchainNetwork: doc.blockchainNetwork,
        metadata: doc.metadata,
        type: 'withdrawal'
      };

      // Notify about withdrawal update
      await notificationService.notifyWithdrawalUpdate(withdrawalUpdatePayload);

      // Notify admins about withdrawal update
      await notificationService.notifyAdmins('withdrawal_updated', withdrawalUpdatePayload);
    }

    // Prepare general transaction update payload
    const transactionUpdatePayload = {
      id: doc._id.toString(),
      userId: doc.userId.toString(),
      type: doc.type,
      amount: doc.amount,
      asset: doc.asset,
      status: doc.status,
      timestamp: new Date(),
      metadata: doc.metadata
    };

    // Emit general transaction update
    await notificationService.notifyTransactionUpdate(transactionUpdatePayload);
  } catch (error) {
    logger.error('Error in transaction post-update hook:', error);
  }
});

const Transaction = mongoose.models.Transaction || mongoose.model<ITransaction>('Transaction', transactionSchema);

export default Transaction;
