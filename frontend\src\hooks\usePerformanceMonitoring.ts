import { useEffect, useCallback } from 'react';
import { logger } from '../utils/logger';

interface PerformanceOptions {
  reportThreshold?: number;
  enableDetailedProfiling?: boolean;
}

export const usePerformanceMonitoring = (options: PerformanceOptions = {}) => {
  const {
    reportThreshold = 3000, // 3 seconds
    enableDetailedProfiling = false
  } = options;

  const reportPerformanceMetric = useCallback((name: string, value: number) => {
    logger.info('Performance metric:', { name, value });

    // Report to analytics if above threshold
    if (value > reportThreshold) {
      logger.warn('Performance threshold exceeded:', { name, value, threshold: reportThreshold });
    }
  }, [reportThreshold]);

  const initPerformanceObserver = useCallback(() => {
    // First Contentful Paint
    const fcpObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      if (entries.length > 0) {
        const fcp = entries[0];
        reportPerformanceMetric('FCP', fcp.startTime);
      }
    });
    fcpObserver.observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      if (entries.length > 0) {
        const lcp = entries[entries.length - 1];
        reportPerformanceMetric('LCP', lcp.startTime);
      }
    });
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    const fidObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach(entry => {
        reportPerformanceMetric('FID', entry.duration);
      });
    });
    fidObserver.observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift
    const clsObserver = new PerformanceObserver((entryList) => {
      let clsValue = 0;
      const entries = entryList.getEntries();
      entries.forEach(entry => {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      });
      reportPerformanceMetric('CLS', clsValue);
    });
    clsObserver.observe({ entryTypes: ['layout-shift'] });

    // Navigation Timing
    const navigationObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach(entry => {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          reportPerformanceMetric('TTFB', navEntry.responseStart - navEntry.requestStart);

          if (enableDetailedProfiling) {
            // Detailed timing metrics
            const timingMetrics = {
              dns: navEntry.domainLookupEnd - navEntry.domainLookupStart,
              tcp: navEntry.connectEnd - navEntry.connectStart,
              ssl: navEntry.secureConnectionStart > 0 ?
                   navEntry.connectEnd - navEntry.secureConnectionStart : 0,
              ttfb: navEntry.responseStart - navEntry.requestStart,
              download: navEntry.responseEnd - navEntry.responseStart,
              domInteractive: navEntry.domInteractive - navEntry.responseEnd,
              domComplete: navEntry.domComplete - navEntry.domInteractive,
              loadEvent: navEntry.loadEventEnd - navEntry.loadEventStart
            };

            Object.entries(timingMetrics).forEach(([name, value]) => {
              reportPerformanceMetric(`NAV_${name.toUpperCase()}`, value);
            });
          }
        }
      });
    });
    navigationObserver.observe({ entryTypes: ['navigation'] });

    // Resource Timing
    let resourceObserver: PerformanceObserver | null = null;
    if (enableDetailedProfiling) {
      resourceObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming;
            reportPerformanceMetric(
              `RESOURCE_${resourceEntry.initiatorType.toUpperCase()}`,
              resourceEntry.duration
            );
          }
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
    }

    // Long Tasks
    const longTaskObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach(entry => {
        reportPerformanceMetric('LONG_TASK', entry.duration);
      });
    });
    longTaskObserver.observe({ entryTypes: ['longtask'] });

    return () => {
      fcpObserver.disconnect();
      lcpObserver.disconnect();
      fidObserver.disconnect();
      clsObserver.disconnect();
      navigationObserver.disconnect();
      if (enableDetailedProfiling && resourceObserver) {
        resourceObserver.disconnect();
      }
      longTaskObserver.disconnect();
    };
  }, [reportPerformanceMetric, enableDetailedProfiling]);

  useEffect(() => {
    const cleanup = initPerformanceObserver();
    return cleanup;
  }, [initPerformanceObserver]);

  return {
    measureUserTiming: (markName: string) => {
      performance.mark(markName);
      logger.debug('Performance mark:', { markName });
    },

    measureDuration: (startMark: string, endMark: string, measureName: string) => {
      try {
        performance.measure(measureName, startMark, endMark);
        const entries = performance.getEntriesByName(measureName);
        if (entries.length > 0) {
          reportPerformanceMetric(measureName, entries[0].duration);
        }
      } catch (error) {
        logger.error('Performance measurement error:', { error: error instanceof Error ? error.message : 'Unknown error' });
      }
    },

    clearMarks: () => {
      performance.clearMarks();
      performance.clearMeasures();
    }
  };
};