import { logger } from '../utils/logger';
import { getSocketService } from './socketService';

/**
 * Service for sending real-time notifications to users and admins
 */
class NotificationService {
  private static instance: NotificationService | null = null;
  private socketService: any = null;

  private constructor() {}

  /**
   * Initialize with Socket.IO service
   */
  public initialize(socketService: any): void {
    this.socketService = socketService;
    logger.info('NotificationService initialized with Socket.IO service');
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Send notification about a new deposit to admins
   */
  public async notifyAdminsAboutDeposit(deposit: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    if (!deposit) {
      logger.error('Invalid deposit data for admin notification');
      return false;
    }

    try {
      // Convert ObjectId to string if needed
      const depositId = deposit._id?.toString() || deposit.id?.toString();
      const userId = deposit.userId?.toString();

      // Create payload with all necessary information
      const payload = {
        id: depositId,
        userId: userId,
        userName: deposit.userName || 'User',
        userEmail: deposit.userEmail || '<EMAIL>',
        amount: deposit.amount,
        asset: deposit.asset,
        timestamp: deposit.createdAt || new Date(),
        status: deposit.status || 'pending',
        txHash: deposit.txHash,
        walletAddress: deposit.walletAddress
      };

      // Use the notifyAdminsAboutNewDeposit method for real-time updates
      const result = await this.socketService.notifyAdminsAboutNewDeposit(deposit);

      // Also broadcast as general transaction update
      await this.socketService.broadcastToAdmins({
        type: 'transaction_update',
        payload: {
          ...payload,
          type: 'deposit'
        }
      });

      logger.info('Notified admins about new deposit', {
        depositId,
        userId,
        result
      });

      return result > 0;
    } catch (error) {
      logger.error('Failed to notify admins about deposit', error);
      return false;
    }
  }

  /**
   * Send notification about a new withdrawal to admins
   */
  public async notifyAdminsAboutWithdrawal(withdrawal: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    if (!withdrawal) {
      logger.error('Invalid withdrawal data for admin notification');
      return false;
    }

    try {
      // Convert ObjectId to string if needed
      const withdrawalId = withdrawal._id?.toString() || withdrawal.id?.toString();
      const userId = withdrawal.userId?.toString();

      // Create payload with all necessary information
      const payload = {
        id: withdrawalId,
        userId: userId,
        userName: withdrawal.userName || 'User',
        userEmail: withdrawal.userEmail || '<EMAIL>',
        amount: withdrawal.amount,
        asset: withdrawal.asset,
        timestamp: withdrawal.createdAt || new Date(),
        status: withdrawal.status || 'pending',
        txHash: withdrawal.txHash,
        walletAddress: withdrawal.walletAddress
      };

      // Send notification with high priority
      const result = await this.socketService.broadcastToAdmins({
        type: 'new_withdrawal',
        payload
      });

      // Also broadcast as general transaction update
      await this.socketService.broadcastToAdmins({
        type: 'transaction_update',
        payload: {
          ...payload,
          type: 'withdrawal',
          priority: 'high'
        }
      });

      logger.info('Notified admins about new withdrawal', {
        withdrawalId,
        userId,
        result
      });

      return result > 0;
    } catch (error) {
      logger.error('Failed to notify admins about withdrawal', error);
      return false;
    }
  }

  /**
   * Send notification about transaction status change to user
   */
  public async notifyUserAboutTransactionUpdate(userId: string, transaction: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    if (!userId || !transaction) {
      logger.error('Invalid parameters for user transaction notification', { userId, transactionId: transaction?._id });
      return false;
    }

    try {
      // Convert ObjectId to string if needed
      const userIdStr = userId.toString();
      const transactionId = transaction._id?.toString() || transaction.id?.toString();

      // Create payload with all necessary information
      const payload = {
        id: transactionId,
        type: transaction.type,
        amount: transaction.amount,
        asset: transaction.asset,
        status: transaction.status,
        timestamp: transaction.updatedAt || new Date(),
        txHash: transaction.txHash,
        walletAddress: transaction.walletAddress,
        completedAt: transaction.completedAt,
        adminNotes: transaction.adminNotes
      };

      // Send notification
      const result = this.socketService.broadcastToUser(userIdStr, {
        type: 'transaction_update',
        payload
      });

      // Also send specific event based on transaction type
      if (transaction.type === 'deposit') {
        this.socketService.broadcastToUser(userIdStr, {
          type: 'deposit_update',
          payload
        });
      } else if (transaction.type === 'withdrawal') {
        this.socketService.broadcastToUser(userIdStr, {
          type: 'withdrawal_update',
          payload
        });
      }

      // Send balance update notification
      this.socketService.broadcastToUser(userIdStr, {
        type: 'balance_update',
        payload: {
          asset: transaction.asset,
          transactionId: transactionId,
          timestamp: new Date(),
          type: transaction.type,
          status: transaction.status
        }
      });

      logger.info('Notified user about transaction update', {
        userId: userIdStr,
        transactionId,
        type: transaction.type,
        status: transaction.status,
        result
      });

      return result > 0;
    } catch (error) {
      logger.error('Failed to notify user about transaction update', error);
      return false;
    }
  }

  /**
   * Send notification about user registration to admins
   */
  public async notifyAdminsAboutNewUser(user: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    if (!user) {
      logger.error('Invalid user data for admin notification');
      return false;
    }

    try {
      // Convert ObjectId to string if needed
      const userId = user._id?.toString() || user.id?.toString();

      // Create payload with all necessary information
      const payload = {
        id: userId,
        email: user.email,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        timestamp: user.createdAt || new Date(),
        country: user.country || '',
        registrationIp: user.registrationIp || '',
        isVerified: user.isVerified || false
      };

      // Send notification
      const result = await this.socketService.broadcastToAdmins({
        type: 'new_user',
        payload
      });

      logger.info('Notified admins about new user', {
        userId,
        email: user.email,
        result
      });

      return result > 0;
    } catch (error) {
      logger.error('Failed to notify admins about new user', error);
      return false;
    }
  }

  /**
   * Gửi thông báo về deposit mới
   */
  public async notifyNewDeposit(deposit: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    try {
      // Gửi thông báo đến user
      const result = await this.socketService.broadcastToUser(deposit.userId.toString(), {
        type: 'new_deposit',
        payload: deposit
      });

      // Gửi thông báo đến admin
      await this.socketService.broadcastToAdmins({
        type: 'new_deposit',
        payload: deposit
      }, 'deposit_updates');

      logger.info(`Notified about new deposit: ${deposit.id || deposit._id}`, {
        userId: deposit.userId.toString(),
        amount: deposit.amount,
        asset: deposit.asset
      });

      return result > 0;
    } catch (error) {
      logger.error('Error notifying about new deposit:', error);
      return false;
    }
  }

  /**
   * Gửi thông báo về cập nhật deposit
   */
  public async notifyDepositUpdate(deposit: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    try {
      // Gửi thông báo đến user
      const result = await this.socketService.broadcastToUser(deposit.userId.toString(), {
        type: 'deposit_update',
        payload: deposit
      });

      // Gửi thông báo đến admin
      await this.socketService.broadcastToAdmins({
        type: 'deposit_status_updated',
        payload: {
          transaction: deposit,
          message: `Deposit status updated to ${deposit.status}`,
          timestamp: new Date().toISOString()
        }
      }, 'deposit_updates');

      logger.info(`Notified about deposit update: ${deposit.id || deposit._id}`, {
        userId: deposit.userId.toString(),
        status: deposit.status
      });

      return result > 0;
    } catch (error) {
      logger.error('Error notifying about deposit update:', error);
      return false;
    }
  }

  /**
   * Send notification about deposit amount update (admin verification)
   */
  public async notifyDepositAmountUpdate(amountUpdate: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    try {
      // Notify user about amount verification
      const userResult = await this.socketService.broadcastToUser(amountUpdate.userId.toString(), {
        type: 'deposit_amount_verified',
        payload: {
          depositId: amountUpdate.id,
          originalAmount: amountUpdate.originalAmount,
          adminVerifiedAmount: amountUpdate.adminVerifiedAmount,
          currency: amountUpdate.currency,
          reason: amountUpdate.amountCorrectionReason,
          verifiedAt: amountUpdate.modifiedAt,
          message: `Your deposit amount has been verified by admin: ${amountUpdate.originalAmount} → ${amountUpdate.adminVerifiedAmount} ${amountUpdate.currency}`
        }
      });

      // Notify admins about amount update
      await this.socketService.broadcastToAdmins({
        type: 'deposit_amount_updated',
        payload: {
          depositId: amountUpdate.id,
          userId: amountUpdate.userId,
          originalAmount: amountUpdate.originalAmount,
          adminVerifiedAmount: amountUpdate.adminVerifiedAmount,
          currency: amountUpdate.currency,
          reason: amountUpdate.amountCorrectionReason,
          modifiedBy: amountUpdate.modifiedBy,
          modifiedAt: amountUpdate.modifiedAt,
          message: `Deposit amount updated: ${amountUpdate.originalAmount} → ${amountUpdate.adminVerifiedAmount} ${amountUpdate.currency}`,
          timestamp: new Date().toISOString()
        }
      }, 'deposit_updates');

      logger.info(`Notified about deposit amount update: ${amountUpdate.id}`, {
        userId: amountUpdate.userId,
        originalAmount: amountUpdate.originalAmount,
        adminVerifiedAmount: amountUpdate.adminVerifiedAmount,
        currency: amountUpdate.currency
      });

      return userResult > 0;
    } catch (error) {
      logger.error('Error notifying about deposit amount update:', error);
      return false;
    }
  }

  /**
   * Send notification about interest calculation to user
   */
  public async notifyInterestCalculated(userId: string, interestData: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    if (!userId || !interestData) {
      logger.error('Invalid parameters for interest calculation notification', { userId, interestData });
      return false;
    }

    try {
      // Convert ObjectId to string if needed
      const userIdStr = userId.toString();
      const packageId = interestData.packageId?.toString();

      // Create payload with all necessary information
      const payload = {
        packageId,
        dailyInterest: interestData.dailyInterest,
        currency: interestData.currency,
        totalEarned: interestData.totalEarned,
        activeDays: interestData.activeDays,
        calculationTime: interestData.calculationTime,
        timestamp: new Date().toISOString(),
        message: `Daily interest calculated: +${interestData.dailyInterest} ${interestData.currency}`
      };

      // Send notification to user
      const userResult = await this.socketService.broadcastToUser(userIdStr, {
        type: 'interest_calculated',
        payload
      });

      // Send balance update notification
      await this.socketService.broadcastToUser(userIdStr, {
        type: 'balance_update',
        payload: {
          asset: interestData.currency,
          packageId,
          interestAmount: interestData.dailyInterest,
          totalEarned: interestData.totalEarned,
          timestamp: new Date().toISOString(),
          type: 'interest_earned'
        }
      });

      // Notify admins about interest calculation
      await this.socketService.broadcastToAdmins({
        type: 'interest_calculated',
        payload: {
          ...payload,
          userId: userIdStr,
          adminMessage: `Interest calculated for user ${userIdStr}: +${interestData.dailyInterest} ${interestData.currency}`
        }
      }, 'interest_updates');

      logger.info('Notified about interest calculation', {
        userId: userIdStr,
        packageId,
        dailyInterest: interestData.dailyInterest,
        currency: interestData.currency,
        totalEarned: interestData.totalEarned,
        result: userResult
      });

      return userResult > 0;
    } catch (error) {
      logger.error('Failed to notify about interest calculation', error);
      return false;
    }
  }

  /**
   * Send notification about referral commission earned
   */
  public async notifyReferralCommission(referrerId: string, commissionData: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    if (!referrerId || !commissionData) {
      logger.error('Invalid parameters for referral commission notification', { referrerId, commissionData });
      return false;
    }

    try {
      // Convert ObjectId to string if needed
      const referrerIdStr = referrerId.toString();

      // Create payload with all necessary information
      const payload = {
        type: commissionData.type || 'referral_commission_earned',
        amount: commissionData.amount,
        currency: commissionData.currency,
        referredUserName: commissionData.referredUserName,
        timestamp: new Date().toISOString(),
        message: `You earned ${commissionData.amount} ${commissionData.currency} commission from ${commissionData.referredUserName}'s deposit`
      };

      // Send notification to referrer
      const result = await this.socketService.broadcastToUser(referrerIdStr, {
        type: 'referral_commission_earned',
        payload
      });

      // Send balance update notification
      await this.socketService.broadcastToUser(referrerIdStr, {
        type: 'balance_update',
        payload: {
          asset: commissionData.currency,
          commissionAmount: commissionData.amount,
          timestamp: new Date().toISOString(),
          type: 'referral_commission'
        }
      });

      logger.info('Notified about referral commission', {
        referrerId: referrerIdStr,
        amount: commissionData.amount,
        currency: commissionData.currency,
        result
      });

      return result > 0;
    } catch (error) {
      logger.error('Failed to notify about referral commission', error);
      return false;
    }
  }

  /**
   * Gửi thông báo về withdrawal mới
   */
  public async notifyNewWithdrawal(withdrawal: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    try {
      // Gửi thông báo đến user
      const result = await this.socketService.broadcastToUser(withdrawal.userId.toString(), {
        type: 'new_withdrawal',
        payload: withdrawal
      });

      // Gửi thông báo đến admin
      await this.socketService.broadcastToAdmins({
        type: 'new_withdrawal',
        payload: withdrawal
      }, 'withdrawal_updates');

      logger.info(`Notified about new withdrawal: ${withdrawal.id || withdrawal._id}`, {
        userId: withdrawal.userId.toString(),
        amount: withdrawal.amount,
        asset: withdrawal.asset
      });

      return result > 0;
    } catch (error) {
      logger.error('Error notifying about new withdrawal:', error);
      return false;
    }
  }

  /**
   * Gửi thông báo về cập nhật withdrawal
   */
  public async notifyWithdrawalUpdate(withdrawal: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    try {
      // Gửi thông báo đến user
      const result = await this.socketService.broadcastToUser(withdrawal.userId.toString(), {
        type: 'withdrawal_update',
        payload: withdrawal
      });

      // Gửi thông báo đến admin
      await this.socketService.broadcastToAdmins({
        type: 'withdrawal_status_updated',
        payload: {
          transaction: withdrawal,
          message: `Withdrawal status updated to ${withdrawal.status}`,
          timestamp: new Date().toISOString()
        }
      }, 'withdrawal_updates');

      logger.info(`Notified about withdrawal update: ${withdrawal.id || withdrawal._id}`, {
        userId: withdrawal.userId.toString(),
        status: withdrawal.status
      });

      return result > 0;
    } catch (error) {
      logger.error('Error notifying about withdrawal update:', error);
      return false;
    }
  }

  /**
   * Gửi thông báo về cập nhật transaction
   */
  public async notifyTransactionUpdate(transaction: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    try {
      // Gửi thông báo đến user
      const result = await this.socketService.broadcastToUser(transaction.userId.toString(), {
        type: 'transaction_update',
        payload: transaction
      });

      logger.info(`Notified about transaction update: ${transaction.id || transaction._id}`, {
        userId: transaction.userId.toString(),
        type: transaction.type,
        status: transaction.status
      });

      return result > 0;
    } catch (error) {
      logger.error('Error notifying about transaction update:', error);
      return false;
    }
  }

  /**
   * Gửi thông báo đến admin
   */
  public async notifyAdmins(type: string, payload: any, room?: string): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    try {
      // Gửi thông báo đến admin
      const result = await this.socketService.broadcastToAdmins({
        type,
        payload
      }, room);

      logger.info(`Notified admins: ${type}`);
      return result > 0;
    } catch (error) {
      logger.error(`Error notifying admins (${type}):`, error);
      return false;
    }
  }

  /**
   * Gửi thông báo đến user
   */
  public async notifyUser(userId: string, type: string, payload: any): Promise<boolean> {
    if (!this.socketService) {
      logger.error('Socket.IO service not initialized');
      return false;
    }

    try {
      // Gửi thông báo đến user
      const result = await this.socketService.broadcastToUser(userId, {
        type,
        payload
      });

      logger.info(`Notified user ${userId}: ${type}`);
      return result > 0;
    } catch (error) {
      logger.error(`Error notifying user ${userId} (${type}):`, error);
      return false;
    }
  }

  /**
   * Lấy instance của SocketService
   */
  public getSocketService(): any {
    return this.socketService;
  }
}

export const notificationService = NotificationService.getInstance();
