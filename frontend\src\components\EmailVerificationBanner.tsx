import React, { useState, useEffect } from 'react';
import {
  <PERSON>ert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Button,
  HStack,
  VStack,
  Text,
  useToast,
  CloseButton,
  Box,
  Collapse,
  Icon
} from '@chakra-ui/react';
import { FaEnvelope, FaClock } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import useAuth from '../hooks/useAuth';

interface EmailVerificationBannerProps {
  onClose?: () => void;
  showCloseButton?: boolean;
  variant?: 'banner' | 'card';
}

interface VerificationStatus {
  emailVerified: boolean;
  canResend: boolean;
  nextResendTime: string | null;
  attemptsRemaining: number;
  lastSentAt: string | null;
}

const EmailVerificationBanner: React.FC<EmailVerificationBannerProps> = ({
  onClose,
  showCloseButton = true,
  variant = 'banner'
}) => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const toast = useToast();

  const [isVisible, setIsVisible] = useState(true);
  const [isResending, setIsResending] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<VerificationStatus | null>(null);
  const [countdown, setCountdown] = useState<number>(0);

  // Check conditions but don't return early
  const shouldShow = user && !user.emailVerified && isVisible;

  useEffect(() => {
    if (shouldShow) {
      fetchVerificationStatus();
    }
  }, [shouldShow]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (countdown > 0) {
      interval = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            fetchVerificationStatus();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [countdown]);

  const fetchVerificationStatus = async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/users/email-verification/status`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setVerificationStatus(data.data);

        // Set countdown if can't resend yet
        if (!data.data.canResend && data.data.nextResendTime) {
          const nextTime = new Date(data.data.nextResendTime).getTime();
          const now = new Date().getTime();
          const secondsLeft = Math.max(0, Math.floor((nextTime - now) / 1000));
          setCountdown(secondsLeft);
        }
      }
    } catch (error) {
      console.error('Error fetching verification status:', error);
    }
  };

  const handleResendVerification = async () => {
    try {
      setIsResending(true);

      const response = await fetch(`${import.meta.env.VITE_API_URL}/users/email-verification/resend`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: t('auth.verification.codeSent', 'Verification Email Sent'),
          description: t('auth.verification.codeSentDesc', 'Please check your email for the verification link'),
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Refresh status
        await fetchVerificationStatus();
      } else {
        throw new Error(data.message || 'Failed to resend verification email');
      }
    } catch (error: any) {
      console.error('Resend verification error:', error);
      
      let errorMessage = t('auth.verification.resendError', 'Failed to resend verification email');
      
      if (error.message.includes('Maximum verification attempts')) {
        errorMessage = t('auth.verification.maxAttemptsReached', 'Maximum verification attempts reached. Please try again tomorrow.');
      } else if (error.message.includes('Please wait')) {
        errorMessage = error.message;
      }

      toast({
        title: t('common.error', 'Error'),
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsResending(false);
    }
  };

  const handleClose = () => {
    setIsVisible(false);
    onClose?.();
  };

  const formatCountdown = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const canResend = verificationStatus?.canResend && countdown === 0;
  const attemptsRemaining = verificationStatus?.attemptsRemaining || 0;

  // Don't render anything if conditions are not met
  if (!shouldShow) {
    return null;
  }

  if (variant === 'card') {
    return (
      <Collapse in={isVisible}>
        <Box
          bg="#1E2329"
          borderColor="#F0B90B"
          borderWidth="1px"
          borderRadius="md"
          p={4}
          mb={4}
        >
          <VStack spacing={4} align="stretch">
            <HStack justify="space-between">
              <HStack spacing={3}>
                <Icon as={FaEnvelope} color="#F0B90B" boxSize={5} />
                <VStack align="start" spacing={1}>
                  <Text color="#EAECEF" fontWeight="medium" fontSize="sm">
                    {t('auth.verification.emailNotVerified', 'Email Not Verified')}
                  </Text>
                  <Text color="#848E9C" fontSize="xs">
                    {t('auth.verification.verifyToUnlock', 'Verify your email to unlock all features')}
                  </Text>
                </VStack>
              </HStack>
              {showCloseButton && (
                <CloseButton
                  size="sm"
                  color="#848E9C"
                  onClick={handleClose}
                />
              )}
            </HStack>

            <HStack spacing={3}>
              <Button
                size="sm"
                colorScheme="yellow"
                variant="outline"
                onClick={handleResendVerification}
                isLoading={isResending}
                isDisabled={!canResend}
                loadingText={t('auth.verification.resending', 'Sending...')}
              >
                {t('auth.verification.resendCode', 'Resend Email')}
              </Button>

              {countdown > 0 && (
                <HStack spacing={2}>
                  <Icon as={FaClock} color="#848E9C" boxSize={3} />
                  <Text color="#848E9C" fontSize="xs">
                    {formatCountdown(countdown)}
                  </Text>
                </HStack>
              )}

              {attemptsRemaining > 0 && attemptsRemaining < 5 && (
                <Text color="#848E9C" fontSize="xs">
                  {t('auth.verification.attemptsRemaining', `${attemptsRemaining} attempts remaining`)}
                </Text>
              )}
            </HStack>
          </VStack>
        </Box>
      </Collapse>
    );
  }

  return (
    <Collapse in={isVisible}>
      <Alert
        status="warning"
        variant="subtle"
        bg="#2D1B0E"
        borderColor="#F0B90B"
        borderWidth="1px"
        borderRadius="md"
        mb={4}
      >
        <AlertIcon color="#F0B90B" />
        <VStack align="start" spacing={2} flex="1">
          <HStack justify="space-between" w="100%">
            <VStack align="start" spacing={1}>
              <AlertTitle color="#EAECEF" fontSize="sm">
                {t('auth.verification.emailNotVerified', 'Email Not Verified')}
              </AlertTitle>
              <AlertDescription color="#848E9C" fontSize="xs">
                {t('auth.verification.verifyEmailMessage', 'Please verify your email address to access all platform features and ensure account security.')}
              </AlertDescription>
            </VStack>
            {showCloseButton && (
              <CloseButton
                size="sm"
                color="#848E9C"
                onClick={handleClose}
              />
            )}
          </HStack>

          <HStack spacing={3} mt={2}>
            <Button
              size="sm"
              colorScheme="yellow"
              variant="outline"
              onClick={handleResendVerification}
              isLoading={isResending}
              isDisabled={!canResend}
              loadingText={t('auth.verification.resending', 'Sending...')}
            >
              {t('auth.verification.resendCode', 'Resend Verification Email')}
            </Button>

            {countdown > 0 && (
              <HStack spacing={2}>
                <Icon as={FaClock} color="#848E9C" boxSize={3} />
                <Text color="#848E9C" fontSize="xs">
                  {t('auth.verification.waitTime', `Wait ${formatCountdown(countdown)}`)}
                </Text>
              </HStack>
            )}

            {attemptsRemaining > 0 && attemptsRemaining < 5 && (
              <Text color="#F0B90B" fontSize="xs" fontWeight="medium">
                {t('auth.verification.attemptsRemaining', `${attemptsRemaining} attempts remaining`)}
              </Text>
            )}
          </HStack>
        </VStack>
      </Alert>
    </Collapse>
  );
};

export default EmailVerificationBanner;
