import mongoose, { Document, Schema } from 'mongoose';

export interface IWalletDetail extends Document {
  _id: mongoose.Types.ObjectId;
  walletId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  symbol: string;
  address: string;
  network: string;
  isDefault: boolean;
  isActive: boolean;
  privateKey?: string;
  addressIndex: number;
  qrCodeUrl?: string;
  label?: string;
  lastUpdated: Date;
  withdrawalEnabled: boolean;
  transactionCount: number;
  lastUsed?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  generateQRCode(): string;
  markAsUsed(): Promise<IWalletDetail>;
  deactivate(): Promise<IWalletDetail>;
  updateLastUsed(): Promise<IWalletDetail>;
}

export interface IWalletDetailModel extends mongoose.Model<IWalletDetail> {
  // Static methods
  findByAddress(address: string): Promise<IWalletDetail | null>;
  findByUser(userId: string, symbol?: string): Promise<IWalletDetail[]>;
  findActiveByUser(userId: string, symbol?: string): Promise<IWalletDetail[]>;
  findDefaultAddress(userId: string, symbol: string): Promise<IWalletDetail | null>;
  createAddress(data: {
    walletId: mongoose.Types.ObjectId;
    userId: mongoose.Types.ObjectId;
    symbol: string;
    address: string;
    network?: string;
    isDefault?: boolean;
  }): Promise<IWalletDetail>;
}

const walletDetailSchema = new Schema<IWalletDetail>({
  walletId: {
    type: Schema.Types.ObjectId,
    ref: 'Wallet',
    required: true,
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  symbol: {
    type: String,
    required: true,
    uppercase: true,
    index: true
  },
  address: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  network: {
    type: String,
    required: true,
    default: 'mainnet',
    enum: ['mainnet', 'testnet', 'regtest']
  },
  isDefault: {
    type: Boolean,
    default: false,
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  privateKey: {
    type: String,
    select: false // Don't include by default for security
  },
  addressIndex: {
    type: Number,
    default: 0
  },
  qrCodeUrl: {
    type: String
  },
  label: {
    type: String,
    maxlength: 100
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  withdrawalEnabled: {
    type: Boolean,
    default: true
  },
  transactionCount: {
    type: Number,
    default: 0
  },
  lastUsed: {
    type: Date
  }
}, {
  timestamps: true,
  collection: 'walletdetails'
});

// Compound indexes for efficient queries
walletDetailSchema.index({ userId: 1, symbol: 1 });
walletDetailSchema.index({ userId: 1, symbol: 1, isDefault: 1 });
walletDetailSchema.index({ userId: 1, symbol: 1, isActive: 1 });
walletDetailSchema.index({ walletId: 1, symbol: 1 });
walletDetailSchema.index({ isActive: 1, symbol: 1 });
walletDetailSchema.index({ network: 1, symbol: 1 });

// Ensure only one default address per user per symbol
walletDetailSchema.index(
  { userId: 1, symbol: 1, isDefault: 1 },
  { 
    unique: true,
    partialFilterExpression: { isDefault: true }
  }
);

// Instance methods
walletDetailSchema.methods.generateQRCode = function(): string {
  return `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(this.address)}`;
};

walletDetailSchema.methods.markAsUsed = async function(): Promise<IWalletDetail> {
  this.lastUsed = new Date();
  this.transactionCount += 1;
  this.lastUpdated = new Date();
  return await this.save();
};

walletDetailSchema.methods.deactivate = async function(): Promise<IWalletDetail> {
  this.isActive = false;
  this.lastUpdated = new Date();
  return await this.save();
};

walletDetailSchema.methods.updateLastUsed = async function(): Promise<IWalletDetail> {
  this.lastUsed = new Date();
  this.lastUpdated = new Date();
  return await this.save();
};

// Static methods
walletDetailSchema.statics.findByAddress = function(address: string) {
  return this.findOne({ address, isActive: true });
};

walletDetailSchema.statics.findByUser = function(userId: string, symbol?: string) {
  const query: any = { userId, isActive: true };
  if (symbol) {
    query.symbol = symbol.toUpperCase();
  }
  return this.find(query).sort({ isDefault: -1, createdAt: -1 });
};

walletDetailSchema.statics.findActiveByUser = function(userId: string, symbol?: string) {
  const query: any = { userId, isActive: true };
  if (symbol) {
    query.symbol = symbol.toUpperCase();
  }
  return this.find(query).sort({ isDefault: -1, createdAt: -1 });
};

walletDetailSchema.statics.findDefaultAddress = function(userId: string, symbol: string) {
  return this.findOne({
    userId,
    symbol: symbol.toUpperCase(),
    isDefault: true,
    isActive: true
  });
};

walletDetailSchema.statics.createAddress = async function(data: {
  walletId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  symbol: string;
  address: string;
  network?: string;
  isDefault?: boolean;
}) {
  const { walletId, userId, symbol, address, network = 'mainnet', isDefault = false } = data;

  // If this is set as default, unset other default addresses for this user/symbol
  if (isDefault) {
    await this.updateMany(
      { userId, symbol: symbol.toUpperCase(), isDefault: true },
      { isDefault: false }
    );
  }

  // If no addresses exist for this user/symbol, make this the default
  const existingCount = await this.countDocuments({
    userId,
    symbol: symbol.toUpperCase(),
    isActive: true
  });

  const addressData = {
    walletId,
    userId,
    symbol: symbol.toUpperCase(),
    address,
    network,
    isDefault: isDefault || existingCount === 0,
    addressIndex: existingCount,
    lastUpdated: new Date()
  };

  return await this.create(addressData);
};

// Pre-save middleware
walletDetailSchema.pre('save', function(next) {
  this.lastUpdated = new Date();
  next();
});

const WalletDetail = mongoose.models.WalletDetail || mongoose.model<IWalletDetail, IWalletDetailModel>('WalletDetail', walletDetailSchema);

export default WalletDetail;
