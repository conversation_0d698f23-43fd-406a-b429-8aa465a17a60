import {
  FaBitcoin,
  FaEthereum,
  FaCoins,
  FaWallet,
  FaDollarSign
} from 'react-icons/fa';
import {
  SiBinance,
  SiCardano,
  SiPolkadot,
  SiChainlink,
  SiSolana,
  SiLitecoin,
  SiDogecoin
} from 'react-icons/si';

// Crypto icon mapping
export const getCryptoIcon = (currency: string) => {
  switch (currency.toUpperCase()) {
    case 'BTC':
      return FaBitcoin;
    case 'ETH':
      return FaEthereum;
    case 'USDT':
      return FaDollarSign;
    case 'BNB':
      return SiBinance;
    case 'ADA':
      return SiCardano;
    case 'DOT':
      return SiPolkadot;
    case 'LINK':
      return SiChainlink;
    case 'UNI':
      return FaCoins;
    case 'MATIC':
      return FaCoins; // Polygon
    case 'AVAX':
      return FaCoins; // Avalanche
    case 'SOL':
      return SiSolana;
    case 'ATOM':
      return FaCoins; // Cosmos
    case 'XRP':
      return FaCoins; // Ripple
    case 'LTC':
      return SiLitecoin;
    case 'BCH':
      return FaBitcoin; // Bitcoin Cash
    case 'DOGE':
      return SiDogecoin;
    case 'TRX':
      return FaCoins; // TRON
    case 'FTM':
      return FaCoins; // Fantom
    case 'NEAR':
      return FaCoins; // NEAR
    case 'ALGO':
      return FaCoins; // Algorand
    case 'XTZ':
      return FaCoins; // Tezos
    case 'FLOW':
      return FaCoins; // Flow
    case 'ICP':
      return FaCoins; // Internet Computer
    default:
      return FaWallet;
  }
};
