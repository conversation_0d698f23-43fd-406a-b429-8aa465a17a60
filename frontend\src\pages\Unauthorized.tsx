import { Box, Heading, Text, But<PERSON>, VStack, Container, Center, Icon } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { FaLock } from 'react-icons/fa';

const Unauthorized = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <Box bg="#0B0E11" minH="100vh" py={10} px={6} color="white">
      <Center minH="80vh">
        <Container maxW="container.md">
          <Box
            bg="gray.800"
            p={8}
            borderRadius="lg"
            boxShadow="lg"
            textAlign="center"
            borderWidth="1px"
            borderColor="gray.700"
          >
            <VStack spacing={8}>
              <Box
                bg="rgba(240, 185, 11, 0.1)"
                color="#F0B90B"
                p={4}
                borderRadius="full"
                boxSize="100px"
                display="flex"
                alignItems="center"
                justifyContent="center"
                border="2px solid"
                borderColor="#F0B90B"
              >
                <Icon as={FaLock} boxSize="50px" />
              </Box>

              <Heading as="h1" size="xl" color="white">
                {t('unauthorized.title', 'Access Denied')}
              </Heading>

              <Text color="gray.300" fontSize="lg" maxW="md">
                {t(
                  'unauthorized.message',
                  'You do not have permission to access this page. This area is restricted to administrators only.'
                )}
              </Text>

              <Button
                bg="#F0B90B"
                color="black"
                size="lg"
                onClick={() => navigate('/')}
                width={{ base: 'full', md: 'auto' }}
                _hover={{
                  bg: '#D4A40C'
                }}
                fontWeight="600"
                px={8}
              >
                {t('unauthorized.backToHome', 'Back to Home')}
              </Button>
            </VStack>
          </Box>
        </Container>
      </Center>
    </Box>
  );
};

export default Unauthorized;
