import React, { useState, useContext, useEffect } from 'react';
import {
  Box,
  SimpleGrid,
  Text,
  Icon,
  HStack,
  VStack,
  Badge,
  Button,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  Divider,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import {
  FaBitcoin,
  FaEthereum,
  FaArrowUp,
  FaArrowDown,
  FaCoins,
  FaChartLine,
  FaWallet,
  FaEye,
  FaEyeSlash,
  FaHistory,
  FaSync,
} from 'react-icons/fa';
import { SiTether, SiDogecoin } from 'react-icons/si';
import { WalletContext } from '../context/WalletContext';

interface WalletDetailProps {
  onDeposit?: (symbol: string) => void;
  onWithdraw?: (symbol: string) => void;
}

const WalletDetail: React.FC<WalletDetailProps> = ({
  onDeposit,
  onWithdraw,
}) => {
  const [showBalances, setShowBalances] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const toast = useToast();
  
  // Get wallet data from context
  const { wallet, loading, error, fetchWallet } = useContext(WalletContext);

  // Crypto info mapping with real-time price simulation
  const cryptoInfo: { [key: string]: { name: string; icon: any; color: string; decimals: number; price: number } } = {
    BTC: { name: 'Bitcoin', icon: FaBitcoin, color: '#F7931A', decimals: 8, price: 67500 },
    USDT: { name: 'Tether', icon: SiTether, color: '#26A17B', decimals: 2, price: 1 },
    ETH: { name: 'Ethereum', icon: FaEthereum, color: '#627EEA', decimals: 6, price: 3200 },
    BNB: { name: 'Binance Coin', icon: FaCoins, color: '#F0B90B', decimals: 4, price: 300 },
    DOGE: { name: 'Dogecoin', icon: SiDogecoin, color: '#C2A633', decimals: 4, price: 0.08 },
    TRX: { name: 'Tron', icon: FaCoins, color: '#FF060A', decimals: 4, price: 0.12 },
  };

  // Calculate portfolio totals from real wallet data
  const totalBalance = wallet?.assets?.reduce((sum, asset) => {
    const info = cryptoInfo[asset.symbol];
    return sum + (asset.balance * (info?.price || 1));
  }, 0) || 0;

  const totalCommission = wallet?.assets?.reduce((sum, asset) => {
    const info = cryptoInfo[asset.symbol];
    return sum + (asset.commissionBalance * (info?.price || 1));
  }, 0) || 0;

  const totalInterest = wallet?.assets?.reduce((sum, asset) => {
    const info = cryptoInfo[asset.symbol];
    return sum + (asset.interestBalance * (info?.price || 1));
  }, 0) || 0;

  const totalValue = totalBalance + totalCommission + totalInterest;

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";
  const successColor = "#02C076";

  // Formatting functions
  const formatCurrency = (amount: number, symbol: string = 'USD') => {
    if (!showBalances) return '••••••';
    if (symbol === 'USD') {
      return `$${amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
    const info = cryptoInfo[symbol];
    return `${amount.toFixed(info?.decimals || 4)} ${symbol}`;
  };

  const formatUSDValue = (amount: number, symbol: string) => {
    if (!showBalances) return '$••••••';
    const info = cryptoInfo[symbol];
    const usdValue = amount * (info?.price || 1);
    return `≈ $${usdValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await fetchWallet();
      toast({
        title: 'Wallet Refreshed',
        description: 'Wallet data has been updated successfully',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Refresh Failed',
        description: 'Failed to refresh wallet data',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Auto-refresh wallet data on component mount
  useEffect(() => {
    if (!wallet && !loading) {
      fetchWallet();
    }
  }, [wallet, loading, fetchWallet]);

  // Loading state
  if (loading) {
    return (
      <Box w="100%" textAlign="center" py={10}>
        <Spinner size="xl" color={primaryColor} thickness="4px" />
        <Text color={textColor} mt={4} fontSize="lg">
          Loading wallet data...
        </Text>
      </Box>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert status="error" bg={cardBgColor} borderColor={borderColor} borderWidth="1px" borderRadius="xl">
        <AlertIcon color="red.400" />
        <Box>
          <Text color={textColor} fontWeight="bold">
            Failed to load wallet data
          </Text>
          <Text color={secondaryTextColor} fontSize="sm">
            {error}
          </Text>
          <Button
            mt={3}
            size="sm"
            colorScheme="red"
            variant="outline"
            onClick={handleRefresh}
            isLoading={isRefreshing}
          >
            Retry
          </Button>
        </Box>
      </Alert>
    );
  }

  // No wallet data
  if (!wallet || !wallet.assets || wallet.assets.length === 0) {
    return (
      <Box w="100%" textAlign="center" py={10}>
        <Icon as={FaWallet} boxSize={16} color={secondaryTextColor} mb={4} />
        <Text color={textColor} fontSize="xl" fontWeight="bold" mb={2}>
          No Wallet Data
        </Text>
        <Text color={secondaryTextColor} mb={4}>
          Your wallet data is not available. Please try refreshing.
        </Text>
        <Button
          leftIcon={<Icon as={FaSync} />}
          colorScheme="yellow"
          onClick={handleRefresh}
          isLoading={isRefreshing}
        >
          Refresh Wallet
        </Button>
      </Box>
    );
  }

  return (
    <Box w="100%">
      {/* Portfolio Overview Header */}
      <Box
        bg={bgColor}
        borderRadius="xl"
        borderWidth="1px"
        borderColor={borderColor}
        p={6}
        mb={6}
        boxShadow="0 4px 20px rgba(0, 0, 0, 0.1)"
      >
        <Flex justify="space-between" align="center" mb={6}>
          <VStack align="start" spacing={1}>
            <HStack spacing={3}>
              <Icon as={FaWallet} color={primaryColor} boxSize={6} />
              <Text
                color={textColor}
                fontSize="2xl"
                fontWeight="900"
                letterSpacing="-0.02em"
              >
                Wallet Overview
              </Text>
            </HStack>
            <Text color={secondaryTextColor} fontSize="md" fontWeight="500">
              Live data from backend • {wallet.assets.length} assets
            </Text>
          </VStack>

          <HStack spacing={3}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowBalances(!showBalances)}
              color={secondaryTextColor}
              _hover={{ color: textColor }}
              leftIcon={<Icon as={showBalances ? FaEye : FaEyeSlash} />}
            >
              {showBalances ? 'Hide' : 'Show'} Balances
            </Button>
            <Button
              leftIcon={<Icon as={FaSync} />}
              bg={`${primaryColor}15`}
              color={primaryColor}
              border="1px solid"
              borderColor={`${primaryColor}30`}
              _hover={{
                bg: `${primaryColor}25`,
                borderColor: `${primaryColor}50`,
              }}
              size="sm"
              fontWeight="600"
              borderRadius="lg"
              onClick={handleRefresh}
              isLoading={isRefreshing}
            >
              Refresh
            </Button>
            <Button
              leftIcon={<Icon as={FaHistory} />}
              bg={`${primaryColor}15`}
              color={primaryColor}
              border="1px solid"
              borderColor={`${primaryColor}30`}
              _hover={{
                bg: `${primaryColor}25`,
                borderColor: `${primaryColor}50`,
              }}
              size="sm"
              fontWeight="600"
              borderRadius="lg"
              onClick={() => {
                toast({
                  title: 'Transaction History',
                  description: 'Viewing wallet transaction history',
                  status: 'info',
                  duration: 3000,
                  isClosable: true,
                });
              }}
            >
              View History
            </Button>
          </HStack>
        </Flex>

        {/* Portfolio Stats */}
        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4} mb={6}>
          <Stat
            bg={cardBgColor}
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
          >
            <StatLabel color={secondaryTextColor} fontSize="xs" fontWeight="700" textTransform="uppercase">
              Total Value
            </StatLabel>
            <StatNumber color={textColor} fontSize="lg" fontWeight="800">
              {formatCurrency(totalValue)}
            </StatNumber>
            <StatHelpText color={successColor} fontSize="xs">
              <StatArrow type="increase" />
              Portfolio
            </StatHelpText>
          </Stat>

          <Stat
            bg={cardBgColor}
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
          >
            <StatLabel color={secondaryTextColor} fontSize="xs" fontWeight="700" textTransform="uppercase">
              Main Balance
            </StatLabel>
            <StatNumber color={textColor} fontSize="lg" fontWeight="800">
              {formatCurrency(totalBalance)}
            </StatNumber>
            <StatHelpText color="blue.400" fontSize="xs">
              Active Funds
            </StatHelpText>
          </Stat>

          <Stat
            bg={cardBgColor}
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
          >
            <StatLabel color={secondaryTextColor} fontSize="xs" fontWeight="700" textTransform="uppercase">
              Commission
            </StatLabel>
            <StatNumber color={successColor} fontSize="lg" fontWeight="800">
              {formatCurrency(totalCommission)}
            </StatNumber>
            <StatHelpText color={successColor} fontSize="xs">
              Referral Earnings
            </StatHelpText>
          </Stat>

          <Stat
            bg={cardBgColor}
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={borderColor}
          >
            <StatLabel color={secondaryTextColor} fontSize="xs" fontWeight="700" textTransform="uppercase">
              Interest
            </StatLabel>
            <StatNumber color="#F0B90B" fontSize="lg" fontWeight="800">
              {formatCurrency(totalInterest)}
            </StatNumber>
            <StatHelpText color="#F0B90B" fontSize="xs">
              Daily Earnings
            </StatHelpText>
          </Stat>
        </SimpleGrid>

        {/* Earnings Progress */}
        <Box
          bg={cardBgColor}
          p={4}
          borderRadius="lg"
          border="1px solid"
          borderColor={borderColor}
        >
          <Flex justify="space-between" align="center" mb={3}>
            <Text color={textColor} fontSize="sm" fontWeight="600">
              Total Earnings Progress
            </Text>
            <Text color={primaryColor} fontSize="sm" fontWeight="700">
              {showBalances ? `${totalValue > 0 ? ((totalCommission + totalInterest) / totalValue * 100).toFixed(1) : '0.0'}%` : '••%'}
            </Text>
          </Flex>
          <Progress
            value={totalValue > 0 ? (totalCommission + totalInterest) / totalValue * 100 : 0}
            colorScheme="yellow"
            size="sm"
            borderRadius="full"
            bg="#2B3139"
          />
          <Text color={secondaryTextColor} fontSize="xs" mt={2}>
            Commission: {formatCurrency(totalCommission)} • Interest: {formatCurrency(totalInterest)}
          </Text>
        </Box>
      </Box>

      {/* Asset Detail Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
        {wallet.assets.map((asset) => {
          const info = cryptoInfo[asset.symbol] || {
            name: asset.symbol,
            icon: FaCoins,
            color: '#F0B90B',
            decimals: 4,
            price: 1
          };

          const totalEarnings = asset.commissionBalance + asset.interestBalance;
          const hasBalance = asset.balance > 0;
          const hasEarnings = totalEarnings > 0;
          const primaryMode = asset.mode || (asset.commissionBalance > asset.interestBalance ? 'commission' : 'interest');

          return (
            <Box
              key={asset._id || asset.symbol}
              bg={bgColor}
              borderRadius="xl"
              borderWidth="1px"
              borderColor={borderColor}
              overflow="hidden"
              position="relative"
              transition="all 0.3s ease"
              boxShadow="0 4px 20px rgba(0, 0, 0, 0.1)"
              _hover={{
                transform: 'translateY(-4px)',
                boxShadow: `0 8px 30px rgba(0, 0, 0, 0.15), 0 0 0 1px ${info.color}40`,
                borderColor: `${info.color}60`,
              }}
            >
              {/* Top gradient border */}
              <Box
                h="3px"
                bg={`linear-gradient(90deg, ${info.color} 0%, ${info.color}80 50%, ${info.color}40 100%)`}
                position="absolute"
                top={0}
                left={0}
                right={0}
              />

              {/* Card Content */}
              <VStack spacing={4} p={5} align="stretch">
                {/* Header */}
                <Flex align="center" justify="space-between">
                  <HStack spacing={3}>
                    <Flex
                      bg={`${info.color}15`}
                      p={3}
                      borderRadius="xl"
                      align="center"
                      justify="center"
                      border="2px solid"
                      borderColor={`${info.color}25`}
                    >
                      <Icon as={info.icon} color={info.color} boxSize={6} />
                    </Flex>
                    <VStack align="start" spacing={1}>
                      <Text color={textColor} fontWeight="800" fontSize="lg">
                        {asset.symbol}
                      </Text>
                      <Text color={secondaryTextColor} fontSize="sm">
                        {info.name}
                      </Text>
                    </VStack>
                  </HStack>

                  <VStack align="end" spacing={1}>
                    <Badge
                      bg={`${info.color}20`}
                      color={info.color}
                      variant="solid"
                      fontSize="xs"
                      fontWeight="600"
                      px={2}
                      py={1}
                      borderRadius="full"
                    >
                      {primaryMode === 'commission' ? 'Commission' : 'Interest'}
                    </Badge>
                    {asset.network && (
                      <Badge
                        colorScheme="blue"
                        variant="outline"
                        fontSize="xs"
                        px={2}
                        py={1}
                        borderRadius="full"
                      >
                        {asset.network}
                      </Badge>
                    )}
                  </VStack>
                </Flex>

                {/* Balance */}
                <Box
                  bg={cardBgColor}
                  p={4}
                  borderRadius="lg"
                  border="1px solid"
                  borderColor={borderColor}
                  textAlign="center"
                >
                  <Text color={secondaryTextColor} fontSize="xs" fontWeight="700" textTransform="uppercase" mb={2}>
                    Balance
                  </Text>
                  <Text
                    color={hasBalance ? textColor : secondaryTextColor}
                    fontSize="xl"
                    fontWeight="900"
                    mb={1}
                  >
                    {formatCurrency(asset.balance, asset.symbol)}
                  </Text>
                  <Text color={hasBalance ? successColor : secondaryTextColor} fontSize="sm">
                    {hasBalance ? formatUSDValue(asset.balance, asset.symbol) : 'No main balance'}
                  </Text>
                </Box>

                {/* Earnings Row */}
                <SimpleGrid columns={2} spacing={3}>
                  <Box
                    bg={cardBgColor}
                    p={3}
                    borderRadius="lg"
                    border="1px solid"
                    borderColor={borderColor}
                  >
                    <VStack spacing={2} align="start">
                      <HStack spacing={2}>
                        <Icon as={FaCoins} color="#F0B90B" boxSize={3} />
                        <Text color={secondaryTextColor} fontSize="xs" fontWeight="700" textTransform="uppercase">
                          Interest
                        </Text>
                      </HStack>
                      <Text
                        color={asset.interestBalance > 0 ? "#F0B90B" : secondaryTextColor}
                        fontSize="md"
                        fontWeight="800"
                      >
                        {formatCurrency(asset.interestBalance, asset.symbol)}
                      </Text>
                      {asset.interestBalance > 0 && (
                        <Text color="#F0B90B" fontSize="xs">
                          {formatUSDValue(asset.interestBalance, asset.symbol)}
                        </Text>
                      )}
                    </VStack>
                  </Box>

                  <Box
                    bg={cardBgColor}
                    p={3}
                    borderRadius="lg"
                    border="1px solid"
                    borderColor={borderColor}
                  >
                    <VStack spacing={2} align="start">
                      <HStack spacing={2}>
                        <Icon as={FaChartLine} color={successColor} boxSize={3} />
                        <Text color={secondaryTextColor} fontSize="xs" fontWeight="700" textTransform="uppercase">
                          Commission
                        </Text>
                      </HStack>
                      <Text
                        color={asset.commissionBalance > 0 ? successColor : secondaryTextColor}
                        fontSize="md"
                        fontWeight="800"
                      >
                        {formatCurrency(asset.commissionBalance, asset.symbol)}
                      </Text>
                      {asset.commissionBalance > 0 && (
                        <Text color={successColor} fontSize="xs">
                          {formatUSDValue(asset.commissionBalance, asset.symbol)}
                        </Text>
                      )}
                    </VStack>
                  </Box>
                </SimpleGrid>

                <Divider borderColor={borderColor} />

                {/* Action Buttons */}
                <HStack spacing={3}>
                  <Button
                    leftIcon={<Icon as={FaArrowDown} />}
                    bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
                    color="#0B0E11"
                    _hover={{
                      bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
                      transform: "translateY(-1px)"
                    }}
                    size="md"
                    flex={1}
                    fontWeight="700"
                    borderRadius="lg"
                    onClick={() => onDeposit?.(asset.symbol)}
                  >
                    Deposit
                  </Button>

                  <Button
                    leftIcon={<Icon as={FaArrowUp} />}
                    bg="rgba(2, 192, 118, 0.1)"
                    color={successColor}
                    border="2px solid"
                    borderColor={successColor}
                    _hover={{
                      bg: "rgba(2, 192, 118, 0.2)",
                      transform: "translateY(-1px)"
                    }}
                    size="md"
                    flex={1}
                    fontWeight="700"
                    borderRadius="lg"
                    onClick={() => onWithdraw?.(asset.symbol)}
                  >
                    Withdraw
                  </Button>
                </HStack>
              </VStack>
            </Box>
          );
        })}
      </SimpleGrid>
    </Box>
  );
};

export default WalletDetail;
