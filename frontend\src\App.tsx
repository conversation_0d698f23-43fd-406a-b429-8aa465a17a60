import { extendTheme } from '@chakra-ui/react'
import { useLocation } from 'react-router-dom'
import { memo } from 'react'
import './App.css'
import './styles/mobile-responsive.css'

// Components
import Navbar from './components/Navbar'
import Footer from './components/Footer'
import { ToastContainer } from './components/common/Toast'
import OptimizedRouteController from './routes/OptimizedRouteController'
import ImpersonationBanner from './components/ImpersonationBanner'

import RoutePrefetcher from './components/routing/RoutePrefetcher'
import RouteTransition from './components/routing/RouteTransition'
import TitleManager from './components/TitleManager'

// Context
import { ToastProvider, useToast } from './context/ToastContext'
import { RouteProvider } from './context/RouteContext'
import { ThemeProvider } from './context/ThemeContext'
import { TransactionProvider } from './context/TransactionContext'
import { CryptoPriceProvider } from './components/CryptoPriceProvider'

// Theme - Binance inspired with improvements
export const theme = extendTheme({
  // Mobile-first responsive breakpoints
  breakpoints: {
    base: '0px',    // 0px and up (mobile)
    sm: '480px',    // 480px and up (large mobile)
    md: '768px',    // 768px and up (tablet)
    lg: '1024px',   // 1024px and up (desktop)
    xl: '1440px',   // 1440px and up (large desktop)
    '2xl': '1920px' // 1920px and up (extra large)
  },
  colors: {
    brand: {
      50: '#fff9e6',
      100: '#ffeeb3',
      200: '#ffe480',
      300: '#ffda4d',
      400: '#ffd11a',
      500: '#F0B90B', // Binance Yellow
      600: '#cc9900',
      700: '#997300',
      800: '#664d00',
      900: '#332600',
    },
    gray: {
      50: '#F9FAFB',
      100: '#EAECEF',
      200: '#B7BDC6',
      300: '#848E9C',
      400: '#707A8A',
      500: '#5E6673',
      600: '#474D57',
      700: '#2B3139',
      800: '#1E2329',
      900: '#0B0E11',
    },
    success: {
      500: '#0ECB81', // Green for positive values
    },
    error: {
      500: '#F6465D', // Red for negative values
    },
    warning: {
      500: '#F0B90B', // Yellow for warnings
    },
    info: {
      500: '#3375BB', // Blue for info
    },
  },
  fonts: {
    heading: '"Inter", "Segoe UI", sans-serif',
    body: '"Source Sans Pro", "Open Sans", "Segoe UI", sans-serif',
  },
  config: {
    initialColorMode: 'dark',
    useSystemColorMode: false,
  },
  styles: {
    global: {
      body: {
        bg: 'gray.900', // Binance dark background
        color: 'gray.100', // Binance light text
        fontSize: { base: '14px', md: '15px' }, // Responsive font size
        lineHeight: '1.5', // Better readability
        letterSpacing: '0.01em', // Slightly increased letter spacing
        textRendering: 'optimizeLegibility', // Font optimization
        WebkitFontSmoothing: 'antialiased', // Font smoothing
        MozOsxFontSmoothing: 'grayscale', // Font smoothing
        transition: 'background-color 0.2s ease-in-out', // Smooth transitions
        WebkitTapHighlightColor: 'rgba(0,0,0,0)', // Remove tap highlight on mobile
        touchAction: 'manipulation', // Optimize for touch
        overflowX: 'hidden', // Prevent horizontal scrolling on mobile
      },
      h1: {
        fontSize: { base: '1.8rem !important', md: '2.2rem !important' }, // Responsive heading
        fontWeight: '700 !important', // Bold heading
        letterSpacing: '-0.01em',
        lineHeight: '1.3 !important',
        marginBottom: '0.4em !important',
        color: 'gray.100',
      },
      h2: {
        fontSize: { base: '1.5rem !important', md: '1.8rem !important' }, // Responsive heading
        fontWeight: '700 !important',
        letterSpacing: '-0.005em',
        lineHeight: '1.3 !important',
        marginBottom: '0.4em !important',
        color: 'gray.100',
      },
      h3: {
        fontSize: { base: '1.3rem !important', md: '1.5rem !important' }, // Responsive heading
        fontWeight: '600 !important',
        lineHeight: '1.4 !important',
        marginBottom: '0.4em !important',
        color: 'gray.100',
      },
      h4: {
        fontSize: { base: '1.1rem !important', md: '1.2rem !important' }, // Responsive heading
        fontWeight: '600 !important',
        lineHeight: '1.4 !important',
        marginBottom: '0.4em !important',
        color: 'gray.100',
      },
      p: {
        fontSize: { base: '0.9rem', md: '0.95rem' }, // Responsive paragraph
        marginBottom: '0.8rem',
        lineHeight: '1.6',
        color: 'gray.200',
      },
      button: {
        fontSize: { base: '0.9rem !important', md: '0.95rem !important' }, // Responsive button
        fontWeight: '600 !important',
        padding: { base: '0.5rem 1rem !important', md: '0.6rem 1.2rem !important' },
        transition: 'all 0.3s ease !important',
        minHeight: { base: '40px', md: '44px' }, // Ensure touch-friendly height
        minWidth: { base: '40px', md: 'auto' }, // Ensure touch-friendly width
      },
      '.chakra-text': {
        fontSize: { base: '0.9rem', md: '0.95rem' }, // Responsive text
        lineHeight: '1.6',
        color: 'gray.200',
      },
      '.chakra-container': {
        maxWidth: '1400px !important',
        width: { base: '100% !important', md: '95% !important' },
        padding: { base: '0 1rem !important', md: '0 1.5rem !important' },
      },
      a: {
        color: 'brand.500',
        transition: 'color 0.2s ease-in-out',
        _hover: {
          textDecoration: 'none',
          color: 'brand.400',
        }
      },
      // Özel seçiciler
      '.card': {
        bg: 'gray.800',
        borderRadius: 'md',
        borderWidth: '1px',
        borderColor: 'gray.700',
        transition: 'all 0.3s ease',
        _hover: {
          transform: 'translateY(-4px)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          borderColor: 'brand.500',
        }
      },
      '.highlight-text': {
        color: 'brand.500',
        fontWeight: '600',
      },
    },
  },
  components: {
    Heading: {
      baseStyle: {
        fontWeight: '700',
        lineHeight: '1.3',
        letterSpacing: '-0.01em',
        color: 'gray.100',
      },
      variants: {
        section: {
          color: 'brand.500',
          position: 'relative',
          _after: {
            content: '""',
            position: 'absolute',
            bottom: '-10px',
            left: '0',
            width: { base: '40px', md: '60px' },
            height: { base: '3px', md: '4px' },
            bg: 'brand.500',
            borderRadius: 'full',
          }
        },
      }
    },
    Text: {
      baseStyle: {
        fontSize: { base: '0.9rem', md: '0.95rem' },
        lineHeight: '1.6',
        color: 'gray.200',
      },
      variants: {
        muted: {
          color: 'gray.300',
          fontSize: { base: '0.85rem', md: '0.9rem' },
        },
        highlight: {
          color: 'brand.500',
          fontWeight: '600',
        },
      }
    },
    Button: {
      baseStyle: {
        fontWeight: '600',
        fontSize: { base: '0.9rem', md: '0.95rem' },
        borderRadius: '0.4rem',
        padding: { base: '0.5rem 1rem', md: '0.6rem 1.2rem' },
        transition: 'all 0.3s ease',
        minHeight: { base: '40px', md: '44px' }, // Touch-friendly height
        _hover: {
          transform: { base: 'none', md: 'translateY(-2px)' }, // Disable hover effect on mobile
          boxShadow: { base: 'none', md: '0 4px 8px rgba(240, 185, 11, 0.3)' },
        },
        _active: {
          transform: 'translateY(1px)', // Provide feedback on touch
        }
      },
      variants: {
        primary: {
          bg: 'brand.500',
          color: 'gray.900',
          _hover: {
            bg: 'brand.400',
            transform: { base: 'none', md: 'translateY(-2px)' },
            boxShadow: { base: 'none', md: '0 4px 12px rgba(240, 185, 11, 0.4)' },
          }
        },
        secondary: {
          bg: 'transparent',
          color: 'brand.500',
          border: '2px solid',
          borderColor: 'brand.500',
          _hover: {
            bg: 'rgba(240, 185, 11, 0.1)',
            transform: { base: 'none', md: 'translateY(-2px)' },
          }
        },
        dark: {
          bg: 'gray.800',
          color: 'gray.100',
          _hover: {
            bg: 'gray.700',
            transform: { base: 'none', md: 'translateY(-2px)' },
          }
        }
      }
    },
    Input: {
      baseStyle: {
        field: {
          fontSize: { base: '0.9rem', md: '0.95rem' },
          padding: { base: '0.7rem 0.8rem', md: '0.6rem 0.9rem' }, // Larger touch target on mobile
          borderRadius: '0.4rem',
          bg: 'gray.800',
          borderColor: 'gray.700',
          color: 'gray.100',
          height: { base: '44px', md: 'auto' }, // Ensure consistent height on mobile
          _hover: {
            borderColor: 'brand.500',
          },
          _focus: {
            borderColor: 'brand.500',
            boxShadow: '0 0 0 1px #F0B90B',
          }
        },
      },
    },
    FormLabel: {
      baseStyle: {
        fontSize: { base: '0.9rem', md: '0.95rem' },
        fontWeight: '600',
        marginBottom: '0.4rem',
        color: 'gray.100',
      },
    },
    Link: {
      baseStyle: {
        fontSize: { base: '0.9rem', md: '0.95rem' },
        fontWeight: '500',
        color: 'brand.500',
        transition: 'color 0.2s ease-in-out',
        padding: { base: '0.25rem', md: '0' }, // Larger touch target on mobile
        _hover: {
          textDecoration: 'none',
          color: 'brand.400',
        }
      },
    },
    Container: {
      baseStyle: {
        maxWidth: '1400px',
        width: { base: '100%', md: '95%' },
        padding: { base: '0 1rem', md: '0 1.5rem' },
      },
    },
    Box: {
      baseStyle: {
        borderRadius: '0.5rem',
        transition: 'all 0.3s ease',
      },
    },
    Card: {
      baseStyle: {
        container: {
          bg: 'gray.800',
          borderRadius: '0.5rem',
          borderWidth: '1px',
          borderColor: 'gray.700',
          overflow: 'hidden',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease',
          _hover: {
            transform: { base: 'none', md: 'translateY(-3px)' }, // Disable hover on mobile
            boxShadow: { base: '0 2px 8px rgba(0, 0, 0, 0.1)', md: '0 4px 16px rgba(0, 0, 0, 0.15)' },
            borderColor: { base: 'gray.700', md: 'brand.500' },
          }
        }
      }
    },
    Badge: {
      baseStyle: {
        borderRadius: 'full',
        px: { base: 2, md: 3 },
        py: 1,
        fontWeight: '600',
        textTransform: 'none',
        fontSize: { base: '0.75rem', md: '0.8rem' },
        minHeight: { base: '22px', md: 'auto' }, // Ensure consistent height on mobile
      },
      variants: {
        success: {
          bg: 'success.500',
          color: 'white',
        },
        error: {
          bg: 'error.500',
          color: 'white',
        },
        warning: {
          bg: 'warning.500',
          color: 'gray.900',
        },
        info: {
          bg: 'info.500',
          color: 'white',
        },
      }
    },
    // Add mobile-specific components
    Modal: {
      baseStyle: {
        dialog: {
          mx: { base: '4', md: 'auto' }, // Smaller margin on mobile
          maxW: { base: 'calc(100% - 32px)', md: '600px' }, // Responsive width
          maxH: { base: 'calc(100% - 64px)', md: 'calc(100% - 128px)' }, // Responsive height
        },
        body: {
          p: { base: 3, md: 6 }, // Smaller padding on mobile
          fontSize: { base: '0.9rem', md: '1rem' }, // Smaller font on mobile
          maxH: { base: 'calc(100vh - 150px)', md: 'calc(100vh - 200px)' }, // Max height for scrolling
          overflowY: 'auto', // Enable scrolling
        },
        header: {
          p: { base: 3, md: 6 }, // Smaller padding on mobile
          fontSize: { base: '1.1rem', md: '1.2rem' }, // Smaller font on mobile
        },
        footer: {
          p: { base: 3, md: 6 }, // Smaller padding on mobile
        },
        content: {
          maxH: { base: 'calc(100vh - 40px)', md: 'calc(100vh - 80px)' }, // Max height for modal
          overflowY: 'auto', // Enable scrolling
        }
      }
    },
    Table: {
      baseStyle: {
        table: {
          fontSize: { base: '0.85rem', md: '0.9rem' }, // Smaller font on mobile
          borderCollapse: 'separate',
          borderSpacing: 0,
        },
        th: {
          p: { base: 2, md: 3 }, // Smaller padding on mobile
          fontWeight: 'bold',
        },
        td: {
          p: { base: 2, md: 3 }, // Smaller padding on mobile
        }
      }
    },
  },
})

// Conditional components for better performance
const ConditionalNavbar = memo(() => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  // Always call all hooks before any conditional rendering
  return isAdminRoute ? null : <Navbar />;
});

const ConditionalFooter = memo(() => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  // Always call all hooks before any conditional rendering
  return isAdminRoute ? null : <Footer />;
});

// Toast container component that uses the toast context
const ToastContainerWithContext = memo(() => {
  const { toasts, removeToast } = useToast();
  return <ToastContainer toasts={toasts} onClose={removeToast} />;
});

// Optimized App component with memoization
const App = memo(() => {
  return (
    <ThemeProvider>
      <ToastProvider>
        <CryptoPriceProvider>
          <TransactionProvider>
            <RouteProvider>
              {/* Title manager to update document title from system config */}
              <TitleManager />

              {/* Route prefetcher for intelligent route prefetching */}
              <RoutePrefetcher />

              {/* Impersonation banner for admin login as user */}
              <ImpersonationBanner />

              {/* Conditional navbar based on route */}
              <ConditionalNavbar />

              {/* Toast container for notifications */}
              <ToastContainerWithContext />

              {/* Route transition wrapper for smooth page transitions */}
              <RouteTransition>
                {/* Optimized route controller with lazy loading */}
                <OptimizedRouteController />
              </RouteTransition>

              {/* Conditional footer based on route */}
              <ConditionalFooter />
            </RouteProvider>
          </TransactionProvider>
        </CryptoPriceProvider>
      </ToastProvider>
    </ThemeProvider>
  );
});

export default App;