import { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Heading,
  Text,
  Link,
  Alert,
  AlertIcon,
  Container,
  SimpleGrid,
  Flex,
  InputGroup,
  InputRightElement,
  Icon,
  Divider,
  HStack,
  Select,
  Checkbox,
  Progress,
  VStack,
  FormHelperText,
  InputLeftAddon,
  useToast,
  List,
  ListItem,
  ListIcon,
  keyframes,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverHeader,
  PopoverBody,
  PopoverArrow,
  PopoverCloseButton,
  Tooltip,
  Badge,
} from '@chakra-ui/react';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  FaCheck,
  FaEye,
  FaEyeSlash,
  FaGlobe,
  FaMapMarkerAlt,
  FaPhone,
  FaLock,
  FaShieldAlt,
  FaCheckCircle,
  FaTimesCircle,
  FaUser,
  FaCalendarAlt,
  FaEnvelope,
  FaUserPlus,
  FaRocket,
  FaIdCard,
  FaAddressCard,
  FaKey,
  FaInfoCircle,
  FaUserFriends,
  FaGem,
  FaUsers,
  FaGift
} from 'react-icons/fa';
import useAuth from '../hooks/useAuth';

// Enhanced modern animations for Register page with updated color scheme
const float = keyframes`
  0% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-15px) rotate(2deg); }
  66% { transform: translateY(8px) rotate(-1deg); }
  100% { transform: translateY(0px) rotate(0deg); }
`;

const glow = keyframes`
  0% { box-shadow: 0 0 20px rgba(252, 213, 53, 0.3); }
  50% { box-shadow: 0 0 35px rgba(252, 213, 53, 0.6); }
  100% { box-shadow: 0 0 20px rgba(252, 213, 53, 0.3); }
`;

const shimmer = keyframes`
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
`;

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;

// Countries data with phone codes
const countries = [
  { code: 'US', name: 'United States', flag: '🇺🇸', phoneCode: '+1' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧', phoneCode: '+44' },
  { code: 'TR', name: 'Turkey', flag: '🇹🇷', phoneCode: '+90' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪', phoneCode: '+49' },
  { code: 'FR', name: 'France', flag: '🇫🇷', phoneCode: '+33' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹', phoneCode: '+39' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸', phoneCode: '+34' },
  { code: 'NL', name: 'Netherlands', flag: '🇳🇱', phoneCode: '+31' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦', phoneCode: '+1' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺', phoneCode: '+61' },
];

// Cities data by country
const citiesByCountry = {
  US: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose'],
  GB: ['London', 'Birmingham', 'Manchester', 'Glasgow', 'Liverpool', 'Leeds', 'Sheffield', 'Edinburgh', 'Bristol', 'Cardiff'],
  TR: ['Istanbul', 'Ankara', 'Izmir', 'Bursa', 'Antalya', 'Adana', 'Konya', 'Gaziantep', 'Mersin', 'Diyarbakir'],
  DE: ['Berlin', 'Hamburg', 'Munich', 'Cologne', 'Frankfurt', 'Stuttgart', 'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig'],
  FR: ['Paris', 'Marseille', 'Lyon', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg', 'Montpellier', 'Bordeaux', 'Lille'],
  IT: ['Rome', 'Milan', 'Naples', 'Turin', 'Palermo', 'Genoa', 'Bologna', 'Florence', 'Bari', 'Catania'],
  ES: ['Madrid', 'Barcelona', 'Valencia', 'Seville', 'Zaragoza', 'Málaga', 'Murcia', 'Palma', 'Las Palmas', 'Bilbao'],
  NL: ['Amsterdam', 'Rotterdam', 'The Hague', 'Utrecht', 'Eindhoven', 'Tilburg', 'Groningen', 'Almere', 'Breda', 'Nijmegen'],
  CA: ['Toronto', 'Montreal', 'Vancouver', 'Calgary', 'Edmonton', 'Ottawa', 'Winnipeg', 'Quebec City', 'Hamilton', 'Kitchener'],
  AU: ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Gold Coast', 'Newcastle', 'Canberra', 'Sunshine Coast', 'Wollongong'],
};

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  country: string;
  city: string;
  phoneNumber: string;
  phoneCode: string;
  referralCode: string;
  termsAccepted: boolean;
  privacyAccepted: boolean;
  marketingConsent: boolean;
}

interface FieldErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
  country?: string;
  city?: string;
  phoneNumber?: string;
  termsAccepted?: string;
  privacyAccepted?: string;
}

interface PasswordCriteria {
  length: boolean;
  uppercase: boolean;
  lowercase: boolean;
  number: boolean;
  special: boolean;
}

const Register = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const { register, loading, user } = useAuth();
  const captchaRef = useRef<HTMLInputElement>(null);

  // Form state
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    country: '',
    city: '',
    phoneNumber: '',
    phoneCode: '+90',
    referralCode: '',
    termsAccepted: false,
    privacyAccepted: false,
    marketingConsent: false,
  });

  // UI state
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [marketingAccepted, setMarketingAccepted] = useState(false);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<FieldErrors>({});
  const [citySearchTerm, setCitySearchTerm] = useState('');
  const [referralValid, setReferralValid] = useState<boolean | null>(null);

  // CAPTCHA state
  const [captchaValue, setCaptchaValue] = useState('');
  const [captchaAnswer, setCaptchaAnswer] = useState('');
  const [captchaError, setCaptchaError] = useState(false);

  // Redirect if user is already logged in
  useEffect(() => {
    if (user) {
      const from = location.state?.from?.pathname || '/dashboard';
      toast({
        title: t('register.alreadyLoggedIn', 'Already Logged In'),
        description: t('register.redirectingToDashboard', 'You are already logged in. Redirecting...'),
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
      navigate(from, { replace: true });
    }
  }, [user, navigate, location, toast, t]);

  // Check for referral code in URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const refCode = urlParams.get('ref');
    if (refCode && refCode.length === 8) {
      setFormData(prev => ({ ...prev, referralCode: refCode.toUpperCase() }));
      // Optionally validate the referral code immediately
      validateReferralCode(refCode.toUpperCase());
    }
  }, [location.search]);

  // Validate referral code function
  const validateReferralCode = async (code: string) => {
    if (!code || code.length !== 8) {
      setReferralValid(null);
      return;
    }

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL || '/api'}/referrals/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ referralCode: code }),
      });

      const data = await response.json();

      if (data.status === 'success' && data.data.valid) {
        setReferralValid(true);
        toast({
          title: t('auth:register.referralValid', 'Valid Referral Code'),
          description: t('auth:register.referralValidDesc', `Referred by ${data.data.referrerName}`),
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else {
        setReferralValid(false);
        toast({
          title: t('auth:register.referralInvalid', 'Invalid Referral Code'),
          description: data.data?.reason || t('auth:register.referralInvalidDesc', 'This referral code does not exist'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error validating referral code:', error);
      setReferralValid(false);
    }
  };

  // Generate CAPTCHA
  const generateCaptcha = useCallback(() => {
    const num1 = Math.floor(Math.random() * 10);
    const num2 = Math.floor(Math.random() * 10);
    setCaptchaValue(`${num1} + ${num2} = ?`);
    setCaptchaAnswer((num1 + num2).toString());
    setCaptchaError(false);
    if (captchaRef.current) captchaRef.current.value = '';
  }, []);

  // Generate CAPTCHA on component mount
  useEffect(() => {
    generateCaptcha();
  }, [generateCaptcha]);

  // Update phone code when country changes
  useEffect(() => {
    if (formData.country) {
      const country = countries.find(c => c.code === formData.country);
      if (country) {
        setFormData(prev => ({ ...prev, phoneCode: country.phoneCode, city: '' }));
      }
    }
  }, [formData.country]);

  // Get cities by country with search filter
  const getCitiesByCountry = useCallback((countryCode: string) => {
    const cities = citiesByCountry[countryCode as keyof typeof citiesByCountry] || [];
    if (!citySearchTerm) return cities;
    return cities.filter(city =>
      city.toLowerCase().includes(citySearchTerm.toLowerCase())
    );
  }, [citySearchTerm]);

  // Password strength calculation
  const passwordCriteria = useMemo<PasswordCriteria>(() => ({
    length: formData.password.length >= 8,
    uppercase: /[A-Z]/.test(formData.password),
    lowercase: /[a-z]/.test(formData.password),
    number: /\d/.test(formData.password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(formData.password),
  }), [formData.password]);

  const passwordStrength = useMemo(() => {
    const criteria = Object.values(passwordCriteria);
    const metCriteria = criteria.filter(Boolean).length;
    return (metCriteria / criteria.length) * 100;
  }, [passwordCriteria]);

  // Form validation
  const validateForm = useCallback(() => {
    const errors: FieldErrors = {};

    if (!formData.firstName.trim()) {
      errors.firstName = t('register.firstNameRequired', 'First name is required');
    } else if (formData.firstName.trim().length < 2) {
      errors.firstName = t('register.firstNameTooShort', 'First name must be at least 2 characters');
    }

    if (!formData.lastName.trim()) {
      errors.lastName = t('register.lastNameRequired', 'Last name is required');
    } else if (formData.lastName.trim().length < 2) {
      errors.lastName = t('register.lastNameTooShort', 'Last name must be at least 2 characters');
    }

    if (!formData.email.trim()) {
      errors.email = t('register.emailRequired', 'Email is required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = t('register.emailInvalid', 'Please enter a valid email address');
    }

    if (!formData.password) {
      errors.password = t('register.passwordRequired', 'Password is required');
    } else if (!Object.values(passwordCriteria).every(Boolean)) {
      errors.password = t('register.passwordWeak', 'Password does not meet requirements');
    }

    if (!formData.birthDate) {
      errors.birthDate = t('register.birthDateRequired', 'Birth date is required');
    } else {
      const birthDate = new Date(formData.birthDate);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 18) {
        errors.birthDate = t('register.ageRequirement', 'You must be at least 18 years old');
      }
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData, passwordCriteria, t]);

  // Real-time referral code validation with debouncing
  useEffect(() => {
    const timer = setTimeout(() => {
      if (formData.referralCode && formData.referralCode.length === 8) {
        validateReferralCode(formData.referralCode);
      } else if (formData.referralCode.length > 0 && formData.referralCode.length < 8) {
        setReferralValid(false);
      } else {
        setReferralValid(null);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [formData.referralCode]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log('🚀 REGISTRATION FORM: Submit started');
    console.log('📧 Form data:', {
      firstName: formData.firstName,
      lastName: formData.lastName,
      username: formData.username,
      email: formData.email,
      country: formData.country,
      city: formData.city,
      hasPassword: !!formData.password,
      passwordLength: formData.password?.length,
      hasReferralCode: !!formData.referralCode,
      termsAccepted,
      marketingAccepted
    });

    // Reset errors
    setFieldErrors({});
    setCaptchaError(false);

    // Validate form
    if (!validateForm()) {
      console.log('❌ REGISTRATION FORM: Form validation failed');
      toast({
        title: t('register.validationError', 'Validation Error'),
        description: t('register.fixErrors', 'Please fix the errors and try again'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    console.log('✅ REGISTRATION FORM: Form validation passed');

    // Check password confirmation
    if (formData.password !== formData.confirmPassword) {
      toast({
        title: t('register.passwordMismatch', 'Password Mismatch'),
        description: t('register.passwordsDoNotMatch', 'Passwords do not match'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    // Check terms acceptance
    if (!termsAccepted) {
      toast({
        title: t('register.termsRequired', 'Terms Required'),
        description: t('register.acceptTerms', 'Please accept the terms and conditions'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    // CAPTCHA verification
    const userCaptchaAnswer = captchaRef.current?.value;
    if (!userCaptchaAnswer || userCaptchaAnswer !== captchaAnswer) {
      setCaptchaError(true);
      toast({
        title: t('register.captchaError', 'CAPTCHA Error'),
        description: t('register.captchaFailed', 'CAPTCHA verification failed'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      generateCaptcha(); // Generate new CAPTCHA
      if (captchaRef.current) captchaRef.current.value = '';
      return;
    }

    try {
      setFormSubmitting(true);
      console.log('🔄 REGISTRATION FORM: Starting registration process...');

      // Prepare registration data
      const registrationData = {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim(),
        password: formData.password,
        country: formData.country,
        city: formData.city,
        phoneNumber: formData.phoneNumber ? `${formData.phoneCode}${formData.phoneNumber}` : '',
        referralCode: formData.referralCode.trim() || undefined,
        termsAccepted: formData.termsAccepted,
        privacyAccepted: formData.privacyAccepted,
        marketingConsent: formData.marketingConsent,
      };

      console.log('📤 REGISTRATION FORM: Prepared registration data:', {
        ...registrationData,
        password: '[HIDDEN]'
      });

      // Call the register function from AuthContext
      console.log('🔄 REGISTRATION FORM: Calling AuthContext register function...');
      await register(registrationData);
      console.log('✅ REGISTRATION FORM: AuthContext register completed successfully');

      // Show success message with email verification notice
      toast({
        title: t('register.successTitle', 'Registration Successful!'),
        description: t('register.successDescription', 'Welcome to SHPN Finance! Please check your email to verify your account and unlock all features.'),
        status: 'success',
        duration: 8000,
        isClosable: true,
      });

      // Navigate to dashboard or verification page
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from);

    } catch (error: any) {
      console.error('Registration error:', error);

      // Display the error message from the backend if available
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          t('register.registrationFailed', 'Registration failed. Please try again.');

      toast({
        title: t('register.errorTitle', 'Registration Failed'),
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });

      // Generate new CAPTCHA on error
      generateCaptcha();
      if (captchaRef.current) captchaRef.current.value = '';
    } finally {
      setFormSubmitting(false);
    }
  };

  return (
    <Box
      minH="100vh"
      bgGradient="linear(135deg, #0B0E11 0%, #1A1D29 25%, #0B0E11 50%, #1A1D29 75%, #0B0E11 100%)"
      position="relative"
      overflow="hidden"
      className="safe-area-all"
    >
      {/* Enhanced animated background elements with new color scheme and mobile optimization */}
      <Box
        position="absolute"
        top="10%"
        left="10%"
        w={{ base: "70px", md: "100px" }}
        h={{ base: "70px", md: "100px" }}
        borderRadius="full"
        bg="linear-gradient(45deg, rgba(252, 213, 53, 0.15), rgba(252, 213, 53, 0.08))"
        animation={`${float} 8s ease-in-out infinite`}
        zIndex={0}
      />
      <Box
        position="absolute"
        top="60%"
        right="15%"
        w={{ base: "55px", md: "80px" }}
        h={{ base: "55px", md: "80px" }}
        borderRadius="full"
        bg="linear-gradient(45deg, rgba(252, 213, 53, 0.12), rgba(252, 213, 53, 0.06))"
        animation={`${float} 10s ease-in-out infinite reverse`}
        zIndex={0}
      />
      <Box
        position="absolute"
        top="25%"
        right="8%"
        w={{ base: "40px", md: "60px" }}
        h={{ base: "40px", md: "60px" }}
        borderRadius="full"
        bg="linear-gradient(45deg, rgba(252, 213, 53, 0.08), rgba(252, 213, 53, 0.04))"
        animation={`${float} 12s ease-in-out infinite`}
        zIndex={0}
        display={{ base: "none", sm: "block" }}
      />

      <Container
        maxW={{ base: "100%", sm: "600px", md: "800px", lg: "6xl" }}
        px={{ base: 3, sm: 4, md: 6, lg: 8 }}
        py={{ base: 4, sm: 6, md: 8, lg: 12 }}
        position="relative"
        zIndex={1}
        className="mobile-full-width"
        minH="100vh"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <VStack spacing={8} align="center">
          {/* Modern Header */}
          <VStack spacing={6} textAlign="center">
            <Box position="relative">
              <Flex align="center" justify="center" mb={4}>
                <Box
                  w={{ base: "50px", md: "60px" }}
                  h={{ base: "50px", md: "60px" }}
                  bg="linear-gradient(135deg, #FCD535 0%, #F8D12F 100%)"
                  borderRadius="2xl"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  boxShadow="0 10px 40px rgba(252, 213, 53, 0.4)"
                  animation={`${glow} 4s ease-in-out infinite`}
                  position="relative"
                  _before={{
                    content: '""',
                    position: 'absolute',
                    top: '-3px',
                    left: '-3px',
                    right: '-3px',
                    bottom: '-3px',
                    borderRadius: '2xl',
                    background: 'linear-gradient(45deg, #FCD535, #F8D12F, #FCD535)',
                    backgroundSize: '200% 200%',
                    animation: `${shimmer} 2s linear infinite`,
                    zIndex: -1,
                  }}
                >
                  <Icon as={FaUserPlus} color="#0B0E11" boxSize={{ base: 6, md: 7 }} />
                </Box>
              </Flex>
              <Heading
                fontSize={{ base: '3xl', md: '4xl' }}
                fontWeight="900"
                bgGradient="linear(135deg, #FCD535 0%, #F8D12F 50%, #FCD535 100%)"
                bgClip="text"
                letterSpacing="tight"
                mb={3}
              >
                Shipping Finance
              </Heading>
              <Text
                fontSize={{ base: 'xl', md: '2xl' }}
                color="#EAECEF"
                fontWeight="700"
                opacity={0.9}
                mb={2}
              >
                {t('register.title', 'Create Your Account')}
              </Text>
              <Text
                fontSize="md"
                color="#848E9C"
                opacity={0.8}
              >
                {t('register.subtitle', 'Join the future of decentralized finance')}
              </Text>
            </Box>
          </VStack>

          {/* Enhanced Modern Glassmorphism Registration Form */}
          <Box
            w="full"
            maxW={{ base: "100%", sm: "600px", md: "800px" }}
            bg="rgba(30, 35, 41, 0.85)"
            backdropFilter="blur(20px)"
            borderRadius="2xl"
            borderWidth="1px"
            borderColor="rgba(252, 213, 53, 0.25)"
            boxShadow="0 25px 50px -12px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(252, 213, 53, 0.15)"
            p={{ base: 4, sm: 6, md: 8 }}
            position="relative"
            _before={{
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius: '2xl',
              background: 'linear-gradient(135deg, rgba(252, 213, 53, 0.12) 0%, transparent 50%, rgba(252, 213, 53, 0.06) 100%)',
              pointerEvents: 'none',
            }}
          >
            <form onSubmit={handleSubmit}>
              <VStack spacing={8} position="relative" zIndex={1}>
                {/* Personal Information Section */}
                <Box w="full">
                  <HStack mb={6} spacing={3}>
                    <Box
                      p={2}
                      borderRadius="lg"
                      bg="rgba(252, 213, 53, 0.15)"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon as={FaUser} color="#FCD535" boxSize={5} />
                    </Box>
                    <Text
                      fontSize="xl"
                      fontWeight="700"
                      color="#EAECEF"
                      letterSpacing="wide"
                    >
                      {t('register.personalInfo', 'Personal Information')}
                    </Text>
                  </HStack>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                    {/* First Name */}
                    <FormControl id="firstName" isRequired isInvalid={!!fieldErrors.firstName}>
                      <FormLabel
                        color="#EAECEF"
                        fontSize="sm"
                        fontWeight="600"
                        mb={3}
                      >
                        <HStack spacing={2}>
                          <Box
                            p={1}
                            borderRadius="md"
                            bg="rgba(252, 213, 53, 0.15)"
                          >
                            <Icon as={FaUser} color="#FCD535" boxSize={3} />
                          </Box>
                          <Text>{t('common.firstName', 'First Name')}</Text>
                        </HStack>
                      </FormLabel>
                      <Input
                        type="text"
                        value={formData.firstName}
                        onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor="#2B3139"
                        borderWidth="1px"
                        color="#EAECEF"
                        placeholder="John"
                        _placeholder={{ color: '#848E9C' }}
                        _hover={{
                          borderColor: "rgba(252, 213, 53, 0.7)",
                          boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor: "#FCD535",
                          boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        size="lg"
                        borderRadius="xl"
                        fontSize="md"
                        transition="all 0.3s ease"
                      />
                      {fieldErrors.firstName && (
                        <Box mt={2} p={2} bg="rgba(248, 73, 96, 0.1)" borderRadius="md" borderLeft="3px solid" borderColor="#F84960">
                          <Text color="#F84960" fontSize="sm" fontWeight="500">
                            {fieldErrors.firstName}
                          </Text>
                        </Box>
                      )}
                    </FormControl>

                    {/* Last Name */}
                    <FormControl id="lastName" isRequired isInvalid={!!fieldErrors.lastName}>
                      <FormLabel
                        color="#EAECEF"
                        fontSize="sm"
                        fontWeight="600"
                        mb={3}
                      >
                        <HStack spacing={2}>
                          <Box
                            p={1}
                            borderRadius="md"
                            bg="rgba(252, 213, 53, 0.15)"
                          >
                            <Icon as={FaUser} color="#FCD535" boxSize={3} />
                          </Box>
                          <Text>{t('common.lastName', 'Last Name')}</Text>
                        </HStack>
                      </FormLabel>
                      <Input
                        type="text"
                        value={formData.lastName}
                        onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor="#2B3139"
                        borderWidth="1px"
                        color="#EAECEF"
                        placeholder="Doe"
                        _placeholder={{ color: '#848E9C' }}
                        _hover={{
                          borderColor: "rgba(252, 213, 53, 0.7)",
                          boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor: "#FCD535",
                          boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        size="lg"
                        borderRadius="xl"
                        fontSize="md"
                        transition="all 0.3s ease"
                      />
                      {fieldErrors.lastName && (
                        <Box mt={2} p={2} bg="rgba(248, 73, 96, 0.1)" borderRadius="md" borderLeft="3px solid" borderColor="#F84960">
                          <Text color="#F84960" fontSize="sm" fontWeight="500">
                            {fieldErrors.lastName}
                          </Text>
                        </Box>
                      )}
                    </FormControl>

                    {/* Username */}
                    <FormControl id="username" isRequired isInvalid={!!fieldErrors.username}>
                      <FormLabel
                        color="#EAECEF"
                        fontSize="sm"
                        fontWeight="600"
                        mb={3}
                      >
                        <HStack spacing={2}>
                          <Box
                            p={1}
                            borderRadius="md"
                            bg="rgba(252, 213, 53, 0.15)"
                          >
                            <Icon as={FaIdCard} color="#FCD535" boxSize={3} />
                          </Box>
                          <Text>{t('common.username', 'Username')}</Text>
                        </HStack>
                      </FormLabel>
                      <Input
                        type="text"
                        value={formData.username}
                        onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor="#2B3139"
                        borderWidth="1px"
                        color="#EAECEF"
                        placeholder="johndoe123"
                        _placeholder={{ color: '#848E9C' }}
                        _hover={{
                          borderColor: "rgba(252, 213, 53, 0.7)",
                          boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor: "#FCD535",
                          boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        size="lg"
                        borderRadius="xl"
                        fontSize="md"
                        transition="all 0.3s ease"
                      />
                      {fieldErrors.username ? (
                        <Box mt={2} p={2} bg="rgba(248, 73, 96, 0.1)" borderRadius="md" borderLeft="3px solid" borderColor="#F84960">
                          <Text color="#F84960" fontSize="sm" fontWeight="500">
                            {fieldErrors.username}
                          </Text>
                        </Box>
                      ) : (
                        <FormHelperText color="#848E9C" fontSize="xs" mt={2}>
                          {t('register.usernameHelp', 'Letters, numbers, and underscores only (3-20 characters)')}
                        </FormHelperText>
                      )}
                    </FormControl>

                    {/* Birth Date */}
                    <FormControl id="birthDate" isRequired isInvalid={!!fieldErrors.birthDate}>
                      <FormLabel
                        color="#EAECEF"
                        fontSize="sm"
                        fontWeight="600"
                        mb={3}
                      >
                        <HStack spacing={2}>
                          <Box
                            p={1}
                            borderRadius="md"
                            bg="rgba(252, 213, 53, 0.15)"
                          >
                            <Icon as={FaCalendarAlt} color="#FCD535" boxSize={3} />
                          </Box>
                          <Text>{t('common.birthDate', 'Birth Date')}</Text>
                        </HStack>
                      </FormLabel>
                      <Input
                        type="date"
                        value={formData.birthDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, birthDate: e.target.value }))}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor="#2B3139"
                        borderWidth="1px"
                        color="#EAECEF"
                        _hover={{
                          borderColor: "rgba(252, 213, 53, 0.7)",
                          boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor: "#FCD535",
                          boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        size="lg"
                        borderRadius="xl"
                        fontSize="md"
                        transition="all 0.3s ease"
                        max={new Date(new Date().setFullYear(new Date().getFullYear() - 18)).toISOString().split('T')[0]}
                      />
                      {fieldErrors.birthDate ? (
                        <Box mt={2} p={2} bg="rgba(248, 73, 96, 0.1)" borderRadius="md" borderLeft="3px solid" borderColor="#F84960">
                          <Text color="#F84960" fontSize="sm" fontWeight="500">
                            {fieldErrors.birthDate}
                          </Text>
                        </Box>
                      ) : (
                        <FormHelperText color="#848E9C" fontSize="xs" mt={2}>
                          {t('register.ageRequirement', 'You must be at least 18 years old')}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </SimpleGrid>
                </Box>

                <Divider borderColor="rgba(252, 213, 53, 0.2)" />

                {/* Contact Information Section */}
                <Box w="full">
                  <HStack mb={6} spacing={3}>
                    <Box
                      p={2}
                      borderRadius="lg"
                      bg="rgba(252, 213, 53, 0.15)"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon as={FaEnvelope} color="#FCD535" boxSize={5} />
                    </Box>
                    <Text
                      fontSize="xl"
                      fontWeight="700"
                      color="#EAECEF"
                      letterSpacing="wide"
                    >
                      {t('register.contactInfo', 'Contact Information')}
                    </Text>
                  </HStack>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                    {/* Email */}
                    <FormControl id="email" isRequired isInvalid={!!fieldErrors.email}>
                      <FormLabel
                        color="#EAECEF"
                        fontSize="sm"
                        fontWeight="600"
                        mb={3}
                      >
                        <HStack spacing={2}>
                          <Box
                            p={1}
                            borderRadius="md"
                            bg="rgba(252, 213, 53, 0.15)"
                          >
                            <Icon as={FaEnvelope} color="#FCD535" boxSize={3} />
                          </Box>
                          <Text>{t('common.email', 'Email Address')}</Text>
                        </HStack>
                      </FormLabel>
                      <Input
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor="#2B3139"
                        borderWidth="1px"
                        color="#EAECEF"
                        placeholder="<EMAIL>"
                        _placeholder={{ color: '#848E9C' }}
                        _hover={{
                          borderColor: "rgba(252, 213, 53, 0.7)",
                          boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor: "#FCD535",
                          boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        size="lg"
                        borderRadius="xl"
                        fontSize="md"
                        transition="all 0.3s ease"
                      />
                      {fieldErrors.email && (
                        <Box mt={2} p={2} bg="rgba(248, 73, 96, 0.1)" borderRadius="md" borderLeft="3px solid" borderColor="#F84960">
                          <Text color="#F84960" fontSize="sm" fontWeight="500">
                            {fieldErrors.email}
                          </Text>
                        </Box>
                      )}
                    </FormControl>

                    {/* Country */}
                    <FormControl id="country" isRequired>
                      <FormLabel
                        color="#EAECEF"
                        fontSize="sm"
                        fontWeight="600"
                        mb={3}
                      >
                        <HStack spacing={2}>
                          <Box
                            p={1}
                            borderRadius="md"
                            bg="rgba(252, 213, 53, 0.15)"
                          >
                            <Icon as={FaGlobe} color="#FCD535" boxSize={3} />
                          </Box>
                          <Text>{t('common.country', 'Country')}</Text>
                        </HStack>
                      </FormLabel>
                      <Select
                        value={formData.country}
                        onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor="#2B3139"
                        borderWidth="1px"
                        color="#EAECEF"
                        placeholder={t('common.selectCountry', 'Select your country')}
                        _hover={{
                          borderColor: "rgba(252, 213, 53, 0.7)",
                          boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor: "#FCD535",
                          boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        size="lg"
                        borderRadius="xl"
                        fontSize="md"
                        transition="all 0.3s ease"
                      >
                        {countries.map(country => (
                          <option key={country.code} value={country.code} style={{ backgroundColor: '#1E2026', color: '#EAECEF' }}>
                            {country.flag} {country.name}
                          </option>
                        ))}
                      </Select>
                    </FormControl>

                    {/* City */}
                    <FormControl id="city">
                      <FormLabel
                        color="#EAECEF"
                        fontSize="sm"
                        fontWeight="600"
                        mb={3}
                      >
                        <HStack spacing={2}>
                          <Box
                            p={1}
                            borderRadius="md"
                            bg="rgba(252, 213, 53, 0.15)"
                          >
                            <Icon as={FaMapMarkerAlt} color="#FCD535" boxSize={3} />
                          </Box>
                          <Text>{t('common.city', 'City')}</Text>
                        </HStack>
                      </FormLabel>
                      <Select
                        value={formData.city}
                        onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor="#2B3139"
                        borderWidth="1px"
                        color="#EAECEF"
                        placeholder={t('common.selectCity', 'Select your city')}
                        isDisabled={!formData.country}
                        _hover={{
                          borderColor: "rgba(252, 213, 53, 0.7)",
                          boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor: "#FCD535",
                          boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        size="lg"
                        borderRadius="xl"
                        fontSize="md"
                        transition="all 0.3s ease"
                      >
                        {getCitiesByCountry(formData.country).map(city => (
                          <option key={city} value={city} style={{ backgroundColor: '#1E2026', color: '#EAECEF' }}>
                            {city}
                          </option>
                        ))}
                      </Select>
                      {!formData.country && (
                        <FormHelperText color="#848E9C" fontSize="xs" mt={2}>
                          {t('register.selectCountryFirst', 'Please select a country first')}
                        </FormHelperText>
                      )}
                    </FormControl>

                    {/* Phone Number */}
                    <FormControl id="phoneNumber">
                      <FormLabel
                        color="#EAECEF"
                        fontSize="sm"
                        fontWeight="600"
                        mb={3}
                      >
                        <HStack spacing={2}>
                          <Box
                            p={1}
                            borderRadius="md"
                            bg="rgba(252, 213, 53, 0.15)"
                          >
                            <Icon as={FaPhone} color="#FCD535" boxSize={3} />
                          </Box>
                          <Text>{t('common.phoneNumber', 'Phone Number')}</Text>
                        </HStack>
                      </FormLabel>
                      <InputGroup size="lg">
                        <InputLeftAddon
                          bg="rgba(11, 14, 17, 0.7)"
                          color="#848E9C"
                          borderColor="#2B3139"
                          borderRadius="xl"
                          minW="80px"
                        >
                          <Text fontSize="sm" fontWeight="600">
                            {formData.phoneCode || '+00'}
                          </Text>
                        </InputLeftAddon>
                        <Input
                          type="tel"
                          value={formData.phoneNumber}
                          onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                          bg="rgba(11, 14, 17, 0.7)"
                          borderColor="#2B3139"
                          borderWidth="1px"
                          color="#EAECEF"
                          placeholder="************"
                          _placeholder={{ color: '#848E9C' }}
                          _hover={{
                            borderColor: "rgba(252, 213, 53, 0.7)",
                            boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                          }}
                          _focus={{
                            borderColor: "#FCD535",
                            boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                            bg: "rgba(11, 14, 17, 0.9)"
                          }}
                          borderRadius="xl"
                          fontSize="md"
                          transition="all 0.3s ease"
                        />
                      </InputGroup>
                      <FormHelperText color="#848E9C" fontSize="xs" mt={2}>
                        {t('register.phoneHelp', 'Optional - Can be used for two-factor authentication')}
                      </FormHelperText>
                    </FormControl>
                  </SimpleGrid>
                </Box>

                <Divider borderColor="rgba(252, 213, 53, 0.2)" />

                {/* Password Section */}
                <Box w="full">
                  <HStack mb={6} spacing={3}>
                    <Box
                      p={2}
                      borderRadius="lg"
                      bg="rgba(252, 213, 53, 0.15)"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon as={FaKey} color="#FCD535" boxSize={5} />
                    </Box>
                    <Text
                      fontSize="xl"
                      fontWeight="700"
                      color="#EAECEF"
                      letterSpacing="wide"
                    >
                      {t('register.accountSecurity', 'Account Security')}
                    </Text>
                  </HStack>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                    {/* Password */}
                    <FormControl id="password" isRequired isInvalid={!!fieldErrors.password}>
                      <FormLabel
                        color="#EAECEF"
                        fontSize="sm"
                        fontWeight="600"
                        mb={3}
                      >
                        <HStack spacing={2}>
                          <Box
                            p={1}
                            borderRadius="md"
                            bg="rgba(252, 213, 53, 0.15)"
                          >
                            <Icon as={FaLock} color="#FCD535" boxSize={3} />
                          </Box>
                          <Text>{t('common.password', 'Password')}</Text>
                        </HStack>
                      </FormLabel>
                      <InputGroup size="lg">
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          value={formData.password}
                          onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                          bg="rgba(11, 14, 17, 0.7)"
                          borderColor="#2B3139"
                          borderWidth="1px"
                          color="#EAECEF"
                          placeholder="Enter a strong password"
                          _placeholder={{ color: '#848E9C' }}
                          _hover={{
                            borderColor: "rgba(252, 213, 53, 0.7)",
                            boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                          }}
                          _focus={{
                            borderColor: "#FCD535",
                            boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                            bg: "rgba(11, 14, 17, 0.9)"
                          }}
                          borderRadius="xl"
                          fontSize="md"
                          transition="all 0.3s ease"
                        />
                        <InputRightElement width="3rem">
                          <Button
                            h="2rem"
                            size="sm"
                            onClick={() => setShowPassword(!showPassword)}
                            bg="transparent"
                            _hover={{
                              bg: "rgba(252, 213, 53, 0.15)",
                              transform: "scale(1.1)"
                            }}
                            _active={{ transform: "scale(0.95)" }}
                            color="#848E9C"
                            borderRadius="lg"
                            transition="all 0.2s ease"
                          >
                            <Icon as={showPassword ? FaEyeSlash : FaEye} />
                          </Button>
                        </InputRightElement>
                      </InputGroup>

                      {/* Password Strength Indicator */}
                      {formData.password && (
                        <Box mt={3}>
                          <Flex justify="space-between" align="center" mb={2}>
                            <Text fontSize="xs" color="#848E9C">
                              {t('register.passwordStrength', 'Password Strength')}:
                            </Text>
                            <Text
                              fontSize="xs"
                              fontWeight="bold"
                              color={
                                passwordStrength < 40 ? "#F84960" :
                                passwordStrength < 60 ? "orange.400" :
                                passwordStrength < 80 ? "yellow.400" :
                                "#02C076"
                              }
                            >
                              {passwordStrength < 40 ? t('register.passwordWeak', 'Weak') :
                               passwordStrength < 60 ? t('register.passwordMedium', 'Medium') :
                               passwordStrength < 80 ? t('register.passwordGood', 'Good') :
                               t('register.passwordStrong', 'Strong')}
                            </Text>
                          </Flex>
                          <Progress
                            value={passwordStrength}
                            size="sm"
                            colorScheme={
                              passwordStrength < 40 ? "red" :
                              passwordStrength < 60 ? "orange" :
                              passwordStrength < 80 ? "yellow" :
                              "green"
                            }
                            borderRadius="full"
                            bg="rgba(43, 49, 57, 0.8)"
                          />
                        </Box>
                      )}

                      {fieldErrors.password && (
                        <Box mt={2} p={2} bg="rgba(248, 73, 96, 0.1)" borderRadius="md" borderLeft="3px solid" borderColor="#F84960">
                          <Text color="#F84960" fontSize="sm" fontWeight="500">
                            {fieldErrors.password}
                          </Text>
                        </Box>
                      )}
                    </FormControl>

                    {/* Confirm Password */}
                    <FormControl id="confirmPassword" isRequired>
                      <FormLabel
                        color="#EAECEF"
                        fontSize="sm"
                        fontWeight="600"
                        mb={3}
                      >
                        <HStack spacing={2}>
                          <Box
                            p={1}
                            borderRadius="md"
                            bg="rgba(252, 213, 53, 0.15)"
                          >
                            <Icon as={FaLock} color="#FCD535" boxSize={3} />
                          </Box>
                          <Text>{t('register.confirmPassword', 'Confirm Password')}</Text>
                        </HStack>
                      </FormLabel>
                      <InputGroup size="lg">
                        <Input
                          type={showConfirmPassword ? 'text' : 'password'}
                          value={formData.confirmPassword}
                          onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                          bg="rgba(11, 14, 17, 0.7)"
                          borderColor="#2B3139"
                          borderWidth="1px"
                          color="#EAECEF"
                          placeholder="Confirm your password"
                          _placeholder={{ color: '#848E9C' }}
                          _hover={{
                            borderColor: "rgba(252, 213, 53, 0.7)",
                            boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                          }}
                          _focus={{
                            borderColor: "#FCD535",
                            boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                            bg: "rgba(11, 14, 17, 0.9)"
                          }}
                          borderRadius="xl"
                          fontSize="md"
                          transition="all 0.3s ease"
                        />
                        <InputRightElement width="3rem">
                          <Button
                            h="2rem"
                            size="sm"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            bg="transparent"
                            _hover={{
                              bg: "rgba(252, 213, 53, 0.15)",
                              transform: "scale(1.1)"
                            }}
                            _active={{ transform: "scale(0.95)" }}
                            color="#848E9C"
                            borderRadius="lg"
                            transition="all 0.2s ease"
                          >
                            <Icon as={showConfirmPassword ? FaEyeSlash : FaEye} />
                          </Button>
                        </InputRightElement>
                      </InputGroup>
                      {formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword && (
                        <Box mt={2} p={2} bg="rgba(248, 73, 96, 0.1)" borderRadius="md" borderLeft="3px solid" borderColor="#F84960">
                          <Text color="#F84960" fontSize="sm" fontWeight="500">
                            {t('register.passwordMismatch', 'Passwords do not match')}
                          </Text>
                        </Box>
                      )}
                    </FormControl>
                  </SimpleGrid>
                </Box>

                <Divider borderColor="rgba(252, 213, 53, 0.2)" />

                {/* Referral Code Section */}
                <Box w="full">
                  <HStack mb={6} spacing={3}>
                    <Box
                      p={2}
                      borderRadius="lg"
                      bg="rgba(252, 213, 53, 0.15)"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon as={FaUsers} color="#FCD535" boxSize={5} />
                    </Box>
                    <Text
                      fontSize="xl"
                      fontWeight="700"
                      color="#EAECEF"
                      letterSpacing="wide"
                    >
                      {t('auth:register.referralSection', 'Referral Code (Optional)')}
                    </Text>
                  </HStack>

                  <FormControl id="referralCode">
                    <FormLabel
                      color="#EAECEF"
                      fontSize="sm"
                      fontWeight="600"
                      mb={3}
                    >
                      <HStack spacing={2}>
                        <Box
                          p={1}
                          borderRadius="md"
                          bg="rgba(252, 213, 53, 0.15)"
                        >
                          <Icon as={FaGift} color="#FCD535" boxSize={3} />
                        </Box>
                        <Text>{t('auth:register.referralCode', 'Referral Code')}</Text>
                      </HStack>
                    </FormLabel>
                    <InputGroup size="lg">
                      <Input
                        value={formData.referralCode}
                        onChange={(e) => setFormData(prev => ({ ...prev, referralCode: e.target.value.toUpperCase() }))}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor={
                          referralValid === true ? "#02C076" :
                          referralValid === false ? "#F84960" :
                          "#2B3139"
                        }
                        borderWidth="1px"
                        color="#EAECEF"
                        placeholder="Enter referral code (8 characters)"
                        _placeholder={{ color: '#848E9C' }}
                        _hover={{
                          borderColor:
                            referralValid === true ? "#02C076" :
                            referralValid === false ? "#F84960" :
                            "rgba(252, 213, 53, 0.7)",
                          boxShadow:
                            referralValid === true ? "0 0 0 1px rgba(2, 192, 118, 0.3)" :
                            referralValid === false ? "0 0 0 1px rgba(248, 73, 96, 0.3)" :
                            "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor:
                            referralValid === true ? "#02C076" :
                            referralValid === false ? "#F84960" :
                            "#FCD535",
                          boxShadow:
                            referralValid === true ? "0 0 0 3px rgba(2, 192, 118, 0.15)" :
                            referralValid === false ? "0 0 0 3px rgba(248, 73, 96, 0.15)" :
                            "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        borderRadius="xl"
                        fontSize="md"
                        transition="all 0.3s ease"
                        maxLength={8}
                        textTransform="uppercase"
                      />
                      {referralValid !== null && (
                        <InputRightElement>
                          <Icon
                            as={referralValid ? FaCheckCircle : FaTimesCircle}
                            color={referralValid ? "#02C076" : "#F84960"}
                            boxSize={5}
                          />
                        </InputRightElement>
                      )}
                    </InputGroup>
                    <FormHelperText color="#848E9C" fontSize="xs" mt={2}>
                      {t('auth:register.referralHelp', 'Enter a referral code to earn bonuses for both you and your referrer')}
                    </FormHelperText>
                  </FormControl>
                </Box>

                <Divider borderColor="rgba(252, 213, 53, 0.2)" />

                {/* CAPTCHA Section */}
                <Box
                  w="full"
                  bg="rgba(252, 213, 53, 0.04)"
                  borderRadius="xl"
                  borderWidth="1px"
                  borderColor="rgba(252, 213, 53, 0.2)"
                  p={6}
                  position="relative"
                >
                  <HStack mb={4} spacing={3}>
                    <Box
                      p={2}
                      borderRadius="lg"
                      bg="rgba(252, 213, 53, 0.15)"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon as={FaShieldAlt} color="#FCD535" boxSize={5} />
                    </Box>
                    <Text
                      fontSize="xl"
                      fontWeight="700"
                      color="#EAECEF"
                      letterSpacing="wide"
                    >
                      {t('register.securityVerification', 'Security Verification')}
                    </Text>
                  </HStack>

                  <FormControl isRequired isInvalid={captchaError}>
                    <Box
                      bg="rgba(11, 14, 17, 0.7)"
                      borderRadius="lg"
                      borderWidth="1px"
                      borderColor={captchaError ? "#F84960" : "rgba(43, 49, 57, 0.8)"}
                      p={4}
                      mb={4}
                      textAlign="center"
                      position="relative"
                    >
                      <Text
                        fontSize="2xl"
                        fontWeight="bold"
                        letterSpacing="wider"
                        color="#EAECEF"
                        fontFamily="monospace"
                        textShadow="2px 2px 4px rgba(0,0,0,0.5)"
                      >
                        {captchaValue}
                      </Text>
                    </Box>

                    <InputGroup size="lg">
                      <Input
                        ref={captchaRef}
                        placeholder={t('register.captchaPlaceholder', 'Enter the result')}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor={captchaError ? "#F84960" : "rgba(43, 49, 57, 0.8)"}
                        borderWidth="1px"
                        color="#EAECEF"
                        textAlign="center"
                        fontSize="lg"
                        fontWeight="600"
                        _placeholder={{ color: '#848E9C' }}
                        _hover={{
                          borderColor: captchaError ? "#F84960" : "rgba(252, 213, 53, 0.7)",
                          boxShadow: captchaError
                            ? "0 0 0 1px rgba(248, 73, 96, 0.3)"
                            : "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor: captchaError ? "#F84960" : "#FCD535",
                          boxShadow: captchaError
                            ? "0 0 0 3px rgba(248, 73, 96, 0.15)"
                            : "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        borderRadius="xl"
                        transition="all 0.3s ease"
                      />
                      <InputRightElement width="4rem">
                        <Button
                          h="2.5rem"
                          size="sm"
                          onClick={generateCaptcha}
                          bg="transparent"
                          _hover={{
                            bg: "rgba(252, 213, 53, 0.15)",
                            transform: "rotate(180deg)"
                          }}
                          _active={{ transform: "scale(0.95)" }}
                          color="#848E9C"
                          borderRadius="lg"
                          transition="all 0.3s ease"
                          title={t('register.refreshCaptcha', 'Refresh')}
                        >
                          <Icon as={FaShieldAlt} />
                        </Button>
                      </InputRightElement>
                    </InputGroup>

                    {captchaError && (
                      <Box mt={2} p={2} bg="rgba(248, 73, 96, 0.1)" borderRadius="md" borderLeft="3px solid" borderColor="#F84960">
                        <Text color="#F84960" fontSize="sm" fontWeight="500">
                          {t('register.captchaError', 'Verification failed. Please try again.')}
                        </Text>
                      </Box>
                    )}
                  </FormControl>
                </Box>

                {/* Terms and Conditions */}
                <VStack spacing={4} align="start" w="full">
                  <Checkbox
                    isChecked={termsAccepted}
                    onChange={(e) => setTermsAccepted(e.target.checked)}
                    colorScheme="yellow"
                    size="lg"
                  >
                    <Text fontSize="sm" color="#EAECEF">
                      {t('register.termsAgreement', 'I accept the')}{' '}
                      <Link as={RouterLink} to="/terms" color="#FCD535" fontWeight="600" _hover={{ textDecoration: 'underline' }}>
                        {t('register.termsOfService', 'Terms of Service')}
                      </Link>
                      {' '}{t('register.and', 'and')}{' '}
                      <Link as={RouterLink} to="/privacy" color="#FCD535" fontWeight="600" _hover={{ textDecoration: 'underline' }}>
                        {t('register.privacyPolicy', 'Privacy Policy')}
                      </Link>
                    </Text>
                  </Checkbox>

                  <Checkbox
                    isChecked={marketingAccepted}
                    onChange={(e) => setMarketingAccepted(e.target.checked)}
                    colorScheme="yellow"
                    size="lg"
                  >
                    <Text fontSize="sm" color="#EAECEF">
                      {t('register.marketingConsent', 'I want to receive updates about new features and opportunities')}
                    </Text>
                  </Checkbox>
                </VStack>

                {/* Submit Button */}
                <Button
                  type="submit"
                  bg="linear-gradient(135deg, #FCD535 0%, #F8D12F 100%)"
                  color="#0B0E11"
                  _hover={{
                    bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
                    transform: "translateY(-3px)",
                    boxShadow: "0 12px 30px rgba(252, 213, 53, 0.5)"
                  }}
                  _active={{
                    transform: "translateY(-1px)",
                    boxShadow: "0 6px 20px rgba(252, 213, 53, 0.4)"
                  }}
                  transition="all 0.3s ease"
                  isLoading={loading || formSubmitting}
                  loadingText="Creating Account..."
                  size="lg"
                  borderRadius="xl"
                  fontWeight="700"
                  fontSize="lg"
                  w="full"
                  h="60px"
                  position="relative"
                  isDisabled={!termsAccepted}
                  _before={{
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 'xl',
                    background: 'linear-gradient(135deg, rgba(255,255,255,0.2), transparent)',
                    pointerEvents: 'none',
                  }}
                >
                  <HStack spacing={3}>
                    <Icon as={FaRocket} boxSize={5} />
                    <Text>{t('register.createAccount', 'Create Account')}</Text>
                  </HStack>
                </Button>

                {/* Login Link */}
                <VStack spacing={2} pt={4}>
                  <Divider borderColor="rgba(252, 213, 53, 0.2)" />
                  <Text fontSize="sm" color="#848E9C" textAlign="center">
                    {t('register.haveAccount', 'Already have an account?')}{' '}
                    <Link
                      as={RouterLink}
                      to="/login"
                      color="#FCD535"
                      fontWeight="600"
                      _hover={{
                        textDecoration: 'underline',
                        color: '#F8D12F'
                      }}
                      transition="color 0.2s ease"
                    >
                      {t('register.signIn', 'Sign In')}
                    </Link>
                  </Text>
                </VStack>
              </VStack>
            </form>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default Register;
