import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
import User from '../models/userModel';
import { logger } from '../utils/logger';

const createSpecificAdmin = async () => {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI || '*******************************************************************';
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    const email = '<EMAIL>';
    const password = 'Admin@123';
    const firstName = 'Admin';
    const lastName = 'User';

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    
    if (existingUser) {
      console.log(`User with email ${email} already exists`);
      
      // Update existing user to be admin with new password
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash(password, salt);
      
      await User.findByIdAndUpdate(existingUser._id, {
        isAdmin: true,
        password: hashedPassword,
        firstName,
        lastName,
        updatedAt: new Date()
      });
      
      console.log(`✅ Updated existing user ${email} to admin with new password`);
    } else {
      // Create new admin user
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash(password, salt);
      
      const adminUser = new User({
        email: email.toLowerCase(),
        password: hashedPassword,
        firstName,
        lastName,
        isAdmin: true,
        emailVerified: true, // Skip email verification for admin
        kycVerified: true,   // Skip KYC for admin
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      await adminUser.save();
      console.log(`✅ Created new admin user: ${email}`);
    }

    // Verify the admin user can authenticate
    const testUser = await User.findOne({ email: email.toLowerCase() });
    if (testUser) {
      const isPasswordValid = await testUser.comparePassword(password);
      console.log(`🔐 Password verification: ${isPasswordValid ? 'SUCCESS' : 'FAILED'}`);
      console.log(`👤 Admin status: ${testUser.isAdmin ? 'YES' : 'NO'}`);
      console.log(`📧 Email: ${testUser.email}`);
      console.log(`👨‍💼 Name: ${testUser.firstName} ${testUser.lastName}`);
    }

    console.log('\n🎉 Admin user setup completed successfully!');
    console.log('📝 Login credentials:');
    console.log(`   Email: ${email}`);
    console.log(`   Password: ${password}`);
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    process.exit(0);
  }
};

// Run the script
createSpecificAdmin();
