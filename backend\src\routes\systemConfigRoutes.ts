import express from 'express';
import {
  getSystemConfig,
  updateSystemConfig,
  getCryptoAddresses,
  updateCryptoAddresses,
} from '../controllers/systemConfigController';
import { getMaintenanceStatus } from '../middleware/maintenanceMiddleware';
import { protect, admin } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

// Apply middleware to each route individually instead of using router.use

// Public maintenance status route (no authentication required)
router.get('/maintenance-status', wrapController(getMaintenanceStatus));

// Legacy public system configuration route (deprecated)
// This route is kept for backward compatibility but will be removed in future versions
// Use /api/user-system/public instead
router.route('/config/public')
  .get(wrapController(getSystemConfig));

// Note: All system configuration modification operations have been moved to /admin/system/config
// User-specific system configuration endpoints are now available at /api/user-system/*

// Public crypto addresses routes (for homepage)
router.route('/crypto-addresses')
  .get(wrapController(getCryptoAddresses));

// Admin-only crypto addresses routes
router.route('/crypto-addresses/:currency')
  .put(protect, admin, wrapController(updateCryptoAddresses));

export default router;
