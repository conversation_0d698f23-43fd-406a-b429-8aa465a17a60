import React from 'react';
import {
  Box,
  VStack,
  Spinner,
  Text,
  Skeleton,
  SkeletonText,
  HStack,
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';

interface LoadingStateProps {
  type?: 'spinner' | 'skeleton' | 'card';
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  rows?: number;
}

const LoadingState: React.FC<LoadingStateProps> = ({
  type = 'spinner',
  message = 'Loading...',
  size = 'md',
  rows = 3
}) => {
  const { t } = useTranslation();

  if (type === 'skeleton') {
    return (
      <Box
        bg="linear-gradient(135deg, rgba(30, 35, 41, 0.95) 0%, rgba(30, 35, 41, 0.85) 100%)"
        backdropFilter="blur(20px)"
        p={{ base: 4, md: 6 }}
        borderRadius="xl"
        borderWidth="1px"
        borderColor="rgba(240, 185, 11, 0.2)"
        boxShadow="0 8px 32px rgba(0, 0, 0, 0.3)"
      >
        <VStack spacing={4} align="stretch">
          <Skeleton height="20px" startColor="rgba(240, 185, 11, 0.1)" endColor="rgba(240, 185, 11, 0.3)" />
          <SkeletonText 
            mt="4" 
            noOfLines={rows} 
            spacing="4" 
            skeletonHeight="2"
            startColor="rgba(240, 185, 11, 0.1)" 
            endColor="rgba(240, 185, 11, 0.3)"
          />
        </VStack>
      </Box>
    );
  }

  if (type === 'card') {
    return (
      <Box
        bg="linear-gradient(135deg, rgba(30, 35, 41, 0.95) 0%, rgba(30, 35, 41, 0.85) 100%)"
        backdropFilter="blur(20px)"
        p={{ base: 4, md: 6 }}
        borderRadius="xl"
        borderWidth="1px"
        borderColor="rgba(240, 185, 11, 0.2)"
        boxShadow="0 8px 32px rgba(0, 0, 0, 0.3)"
      >
        <VStack spacing={4}>
          <HStack spacing={4} w="100%">
            <Skeleton 
              height="60px" 
              width="60px" 
              borderRadius="lg"
              startColor="rgba(240, 185, 11, 0.1)" 
              endColor="rgba(240, 185, 11, 0.3)"
            />
            <VStack align="start" flex="1" spacing={2}>
              <Skeleton 
                height="16px" 
                width="80%"
                startColor="rgba(240, 185, 11, 0.1)" 
                endColor="rgba(240, 185, 11, 0.3)"
              />
              <Skeleton 
                height="12px" 
                width="60%"
                startColor="rgba(240, 185, 11, 0.1)" 
                endColor="rgba(240, 185, 11, 0.3)"
              />
            </VStack>
          </HStack>
          <SkeletonText 
            mt="2" 
            noOfLines={2} 
            spacing="3" 
            skeletonHeight="2"
            startColor="rgba(240, 185, 11, 0.1)" 
            endColor="rgba(240, 185, 11, 0.3)"
            w="100%"
          />
        </VStack>
      </Box>
    );
  }

  // Default spinner type
  return (
    <Box
      bg="linear-gradient(135deg, rgba(30, 35, 41, 0.95) 0%, rgba(30, 35, 41, 0.85) 100%)"
      backdropFilter="blur(20px)"
      p={{ base: 6, md: 8 }}
      borderRadius="xl"
      borderWidth="1px"
      borderColor="rgba(240, 185, 11, 0.2)"
      boxShadow="0 8px 32px rgba(0, 0, 0, 0.3)"
      textAlign="center"
    >
      <VStack spacing={4}>
        <Spinner
          thickness="3px"
          speed="0.8s"
          emptyColor="rgba(240, 185, 11, 0.2)"
          color="#FCD535"
          size={size}
        />
        <Text
          color="#848E9C"
          fontSize="sm"
          fontWeight="500"
        >
          {t('common.loading', message)}
        </Text>
      </VStack>
    </Box>
  );
};

export default LoadingState;
